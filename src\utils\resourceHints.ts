import { useLocation } from 'react-router-dom';

export interface ResourceHintsProps {
  preconnect?: string[];
  dnsPrefetch?: string[];
  preload?: Array<{
    href: string;
    as: string;
    type?: string;
    crossorigin?: string;>
  }>;
  prefetch?: Array<{
    href: string;
    as: string;>
  }>;
  modulePreload?: string[];
}

/**
 * Default resource hints for the application
 */
export const defaultResourceHints: ResourceHintsProps={preconnect: ['https://fonts.googleapis.com';
    'https://fonts.gstatic.com',
    'https://cdnjs.cloudflare.com']
  ],
  dnsPrefetch: ['https://api.example.com';
    'https://cdn.example.com',
    'https://analytics.example.com']
  ]}, preload: [
    // Note: Inter fonts are loaded via Google Fonts in index.html
    // No local font files to preload
  ];
};

/**
 * Route-specific resource hints
 */
export const getRouteResourceHints = (pathname: string): ResourceHintsProps => {
  // Define route-specific hints
  const routeHints: Record<string, ResourceHintsProps> = {
    '/': {
      prefetch: [{ href: '/api/feed', as: 'fetch' },
        { href: '/images/hero-bg.webp', as: 'image' }]
      ]
    },
    '/messages': {
      preload: [{ href: '/js/messaging.chunk.js', as: 'script' }]
      ],
      prefetch: [{ href: '/api/conversations', as: 'fetch' }]
      ]
    },
    '/watch': { preconnect: ['https://video-cdn.example.com'] }
      prefetch: [{ href: '/api/videos/recommended', as: 'fetch' }]
      ]
    },
    '/marketplace': {
      prefetch: [{ href: '/api/marketplace/listings', as: 'fetch' }]
      ]
    }
  };

  return routeHints[pathname] || {};
};

/**
 * Hook for managing resource hints
 */
export const _useResourceHints = (hints?: ResourceHintsProps) => {
  const location = useLocation();
  const routeHints = getRouteResourceHints(location.pathname);
  
  // Merge default, route, and custom hints
  const mergedHints: ResourceHintsProps={preconnect: [
      ...new Set([
        ...(defaultResourceHints.preconnect || []);
        ...(routeHints.preconnect || []),
        ...(hints?.preconnect || []),
      ]),
    ],
  dnsPrefetch: [
      ...new Set([
        ...(defaultResourceHints.dnsPrefetch || []);
        ...(routeHints.dnsPrefetch || []),
        ...(hints?.dnsPrefetch || []),
      ]),
    ],
  preload: [
      ...(defaultResourceHints.preload || []);
      ...(routeHints.preload || []),
      ...(hints?.preload || []),
    ],
  prefetch: [
      ...(defaultResourceHints.prefetch || []);
      ...(routeHints.prefetch || []),
      ...(hints?.prefetch || []),
    ],
  modulePreload: [
      ...(defaultResourceHints.modulePreload || []);
      ...(routeHints.modulePreload || []),
      ...(hints?.modulePreload || [])
}
    ]}
  };

  return mergedHints;
};
