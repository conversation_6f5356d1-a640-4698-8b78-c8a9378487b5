import React, { useState, useEffect, useCallback, memo } from 'react';
import { Bell, X, Check, Heart, MessageCircle, UserPlus, Calendar, Gift, Zap } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { formatDistanceToNow } from 'date-fns';
import { motion, AnimatePresence } from 'framer-motion';
import { toast } from 'sonner';
import { storage, safeGetArray } from '@/lib/storage';

export interface Notification {
  id: string, type: 'like' | 'comment' | 'friend_request' | 'message' | 'event' | 'birthday' | 'mention' | 'share' | 'reaction', title: string, message: string, timestamp: Date, isRead: boolean, isNew: boolean;
  actionUrl?: string;
  actor: {
    id: string, name: string, avatar: string;
  };
  metadata?: {
    postId?: string;
    eventId?: string;
    messageId?: string;
    reactionType?: string;
  };
}

interface EnhancedNotificationSystemProps {
  isOpen: boolean, onClose: () => void;
  onNotificationClick?: (notification: Notification) => void;
}

const EnhancedNotificationSystem: React.FC<EnhancedNotificationSystemProps> = memo(({
  isOpen,
  onClose,
  onNotificationClick
}) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [filter, setFilter] = useState<'all' | 'unread' | 'mentions'>('all');
  const [isLoading, setIsLoading] = useState(false);

  // Load notifications from storage
  useEffect(() => {
    const savedNotifications = safeGetArray<Notification>('notifications');
    if (savedNotifications.length > 0) {
      setNotifications(savedNotifications.map(n => ({
        ...n,
        timestamp: new Date(n.timestamp)
      })));
    } else {
      // Initialize with mock notifications
      const mockNotifications: Notification[] = [
        {
          id: '1',
          type: 'like',
          title: 'New Like',
          message: 'Sarah Johnson liked your post',
          timestamp: new Date(Date.now() - 5 * 60 * 1000),
          isRead: false,
          isNew: true,
          actor: {
            id: '2',
            name: 'Sarah Johnson',
            avatar: 'https://images.pexels.com/photos/415829/pexels-photo-415829.jpeg?w=400&h=400&fit=crop&crop=face'
          },
          metadata: { postId: 'post-1' }
        },
        {
          id: '2',
          type: 'comment',
          title: 'New Comment',
          message: 'Mike Chen commented on your photo: "Great shot!"',
          timestamp: new Date(Date.now() - 15 * 60 * 1000),
          isRead: false,
          isNew: true,
          actor: {
            id: '3',
            name: 'Mike Chen',
            avatar: 'https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg?w=400&h=400&fit=crop&crop=face'
          },
          metadata: { postId: 'post-2' }
        },
        {
          id: '3',
          type: 'friend_request',
          title: 'Friend Request',
          message: 'Emma Wilson sent you a friend request',
          timestamp: new Date(Date.now() - 30 * 60 * 1000),
          isRead: false,
          isNew: false,
          actor: {
            id: '4',
            name: 'Emma Wilson',
            avatar: 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?w=400&h=400&fit=crop&crop=face'
          }
        },
        {
          id: '4',
          type: 'birthday',
          title: 'Birthday Reminder',
          message: "It's David Kim's birthday today!",
          timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
          isRead: true,
          isNew: false,
          actor: {
            id: '5',
            name: 'David Kim',
            avatar: 'https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg?w=400&h=400&fit=crop&crop=face'
          }
        },
        {
          id: '5',
          type: 'event',
          title: 'Event Reminder',
          message: 'Team Meeting starts in 1 hour',
          timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000),
          isRead: true,
          isNew: false,
          actor: {
            id: '6',
            name: 'Team Calendar',
            avatar: 'https://images.pexels.com/photos/3182812/pexels-photo-3182812.jpeg?w=400&h=400&fit=crop'
          },
          metadata: { eventId: 'event-1' }
        },
        {
          id: '6',
          type: 'reaction',
          title: 'New Reaction',
          message: 'Lisa Wang reacted ❤️ to your story',
          timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),
          isRead: true,
          isNew: false,
          actor: {
            id: '7',
            name: 'Lisa Wang',
            avatar: 'https://images.pexels.com/photos/1130626/pexels-photo-1130626.jpeg?w=400&h=400&fit=crop&crop=face'
          },
          metadata: { reactionType: '❤️' }
        }
      ];
      setNotifications(mockNotifications);
    }
  }, []);

  // Save notifications to storage when they change
  useEffect(() => {
    if (notifications.length > 0) {
      storage.set('notifications', notifications);
    }
  }, [notifications]);

  // Simulate real-time notifications
  useEffect(() => {
    if (!isOpen) return;

    const interval = setInterval(() => {
      if (Math.random() > 0.8) {
        const newNotification: Notification = {
          id: Date.now().toString(),
          type: (['like', 'comment', 'message'] as const)[Math.floor(Math.random() * 3)],
          title: 'New Activity',
          message: 'Someone interacted with your content',
          timestamp: new Date(),
          isRead: false,
          isNew: true,
          actor: {
            id: 'random',
            name: 'Random User',
            avatar: 'https://images.pexels.com/photos/415829/pexels-photo-415829.jpeg?w=400&h=400&fit=crop&crop=face'
          }
        };
        
        setNotifications(prev => [newNotification, ...prev]);
        toast.success('New notification received!');
      }
    }, 30000);

    return () => clearInterval(interval);
  }, [isOpen]);

  const getNotificationIcon = (type: Notification['type']) => {
    switch (type) {
      case 'like':
      case 'reaction':
        return <Heart className="w-4 h-4 text-red-500" />;
      case 'comment':
        return <MessageCircle className="w-4 h-4 text-blue-500" />;
      case 'friend_request':
        return <UserPlus className="w-4 h-4 text-green-500" />;
      case 'event':
        return <Calendar className="w-4 h-4 text-purple-500" />;
      case 'birthday':
        return <Gift className="w-4 h-4 text-pink-500" />;
      case 'mention':
        return <Zap className="w-4 h-4 text-yellow-500" />;
      default:
        return <Bell className="w-4 h-4 text-gray-500" />;
    }
  };

  const markAsRead = useCallback((notificationId: string) => {
    setNotifications(prev => prev.map(n => 
      n.id === notificationId ? { ...n, isRead: true, isNew: false } : n
    ));
  }, []);

  const markAllAsRead = useCallback(() => {
    setIsLoading(true);
    setTimeout(() => {
      setNotifications(prev => prev.map(n => ({ ...n, isRead: true, isNew: false })));
      setIsLoading(false);
      toast.success('All notifications marked as read');
    }, 500);
  }, []);

  const deleteNotification = useCallback((notificationId: string) => {
    setNotifications(prev => prev.filter(n => n.id !== notificationId));
    toast.success('Notification deleted');
  }, []);

  const handleNotificationClick = useCallback((notification: Notification) => {
    markAsRead(notification.id);
    onNotificationClick?.(notification);
  }, [markAsRead, onNotificationClick]);

  const filteredNotifications = notifications.filter(notification => {
    switch (filter) {
      case 'unread':
        return !notification.isRead;
      case 'mentions':
        return notification.type === 'mention';
      default:
        return true;
    }
  });

  const unreadCount = notifications.filter(n => !n.isRead).length;

  if (!isOpen) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}, animate={{ opacity: 1, y: 0 }}, exit={{ opacity: 0, y: -20 }}, className="absolute top-full right-0 mt-2 w-96 bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700 z-50"
    >
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Notifications
          </h3>
          <div className="flex items-center space-x-2">
            {unreadCount > 0 && (
              <Badge variant="destructive" className="text-xs">
                {unreadCount}
              </Badge>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose} className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            >
              <X className="w-4 h-4" />
    </Button>
          </div>
    </div>
        <div className="flex items-center space-x-2 mb-3">
          <Button
            variant={filter === 'all' ? 'default' : 'ghost'} size="sm"
            onClick={() => setFilter('all')} className="text-xs"
          >
            All
          </Button>
          <Button
            variant={filter === 'unread' ? 'default' : 'ghost'} size="sm"
            onClick={() => setFilter('unread')} className="text-xs"
          >
            Unread
          </Button>
          <Button
            variant={filter === 'mentions' ? 'default' : 'ghost'} size="sm"
            onClick={() => setFilter('mentions')} className="text-xs"
          >
            Mentions
          </Button>
    </div>
        {unreadCount > 0 && (
          <Button
            variant="outline"
            size="sm"
            onClick={markAllAsRead} disabled={isLoading}, className="w-full text-xs"
          >
            <Check className="w-3 h-3 mr-1" />
            {isLoading ? 'Marking...' : 'Mark all as read'}
          </Button>
        )}
      </div>

      <ScrollArea className="max-h-96">
        <div className="p-2">
          <AnimatePresence>
            {filteredNotifications.length > 0 ? (
              filteredNotifications.map((notification) => (
                <motion.div
                  key={notification.id} initial={{ opacity: 0, x: -20 }}, animate={{ opacity: 1, x: 0 }}, exit={{ opacity: 0, x: 20 }}, className={`p-3 rounded-lg cursor-pointer transition-colors mb-2 relative ${
                    !notification.isRead
                      ? 'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800'
                      : 'hover:bg-gray-50 dark:hover:bg-gray-700'
                  }`}, onClick={() => handleNotificationClick(notification)}
                >
                  {notification.isNew && (
                    <div className="absolute top-2 right-2 w-2 h-2 bg-blue-500 rounded-full"></div>
                  )}
                  
                  <div className="flex items-start space-x-3">
                    <div className="relative">
                      <Avatar className="w-10 h-10">
                        <AvatarImage src={notification.actor.avatar} />
                        <AvatarFallback>
                          {notification.actor.name.charAt(0)}
                        </AvatarFallback>
    </Avatar>
                      <div className="absolute -bottom-1 -right-1 bg-white dark:bg-gray-800 rounded-full p-1">
                        {getNotificationIcon(notification.type)}
                      </div>
    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 dark:text-white">
                        {notification.title}
                      </p>
                      <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                        {notification.message}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                        {formatDistanceToNow(notification.timestamp, { addSuffix: true })}
                      </p>
    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        deleteNotification(notification.id);
                      }}, className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 opacity-0 group-hover:opacity-100 transition-opacity"
                    >
                      <X className="w-3 h-3" />
    </Button>
                  </div>
                </motion.div>
              ))
            ) : (
              <div className="text-center py-8">
                <Bell className="w-12 h-12 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
                <p className="text-gray-500 dark:text-gray-400">
                  {filter === 'unread' ? 'No unread notifications' : 'No notifications yet'}
                </p>
    </div>
            )}
          </AnimatePresence>
    </div>
      </ScrollArea>
    </motion.div>
  );
});

EnhancedNotificationSystem.displayName = 'EnhancedNotificationSystem';

export default EnhancedNotificationSystem;
