import React from 'react';
import { AlertTriangle, RefreshCw, Home } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import EnhancedErrorBoundary, { type ErrorFallbackProps } from '../errorBoundaries/EnhancedErrorBoundary';

interface StoryErrorFallbackProps extends ErrorFallbackProps {
  onRetry?: () => void;
  onGoHome?: () => void;
}

const StoryErrorFallback: React.FC<StoryErrorFallbackProps> = ({
  error,
  resetError,
  retryCount,
  errorId,
  onRetry,
  onGoHome
}) => {
  const handleRetry = () => {
    if (onRetry) {
      onRetry();
    }
    resetError();
  };

  const handleGoHome = () => {
    if (onGoHome) {
      onGoHome();
    } else {
      window.location.href = '/';
    }
  };

  return (
    <div className="flex items-center justify-center min-h-[400px] p-4">
      <Card className="w-full max-w-md border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20">
        <CardHeader className="text-center">
          <div className="flex items-center justify-center mb-2">
            <AlertTriangle className="w-12 h-12 text-red-500" />
    </div>
          <CardTitle className="text-red-700 dark:text-red-400">
            Story Creator Error
          </CardTitle>
          <div className="flex items-center justify-center gap-2 mt-2">
            <Badge variant="destructive" className="text-xs">
              STORY ERROR
            </Badge>
            {retryCount > 0 && (
              <Badge variant="outline" className="text-xs">
                Retry {retryCount}
              </Badge>
            )}
          </div>
    </CardHeader>
        <CardContent className="text-center space-y-4">
          <p className="text-red-600 dark:text-red-300 text-sm">
            Something went wrong while creating your story. Your progress may have been lost.
          </p>
          
          {process.env.NODE_ENV === 'development' && (
            <details className="text-left">
              <summary className="text-xs text-gray-500 cursor-pointer hover:text-gray-700">
                Error Details (Dev Mode)
              </summary>
              <div className="mt-2 p-2 bg-gray-100 dark:bg-gray-800 rounded text-xs font-mono">
                <div className="text-red-600 dark:text-red-400 mb-1">
                  {error.message}
                </div>
                <div className="text-gray-600 dark:text-gray-400 text-xs">
                  ID: {errorId}
                </div>
                {error.stack && (
                  <div className="text-gray-500 dark:text-gray-500 text-xs mt-1 max-h-20 overflow-y-auto">
                    {error.stack.split('\n').slice(0, 3).join('\n')}
                  </div>
                )}
              </div>
    </details>
          )}
          
          <div className="flex gap-2 justify-center">
            <Button
              onClick={handleRetry} size="sm"
              variant="outline"
              className="border-red-300 text-red-700 hover:bg-red-100 dark:border-red-600 dark:text-red-400 dark:hover:bg-red-900/30"
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Try Again
            </Button>
            
            <Button
              onClick={handleGoHome} size="sm"
              variant="outline"
            >
              <Home className="w-4 h-4 mr-2" />
              Go Home
            </Button>
    </div>
          <p className="text-xs text-gray-500 dark:text-gray-400">
            If this problem persists, please refresh the page or contact support.
          </p>
    </CardContent>
      </Card>
    </div>
  );
};

interface StoryErrorBoundaryProps {
  children: React.ReactNode;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
  onRetry?: () => void;
  onGoHome?: () => void;
}

const StoryErrorBoundary: React.FC<StoryErrorBoundaryProps> = ({
  children,
  onError,
  onRetry,
  onGoHome
}) => {
  const handleError = (error: Error, errorInfo: React.ErrorInfo) => {
    // Log story-specific error context
    console.error('Story Creator Error:', {
      error: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    });

    // Call custom error handler if provided
    onError?.(error, errorInfo);
  };

  return (
    <EnhancedErrorBoundary
      fallback={(props) => (
        <StoryErrorFallback
          {...props} onRetry={onRetry}, onGoHome={onGoHome}
        />
      )}, onError={handleError} level="feature"
      componentName="StoryCreator"
      enableRetry={true} maxRetries={3}, showErrorDetails={process.env.NODE_ENV === 'development'} enableErrorReporting={true}
    >
      {children}
    </EnhancedErrorBoundary>
  );
};

export default StoryErrorBoundary;
export { StoryErrorFallback };
