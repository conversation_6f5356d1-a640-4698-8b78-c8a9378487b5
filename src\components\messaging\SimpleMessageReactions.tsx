/**
 * SimpleMessageReactions Component
 * Lightweight version of MessageReactions for better performance
 */

import React, { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { COMMON_REACTIONS } from '@/types/messaging';

interface SimpleMessageReactionsProps {
  messageId: string, reactions: Record<string, string[]>;
  onToggleReaction: (messageId: string, emoji: string) => void; currentUserId: string;
  className?: string;
  disabled?: boolean;
}

export const SimpleMessageReactions: React.FC<SimpleMessageReactionsProps> = ({
  messageId,
  reactions,
  onToggleReaction,
  currentUserId,
  className,
  disabled = false
}) => {
  const [showPicker, setShowPicker] = useState(false);

  // Get existing reactions
  const existingReactions = Object.entries(reactions)
    .filter(([_, userIds]) => userIds.length > 0)
    .sort(([; a], [, b]) => b.length - a.length);

  const handleReactionClick = useCallback((emoji: string) => {
    if (!disabled) {
      onToggleReaction(messageId, emoji);
    }
  }, [messageId, onToggleReaction, disabled]);

  const handleQuickReaction = useCallback((emoji: string) => {
    handleReactionClick(emoji);
    setShowPicker(false);
  }, [handleReactionClick]);

  return (
    <div className={cn("flex items-center gap-1 mt-1", className)}>
      {/* Display existing reactions */}
      <AnimatePresence>
        {existingReactions.map(([emoji, userIds]) => (
          <motion.button
            key={emoji} initial={{ scale: 0, opacity: 0 }}, animate={{ scale: 1, opacity: 1 }}, exit={{ scale: 0, opacity: 0 }}, whileHover={{ scale: 1.05 }}, whileTap={{ scale: 0.95 }}, onClick={() => handleReactionClick(emoji)} disabled={disabled}, className={cn(
              "flex items-center gap-1 px-2 py-1 rounded-full text-xs",
              "transition-colors duration-200 border",
              userIds.includes(currentUserId)
                ? "bg-blue-100 border-blue-300 text-blue-700 dark:bg-blue-900/50 dark:border-blue-600" 
                : "bg-gray-100 border-gray-300 hover:bg-gray-200 dark:bg-gray-800 dark:border-gray-600",
              disabled && "opacity-50 cursor-not-allowed"
            )}
          >
            <span className="text-sm">{emoji}</span>
            <span className="font-medium">{userIds.length}</span>
          </motion.button>
        ))}
      </AnimatePresence>

      {/* Quick reaction picker */}
      <div className="relative">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setShowPicker(!showPicker)} disabled={disabled}, className="h-7 w-7 p-0 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700"
        >
          <span className="text-lg">😊</span>
    </Button>
        {showPicker && (
          <motion.div
            initial={{ scale: 0.8, opacity: 0, y: 10 }}, animate={{ scale: 1, opacity: 1, y: 0 }}, exit={{ scale: 0.8, opacity: 0, y: 10 }}, className="absolute bottom-full left-0 mb-2 p-2 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50"
          >
            <div className="flex gap-1">
              {COMMON_REACTIONS.map(emoji => (
                <button
                  key={emoji} onClick={() => handleQuickReaction(emoji)}, className="text-xl p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                >
                  {emoji}
                </button>
              ))}
            </div>
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default SimpleMessageReactions;