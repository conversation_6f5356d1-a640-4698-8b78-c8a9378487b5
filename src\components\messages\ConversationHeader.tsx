import React, { memo, useCallback } from 'react';
import { MoreHorizontal, Phone, Video } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { User } from '@/types/messaging';

interface ConversationHeaderProps {
  user: User | undefined, isMobile: boolean, onBackToList: () => void;
}

/**
 * ConversationHeader component displays the header for a conversation
 * including user info and action buttons for calls and more options.
 * 
 * @param {ConversationHeaderProps} props - The component props
 * @param {User | undefined} props.user - The user object for the conversation
 * @param {boolean} props.isMobile - Whether the device is mobile
 * @param {Function} props.onBackToList - Callback to navigate back to conversation list
 * @returns {JSX.Element} The rendered conversation header
 */
const ConversationHeader: React.FC<ConversationHeaderProps> = memo(({
  user,
  isMobile,
  onBackToList
}) => {
  /**
   * <PERSON>les starting an audio call by opening the live page
   * and showing a success toast notification
   */
  const handleAudioCall = useCallback(() => {
    window.open('/live', '_blank');
    toast.success('Starting audio call...');
  }, []);

  /**
   * Handles starting a video call by opening the live page
   * and showing a success toast notification
   */
  const handleVideoCall = useCallback(() => {
    window.open('/live', '_blank');
    toast.success('Starting video call...');
  }, []);
  // Handle case where user might be undefined
  if (!user) {
    return (
      <div className="p-3 border-b flex items-center justify-between dark:border-gray-700">
        <div className="flex items-center space-x-3">
          {isMobile && (
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={onBackToList} className="md:hidden h-9 w-9 p-0"
              data-testid="back-button"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="w-5 h-5"
              >
                <path d="m15 18-6-6 6-6" />
    </svg>
            </Button>
          )}
          <Avatar className="h-10 w-10">
            <AvatarFallback>?</AvatarFallback>
    </Avatar>
          <div>
            <p className="font-medium text-sm dark:text-white">Unknown User</p>
            <p className="text-xs text-gray-500 dark:text-gray-400">Offline</p>
    </div>
        </div>
        <div className="flex items-center space-x-1">
          <Button 
            variant="ghost" 
            size="sm" 
            className="h-9 w-9 p-0" 
            onClick={handleAudioCall}
          >
            <Phone className="w-5 h-5" />
    </Button>
          <Button 
            variant="ghost" 
            size="sm" 
            className="h-9 w-9 p-0" 
            onClick={handleVideoCall}
          >
            <Video className="w-5 h-5" />
    </Button>
          <Button variant="ghost" size="sm" className="h-9 w-9 p-0">
            <MoreHorizontal className="w-5 h-5" />
    </Button>
        </div>
    </div>
    );
  }

  return (
    <div className="p-3 border-b flex items-center justify-between dark:border-gray-700">
      <div className="flex items-center space-x-3">
        {isMobile && (
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={onBackToList} className="md:hidden h-9 w-9 p-0"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="w-5 h-5"
            >
              <path d="m15 18-6-6 6-6" />
    </svg>
          </Button>
        )}
        <Avatar className="h-10 w-10">
          <AvatarImage src={user.avatar} />
          <AvatarFallback>{user.name.charAt(0)}</AvatarFallback>
    </Avatar>
        <div>
          <p className="font-medium text-sm dark:text-white">{user.name}</p>
          <p className="text-xs text-gray-500 dark:text-gray-400">
            {user.isOnline ? 'Active now' : user.lastActive}
          </p>
    </div>
      </div>
      <div className="flex items-center space-x-1">
        <Button 
          variant="ghost" 
          size="sm" 
          className="h-9 w-9 p-0" 
          onClick={handleAudioCall}
        >
          <Phone className="w-5 h-5" />
    </Button>
        <Button 
          variant="ghost" 
          size="sm" 
          className="h-9 w-9 p-0" 
          onClick={handleVideoCall}
        >
          <Video className="w-5 h-5" />
    </Button>
        <Button variant="ghost" size="sm" className="h-9 w-9 p-0">
          <MoreHorizontal className="w-5 h-5" />
    </Button>
      </div>
    </div>
  );
});

ConversationHeader.displayName = 'ConversationHeader';

export default ConversationHeader;
