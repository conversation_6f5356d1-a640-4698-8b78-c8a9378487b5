import React, { useState, useCallback, useRef, useEffect } from 'react';
import { MoreHorizontal, Reply, Edit, Trash2, Co<PERSON>, Forward, Heart, ThumbsUp, Smile } from 'lucide-react';
import { useKeyboardNavigation, useMessagingShortcuts } from '../../hooks/useKeyboardNavigation';
import { AdvancedMessage } from '../../types/messaging';

interface KeyboardNavigableMessageProps {
  message: AdvancedMessage, currentUserId: string;
  isSelected?: boolean;
  onReply?: (messageId: string) => void;
  onEdit?: (messageId: string) => void;
  onDelete?: (messageId: string) => void;
  onCopy?: (content: string) => void;
  onForward?: (message: AdvancedMessage) => void;
  onAddReaction?: (messageId: string, emoji: string) => void;
  onOpenThread?: (messageId: string) => void;
  className?: string;
}

export const KeyboardNavigableMessage: React.FC<KeyboardNavigableMessageProps> = ({
  message,
  currentUserId,
  isSelected = false,
  onReply,
  onEdit,
  onDelete,
  onCopy,
  onForward,
  onAddReaction,
  onOpenThread,
  className = ''
}) => {
  const [showActions, setShowActions] = useState(false);
  const [showReactionPicker, setShowReactionPicker] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editContent, setEditContent] = useState(message.content);
  const editInputRef = useRef<HTMLTextAreaElement>(null);

  const isOwnMessage = message.senderId === currentUserId;

  // Keyboard navigation for the message
  const {
    elementRef,
    isFocused,
    announceAction;
    focusElement
  } = useKeyboardNavigation({
    elementId: `message-${message.id}`,
    elementType: 'message',
    priority: message.timestamp,
    onShortcut: handleKeyboardShortcut
  });

  // Messaging shortcuts
  useMessagingShortcuts({
    onEditMessage: () => isOwnMessage && handleEdit(); onDeleteMessage: () => isOwnMessage && handleDelete(); onReplyMessage: () => handleReply(); onCopyMessage: () => handleCopy(); onForwardMessage: () => handleForward(); onOpenThread: () => handleOpenThread(); onAddReaction: (emoji) => handleAddReaction(emoji)
  });

  function handleKeyboardShortcut(action: string, event: KeyboardEvent) {
    if (!isFocused) return;

    switch (action) {
      case 'reply_message':
        handleReply();
        break;
      case 'edit_message':
        if (isOwnMessage) handleEdit();
        break;
      case 'delete_message':
        if (isOwnMessage) handleDelete();
        break;
      case 'copy_message':
        handleCopy();
        break;
      case 'forward_message':
        handleForward();
        break;
      case 'open_thread':
        handleOpenThread();
        break;
      case 'open_reactions':
        setShowReactionPicker(!showReactionPicker);
        break;
      case 'cancel_edit':
        if (isEditing) {
          setIsEditing(false);
          setEditContent(message.content);
        }
        break;
    }
  }

  const handleReply = useCallback(() => {
    onReply?.(message.id);
    announceAction(`Replying to message from ${message.senderId}`);
  }, [message.id, message.senderId, onReply, announceAction]);

  const handleEdit = useCallback(() => {
    if (!isOwnMessage) return;
    setIsEditing(true);
    announceAction('Editing message');
    
    // Focus the edit input after a brief delay
    setTimeout(() => {
      editInputRef.current?.focus();
    }, 100);
  }, [isOwnMessage, announceAction]);

  const handleDelete = useCallback(() => {
    if (!isOwnMessage) return;
    onDelete?.(message.id);
    announceAction('Message deleted');
  }, [isOwnMessage, message.id, onDelete, announceAction]);

  const handleCopy = useCallback(() => {
    onCopy?.(message.content);
    announceAction('Message copied to clipboard');
  }, [message.content, onCopy, announceAction]);

  const handleForward = useCallback(() => {
    onForward?.(message);
    announceAction('Forwarding message');
  }, [message, onForward, announceAction]);

  const handleOpenThread = useCallback(() => {
    onOpenThread?.(message.id);
    announceAction('Opening message thread');
  }, [message.id, onOpenThread, announceAction]);

  const handleAddReaction = useCallback((emoji: string) => {
    onAddReaction?.(message.id, emoji);
    announceAction(`Added ${emoji} reaction`);
    setShowReactionPicker(false);
  }, [message.id, onAddReaction, announceAction]);

  const handleSaveEdit = useCallback(() => {
    // In a real implementation, this would call an onEditSave callback
    setIsEditing(false);
    announceAction('Message edited');
  }, [announceAction]);

  const handleCancelEdit = useCallback(() => {
    setIsEditing(false);
    setEditContent(message.content);
    announceAction('Edit cancelled');
  }, [message.content, announceAction]);

  const formatTimestamp = (timestamp: number) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  // Focus management for editing
  useEffect(() => {
    if (isEditing && editInputRef.current) {
      editInputRef.current.focus();
      editInputRef.current.setSelectionRange(editContent.length, editContent.length);
    }
  }, [isEditing, editContent.length]);

  const commonReactions = ['👍', '❤️', '😂', '😮', '😢', '😡'];

  return (
    <div
      ref={elementRef} className={`keyboard-navigable-message group relative ${className} ${
        isFocused ? 'ring-2 ring-blue-500 ring-opacity-50' : ''
      } ${isSelected ? 'bg-blue-50' : ''}`}, tabIndex={0} role="article"
      aria-label={`Message from ${message.senderId}, at ${formatTimestamp(message.timestamp)}`}, onMouseEnter={() => setShowActions(true)} onMouseLeave={() => setShowActions(false)}, onFocus={() => setShowActions(true)} onBlur={() => setShowActions(false)}
    >
      <div className={`flex ${isOwnMessage ? 'justify-end' : 'justify-start'}, mb-2`}>
        <div className={`max-w-xs lg:max-w-md ${isOwnMessage ? 'order-2' : 'order-1'}`}>
          {/* Message Bubble */}
          <div
            className={`relative px-4 py-2 rounded-lg ${
              isOwnMessage
                ? 'bg-blue-600 text-white'
                : 'bg-gray-200 text-gray-900'
            }`}
          >
            {/* Message Content */}
            {isEditing ? (
              <div className="space-y-2">
                <textarea
                  ref={editInputRef} value={editContent}, onChange={(e) => setEditContent(e.target.value)} className="w-full p-2 text-gray-900 bg-white rounded border resize-none"
                  rows={3} onKeyDown={(e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault();
                      handleSaveEdit();
                    }, else if (e.key === 'Escape') {
                      handleCancelEdit();
                    }
                  }}
                  aria-label="Edit message content"
                />
                <div className="flex gap-2">
                  <button
                    onClick={handleSaveEdit} className="px-3 py-1 text-xs bg-green-600 text-white rounded hover:bg-green-700"
                    aria-label="Save changes"
                  >
                    Save
                  </button>
                  <button
                    onClick={handleCancelEdit} className="px-3 py-1 text-xs bg-gray-600 text-white rounded hover:bg-gray-700"
                    aria-label="Cancel editing"
                  >
                    Cancel
                  </button>
    </div>
              </div>
            ) : (
              <div className="break-words">
                {message.content}
              </div>
            )}

            {/* Message Metadata */}
            <div className={`flex items-center justify-between mt-1 text-xs ${
              isOwnMessage ? 'text-blue-100' : 'text-gray-500'
            }`}>
              <span>{formatTimestamp(message.timestamp)}</span>
              {message.status && (
                <span className="ml-2" aria-label={`Message status: ${message.status}`}>
                  {message.status === 'read' ? '✓✓' : message.status === 'delivered' ? '✓✓' : '✓'}
                </span>
              )}
            </div>
    </div>
          {/* Reactions */}
          {message.reactions && Object.keys(message.reactions).length > 0 && (
            <div className="flex flex-wrap gap-1 mt-1">
              {Object.entries(message.reactions).map(([emoji, users]) => (
                <button
                  key={emoji} onClick={() => handleAddReaction(emoji)}, className="flex items-center gap-1 px-2 py-1 text-xs bg-gray-100 rounded-full hover:bg-gray-200 transition-colors"
                  aria-label={`${emoji}, reaction by ${users.length} user${users.length > 1 ? 's' : ''}`}
                >
                  <span>{emoji}</span>
                  <span>{users.length}</span>
    </button>
              ))}
            </div>
          )}

          {/* Message Actions */}
          {(showActions || isFocused) && !isEditing && (
            <div className={`flex items-center gap-1 mt-1 ${
              isOwnMessage ? 'justify-end' : 'justify-start'
            }`}>
              <button
                onClick={handleReply} className="p-1 rounded hover:bg-gray-100 transition-colors"
                title="Reply (Ctrl+R)"
                aria-label="Reply to message"
              >
                <Reply className="w-3 h-3 text-gray-500" />
    </button>
              {isOwnMessage && (
                <button
                  onClick={handleEdit} className="p-1 rounded hover:bg-gray-100 transition-colors"
                  title="Edit (Ctrl+E)"
                  aria-label="Edit message"
                >
                  <Edit className="w-3 h-3 text-gray-500" />
    </button>
              )}

              <button
                onClick={handleCopy} className="p-1 rounded hover:bg-gray-100 transition-colors"
                title="Copy (Ctrl+C)"
                aria-label="Copy message"
              >
                <Copy className="w-3 h-3 text-gray-500" />
    </button>
              <button
                onClick={handleForward} className="p-1 rounded hover:bg-gray-100 transition-colors"
                title="Forward (Ctrl+F)"
                aria-label="Forward message"
              >
                <Forward className="w-3 h-3 text-gray-500" />
    </button>
              <button
                onClick={() => setShowReactionPicker(!showReactionPicker)} className="p-1 rounded hover:bg-gray-100 transition-colors"
                title="Add reaction (Alt+R)"
                aria-label="Add reaction"
              >
                <Smile className="w-3 h-3 text-gray-500" />
    </button>
              <button
                onClick={handleOpenThread} className="p-1 rounded hover:bg-gray-100 transition-colors"
                title="Open thread (Ctrl+T)"
                aria-label="Open message thread"
              >
                <MoreHorizontal className="w-3 h-3 text-gray-500" />
    </button>
              {isOwnMessage && (
                <button
                  onClick={handleDelete} className="p-1 rounded hover:bg-gray-100 transition-colors"
                  title="Delete (Delete)"
                  aria-label="Delete message"
                >
                  <Trash2 className="w-3 h-3 text-gray-500" />
    </button>
              )}
            </div>
          )}

          {/* Reaction Picker */}
          {showReactionPicker && (
            <div className="absolute z-10 mt-1 p-2 bg-white border border-gray-200 rounded-lg shadow-lg">
              <div className="flex gap-1" role="toolbar" aria-label="Reaction picker">
                {commonReactions.map((emoji, index) => (
                  <button
                    key={emoji} onClick={() => handleAddReaction(emoji)}, className="p-2 rounded hover:bg-gray-100 transition-colors"
                    title={`Add ${emoji}, reaction (Alt+${index + 1})`}
                    aria-label={`Add ${emoji}, reaction`}
                  >
                    {emoji}
                  </button>
                ))}
              </div>
    </div>
          )}
        </div>
    </div>
      {/* Screen reader only content */}
      <div className="sr-only">
        Message content: {message.content}. 
        Sent by {message.senderId} at {formatTimestamp(message.timestamp)}.
        {message.reactions && Object.keys(message.reactions).length > 0 && (
          <>
            Reactions: {Object.entries(message.reactions).map(([emoji, users]) => 
              `${emoji} by ${users.length} user${users.length > 1 ? 's' : ''}`
            ).join(', ')}.
          </>
        )}
        Press Enter to focus message actions, or use keyboard shortcuts for quick actions.
      </div>
    </div>
  );
};

export default KeyboardNavigableMessage;