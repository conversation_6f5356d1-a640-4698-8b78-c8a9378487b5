import React from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { MessageCircle, Share, Bell, BellOff, MoreHorizontal, MapPin, Link, Mail, Phone, Flag, Ban, UserX, Copy, ExternalLink, X, Users } from 'lucide-react';
import { toast } from 'sonner';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { storage } from '@/lib/storage';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/hooks/useAuth';

interface PageHeaderProps {
  page: {
    id: string, name: string, category: string, description: string, avatar: string, cover: string, followers: number, isVerified: boolean, isFollowing: boolean;
    website?: string;
    location?: string;
    phone?: string;
    email?: string;
  };
}

const PageHeader: React.FC<PageHeaderProps> = ({ page }) => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [isFollowing, setIsFollowing] = React.useState(page.isFollowing);
  const [isNotificationsEnabled, setIsNotificationsEnabled] = React.useState(false);
  const [showMessagingModal, setShowMessagingModal] = React.useState(false);
  const [messageText, setMessageText] = React.useState('');

  const handleFollow = () => {
    const newFollowState = !isFollowing;
    setIsFollowing(newFollowState);
    
    // Update localStorage
    interface StoredPage {
      id: string | number, isFollowing: boolean;
      [key: string]: unknown;
    }
    const allPages = storage.get<StoredPage[]>('user_pages', []);
    const updatedPages = allPages.map(p => 
      p.id.toString() === page.id ? { ...p, isFollowing: newFollowState } : p
    );
    storage.set('user_pages', updatedPages);
    
    toast.success(isFollowing ? `Unfollowed ${page.name}` : `Following ${page.name}`);
  };

  const handleNotifications = () => {
    setIsNotificationsEnabled(!isNotificationsEnabled);
    toast.success(isNotificationsEnabled 
      ? `Notifications turned off for ${page.name}` 
      : `Notifications turned on for ${page.name}`);
  };

  const handleMessage = () => {
    setShowMessagingModal(true);
  };

  const handleShare = () => {
    navigator.clipboard.writeText(`Check out ${page.name} on Facebook: https://facebook.com/pages/${page.id}`);
    toast.success('Page link copied to clipboard');
  };

  return (
    <>
      <div className="space-y-4">
      {/* Cover Photo */}
      <div className="relative h-48 sm:h-64 md:h-80 bg-gray-200 rounded-lg overflow-hidden">
        <img
          src={page.cover} alt={`${page.name}, cover`}, className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent"></div>
    </div>
      {/* Page Info */}
      <div className="relative px-4 sm:px-6 -mt-16 sm:-mt-20">
        <div className="bg-white rounded-lg shadow-md p-4 sm:p-6 dark:bg-gray-800">
          <div className="flex flex-col sm:flex-row sm:items-end gap-4">
            <Avatar className="w-24 h-24 sm:w-32 sm:h-32 border-4 border-white rounded-full dark:border-gray-800">
              <AvatarImage src={page.avatar} />
              <AvatarFallback className="text-2xl">{page.name.charAt(0)}</AvatarFallback>
    </Avatar>
            <div className="flex-1">
              <div className="flex items-center space-x-2">
                <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white">{page.name}</h1>
                {page.isVerified && (
                  <Badge className="bg-blue-500 text-white">
                    <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round">
                      <polyline points="20 6 9 17 4 12"></polyline>
    </svg>
                  </Badge>
                )}
              </div>
              <div className="flex flex-wrap items-center gap-x-4 gap-y-2 mt-2">
                <Badge variant="outline" className="text-sm dark:border-gray-600">
                  {page.category}
                </Badge>
                <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                  <Users className="w-4 h-4 mr-1" />
                  <span>{page.followers.toLocaleString()} followers</span>
    </div>
              </div>
              
              <div className="flex flex-wrap items-center gap-x-4 gap-y-2 mt-3 text-sm text-gray-600 dark:text-gray-400">
                {page.website && (
                  <div className="flex items-center">
                    <Link className="w-4 h-4 mr-1" />
                    <a href={page.website} target="_blank" rel="noopener noreferrer" className="hover:underline">
                      {page.website.replace(/^https?:\/\//, '')}
                    </a>
    </div>
                )}
                {page.location && (
                  <div className="flex items-center">
                    <MapPin className="w-4 h-4 mr-1" />
                    <span>{page.location}</span>
    </div>
                )}
                {page.phone && (
                  <div className="flex items-center">
                    <Phone className="w-4 h-4 mr-1" />
                    <a href={`tel:${page.phone}`}, className="hover:underline">
                      {page.phone}
                    </a>
    </div>
                )}
                {page.email && (
                  <div className="flex items-center">
                    <Mail className="w-4 h-4 mr-1" />
                    <a href={`mailto:${page.email}`}, className="hover:underline">
                      {page.email}
                    </a>
    </div>
                )}
              </div>
    </div>
            <div className="flex flex-wrap gap-2 mt-4 sm:mt-0">
              <Button 
                onClick={handleFollow} variant={isFollowing ? "outline" : "default"}, className={isFollowing ? "bg-gray-100" : ""}
              >
                {isFollowing ? 'Following' : 'Follow'}
              </Button>
              
              {isFollowing && (
                <Button
                  variant="outline"
                  size="icon"
                  onClick={handleNotifications} className={isNotificationsEnabled ? "bg-gray-100" : ""}
                >
                  {isNotificationsEnabled ? (
                    <Bell className="h-4 w-4" />
                  ) : (
                    <BellOff className="h-4 w-4" />
                  )}
                </Button>
              )}
              
              <Button variant="outline" onClick={handleMessage}>
                <MessageCircle className="h-4 w-4 mr-2" />
                Message
              </Button>
              
              <Button variant="outline" onClick={handleShare}>
                <Share className="h-4 w-4 mr-2" />
                Share
              </Button>
              
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="icon">
                    <MoreHorizontal className="h-4 w-4" />
    </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56">
                  <DropdownMenuItem onClick={() => {
                    navigator.clipboard.writeText(window.location.href);
                    toast.success('Page link copied!');
                  }}>
                    <Copy className="mr-2 h-4 w-4" />
                    Copy link to page
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => window.open(window.location.href, '_blank')}>
                    <ExternalLink className="mr-2 h-4 w-4" />
                    Open in new tab
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => {
                    toast.success(`Blocked ${page.name}`);
                    // In a real app, this would update backend and UI state
                  }}>
                    <Ban className="mr-2 h-4 w-4" />
                    Block this page
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => {
                    navigate('/settings');
                    toast.success('Opening report form...');
                  }}>
                    <Flag className="mr-2 h-4 w-4" />
                    Report page
                  </DropdownMenuItem>
                  {isFollowing && (
                    <>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={() => {
                        setIsFollowing(false);
                        toast.success(`Unfollowed ${page.name}`);
                      }}>
                        <UserX className="mr-2 h-4 w-4" />
                        Unfollow
                      </DropdownMenuItem>
                    </>
                  )}
                </DropdownMenuContent>
    </DropdownMenu>
            </div>
    </div>
        </div>
    </div>
      </div>
      
      {/* Messaging Modal */}
      <Dialog open={showMessagingModal} onOpenChange={setShowMessagingModal}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <Avatar className="h-8 w-8">
                  <AvatarImage src={page.avatar} />
                  <AvatarFallback>{page.name[0]}</AvatarFallback>
    </Avatar>
                <div>
                  <div className="font-semibold">{page.name}</div>
                  <div className="text-sm text-gray-500 font-normal">
                    {page.category}
                  </div>
    </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowMessagingModal(false)} className="h-6 w-6 p-0"
              >
                <X className="h-4 w-4" />
    </Button>
            </div>
    </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          <div className="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <img
              src={page.avatar} alt={page.name}, className="w-12 h-12 object-cover rounded"
            />
            <div className="flex-1 min-w-0">
              <div className="font-medium text-sm truncate">{page.name}</div>
              <div className="text-sm text-gray-500">
                {page.category}
              </div>
    </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-2">Your message</label>
            <textarea
              value={messageText} onChange={(e) => setMessageText(e.target.value)}, className="w-full p-3 border border-gray-300 rounded-lg resize-none dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              rows={4} placeholder="Write your message..."
            />
    </div>
          <div className="flex space-x-2">
            <Button
              variant="outline"
              className="flex-1"
              onClick={() => setShowMessagingModal(false)}
            >
              Cancel
            </Button>
            <Button
              className="flex-1"
              onClick={() => {
                if (messageText.trim()) {
                  // Create a new conversation
                  const newConversation = {
                    id: `page-${page.id}-${Date.now()}`,
                    participants: [
                      {
                        id: user?.id || 'current-user',
                        name: user?.user_metadata?.full_name || 'You',
                        avatar: user?.user_metadata?.avatar_url || ''
                      },
                      {
                        id: page.id,
                        name: page.name,
                        avatar: page.avatar
                      }
                    ],
                    lastMessage: {
                      content: messageText,
                      timestamp: new Date().toISOString(),
                      senderId: user?.id || 'current-user'
                    },
                    unreadCount: 0,
                    type: 'page' as const
                  };

                  // Store conversation in localStorage
                  interface StoredConversation {
                    id: string, participants: Array<{
                      id: string, name: string, avatar: string;
                    }>;
                    lastMessage: {
                      content: string, timestamp: string, senderId: string;
                    };
                    unreadCount: number, type: 'page' | 'user' | 'group';
                  }
                  const existingConversations = storage.get<StoredConversation[]>('conversations', []);
                  existingConversations.unshift(newConversation);
                  storage.set('conversations', existingConversations);

                  toast.success(`Message sent to ${page.name}!`);
                  setShowMessagingModal(false);
                  setMessageText('');

                  // Navigate to messages
                  navigate('/messages');
                } else {
                  toast.error('Please enter a message');
                }
              }}
            >
              <MessageCircle className="w-4 h-4 mr-2" />
              Send Message
            </Button>
    </div>
          <div className="text-center space-y-2">
            <p className="text-sm text-gray-500">
              Messages to pages are typically responded to within 24 hours
            </p>
            <div className="flex justify-center gap-2 pt-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  setMessageText('Hi, I have a question about your page.');
                }}
              >
                Quick: Question
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  setMessageText('Hi, I\'m interested in learning more about your services.');
                }}
              >
                Quick: Info Request
              </Button>
    </div>
          </div>
    </div>
      </DialogContent>
    </Dialog>
  </>
  );
};

export default PageHeader;