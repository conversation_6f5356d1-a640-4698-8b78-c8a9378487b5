import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { UserPlus, MoreHorizontal, MessageCircle } from 'lucide-react';
import { toast } from 'sonner';

interface Friend {
  id: string, name: string, avatar: string, onlineStatus: 'online' | 'offline' | 'busy', lastSeen: string, mutualFriends: number;
}

const FriendSystemEnhancements: React.FC = () => {
  const [friends] = useState<Friend[]>([
    {
      id: '1',
      name: '<PERSON>',
      avatar: '/images/alice.jpg',
      onlineStatus: 'online',
      lastSeen: '',
      mutualFriends: 5
    },
    {
      id: '2',
      name: '<PERSON>',
      avatar: '/images/bob.jpg',
      onlineStatus: 'offline',
      lastSeen: '2 hours ago',
      mutualFriends: 3
    },
    {
      id: '3',
      name: '<PERSON>',
      avatar: '/images/cathy.jpg',
      onlineStatus: 'busy',
      lastSeen: '1 day ago',
      mutualFriends: 8
    }
  ]);

  const handleAddFriend = (_friendId: string) => {
    toast.success('Friend request sent!');
  };

  const handleSendMessage = (_friendId: string) => {
    toast.info('Opening chat!');
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Friends</CardTitle>
    </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 gap-4">
          {friends.map((friend) => (
            <div key={friend.id} className="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
              <div className="flex items-center">
                <Avatar className="w-10 h-10">
                  <AvatarImage src={friend.avatar} />
                  <AvatarFallback>{friend.name.charAt(0)}</AvatarFallback>
    </Avatar>
                <div className="ml-3">
                  <p className="font-semibold">{friend.name}</p>
                  <p className="text-xs text-gray-500">
                    {friend.onlineStatus === 'online'
                      ? 'Active now'
                      : `Last seen: ${friend.lastSeen}`}
                  </p>
                  <Badge variant="outline" className="text-xs mt-1">
                    {friend.mutualFriends} mutual friends
                  </Badge>
    </div>
              </div>
              <div className="flex space-x-2">
                <Button size="sm" onClick={() => handleAddFriend(friend.id)}>
                  <UserPlus className="w-4 h-4" />
    </Button>
                <Button size="sm" onClick={() => handleSendMessage(friend.id)}>
                  <MessageCircle className="w-4 h-4" />
    </Button>
                <Button size="sm">
                  <MoreHorizontal className="w-4 h-4" />
    </Button>
              </div>
    </div>
          ))}
        </div>
    </CardContent>
    </Card>
  );
};

export default FriendSystemEnhancements;
