import React, { useState, useCallback, memo, useMemo } from 'react';
import { Camera, Video, Type, Globe, Users, Lock, Clock } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { But<PERSON> } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { toast } from 'sonner';

// Import refactored components
import StoryPreview from './StoryPreview';
import MediaUpload from './MediaUpload';
import TextStyling from './TextStyling';
import InteractiveFeatures from './InteractiveFeatures';

// Import types and utilities
import {
  StoryCreatorProps,
  StoryFormState,
  DEFAULT_FORM_STATE,
  validateStoryForm,
  getDurationInHours,
  StoryType,
  PrivacyLevel,
  DurationOption
} from './types';

const StoryCreatorRefactored: React.FC<StoryCreatorProps> = memo(({ 
  isOpen, 
  onClose, 
  onCreateStory 
}) => {
  // Consolidated state management
  const [formState, setFormState] = useState<StoryFormState>(DEFAULT_FORM_STATE);
  const [selectedMusicTrack, setSelectedMusicTrack] = useState('');

  // Memoized handlers for better performance
  const updateFormState = useCallback((updates: Partial<StoryFormState>) => {
    setFormState(prev => ({ ...prev, ...updates }));
  }, []);

  const resetForm = useCallback(() => {
    setFormState(DEFAULT_FORM_STATE);
    setSelectedMusicTrack('');
  }, []);

  // Media handlers
  const handleImageSelect = useCallback((imageUrl: string) => {
    updateFormState({ selectedImage: imageUrl });
  }, [updateFormState]);

  const handleVideoSelect = useCallback((videoUrl: string, thumbnailUrl: string) => {
    updateFormState({ 
      videoUrl, 
      selectedImage: thumbnailUrl 
    });
  }, [updateFormState]);

  const handleRemoveMedia = useCallback(() => {
    updateFormState({ 
      selectedImage: '', 
      videoUrl: '' 
    });
  }, [updateFormState]);

  const handleUploadStart = useCallback(() => {
    updateFormState({ isUploading: true });
  }, [updateFormState]);

  const handleUploadEnd = useCallback(() => {
    updateFormState({ isUploading: false });
  }, [updateFormState]);

  // Text styling handlers
  const handleBackgroundChange = useCallback((background: string) => {
    updateFormState({ background });
  }, [updateFormState]);

  const handleTextColorChange = useCallback((textColor: string) => {
    updateFormState({ textColor });
  }, [updateFormState]);

  const handleFontSizeChange = useCallback((fontSize: number[]) => {
    updateFormState({ fontSize: fontSize[0] });
  }, [updateFormState]);

  const handleTextAlignmentChange = useCallback((textAlignment: string) => {
    updateFormState({ textAlignment });
  }, [updateFormState]);

  // Interactive features handlers
  const handleUpdateMusic = useCallback((music: Partial<StoryFormState['music']>) => {
    updateFormState({ 
      music: { ...formState.music, ...music } 
    });
  }, [formState.music, updateFormState]);

  const handleUpdatePoll = useCallback((poll: Partial<StoryFormState['poll']>) => {
    updateFormState({ 
      poll: { ...formState.poll, ...poll } 
    });
  }, [formState.poll, updateFormState]);

  const handleUpdateCountdown = useCallback((countdown: Partial<StoryFormState['countdown']>) => {
    updateFormState({ 
      countdown: { ...formState.countdown, ...countdown } 
    });
  }, [formState.countdown, updateFormState]);

  const handleUpdateQuestion = useCallback((question: string) => {
    updateFormState({ question });
  }, [updateFormState]);

  const handleUpdateARFilters = useCallback((arFilters: string[]) => {
    updateFormState({ arFilters });
  }, [updateFormState]);

  const handleUpdateStickers = useCallback((stickers: Partial<StoryFormState['stickers']>) => {
    updateFormState({ 
      stickers: { ...formState.stickers, ...stickers } 
    });
  }, [formState.stickers, updateFormState]);

  // Story creation handler
  const handleCreateStory = useCallback(() => {
    const validationError = validateStoryForm(formState);
    if (validationError) {
      toast.error(validationError);
      return;
    }

    const storyData = {
      type: formState.type,
      content: formState.content,
      background: formState.background,
      textColor: formState.textColor,
      fontSize: formState.fontSize,
      textAlignment: formState.textAlignment,
      media: formState.type === 'video' ? formState.videoUrl : formState.selectedImage,
      privacy: formState.privacy,
      duration: getDurationInHours(formState.duration, formState.customHours),
      timestamp: new Date().toISOString(),
      music: (formState.music.title || formState.music.artist) ? formState.music : undefined,
      polls: formState.poll.question ? formState.poll : undefined,
      countdown: formState.countdown.enabled && formState.countdown.endTime ? 
        { endTime: formState.countdown.endTime } : undefined,
      arFilters: formState.arFilters.length > 0 ? formState.arFilters : undefined,
      stickers: formState.stickers.enabled && formState.stickers.selected.length > 0 ? 
        formState.stickers.selected : undefined,
      question: formState.question || undefined
    };

    onCreateStory(storyData);
    resetForm();
    toast.success('Story created successfully!');
  }, [formState, onCreateStory, resetForm]);

  // Close handler
  const handleClose = useCallback(() => {
    resetForm();
    onClose();
  }, [resetForm, onClose]);

  // Memoized privacy options
  const privacyOptions = useMemo(() => [{ value: 'public', label: 'Public', icon: Globe, color: 'text-green-500' },
    { value: 'friends', label: 'Friends', icon: Users, color: 'text-blue-500' },
    { value: 'close-friends', label: 'Close Friends', icon: Lock, color: 'text-purple-500' }], []);

  return (
    <Dialog open={isOpen} onOpenChange={(open) => {
      if (!open) handleClose();
    }}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden p-0">
        <DialogHeader className="p-4 border-b">
          <DialogTitle>Create Story</DialogTitle>
    </DialogHeader>
        <div className="grid grid-cols-1 md:grid-cols-2 h-[80vh]">
          {/* Preview Panel */}
          <StoryPreview 
            formState={formState} className="w-full h-full"
          />

          {/* Editor Panel */}
          <div className="bg-white p-4 overflow-y-auto dark:bg-gray-800">
            <Tabs 
              value={formState.type} onValueChange={(value: string) => updateFormState({ type: value as StoryType })}, className="w-full"
            >
              <TabsList className="grid w-full grid-cols-3 mb-4">
                <TabsTrigger value="photo" className="flex items-center space-x-2">
                  <Camera className="w-4 h-4" />
                  <span>Photo</span>
    </TabsTrigger>
                <TabsTrigger value="video" className="flex items-center space-x-2">
                  <Video className="w-4 h-4" />
                  <span>Video</span>
    </TabsTrigger>
                <TabsTrigger value="text" className="flex items-center space-x-2">
                  <Type className="w-4 h-4" />
                  <span>Text</span>
    </TabsTrigger>
              </TabsList>

              {/* Photo Tab */}
              <TabsContent value="photo" className="space-y-4">
                <MediaUpload
                  type="photo"
                  selectedImage={formState.selectedImage} videoUrl=""
                  isUploading={formState.isUploading} onImageSelect={handleImageSelect}, onVideoSelect={handleVideoSelect} onRemoveMedia={handleRemoveMedia}, onUploadStart={handleUploadStart} onUploadEnd={handleUploadEnd}
                />

                <div>
                  <label className="block text-sm font-medium mb-2 dark:text-gray-200">
                    Caption (Optional)
                  </label>
                  <Textarea
                    placeholder="Add a caption to your story..."
                    value={formState.content} onChange={(e) => updateFormState({ content: e.target.value })}, rows={3} className="dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  />
    </div>
              </TabsContent>

              {/* Video Tab */}
              <TabsContent value="video" className="space-y-4">
                <MediaUpload
                  type="video"
                  selectedImage={formState.selectedImage} videoUrl={formState.videoUrl}, isUploading={formState.isUploading} onImageSelect={handleImageSelect}, onVideoSelect={handleVideoSelect} onRemoveMedia={handleRemoveMedia}, onUploadStart={handleUploadStart} onUploadEnd={handleUploadEnd}
                />

                <div>
                  <label className="block text-sm font-medium mb-2 dark:text-gray-200">
                    Caption (Optional)
                  </label>
                  <Textarea
                    placeholder="Add a caption to your story..."
                    value={formState.content} onChange={(e) => updateFormState({ content: e.target.value })}, rows={3} className="dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  />
    </div>
              </TabsContent>

              {/* Text Tab */}
              <TabsContent value="text" className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2 dark:text-gray-200">
                    Your Story Text
                  </label>
                  <Textarea
                    placeholder="What's on your mind?"
                    value={formState.content} onChange={(e) => updateFormState({ content: e.target.value })}, rows={5} className="mb-4 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  />
    </div>
                <TextStyling
                  background={formState.background} textColor={formState.textColor}, fontSize={formState.fontSize} textAlignment={formState.textAlignment}, onBackgroundChange={handleBackgroundChange} onTextColorChange={handleTextColorChange}, onFontSizeChange={handleFontSizeChange} onTextAlignmentChange={handleTextAlignmentChange}
                />
    </TabsContent>
            </Tabs>

            {/* Interactive Features */}
            <InteractiveFeatures
              formState={formState} onUpdateMusic={handleUpdateMusic}, onUpdatePoll={handleUpdatePoll} onUpdateCountdown={handleUpdateCountdown}, onUpdateQuestion={handleUpdateQuestion} onUpdateARFilters={handleUpdateARFilters}, onUpdateStickers={handleUpdateStickers} selectedMusicTrack={selectedMusicTrack}, onMusicTrackChange={setSelectedMusicTrack} className="mt-6"
            />

            {/* Privacy and Duration Settings */}
            <div className="space-y-4 mt-6 pt-4 border-t dark:border-gray-700">
              <div>
                <label className="block text-sm font-medium mb-2 flex items-center dark:text-gray-200">
                  <Globe className="w-4 h-4 mr-2" />
                  Privacy
                </label>
                <Select 
                  value={formState.privacy} onValueChange={(value: string) => updateFormState({ privacy: value as PrivacyLevel })}
                >
                  <SelectTrigger className="dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                    <SelectValue placeholder="Who can see your story?" />
    </SelectTrigger>
                  <SelectContent>
                    {privacyOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        <div className="flex items-center">
                          <option.icon className={`w-4 h-4 mr-2 ${option.color}`} />
                          <span>{option.label}</span>
    </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
    </Select>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2 flex items-center dark:text-gray-200">
                  <Clock className="w-4 h-4 mr-2" />
                  Duration
                </label>
                <div className="grid grid-cols-4 gap-2 mb-2">
                  {(['24h', '12h', '6h', 'custom'] as DurationOption[]).map((duration) => (
                    <Button
                      key={duration} variant={formState.duration === duration ? 'default' : 'outline'}, size="sm"
                      onClick={() => updateFormState({ duration })}, className="dark:border-gray-600"
                    >
                      {duration === 'custom' ? 'Custom' : duration}
                    </Button>
                  ))}
                </div>
                
                {formState.duration === 'custom' && (
                  <div className="mt-4">
                    <label className="block text-sm font-medium mb-2 dark:text-gray-200">
                      Custom Duration (hours)
                    </label>
                    <div className="flex items-center space-x-4">
                      <Slider
                        value={[formState.customHours]} onValueChange={(value) => updateFormState({ customHours: value[0] })}, min={1} max={48}, step={1} className="flex-1"
                      />
                      <span className="font-medium dark:text-white">{formState.customHours}h</span>
    </div>
                  </div>
                )}
              </div>
    </div>
            {/* Action Buttons */}
            <div className="flex justify-end space-x-3 mt-6 pt-4 border-t dark:border-gray-700">
              <Button 
                variant="outline" 
                onClick={handleClose} className="dark:border-gray-600 dark:text-gray-200"
              >
                Cancel
              </Button>
              <Button 
                onClick={handleCreateStory} disabled={formState.isUploading}
              >
                Share to Story
              </Button>
    </div>
          </div>
    </div>
      </DialogContent>
    </Dialog>
  );
});

StoryCreatorRefactored.displayName = 'StoryCreatorRefactored';

export default StoryCreatorRefactored;
