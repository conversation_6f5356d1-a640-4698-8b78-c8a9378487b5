/**
 * Story Creator Types and Interfaces
 */

export type StoryType = 'photo' | 'video' | 'text';
export type PrivacyLevel = 'public' | 'friends' | 'close-friends';
export type DurationOption = '24h' | '12h' | '6h' | 'custom';
export type TextAlignment = 'left' | 'center' | 'right';

export interface StoryMusic {
  title: string, artist: string;
  trackId?: string;
}

export interface StoryPoll {
  question: string, options: string[];
}

export interface StoryCountdown {
  endTime: string;
  title?: string;
}

export interface StoryData {
  type: StoryType;
  media?: string;
  content: string;
  background?: string;
  textColor: string, fontSize: number, textAlignment: TextAlignment, privacy: PrivacyLevel, duration: number, timestamp: string;
  music?: StoryMusic;
  polls?: StoryPoll;
  countdown?: StoryCountdown;
  arFilters?: string[];
  stickers?: string[];
  question?: string;
}

export interface StoryCreatorProps {
  isOpen: boolean, onClose: () => void; onCreateStory: (storyData: StoryData) => void;
}

// Form state interface for better state management
export interface StoryFormState {
  // Basic story data
  type: StoryType, content: string;
  
  // Media
  selectedImage: string, videoUrl: string, isUploading: boolean;
  
  // Text styling (for text stories)
  background: string, textColor: string, fontSize: number, textAlignment: TextAlignment;
  
  // Privacy and duration
  privacy: PrivacyLevel, duration: DurationOption, customHours: number;
  
  // Interactive features
  music: StoryMusic, poll: StoryPoll, countdown: {
    enabled: boolean, endTime: string;
  };
  question: string;
  
  // Effects and filters
  arFilters: string[], stickers: {
    enabled: boolean, selected: string[];
  };
}

// Background and color options
export interface BackgroundOption {
  id: string, name: string, class: string;
}

export interface ColorOption {
  id: string, name: string, class: string;
}

export interface AlignmentOption {
  id: string, name: string, class: string;
}

// Constants
export const BACKGROUNDS: BackgroundOption[] = [{ id: 'gradient-1', name: 'Blue to Purple', class: 'bg-gradient-to-br from-blue-500 to-purple-600' },
  { id: 'gradient-2', name: 'Green to Blue', class: 'bg-gradient-to-br from-green-400 to-blue-500' },
  { id: 'gradient-3', name: 'Red to Orange', class: 'bg-gradient-to-br from-red-500 to-orange-500' },
  { id: 'gradient-4', name: 'Purple to Pink', class: 'bg-gradient-to-br from-purple-500 to-pink-500' },
  { id: 'solid-1', name: 'Blue', class: 'bg-blue-600' },
  { id: 'solid-2', name: 'Green', class: 'bg-green-600' },
  { id: 'solid-3', name: 'Red', class: 'bg-red-600' },
  { id: 'solid-4', name: 'Black', class: 'bg-black' }]
];

export const COLORS: ColorOption[] = [{ id: 'white', name: 'White', class: 'text-white' },
  { id: 'black', name: 'Black', class: 'text-black' },
  { id: 'yellow', name: 'Yellow', class: 'text-yellow-400' },
  { id: 'blue', name: 'Blue', class: 'text-blue-400' },
  { id: 'green', name: 'Green', class: 'text-green-400' },
  { id: 'red', name: 'Red', class: 'text-red-400' }]
];

export const ALIGNMENTS: AlignmentOption[] = [{ id: 'left', name: 'Left', class: 'text-left' },
  { id: 'center', name: 'Center', class: 'text-center' },
  { id: 'right', name: 'Right', class: 'text-right' }]
];

export const _AR_FILTERS = [{ name: 'Sparkles', icon: '✨' },
  { name: 'Hearts', icon: '❤️' },
  { name: 'Rainbow', icon: '🌈' },
  { name: 'Butterfly', icon: '🦋' },
  { name: 'Snow', icon: '❄️' }]
  { name: 'Fire', icon: '🔥' }
];

export const _STICKERS = ['😀', '❤️', '🔥', '👏', '🎉', '⭐', '🌟', '💯', '🚀', '🎯', '💪', '🙌'];

export const _MUSIC_TRACKS = [{ id: 'trending1', name: '🎵 Popular Song 1' },
  { id: 'trending2', name: '🎵 Popular Song 2' },
  { id: 'trending3', name: '🎵 Popular Song 3' },
  { id: 'custom', name: '🎼 Custom Audio' }]
];

// Default form state
export const DEFAULT_FORM_STATE: StoryFormState = {
  type: 'photo', content: '', selectedImage: '', videoUrl: '', isUploading: false, background: 'gradient-1', textColor: 'white', fontSize: 16, textAlignment: 'center', privacy: 'friends', duration: '24h', customHours: 24, music: { title: '', artist: '' },
  poll: { question: '', options: ['', ''] },
  countdown: { enabled: false, endTime: '' },
  question: '', arFilters: [], stickers: { enabled: false, selected: [] }
};

// Enhanced validation functions with better type safety
export const _validateStoryForm = (state: StoryFormState): string | null => {
  // Type-specific validation
  if (state.type === 'text' && !state.content.trim()) {
    return 'Please add some text to your story';
  }

  if ((state.type === 'photo' || state.type === 'video') && !state.selectedImage) {
    return `Please select a ${state.type} for your story`;
  }

  // Poll validation
  if (state.poll.question.trim()) {
    const validOptions = state.poll.options.filter(option => option.trim());
    if (validOptions.length < 2) {
      return 'Poll must have at least 2 options';
    }
    if (state.poll.options.some(option => option.trim() && option.length > 100)) {
      return 'Poll options must be 100 characters or less';
    }
  }

  // Content length validation
  if (state.content.length > 2000) {
    return 'Story content must be 2000 characters or less';
  }

  // Countdown validation
  if (state.countdown.enabled && state.countdown.endTime) {
    const endTime = new Date(state.countdown.endTime);
    const now = new Date();
    if (endTime <= now) {
      return 'Countdown end time must be in the future';
    }
  }

  // Custom duration validation
  if (state.duration === 'custom' && (state.customHours < 1 || state.customHours > 48)) {
    return 'Custom duration must be between 1 and 48 hours';
  }

  return null;
};

// Type guards for better type safety
export const _isValidStoryType = (type: string): type is StoryType => {
  return ['photo', 'video', 'text'].includes(type);
};

export const _isValidPrivacyLevel = (privacy: string): privacy is PrivacyLevel => {
  return ['public', 'friends', 'close-friends'].includes(privacy);
};

export const _isValidDurationOption = (duration: string): duration is DurationOption => {
  return ['24h', '12h', '6h', 'custom'].includes(duration);
};

export const _isValidTextAlignment = (alignment: string): alignment is TextAlignment => {
  return ['left', 'center', 'right'].includes(alignment);
};

// Utility functions
export const _getDurationInHours = (duration: DurationOption, customHours: number): number => {
  switch (duration) {
    case '12h': return 12;
    case '6h': return 6;
    case 'custom': return customHours;
    default: return 24;
  }
};

export const _getBackgroundClass = (bgId: string): string => {
  return BACKGROUNDS.find(bg => bg.id === bgId)?.class || 'bg-gray-900';
};

export const _getTextColorClass = (colorId: string): string => {
  return COLORS.find(c => c.id === colorId)?.class || 'text-white';
};

export const _getAlignmentClass = (alignId: string): string => {
  return ALIGNMENTS.find(a => a.id === alignId)?.class || 'text-center';
};
