import React, { useState, useCallback } from 'react';
import { 
  Camera, Star, Edit3, Plus, Calendar, MapPin, Home, Heart, 
  Briefcase, GraduationCap, Globe, Music, Book, Film, 
  Trophy, Coffee, Activity, Clock,
  ChevronRight, Grid, List, X, Lock, Users
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { uniqueToast } from '@/utils/toastManager';
import { motion, AnimatePresence } from 'framer-motion';
import { MOCK_IMAGES, getSafeImage } from '@/lib/constants';

interface IntroSection {
  id: string, icon: React.ComponentType<{ className?: string }>;
  title: string, value: string, privacy: 'public' | 'friends' | 'private';
}

interface FeaturedPhoto {
  id: string, url: string, caption: string, likes: number, comments: number;
}

interface ActivityLogItem {
  id: string, type: 'post' | 'photo' | 'friend' | 'event' | 'like' | 'comment', title: string, description: string, timestamp: string, icon: React.ComponentType<{ className?: string }>;
  privacy: 'public' | 'friends' | 'private';
}

interface EnhancedProfileCustomizationProps {
  onUpdate?: (data: Record<string, unknown>) => void;
}

const EnhancedProfileCustomization: React.FC<EnhancedProfileCustomizationProps> = ({
  onUpdate
}) => {
  const [activeTab, setActiveTab] = useState('featured');
  const [isEditingIntro, setIsEditingIntro] = useState(false);
  const [selectedSection, setSelectedSection] = useState<IntroSection | null>(null);
  const [activityViewMode, setActivityViewMode] = useState<'grid' | 'list'>('list');
  const [activityFilter, setActivityFilter] = useState('all');

  // Featured Photos State
  const [featuredPhotos, setFeaturedPhotos] = useState<FeaturedPhoto[]>([
    {
      id: '1',
      url: MOCK_IMAGES.POSTS[0],
      caption: 'Summer vacation in Hawaii 🌺',
      likes: 234,
      comments: 45
    },
    {
      id: '2',
      url: getSafeImage('POSTS', 1),
      caption: 'Graduation day! 🎓',
      likes: 567,
      comments: 89
    },
    {
      id: '3',
      url: getSafeImage('POSTS', 2),
      caption: 'New adventure begins',
      likes: 123,
      comments: 23
    }
  ]);

  // Intro Sections State
  const [introSections, setIntroSections] = useState<IntroSection[]>([
    {
      id: '1',
      icon: Briefcase,
      title: 'Work',
      value: 'Software Engineer at Tech Corp',
      privacy: 'public'
    },
    {
      id: '2',
      icon: GraduationCap,
      title: 'Education',
      value: 'Computer Science, Stanford University',
      privacy: 'public'
    },
    {
      id: '3',
      icon: Home,
      title: 'Lives in',
      value: 'San Francisco, CA',
      privacy: 'friends'
    },
    {
      id: '4',
      icon: MapPin,
      title: 'From',
      value: 'Seattle, WA',
      privacy: 'friends'
    },
    {
      id: '5',
      icon: Heart,
      title: 'Relationship',
      value: 'Single',
      privacy: 'friends'
    },
    {
      id: '6',
      icon: Globe,
      title: 'Languages',
      value: 'English, Spanish, Mandarin',
      privacy: 'public'
    },
    {
      id: '7',
      icon: Music,
      title: 'Favorite Music',
      value: 'Jazz, Classical, Electronic',
      privacy: 'public'
    },
    {
      id: '8',
      icon: Book,
      title: 'Favorite Books',
      value: 'Science Fiction, Philosophy',
      privacy: 'public'
    },
    {
      id: '9',
      icon: Film,
      title: 'Favorite Movies',
      value: 'Sci-Fi, Documentaries',
      privacy: 'public'
    },
    {
      id: '10',
      icon: Coffee,
      title: 'Hobbies',
      value: 'Photography, Hiking, Cooking',
      privacy: 'public'
    }
  ]);

  // Activity Log State
  const [activityLog] = useState<ActivityLogItem[]>([
    {
      id: '1',
      type: 'post',
      title: 'Updated profile picture',
      description: 'Changed profile picture',
      timestamp: '2 hours ago',
      icon: Camera,
      privacy: 'public'
    },
    {
      id: '2',
      type: 'friend',
      title: 'Became friends with Sarah Johnson',
      description: 'You and Sarah are now friends',
      timestamp: '1 day ago',
      icon: Users,
      privacy: 'friends'
    },
    {
      id: '3',
      type: 'photo',
      title: 'Added 5 photos to Summer Vacation',
      description: 'Beach photos from Hawaii trip',
      timestamp: '3 days ago',
      icon: Camera,
      privacy: 'friends'
    },
    {
      id: '4',
      type: 'event',
      title: 'Attending Tech Conference 2025',
      description: 'Going to the annual tech conference',
      timestamp: '1 week ago',
      icon: Calendar,
      privacy: 'public'
    },
    {
      id: '5',
      type: 'like',
      title: 'Liked 3 posts',
      description: 'Liked posts from friends',
      timestamp: '2 weeks ago',
      icon: Heart,
      privacy: 'private'
    }
  ]);

  const handleAddFeaturedPhoto = useCallback(() => {
    uniqueToast.info('Photo selection feature coming soon!');
  }, []);

  const handleRemoveFeaturedPhoto = useCallback((photoId: string) => {
    setFeaturedPhotos(prev => prev.filter(photo => photo.id !== photoId));
    uniqueToast.success('Featured photo removed');
  }, []);

  const handleEditIntroSection = useCallback((section: IntroSection) => {
    setSelectedSection(section);
    setIsEditingIntro(true);
  }, []);

  const handleSaveIntroSection = useCallback((updatedSection: IntroSection) => {
    setIntroSections(prev => prev.map(section => 
      section.id === updatedSection.id ? updatedSection : section
    ));
    setIsEditingIntro(false);
    setSelectedSection(null);
    uniqueToast.success('Intro section updated');
    onUpdate?.({ introSections });
  }, [introSections, onUpdate]);

  const handleAddIntroSection = useCallback(() => {
    const newSection: IntroSection = {
      id: Date.now().toString(),
      icon: Trophy,
      title: 'New Section',
      value: '',
      privacy: 'public'
    };
    setIntroSections(prev => [...prev; newSection]);
    setSelectedSection(newSection);
    setIsEditingIntro(true);
  }, []);

  const getPrivacyIcon = (privacy: string) => {
    switch (privacy) {
      case 'public':
        return <Globe className="w-3 h-3" />;
      case 'friends':
        return <Users className="w-3 h-3" />;
      case 'private':
        return <Lock className="w-3 h-3" />;
      default:
        return null;
    }
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'post':
        return <Edit3 className="w-4 h-4" />;
      case 'photo':
        return <Camera className="w-4 h-4" />;
      case 'friend':
        return <Users className="w-4 h-4" />;
      case 'event':
        return <Calendar className="w-4 h-4" />;
      case 'like':
        return <Heart className="w-4 h-4" />;
      case 'comment':
        return <Activity className="w-4 h-4" />;
      default:
        return <Activity className="w-4 h-4" />;
    }
  };

  const filteredActivity = activityLog.filter(item => {
    if (activityFilter === 'all') return true;
    return item.type === activityFilter;
  });

  return (
    <div className="w-full max-w-4xl mx-auto space-y-6">
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="featured">Featured</TabsTrigger>
          <TabsTrigger value="intro">Intro</TabsTrigger>
          <TabsTrigger value="activity">Activity Log</TabsTrigger>
    </TabsList>
        {/* Featured Photos Tab */}
        <TabsContent value="featured" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center space-x-2">
                  <Star className="w-5 h-5" />
                  <span>Featured Photos</span>
    </CardTitle>
                <Button onClick={handleAddFeaturedPhoto} size="sm">
                  <Plus className="w-4 h-4 mr-2" />
                  Add Photo
                </Button>
    </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                <AnimatePresence>
                  {featuredPhotos.map((photo, index) => (
                    <motion.div
                      key={photo.id} initial={{ opacity: 0, scale: 0.8 }}, animate={{ opacity: 1, scale: 1 }}, exit={{ opacity: 0, scale: 0.8 }}, transition={{ duration: 0.3, delay: index * 0.1 }}, className="relative group"
                    >
                      <div className="aspect-square rounded-lg overflow-hidden bg-gray-100">
                        <img
                          src={photo.url} alt={photo.caption}, className="w-full h-full object-cover"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="absolute top-2 right-2 bg-white/20 hover:bg-white/30"
                            onClick={() => handleRemoveFeaturedPhoto(photo.id)}
                          >
                            <X className="w-4 h-4" />
    </Button>
                          <div className="absolute bottom-2 left-2 right-2">
                            <p className="text-white text-sm font-medium truncate">
                              {photo.caption}
                            </p>
                            <div className="flex items-center space-x-3 text-white/80 text-xs">
                              <span className="flex items-center">
                                <Heart className="w-3 h-3 mr-1" />
                                {photo.likes}
                              </span>
                              <span className="flex items-center">
                                <Activity className="w-3 h-3 mr-1" />
                                {photo.comments}
                              </span>
    </div>
                          </div>
    </div>
                      </div>
                    </motion.div>
                  ))}
                </AnimatePresence>

                {/* Add Photo Placeholder */}
                <motion.div
                  initial={{ opacity: 0 }}, animate={{ opacity: 1 }}, className="aspect-square rounded-lg border-2 border-dashed border-gray-300 flex items-center justify-center cursor-pointer hover:border-gray-400 transition-colors"
                  onClick={handleAddFeaturedPhoto}
                >
                  <div className="text-center">
                    <Plus className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                    <p className="text-sm text-gray-500">Add Featured Photo</p>
    </div>
                </motion.div>
    </div>
            </CardContent>
    </Card>
        </TabsContent>

        {/* Intro Customization Tab */}
        <TabsContent value="intro" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center space-x-2">
                  <Edit3 className="w-5 h-5" />
                  <span>Customize Your Intro</span>
    </CardTitle>
                <Button onClick={handleAddIntroSection} size="sm" variant="outline">
                  <Plus className="w-4 h-4 mr-2" />
                  Add Section
                </Button>
    </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {introSections.map((section) => {
                  const Icon = section.icon;
                  return (
                    <div
                      key={section.id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                    >
                      <div className="flex items-center space-x-3 flex-1">
                        <Icon className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                        <div className="flex-1">
                          <p className="text-sm font-medium text-gray-900 dark:text-white">
                            {section.title}
                          </p>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            {section.value || 'Not specified'}
                          </p>
    </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge variant="outline" className="text-xs">
                          {getPrivacyIcon(section.privacy)}
                          <span className="ml-1">{section.privacy}</span>
    </Badge>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEditIntroSection(section)}
                        >
                          <Edit3 className="w-4 h-4" />
    </Button>
                      </div>
    </div>
                  );
                })}
              </div>
    </CardContent>
          </Card>
    </TabsContent>
        {/* Activity Log Tab */}
        <TabsContent value="activity" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center space-x-2">
                  <Clock className="w-5 h-5" />
                  <span>Activity Log</span>
    </CardTitle>
                <div className="flex items-center space-x-2">
                  <Select value={activityFilter} onValueChange={setActivityFilter}>
                    <SelectTrigger className="w-32">
                      <SelectValue />
    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Activity</SelectItem>
                      <SelectItem value="post">Posts</SelectItem>
                      <SelectItem value="photo">Photos</SelectItem>
                      <SelectItem value="friend">Friends</SelectItem>
                      <SelectItem value="event">Events</SelectItem>
                      <SelectItem value="like">Likes</SelectItem>
    </SelectContent>
                  </Select>
                  <div className="flex border rounded-md">
                    <Button
                      variant={activityViewMode === 'list' ? 'secondary' : 'ghost'} size="sm"
                      className="rounded-r-none"
                      onClick={() => setActivityViewMode('list')}
                    >
                      <List className="w-4 h-4" />
    </Button>
                    <Button
                      variant={activityViewMode === 'grid' ? 'secondary' : 'ghost'} size="sm"
                      className="rounded-l-none"
                      onClick={() => setActivityViewMode('grid')}
                    >
                      <Grid className="w-4 h-4" />
    </Button>
                  </div>
    </div>
              </div>
    </CardHeader>
            <CardContent>
              {activityViewMode === 'list' ? (
                <div className="space-y-3">
                  {filteredActivity.map((item) => (
                    <div
                      key={item.id} className="flex items-start space-x-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                    >
                      <div className="flex-shrink-0 w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                        {getActivityIcon(item.type)}
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium text-gray-900 dark:text-white">
                          {item.title}
                        </p>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {item.description}
                        </p>
                        <div className="flex items-center space-x-3 mt-1">
                          <span className="text-xs text-gray-500 dark:text-gray-500">
                            {item.timestamp}
                          </span>
                          <Badge variant="outline" className="text-xs">
                            {getPrivacyIcon(item.privacy)}
                            <span className="ml-1">{item.privacy}</span>
    </Badge>
                        </div>
    </div>
                      <Button variant="ghost" size="sm">
                        <ChevronRight className="w-4 h-4" />
    </Button>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
                  {filteredActivity.map((item) => (
                    <div
                      key={item.id} className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-center"
                    >
                      <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-2">
                        {getActivityIcon(item.type)}
                      </div>
                      <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                        {item.title}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">
                        {item.timestamp}
                      </p>
    </div>
                  ))}
                </div>
              )}
            </CardContent>
    </Card>
        </TabsContent>
    </Tabs>
      {/* Edit Intro Section Dialog */}
      <Dialog open={isEditingIntro} onOpenChange={setIsEditingIntro}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Intro Section</DialogTitle>
    </DialogHeader>
          {selectedSection && (
            <div className="space-y-4">
              <div>
                <Label>Section Title</Label>
                <Input
                  value={selectedSection.title} onChange={(e) => setSelectedSection({
                    ...selectedSection,
                    title: e.target.value
                  })}, placeholder="Enter section title"
                />
    </div>
              <div>
                <Label>Content</Label>
                <Textarea
                  value={selectedSection.value} onChange={(e) => setSelectedSection({
                    ...selectedSection,
                    value: e.target.value
                  })}, placeholder="Enter content"
                  rows={3}
                />
    </div>
              <div>
                <Label>Privacy</Label>
                <Select
                  value={selectedSection.privacy} onValueChange={(value: 'public' | 'friends' | 'private') =>
                    setSelectedSection({
                      ...selectedSection,
                      privacy: value
                    })
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
    </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="public">
                      <div className="flex items-center">
                        <Globe className="w-4 h-4 mr-2" />
                        Public
                      </div>
    </SelectItem>
                    <SelectItem value="friends">
                      <div className="flex items-center">
                        <Users className="w-4 h-4 mr-2" />
                        Friends
                      </div>
    </SelectItem>
                    <SelectItem value="private">
                      <div className="flex items-center">
                        <Lock className="w-4 h-4 mr-2" />
                        Only Me
                      </div>
    </SelectItem>
                  </SelectContent>
    </Select>
              </div>
    </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditingIntro(false)}>
              Cancel
            </Button>
            <Button onClick={() => selectedSection && handleSaveIntroSection(selectedSection)}>
              Save Changes
            </Button>
    </DialogFooter>
        </DialogContent>
    </Dialog>
    </div>
  );
};

export default EnhancedProfileCustomization;
