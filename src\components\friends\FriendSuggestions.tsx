import React, { useState, useEffect, memo, useCallback } from 'react';
import { User, UserPlus, X } from 'lucide-react';
import { FriendSuggestion } from '@/types/interfaces';

/**
 * FriendSuggestions component displays a list of suggested friends
 * with mutual friends count and common interests.
 * 
 * @returns {JSX.Element} - The rendered friend suggestions component
 */
export const FriendSuggestions = memo(() => {
  const [suggestions, setSuggestions] = useState<FriendSuggestion[]>([]);
  const [loading, setLoading] = useState(true);
  const [dismissedIds, setDismissedIds] = useState<Set<string>>(new Set());

  useEffect(() => {
    // Simulate fetching friend suggestions
    const fetchSuggestions = async () => {
      setLoading(true);
      // Mock data - replace with actual API call
      setTimeout(() => {
        setSuggestions([
          {
            id: '1',
            name: '<PERSON>',
            username: 'alice_j',
            avatarUrl: 'https://i.pravatar.cc/150?img=1',
            mutualFriends: 5,
            commonInterests: ['Photography', 'Travel', 'Music']
          },
          {
            id: '2',
            name: '<PERSON>',
            username: 'bob<PERSON>',
            avatarUrl: 'https://i.pravatar.cc/150?img=2',
            mutualFriends: 3,
            commonInterests: ['Technology', 'Gaming']
          },
          {
            id: '3',
            name: 'Carol Davis',
            username: 'carol_d',
            avatarUrl: 'https://i.pravatar.cc/150?img=3',
            mutualFriends: 8,
            commonInterests: ['Cooking', 'Books', 'Yoga']
          }
        ]);
        setLoading(false);
      }, 1000);
    };

    fetchSuggestions();
  }, []);

  /**
   * Handles sending a friend request and dismissing the suggestion
   * @param {string} suggestionId - The ID of the friend suggestion
   */
  const handleAddFriend = useCallback((suggestionId: string) => {
    // Handle friend request logic
    console.log('Sending friend request to:', suggestionId);
    setDismissedIds(prev => new Set(prev).add(suggestionId));
  }, []);

  /**
   * Handles dismissing a friend suggestion without sending a request
   * @param {string} suggestionId - The ID of the friend suggestion to dismiss
   */
  const handleDismiss = useCallback((suggestionId: string) => {
    setDismissedIds(prev => new Set(prev).add(suggestionId));
  }, []);

  const visibleSuggestions = suggestions.filter(s => !dismissedIds.has(s.id));

  if (loading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h2 className="text-lg font-semibold mb-4">People You May Know</h2>
        <div className="space-y-4">
          {[1, 2, 3].map(i => (
            <div key={i} className="animate-pulse">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
                <div className="flex-1">
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
    </div>
              </div>
    </div>
          ))}
        </div>
    </div>
    );
  }

  if (visibleSuggestions.length === 0) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h2 className="text-lg font-semibold mb-4">People You May Know</h2>
        <p className="text-gray-500 dark:text-gray-400 text-center py-8">
          No suggestions available at the moment
        </p>
    </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      <h2 className="text-lg font-semibold mb-4">People You May Know</h2>
      <div className="space-y-4">
        {visibleSuggestions.map(suggestion => (
          <div key={suggestion.id} className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              {suggestion.avatarUrl ? (
                <img
                  src={suggestion.avatarUrl} alt={suggestion.name}, className="w-12 h-12 rounded-full object-cover"
                />
              ) : (
                <div className="w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center">
                  <User className="w-6 h-6 text-gray-500" />
    </div>
              )}
            </div>
            <div className="flex-1 min-w-0">
              <div className="flex items-start justify-between">
                <div>
                  <h3 className="font-medium text-gray-900 dark:text-gray-100">
                    {suggestion.name}
                  </h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    @{suggestion.username}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    {suggestion.mutualFriends} mutual friends
                  </p>
                  {suggestion.commonInterests.length > 0 && (
                    <div className="flex flex-wrap gap-1 mt-2">
                      {suggestion.commonInterests.slice(0, 3).map(interest => (
                        <span
                          key={interest} className="inline-block px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 rounded-full"
                        >
                          {interest}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
                <button
                  onClick={() => handleDismiss(suggestion.id)} className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                  aria-label="Dismiss suggestion"
                >
                  <X className="w-4 h-4" />
    </button>
              </div>
              <button
                onClick={() => handleAddFriend(suggestion.id)} className="mt-3 inline-flex items-center px-3 py-1 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <UserPlus className="w-4 h-4 mr-1" />
                Add Friend
              </button>
    </div>
          </div>
        ))}
      </div>
    </div>
  );
});

FriendSuggestions.displayName = 'FriendSuggestions';

export default FriendSuggestions;
