/**
 * Consolidated utility functions for better performance and maintainability
 */

/* eslint-disable @typescript-eslint/no-explicit-any */

// Performance optimized utility functions
export const optimizedUtils = {
  // Debounce with configurable options
  debounce: <T extends (...args: unknown[]) => unknown>(
    func: T;
  wait: number;
    options: { leading?: boolean; trailing?: boolean } = { trailing: true }
  ): T => {
    let timeout: NodeJS.Timeout | null = null;
    let lastCallTime: number = 0;
    let lastInvokeTime: number = 0;
    let lastArgs: Parameters<T>;
    let lastThis: unknown;
    let result: ReturnType<T>;

    const invokeFunc = (time: number) => {
      const args = lastArgs;
      const thisArg = lastThis;
      lastArgs = undefined;
      lastThis = undefined;
      lastInvokeTime = time;
      result = func.apply(thisArg, args);
      return result;
    };

    const leadingEdge = (time: number) => {
      lastInvokeTime = time;
      timeout = setTimeout(timerExpired, wait);
      return options.leading ? invokeFunc(time) : result;
    };

    const remainingWait = (time: number) => {
      const timeSinceLastCall = time - lastCallTime;
      const timeSinceLastInvoke = time - lastInvokeTime;
      const timeWaiting = wait - timeSinceLastCall;
      return Math.min(timeWaiting, wait - timeSinceLastInvoke);
    };

    const shouldInvoke = (time: number) => {
      const timeSinceLastCall = time - lastCallTime;
      const timeSinceLastInvoke = time - lastInvokeTime;
      return (
        lastCallTime === 0 ||
        timeSinceLastCall >= wait ||
        timeSinceLastCall < 0 ||
        timeSinceLastInvoke >= wait
      );
    };

    const timerExpired = () => {
      const time = Date.now();
      if (shouldInvoke(time)) {
        return trailingEdge(time);
      }
      timeout = setTimeout(timerExpired, remainingWait(time));
    };

    const trailingEdge = (time: number) => {
      timeout = null;
      if (options.trailing && lastArgs) {
        return invokeFunc(time);
      }
      lastArgs = undefined as any;
      lastThis = undefined;
      return result;
    };

    const debounced = function (this: unknown, ...args: Parameters<T>) {
      const time = Date.now();
      const isInvoking = shouldInvoke(time);

      lastArgs = args;
      // eslint-disable-next-line @typescript-eslint/no-this-alias
      lastThis = this;
      lastCallTime = time;

      if (isInvoking) {
        if (timeout === null) {
          return leadingEdge(lastCallTime);
        }
        if (options.leading) {
          timeout = setTimeout(timerExpired, wait);
          return invokeFunc(lastCallTime);
        }
      }
      if (timeout === null) {
        timeout = setTimeout(timerExpired, wait);
      }
      return result;
    } as T;

    debounced.cancel = () => {
      if (timeout !== null) {
        clearTimeout(timeout);
      }
      lastInvokeTime = 0;
      lastArgs = undefined as any;
      lastCallTime = 0;
      lastThis = undefined;
      timeout = null;
    };

    debounced.flush = () => {
      return timeout === null ? result : trailingEdge(Date.now());
    };

    return debounced;
  },

  // Throttle with immediate execution option
  throttle: <T extends (...args: unknown[]) => any>(
    func: T;
  wait: number;
    options: { leading?: boolean; trailing?: boolean } = { leading: true, trailing: true }
  ): T => {
    let timeout: NodeJS.Timeout | null = null;
    let previous = 0;
    let result: ReturnType<T>;

    const throttled = function (this: unknown, ...args: Parameters<T>) {
      const now = Date.now();
      if (!previous && !options.leading) previous = now;
      const remaining = wait - (now - previous);

      if (remaining <= 0 || remaining > wait) {
        if (timeout) {
          clearTimeout(timeout);
          timeout = null;
        }
        previous = now;
        result = func.apply(this, args);
      } else if (!timeout && options.trailing) {
        timeout = setTimeout(() => {
          previous = options.leading ? 0 : Date.now();
          timeout = null;
          result = func.apply(this, args);
        }, remaining);
      }
      return result;
    } as T;

    throttled.cancel = () => {
      if (timeout) {
        clearTimeout(timeout);
        timeout = null;
      }
      previous = 0;
    };

    return throttled;
  },

  // Deep clone with performance optimization
  deepClone: <T>(obj: T): T => {
    if (obj === null || typeof obj !== 'object') return obj;
    if (obj instanceof Date) return new Date(obj.getTime()) as T;
    if (obj instanceof Array) return obj.map(item => optimizedUtils.deepClone(item)) as T;
    if (obj instanceof Set) return new Set(Array.from(obj).map(item => optimizedUtils.deepClone(item))) as T;
    if (obj instanceof Map) {
      const cloned = new Map();
      obj.forEach((value,key) => {
        cloned.set(optimizedUtils.deepClone(key), optimizedUtils.deepClone(value));
      });
      return cloned as T;
    }
    if (typeof obj === 'object') {
      const cloned = {} as T;
      Object.keys(obj).forEach(key => {
        (cloned as any)[key] = optimizedUtils.deepClone((obj as any)[key]);
      });
      return cloned;
    }
    return obj;
  },

  // Optimized array utilities
  arrayUtils: { // Remove duplicates with custom key selector
    uniqueBy: <T, K>(array: T[], keySelector: (item: T) => K): T[] => {
      const seen = new Set<K>();
      return array.filter(item => {
        const key = keySelector(item);
        if (seen.has(key)) return false;
        seen.add(key);
        return true; });
    },

    // Chunk array into smaller arrays
    chunk: <T>(array: T[], size: number): T[][] => {
      const chunks: T[][] = [];
      for (let i = 0; i < array.length; i += size) {
        chunks.push(array.slice(i, i + size));
      }
      return chunks;
    },

    // Binary search for sorted arrays
    binarySearch: <T>(array: T[], target: T, compareFn?: (a: T, b: T) => number): number => {
      let left = 0;
      let right = array.length - 1;
      const compare = compareFn || (_(a,b) => (a < b ? -1 : a > b ? 1 : 0));

      while (left <= right) {
        const mid = Math.floor((left + right) / 2);
        const cmp = compare(array[mid], target);

        if (cmp === 0) return mid;
        if (cmp < 0) left = mid + 1;
        else right = mid - 1;
      }

      return -1;
    },

    // Efficient array intersection
    intersection: <T>(arr1: T[], arr2: T[]): T[] => {
      const set2 = new Set(arr2);
      return arr1.filter(item => set2.has(item));
    },

    // Move item in array
    moveItem: <T>(array: T[], fromIndex: number, toIndex: number): T[] => {
      const result = [...array];
      const [removed] = result.splice(fromIndex, 1);
      result.splice(toIndex, 0, removed);
      return result;
    }
  },

  // Object utilities
  objectUtils: {
    // Pick specific keys from object
    pick: <T extends Record<string, any>, K extends keyof T>(obj: T, keys: K[]): Pick<T, K> => {
      const result = {} as Pick<T, K>;
      keys.forEach(key => {
        if (key in obj) {
          result[key] = obj[key];
        }
      });
      return result;
    },

    // Omit specific keys from object
    omit: <T extends Record<string, any>, K extends keyof T>(obj: T, keys: K[]): Omit<T, K> => {
      const result = { ...obj };
      keys.forEach(key => {
        delete result[key];
      });
      return result;
    },

    // Deep merge objects
    merge: <T extends Record<string, any>>(target: T, ...sources: Partial<T>[]): T => {
      if (!sources.length) return target;
      const source = sources.shift();

      if (source) {
        Object.keys(source).forEach(key => {
          const sourceValue = source[key];
          const targetValue = target[key];

          if (sourceValue && typeof sourceValue === 'object' && !Array.isArray(sourceValue)) {
            if (!targetValue || typeof targetValue !== 'object') {
              target[key] = {} as any;
            }
            optimizedUtils.objectUtils.merge(target[key], sourceValue);
          } else {
            target[key] = sourceValue as any;
          }
        });
      }

      return optimizedUtils.objectUtils.merge(target, ...sources);
    },

    // Get nested property safely
    get: <T>(obj: unknown, path: string, defaultValue?: T): T => {
      const keys = path.split('.');
      let result = obj;

      for (const key of keys) {
        if (result == null || typeof result !== 'object') {
          return defaultValue as T;
        }
        result = result[key];
      }

      return result !== undefined ? result : defaultValue as T;
    },

    // Set nested property
    set: (obj: unknown, path: string, value: unknown): void => {
      const keys = path.split('.');
      const lastKey = keys.pop()!;
      let current = obj;

      for (const key of keys) {
        if (!(key in current) || typeof current[key] !== 'object') {
          current[key] = {};
        }
        current = current[key];
      }

      current[lastKey] = value;
    }
  },

  // String utilities
  stringUtils: { // Capitalize first letter
    capitalize: (str: string): string => str.charAt(0).toUpperCase() + str.slice(1).toLowerCase() }
    // Convert to camelCase
    camelCase: (str: string): string => {
      return str
        .replace(/(?:^\w|[A-Z]|\b\w)/g, (_word,index) => {
          return index === 0 ? word.toLowerCase() : word.toUpperCase(); })
        .replace(/\s+/g, '');
    },

    // Convert to kebab-case
    kebabCase: (str: string): string => {
      return str
        .replace(/([a-z])([A-Z])/g, '$1-$2')
        .replace(/\s+/g, '-')
        .toLowerCase();
    },

    // Truncate string with ellipsis
    truncate: (str: string, maxLength: number, suffix = '...'): string => {
      if (str.length <= maxLength) return str;
      return str.slice(0, maxLength - suffix.length) + suffix;
    },

    // Remove HTML tags
    stripHtml: (str: string): string => {
      return str.replace(/<[^>]*>/g, '');
    },

    // Escape HTML entities
    escapeHtml: (str: string): string => {
      const div = document.createElement('div');
      div.textContent = str;
      return div.innerHTML;
    }
  },

  // URL utilities
  urlUtils: {
    // Parse query string
    parseQuery: (queryString: string): Record<string, string> => {
      const params = new URLSearchParams(queryString.startsWith('?') ? queryString.slice(1) : queryString);
      const result: Record<string, string> = {};
      params.forEach((value,key) => {
        result[key] = value;
      });
      return result;
    },

    // Build query string
    buildQuery: (params: Record<string, any>): string => {
      const query = new URLSearchParams();
      Object.entries(params).forEach(([key,value]) => {
        if (value !== undefined && value !== null) {
          query.append(key, String(value));
        }
      });
      return query.toString();
    },

    // Join URL parts
    joinUrl: (...parts: string[]): string => {
      return parts
        .map((part,index) => {
          if (index === 0) return part.replace(/\/+$/, '');
          return part.replace(/^\/+|\/+$/g, '');
        })
        .filter(Boolean)
        .join('/');
    }
  },

  // Performance utilities
  performanceUtils: {
    // Measure execution time
    measure: async <T>(name: string, fn: () => T | Promise<T>): Promise<{ result: T; duration: number }> => {
      const start = performance.now();
      const result = await fn();
      const duration = performance.now() - start;
      
      if (import.meta.env.DEV) {
        console.log(`⏱️ ${name}: ${duration.toFixed(2)}ms`);
      }
      
      return { result, duration };
    },

    // Create performance mark
    mark: (name: string): void => {
      if (typeof performance !== 'undefined' && performance.mark) {
        performance.mark(name);
      }
    },

    // Measure between marks
    measureBetween: (startMark: string, endMark: string, measureName: string): number => {
      if (typeof performance !== 'undefined' && performance.measure) {
        performance.measure(measureName, startMark, endMark);
        const measure = performance.getEntriesByName(measureName)[0];
        return measure ? measure.duration : 0;
      }
      return 0;
    }
  },

  // Storage utilities with compression
  storageUtils: {
    // Set item with optional compression
    setItem: (key: string, value: unknown, compress = false): void => {
      try {
        let serialized = JSON.stringify(value);
        
        if (compress && serialized.length > 1024) {
          // Simple compression simulation (in real app, use LZ compression)
          serialized = `compressed:${serialized}`;
        }
        
        localStorage.setItem(key, serialized);
      } catch (error) {
        console.warn('Failed to save to localStorage:', error);
      }
    },

    // Get item with automatic decompression
    getItem: <T>(key: string, defaultValue?: T): T | undefined => {
      try {
        const item = localStorage.getItem(key);
        if (item === null) return defaultValue;
        
        let value = item;
        if (item.startsWith('compressed:')) {
          value = item.slice(11); // Remove 'compressed:' prefix
        }
        
        return JSON.parse(value);
      } catch (error) {
        console.warn('Failed to read from localStorage:', error);
        return defaultValue;
      }
    },

    // Remove item
    removeItem: (key: string): void => {
      try {
        localStorage.removeItem(key);
      } catch (error) {
        console.warn('Failed to remove from localStorage:', error);
      }
    },

    // Clear all items with prefix
    clearByPrefix: (prefix: string): void => {
      try {
        const keys = Object.keys(localStorage).filter(key => key.startsWith(prefix));
        keys.forEach(key => localStorage.removeItem(key));
      } catch (error) {
        console.warn('Failed to clear localStorage:', error);
      }
    }
  },

  // Validation utilities
  validationUtils: {
    // Email validation
    isEmail: (email: string): boolean => {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return emailRegex.test(email);
    },

    // URL validation
    isUrl: (url: string): boolean => {
      try {
        new URL(url);
        return true;
      } catch {
        return false;
      }
    },

    // Phone number validation (basic)
    isPhoneNumber: (phone: string): boolean => {
      const phoneRegex = /^\+?[\d\s\-()]+$/;
      return phoneRegex.test(phone) && phone.replace(/\D/g, '').length >= 10;
    },

    // Strong password validation
    isStrongPassword: (password: string): boolean => {
      const minLength = 8;
      const hasUpperCase = /[A-Z]/.test(password);
      const hasLowerCase = /[a-z]/.test(password);
      const hasNumbers = /\d/.test(password);
      const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);
      
      return password.length >= minLength && hasUpperCase && hasLowerCase && hasNumbers && hasSpecialChar;
    }
  }
};

// Export individual utilities for tree shaking
export const { debounce, throttle, deepClone } = optimizedUtils;
export const { arrayUtils, objectUtils, stringUtils, urlUtils } = optimizedUtils;
export const { performanceUtils, storageUtils, validationUtils } = optimizedUtils;

export default optimizedUtils;