import React, { useState, useEffect, memo, useCallback } from 'react';
import { useMediaRecording } from '@/hooks/useMediaRecording';
import { FileAttachment } from '@/types/messaging';
import { AudioRecorderConfig } from '@/types/enhanced-messaging';
import { Button } from '@/components/ui/button';
import { Mic, Play, Pause, Trash2, Send, Volume2 } from 'lucide-react';

interface VoiceMessageRecorderProps {
  onRecordingComplete: (attachment: FileAttachment) => void;
  onCancel?: () => void;
  config?: Partial<AudioRecorderConfig>;
  className?: string;
}

const VoiceMessageRecorder: React.FC<VoiceMessageRecorderProps> = memo(({
  onRecordingComplete,
  onCancel,
  config,
  className = ''
}) => {
  const [showRecorder, setShowRecorder] = useState(false);
  const [permissionGranted, setPermissionGranted] = useState(false);

  const {
    isRecording,
    isPaused,
    recordingTime,
    recordingState,
    startRecording,
    stopRecording,
    pauseRecording,
    resumeRecording,
    cancelRecording;
    requestPermission
  } = useMediaRecording(onRecordingComplete, config);

  useEffect(() => {
    if (recordingState.recordingPermission === 'granted') {
      setPermissionGranted(true);
    }
  }, [recordingState.recordingPermission]);

  const handleMicrophoneClick = useCallback(async () => {
    if (!permissionGranted) {
      const permission = await requestPermission();
      if (permission === 'denied') {
        return;
      }
    }
    setShowRecorder(true);
    startRecording();
  }, [permissionGranted, requestPermission, startRecording]);

  const handleCancel = useCallback(() => {
    cancelRecording();
    setShowRecorder(false);
    onCancel?.();
  }, [cancelRecording, onCancel]);

  const handleComplete = useCallback(() => {
    stopRecording();
    setShowRecorder(false);
  }, [stopRecording]);

  const formatTime = useCallback((seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  }, []);

  const renderWaveform = useCallback(() => {
    if (!recordingState.waveform || recordingState.waveform.length === 0) {
      return null;
    }

    const maxHeight = 40;
    const barWidth = 2;
    // const _barGap = 1; // unused
    const visibleBars = Math.min(recordingState.waveform.length, 50);
    const recentWaveform = recordingState.waveform.slice(-visibleBars);

    return (
      <div className="flex items-center justify-center h-12 space-x-0.5">
        {recentWaveform.map((value, index) => (
          <div
            key={index} className="bg-blue-500 rounded-full transition-all duration-100 ease-out"
            style={{
              width: `${barWidth}px`,
              height: `${Math.max(2, value * maxHeight)}px`,
              opacity: isRecording ? 0.7 + (value * 0.3) : 0.5
            }}
          />
        ))}
      </div>
    );
  }, [recordingState.waveform, isRecording]);

  if (!showRecorder) {
    return (
      <Button
        type="button"
        variant="ghost"
        size="sm"
        onClick={handleMicrophoneClick} className={`p-2 ${className}`}, disabled={recordingState.recordingPermission === 'denied'}
      >
        <Mic size={20} />
    </Button>
    );
  }

  return (
    <div className={`bg-white border rounded-lg p-4 shadow-lg ${className}`}>
      <div className="flex flex-col space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className={`w-3 h-3 rounded-full ${isRecording && !isPaused ? 'bg-red-500 animate-pulse' : 'bg-gray-400'}`} />
            <span className="text-sm font-medium">
              {isRecording ? (isPaused ? 'Paused' : 'Recording') : 'Ready'}
            </span>
    </div>
          <div className="text-sm text-gray-600">
            {formatTime(recordingTime)}
          </div>
    </div>
        {recordingState.waveform && renderWaveform()}

        <div className="flex items-center space-x-2">
          <div className="flex items-center space-x-1">
            <Volume2 size={16} />
            <div className="w-12 h-2 bg-gray-200 rounded-full overflow-hidden">
              <div
                className="h-full bg-green-500 transition-all duration-100"
                style={{ width: `${recordingState.volume * 100}%` }}
              />
    </div>
          </div>
          <span className="text-xs text-gray-500">
            {Math.round(recordingState.volume * 100)}%
          </span>
    </div>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={handleCancel} className="text-red-600 hover:bg-red-50"
            >
              <Trash2 size={16} />
    </Button>
            {isRecording && (
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={isPaused ? resumeRecording : pauseRecording} className="text-blue-600 hover:bg-blue-50"
              >
                {isPaused ? <Play size={16} /> : <Pause size={16} />}
              </Button>
            )}
          </div>

          <div className="flex items-center space-x-2">
            {!isRecording ? (
              <Button
                type="button"
                variant="default"
                size="sm"
                onClick={startRecording} className="bg-red-500 hover:bg-red-600 text-white"
              >
                <Mic size={16} className="mr-1" />
                Start Recording
              </Button>
            ) : (
              <Button
                type="button"
                variant="default"
                size="sm"
                onClick={handleComplete} className="bg-green-500 hover:bg-green-600 text-white"
              >
                <Send size={16} className="mr-1" />
                Send Voice Message
              </Button>
            )}
          </div>
    </div>
        {recordingState.error && (
          <div className="text-red-500 text-sm p-2 bg-red-50 rounded">
            {recordingState.error}
          </div>
        )}
      </div>
    </div>
  );
});

VoiceMessageRecorder.displayName = 'VoiceMessageRecorder';

export default VoiceMessageRecorder;