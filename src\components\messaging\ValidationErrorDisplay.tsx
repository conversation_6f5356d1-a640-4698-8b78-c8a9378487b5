import React from 'react';
import { AlertTriangle, X, <PERSON>, Shield } from 'lucide-react';

export interface ValidationErrorDisplayProps {
  errors: string[];
  rateLimitStatus?: Record<string, { remaining: number, resetTime: number }>;
  onDismiss?: () => void;
  className?: string;
}

export const ValidationErrorDisplay: React.FC<ValidationErrorDisplayProps> = ({
  errors,
  rateLimitStatus,
  onDismiss,
  className = ''
}) => {
  if (errors.length === 0) return null;

  const formatTimeRemaining = (resetTime: number): string => {
    const remaining = Math.max(0, resetTime - Date.now());
    const minutes = Math.floor(remaining / (1000 * 60));
    const seconds = Math.floor((remaining % (1000 * 60)) / 1000);
    
    if (minutes > 0) {
      return `${minutes}m ${seconds}s`;
    }
    return `${seconds}s`;
  };

  const isRateLimitError = errors.some(error => 
    error.toLowerCase().includes('rate limit') || 
    error.toLowerCase().includes('slow down')
  );

  return (
    <div className={`bg-red-50 border border-red-200 rounded-lg p-4 ${className}`}>
      <div className="flex items-start justify-between">
        <div className="flex items-start space-x-3">
          <div className="flex-shrink-0">
            {isRateLimitError ? (
              <Clock className="h-5 w-5 text-red-400" />
            ) : (
              <AlertTriangle className="h-5 w-5 text-red-400" />
            )}
          </div>
          
          <div className="flex-1">
            <h3 className="text-sm font-medium text-red-800 mb-2">
              {isRateLimitError ? 'Rate Limit Exceeded' : 'Validation Error'}
            </h3>
            
            <div className="space-y-1">
              {errors.map((error, index) => (
                <p key={index} className="text-sm text-red-700">
                  {error}
                </p>
              ))}
            </div>

            {/* Rate limit status display */}
            {rateLimitStatus && Object.keys(rateLimitStatus).length > 0 && (
              <div className="mt-3 p-3 bg-red-100 rounded-md">
                <div className="flex items-center space-x-2 mb-2">
                  <Shield className="h-4 w-4 text-red-600" />
                  <span className="text-sm font-medium text-red-800">Rate Limit Status</span>
    </div>
                <div className="space-y-2">
                  {Object.entries(rateLimitStatus).map(([action, status]) => (
                    <div key={action} className="flex justify-between items-center text-xs">
                      <span className="text-red-700 capitalize">
                        {action.replace(/([A-Z])/g, ' $1').toLowerCase()}:
                      </span>
                      <div className="text-right">
                        <span className="text-red-800 font-medium">
                          {status.remaining} remaining
                        </span>
                        {status.remaining === 0 && (
                          <div className="text-red-600">
                            Resets in {formatTimeRemaining(status.resetTime)}
                          </div>
                        )}
                      </div>
    </div>
                  ))}
                </div>
    </div>
            )}
          </div>
    </div>
        {onDismiss && (
          <button
            onClick={onDismiss} className="flex-shrink-0 ml-4 text-red-400 hover:text-red-600 transition-colors"
            aria-label="Dismiss error"
          >
            <X className="h-4 w-4" />
    </button>
        )}
      </div>
    </div>
  );
};

export default ValidationErrorDisplay;