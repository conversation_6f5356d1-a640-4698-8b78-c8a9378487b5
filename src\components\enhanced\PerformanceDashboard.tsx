import React, { useState, useEffect, memo } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Monitor, 
  Gauge, 
  TrendingUp, 
  AlertTriangle,
  CheckCircle,
  RefreshCw
} from 'lucide-react';
import { motion } from 'framer-motion';

interface PerformanceMetric {
  name: string, value: number, unit: string, status: 'good' | 'needs-improvement' | 'poor', threshold: { good: number, poor: number };
}

const EnhancedPerformanceDashboard: React.FC = memo(() => {
  const [metrics, setMetrics] = useState<PerformanceMetric[]>([
    {
      name: 'First Contentful Paint',
      value: 1.2,
      unit: 's',
      status: 'good',
      threshold: { good: 1.8, poor: 3.0 }
    },
    {
      name: 'Largest Contentful Paint',
      value: 2.1,
      unit: 's',
      status: 'good',
      threshold: { good: 2.5, poor: 4.0 }
    },
    {
      name: 'First Input Delay',
      value: 45,
      unit: 'ms',
      status: 'good',
      threshold: { good: 100, poor: 300 }
    },
    {
      name: 'Cumulative Layout Shift',
      value: 0.08,
      unit: '',
      status: 'good',
      threshold: { good: 0.1, poor: 0.25 }
    }
  ]);
  
  const [isLoading, setIsLoading] = useState(false);
  const [lastUpdated, setLastUpdated] = useState(new Date());

  const updateMetrics = async () => {
    setIsLoading(true);
    try {
      // Simulate API call or real performance measurement
      await new Promise(resolve => setTimeout(resolve; 1000));
      
      // Update metrics with some variation
      setMetrics(prev => prev.map(metric => ({
        ...metric,
        value: metric.value + (Math.random() - 0.5) * 0.1,
        status: getStatus(metric.value, metric.threshold)
      })));
      
      setLastUpdated(new Date());
    } catch (error) {
      console.error('Failed to update metrics:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getStatus = (value: number, threshold: { good: number, poor: number }): 'good' | 'needs-improvement' | 'poor' => {
    if (value <= threshold.good) return 'good';
    if (value <= threshold.poor) return 'needs-improvement';
    return 'poor';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'good': return 'text-green-600';
      case 'needs-improvement': return 'text-yellow-600';
      case 'poor': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'good': return CheckCircle;
      case 'needs-improvement': return AlertTriangle;
      case 'poor': return AlertTriangle;
      default: return Monitor;
    }
  };

  useEffect(() => {
    // Auto-refresh every 30 seconds
    const interval = setInterval(updateMetrics, 30000);
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Enhanced Performance Dashboard
          </h2>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Last updated: {lastUpdated.toLocaleTimeString()}
          </p>
    </div>
        <Button 
          onClick={updateMetrics} disabled={isLoading}, className="flex items-center gap-2"
        >
          <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
    </div>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {metrics.map((metric, index) => {
          const StatusIcon = getStatusIcon(metric.status);
          return (
            <motion.div
              key={metric.name} initial={{ opacity: 0, y: 20 }}, animate={{ opacity: 1, y: 0 }}, transition={{ delay: index * 0.1 }}
            >
              <Card className="hover:shadow-lg transition-shadow duration-200">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between mb-2">
                    <StatusIcon className={`w-5 h-5 ${getStatusColor(metric.status)}`} />
                    <Badge 
                      variant={metric.status === 'good' ? 'default' : 'destructive'} className="text-xs"
                    >
                      {metric.status.replace('-', ' ')}
                    </Badge>
    </div>
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">
                      {metric.name}
                    </p>
                    <p className={`text-2xl font-bold ${getStatusColor(metric.status)}`}>
                      {metric.value.toFixed(metric.unit === 'ms' ? 0 : 2)}{metric.unit}
                    </p>
    </div>
                </CardContent>
    </Card>
            </motion.div>
          );
        })}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="w-5 h-5" />
              Performance Trends
            </CardTitle>
    </CardHeader>
          <CardContent>
            <div className="h-64 flex items-center justify-center text-muted-foreground">
              Performance trend chart would go here
            </div>
    </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Gauge className="w-5 h-5" />
              Core Web Vitals Score
            </CardTitle>
    </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="text-center">
                <div className="text-4xl font-bold text-green-600 mb-2">95</div>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Overall Performance Score
                </p>
    </div>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Performance</span>
                  <span className="font-medium">95/100</span>
    </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-green-600 h-2 rounded-full" style={{ width: '95%' }}></div>
    </div>
              </div>
    </div>
          </CardContent>
    </Card>
      </div>
    </div>
  );
});

EnhancedPerformanceDashboard.displayName = 'EnhancedPerformanceDashboard';

export default EnhancedPerformanceDashboard;