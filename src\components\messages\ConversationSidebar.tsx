import React, { useMemo } from 'react';
import { motion } from 'framer-motion';
import { Search, UserPlus, X, Bell, BellOff, Settings } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui';
import { Badge } from '@/components/ui/badge';
import { formatDistanceToNow } from 'date-fns';
import { Conversation } from '@/types/enhanced-messaging';
import { MESSAGING_CONSTANTS } from '@/constants/messaging';

interface ConversationSidebarProps {
  conversations: Conversation[], activeConversationId: string | null, onlineUsers: Conversation[], isConnected: boolean, isMobile: boolean, showSidebar: boolean, pushNotificationsEnabled: boolean, onSelectConversation: (id: string) => void; onCloseSidebar: () => void; onCreateGroup: () => void; onShowNotificationSettings: () => void;
}

// Memoized conversation item component for better performance
const ConversationItem = React.memo<{
  conversation: Conversation, isActive: boolean, isMobile: boolean, onSelect: (id: string) => void;
}>(({ conversation, isActive, isMobile, onSelect }) => {
  const formattedTime = useMemo(() => {
    if (!conversation.lastMessage) return null;
    return formatDistanceToNow(new Date(conversation.lastMessage.timestamp), { addSuffix: true });
  }, [conversation.lastMessage]);

  return (
    <button
      onClick={() => onSelect(conversation.id)} className={`w-full ${isMobile ? 'p-4' : 'p-3'}, flex items-center gap-3 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-lg transition-colors mb-1 ${
        isActive
          ? 'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800'
          : ''
      } ${isMobile ? 'active:bg-gray-100 dark:active:bg-gray-700' : ''}`}
    >
      <Avatar className={isMobile ? "h-12 w-12" : "h-10 w-10"}>
        <AvatarImage src={conversation.user?.avatar} />
        <AvatarFallback className={isMobile ? "text-lg" : ""}>
          {conversation.user?.name?.[0] || conversation.name?.[0]}
        </AvatarFallback>
    </Avatar>
      <div className="flex-1 text-left min-w-0">
        <div className="flex items-center justify-between">
          <h3 className={`font-medium truncate ${isMobile ? 'text-base' : 'text-sm'}`}>
            {conversation.user?.name || conversation.name}
          </h3>
          {formattedTime && (
            <span className={`text-gray-500 flex-shrink-0 ml-2 ${isMobile ? 'text-sm' : 'text-xs'}`}>
              {formattedTime}
            </span>
          )}
        </div>
        
        {conversation.lastMessage && (
          <p className={`text-gray-500 truncate ${isMobile ? 'text-sm' : 'text-xs'}`}>
            {conversation.lastMessage?.content || 'No messages yet'}
          </p>
        )}
      </div>
      
      {conversation.unreadCount && conversation.unreadCount > 0 && (
        <Badge variant="default" className={`ml-2 bg-blue-500 text-white ${isMobile ? 'text-sm px-2 py-1' : ''}`}>
          {conversation.unreadCount}
        </Badge>
      )}
    </button>
  );
});

ConversationItem.displayName = 'ConversationItem';

// Memoized online user item component
const OnlineUserItem = React.memo<{
  conversation: Conversation, onSelect: (id: string) => void;
}>(({ conversation, onSelect }) => {
  const firstName = useMemo(() => 
    conversation.user?.name?.split(' ')[0] || '',
  [conversation.user?.name]);

  return (
    <button
      onClick={() => onSelect(conversation.id)} className="flex-shrink-0 flex flex-col items-center gap-1 p-2 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-lg transition-colors"
    >
      <div className="relative">
        <Avatar className="w-12 h-12">
          <AvatarImage src={conversation.user?.avatar} />
          <AvatarFallback>{conversation.user?.name?.[0]}</AvatarFallback>
    </Avatar>
        <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white dark:border-gray-900" />
    </div>
      <span className="text-xs text-center truncate max-w-16">
        {firstName}
      </span>
    </button>
  );
});

OnlineUserItem.displayName = 'OnlineUserItem';

const ConversationSidebar: React.FC<ConversationSidebarProps> = ({
  conversations,
  activeConversationId,
  onlineUsers,
  isConnected,
  isMobile,
  showSidebar,
  pushNotificationsEnabled,
  onSelectConversation,
  onCloseSidebar,
  onCreateGroup,
  onShowNotificationSettings
}) => {
  // Memoize sidebar classes to prevent recalculation on every render
  const sidebarClasses = useMemo(() => `${
    isMobile 
      ? `fixed left-0 top-0 h-full w-full max-w-sm z-50` 
      : 'w-80 relative'
  } border-r border-gray-200 dark:border-gray-700 flex flex-col bg-white dark:bg-gray-900 ${
    isMobile ? 'shadow-2xl' : ''
  }`, [isMobile]);
  
  // Memoize connection status styling
  const connectionStatus = useMemo(() => ({
    dotClass: `w-3 h-3 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`,
    textClass: `font-medium ${isConnected ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`,
    text: isConnected ? 'Connected' : 'Disconnected'
  }), [isConnected]);

  return (
    <motion.div 
      className={sidebarClasses} initial={false}, animate={{
        x: isMobile && !showSidebar ? '-100%' : '0%'
      }}, transition={MESSAGING_CONSTANTS.UI.ANIMATION.SIDEBAR_SPRING}
    >
      {/* Enhanced Header with better mobile spacing */}
      <div className={`p-4 border-b border-gray-200 dark:border-gray-700 ${isMobile ? 'px-6' : ''}`}>
        <div className="flex items-center justify-between mb-4">
          <h1 className={`font-semibold ${isMobile ? 'text-2xl' : 'text-xl'}`}>Messages</h1>
          <div className="flex gap-2">
            <Button
              variant="ghost"
              size={isMobile ? "default" : "sm"} onClick={onCreateGroup}, className={isMobile ? "h-10 w-10 p-0" : ""} title="Create Group"
            >
              <UserPlus className={`${isMobile ? 'w-5 h-5' : 'w-4 h-4'}`} />
    </Button>
            {isMobile && (
              <Button
                variant="ghost"
                size={isMobile ? "default" : "sm"} onClick={onCloseSidebar}, className={isMobile ? "h-10 w-10 p-0" : ""}
              >
                <X className={`${isMobile ? 'w-5 h-5' : 'w-4 h-4'}`} />
    </Button>
            )}
            <Button
              variant="ghost"
              size={isMobile ? "default" : "sm"}, aria-label="Search messages"
              className={isMobile ? "h-10 w-10 p-0" : ""}
            >
              <Search className={`${isMobile ? 'w-5 h-5' : 'w-4 h-4'}`} />
    </Button>
            <Button
              variant="ghost"
              size={isMobile ? "default" : "sm"} onClick={onShowNotificationSettings}, aria-label="Notification settings"
              className={isMobile ? "h-10 w-10 p-0" : ""} title="Notification Settings"
            >
              {pushNotificationsEnabled ? (
                <Bell className={`${isMobile ? 'w-5 h-5' : 'w-4 h-4'}, text-blue-600`} />
              ) : (
                <BellOff className={`${isMobile ? 'w-5 h-5' : 'w-4 h-4'}, text-gray-400`} />
              )}
            </Button>
            <Button
              variant="ghost"
              size={isMobile ? "default" : "sm"}, aria-label="Settings"
              className={isMobile ? "h-10 w-10 p-0" : ""}
            >
              <Settings className={`${isMobile ? 'w-5 h-5' : 'w-4 h-4'}`} />
    </Button>
          </div>
    </div>
        {/* Connection Status */}
        <div className="flex items-center gap-2 text-sm bg-gray-50 dark:bg-gray-800 p-2 rounded-lg">
          <div className={connectionStatus.dotClass} />
          <span className={connectionStatus.textClass}>
            {connectionStatus.text}
          </span>
    </div>
      </div>

      {/* Online Users */}
      {onlineUsers.length > 0 && (
        <div className="p-3 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Online Now ({onlineUsers.length})
          </h3>
          <div className="flex gap-2 overflow-x-auto pb-2">
            {onlineUsers.map((conversation) => (
              <OnlineUserItem
                key={conversation.id} conversation={conversation}, onSelect={onSelectConversation}
              />
            ))}
          </div>
    </div>
      )}

      {/* Conversations List */}
      <ScrollArea className="flex-1">
        <div className="p-2">
          {conversations.length === 0 ? (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
              <div className="text-sm">
                {isConnected ? 'No conversations yet' : 'Loading conversations...'}
              </div>
              <div className="text-xs mt-1">
                {isConnected ? 'Start a new conversation!' : 'Connecting to messaging service...'}
              </div>
    </div>
          ) : (
            conversations.map((conversation) => (
              <ConversationItem
                key={conversation.id} conversation={conversation}, isActive={activeConversationId === conversation.id} isMobile={isMobile}, onSelect={onSelectConversation}
              />
            ))
          )}
        </div>
    </ScrollArea>
    </motion.div>
  );
};

export default React.memo(ConversationSidebar);
