#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

class JSXPropsCommaFixer {
  constructor() {
    this.fixedFiles = 0;
    this.fixedErrors = 0;
  }

  fixJSXPropsCommas(content) {
    let modified = false;
    let fixes = 0;

    // Pattern 1: Fix ", prop=" where comma is at start of line
    // Example: , isLoading={false} -> isLoading={false}
    const pattern1 = /(\n\s*),\s*([a-zA-Z_$][a-zA-Z0-9_$]*)(\s*=)/g;
    const newContent1 = content.replace(pattern1, (match, newline, propName, equals) => {
      fixes++;
      modified = true;
      return `${newline}${propName}${equals}`;
    });

    let result = newContent1;

    // Pattern 2: Fix missing commas between JSX props on same line
    // Example: prop1={value1} prop2={value2} -> prop1={value1}, prop2={value2}
    const pattern2 = /(\}\s+)([a-zA-Z_$][a-zA-Z0-9_$]*\s*=)/g;
    result = result.replace(pattern2, (match, closeBrace, nextProp) => {
      fixes++;
      modified = true;
      return `${closeBrace.trim()}, ${nextProp}`;
    });

    // Pattern 3: Fix props that got merged incorrectly
    // Example: prop={value} isOtherProp -> prop={value}, isOtherProp
    const pattern3 = /(\}\s+)(is[A-Z][a-zA-Z0-9_$]*)/g;
    result = result.replace(pattern3, (match, closeBrace, prop) => {
      fixes++;
      modified = true;
      return `${closeBrace.trim()}, ${prop}`;
    });

    // Pattern 4: Fix props that got merged with "on" handlers
    // Example: prop={value} onSomething -> prop={value}, onSomething
    const pattern4 = /(\}\s+)(on[A-Z][a-zA-Z0-9_$]*)/g;
    result = result.replace(pattern4, (match, closeBrace, prop) => {
      fixes++;
      modified = true;
      return `${closeBrace.trim()}, ${prop}`;
    });

    return { content: result, modified, fixes };
  }

  fixFile(filePath) {
    try {
      const originalContent = fs.readFileSync(filePath, 'utf8');
      const { content: newContent, modified, fixes } = this.fixJSXPropsCommas(originalContent);

      if (modified) {
        fs.writeFileSync(filePath, newContent, 'utf8');
        this.fixedFiles++;
        this.fixedErrors += fixes;
        console.log(`✅ Fixed ${fixes} JSX prop comma issues in ${path.basename(filePath)}`);
        return true;
      }

      return false;
    } catch (error) {
      console.error(`❌ Error fixing ${filePath}:`, error.message);
      return false;
    }
  }

  processDirectory(dirPath) {
    const entries = fs.readdirSync(dirPath, { withFileTypes: true });

    for (const entry of entries) {
      const fullPath = path.join(dirPath, entry.name);

      if (entry.isDirectory() && !entry.name.startsWith('.') && entry.name !== 'node_modules') {
        this.processDirectory(fullPath);
      } else if (entry.isFile() && /\.(tsx|jsx)$/.test(entry.name)) {
        this.fixFile(fullPath);
      }
    }
  }

  run() {
    console.log('🔧 Starting JSX props comma fixing...\n');

    const srcPath = path.join(process.cwd(), 'src');
    if (fs.existsSync(srcPath)) {
      this.processDirectory(srcPath);
    }

    console.log('\n📊 JSX Props Fix Summary:');
    console.log(`✅ Files fixed: ${this.fixedFiles}`);
    console.log(`🔧 Prop comma errors fixed: ${this.fixedErrors}`);
    console.log('\n🎉 JSX props comma fixing complete!');

    return {
      filesFixed: this.fixedFiles,
      errorsFixed: this.fixedErrors
    };
  }
}

// Run the fixer
const fixer = new JSXPropsCommaFixer();
fixer.run();
