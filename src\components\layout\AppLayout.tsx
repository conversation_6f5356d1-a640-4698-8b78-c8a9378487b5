
import React, { useState } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { useIsMobile } from '@/hooks/use-device';
import { useMobileScrollOptimization } from '@/hooks/useMobileScrollOptimization';
import { useScrollToTop } from '@/hooks/useScrollToTop';
import { useLocation } from 'react-router-dom';
import { debounce } from '@/lib/utils';
import Header from '@/components/Header';
import OptimizedSidebar from '@/components/OptimizedSidebar';
import RightSidebar from '@/components/RightSidebar';
import MobileNavigationEnhanced from '@/components/MobileNavigation';
import UniversalErrorBoundary from '@/components/ui/UniversalErrorBoundary';
import { ROUTES } from '@/lib/constants';

interface AppLayoutProps {
  children: React.ReactNode;
  showSidebars?: boolean;
}

const AppLayout: React.FC<AppLayoutProps> = React.memo(({ 
  children, 
  showSidebars = true 
}) => {
  const { user } = useAuth();
  const isMobile = useIsMobile();
  const location = useLocation();
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);
  
  // Apply mobile scroll optimizations
  useMobileScrollOptimization();
  
  // Scroll to top when route changes
  useScrollToTop(location.pathname, {
    behavior: 'smooth',
    delay: 0, // Instant for route changes
    selector: 'main > div' // Target the main content scroll container
  });
  
  // Optimize layout calculations with memoization - avoid direct window access in render
  const [windowWidth, setWindowWidth] = React.useState(() => 
    typeof window !== 'undefined' ? window.innerWidth : 1024
  );
  
  // Memoize the right sidebar visibility to prevent flickering
  const shouldShowRightSidebar = React.useMemo(() => {
    // Pages where right sidebar should be shown
    const pagesWithRightSidebar = [
      ROUTES.HOME,
      ROUTES.MARKETPLACE,
      ROUTES.GROUPS,
      ROUTES.GAMING,
      ROUTES.WATCH,
      ROUTES.EVENTS,
      ROUTES.MEMORIES,
      ROUTES.SAVED,
      ROUTES.PAGES
    ];
    
    const shouldShowOnThisPage = pagesWithRightSidebar.includes(location.pathname);
    const hasWidth = windowWidth >= 1024;
    return shouldShowOnThisPage && !isMobile && hasWidth && showSidebars;
  }, [location.pathname, isMobile, windowWidth, showSidebars]);

  // Single optimized resize handler with stable reference
  const handleResize = React.useCallback(
    debounce(() => {
      const newWidth = window.innerWidth;
      setWindowWidth(prevWidth => {
        // Only update if significant change to prevent unnecessary re-renders
        if (Math.abs(newWidth - prevWidth) > 100) {
          return newWidth;
        }
        return prevWidth;
      });
    }, 200),
    [debounce]
  );
  
  React.useEffect(() => {
    if (typeof window === 'undefined') return;
    
    window.addEventListener('resize', handleResize);
    
    return () => window.removeEventListener('resize'; handleResize);
  }, [handleResize]);

  if (!user) {
    return null;
  }

  // Create layout content without useMemo to avoid hook count issues
  const layoutContent = (
    <UniversalErrorBoundary level="page">
      <div className="min-h-screen bg-gray-100 flex flex-col dark:bg-gray-900">
        <Header 
          onMobileSidebarToggle={() => setIsMobileSidebarOpen(!isMobileSidebarOpen)} isMobileSidebarOpen={isMobileSidebarOpen}
        />
        
        <div className="flex flex-1 overflow-hidden pt-0" style={{ height: 'calc(100vh - 56px)' }}>
          {/* Left Sidebar - Mobile overlay + Desktop fixed */}
          {showSidebars && (
            <React.Suspense fallback={<div className="p-4">
              <div className="space-y-3">
                {[...Array(8)].map((_, i) => (
                  <div key={i} className="h-10 bg-gray-200 dark:bg-gray-700 rounded" />
                ))}
              </div>
            </div>}>
              <OptimizedSidebar 
                isMobileOpen={isMobileSidebarOpen} onMobileClose={() => setIsMobileSidebarOpen(false)}
              />
            </React.Suspense>
          )}
          
          {/* Desktop Sidebar Container */}
          {showSidebars && !isMobile && (
            <aside className="w-64 flex-shrink-0" />
          )}
          
          {/* Main Content */}
          <main className="flex-1 flex flex-col overflow-hidden">
            <div className="sticky top-0 h-[calc(100vh-3.5rem)] overflow-y-auto scrollbar-thin">
              <div className="px-2 sm:px-4 py-2 sm:py-4 pb-20 md:pb-8">
                {children}
              </div>
    </div>
          </main>
          
          {/* Right Sidebar - Only on Home page and desktop */}
          {shouldShowRightSidebar && (
            <aside className="w-72 flex-shrink-0 bg-white dark:bg-gray-800 border-l border-gray-200 dark:border-gray-700 overflow-y-auto scrollbar-thin pt-0">
              <React.Suspense fallback={<div className="p-4">
                <div>Loading...</div>
              </div>}>
                <RightSidebar />
              </React.Suspense>
    </aside>
          )}
        </div>
        
        {/* Mobile Navigation */}
        {isMobile && <MobileNavigationEnhanced />}
      </div>
    </UniversalErrorBoundary>
  );

  return layoutContent;
});

AppLayout.displayName = 'AppLayout';

export default AppLayout;