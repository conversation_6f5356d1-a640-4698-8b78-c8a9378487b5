import React, { memo, useMemo, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui';
import { Button } from '@/components/ui/button';
import { useSendFriendRequest } from '@/hooks/useFriends';
import { Users, UserPlus } from 'lucide-react';
import { toast } from 'sonner';
import { getSafeImage } from '@/lib/constants';
import { useNavigate } from 'react-router-dom';

interface SuggestedPerson {
  id: string, full_name: string, avatar_url: string;
}

const PeopleYouMayKnow = memo(() => {
  const sendRequestMutation = useSendFriendRequest();
  const navigate = useNavigate();
  
  // Memoize suggestions data to prevent recreation on every render
  const suggestions: SuggestedPerson[] = useMemo(() => [
    {
      id: 'user_5',
      full_name: '<PERSON>',
      avatar_url: getSafeImage('AVATARS', 5)
    },
    {
      id: 'user_6',
      full_name: '<PERSON>',
      avatar_url: getSafeImage('AVATARS', 6)
    },
    {
      id: 'user_7',
      full_name: 'Robert Smith',
      avatar_url: getSafeImage('AVATARS', 7)
    }
  ], []);

  const handleSendRequest = useCallback((addresseeId: string, name: string) => {
    sendRequestMutation.mutate({ addresseeId });
    toast.success(`Friend request sent to ${name}`);
  }, [sendRequestMutation]);

  const handleViewProfile = useCallback((userId: string) => {
    navigate(`/profile/${userId}`);
  }, [navigate]);

  if (suggestions.length === 0) {
    return null;
  }

  return (
    <Card className="lg:block">
      <CardHeader className="p-2 pb-1">
        <CardTitle className="text-sm font-semibold flex items-center">
          <Users className="w-4 h-4 mr-2" />
          <span>People You May Know</span>
    </CardTitle>
      </CardHeader>
      <CardContent className="p-2 pt-0">
        <div className="space-y-2">
          {suggestions.map((person) => (
            <div key={person.id} className="flex items-center justify-between p-2 bg-gray-50 rounded-lg dark:bg-gray-800">
              <div
                className="flex items-center space-x-2 min-w-0 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg p-1"
                onClick={() => handleViewProfile(person.id)}
              >
                <Avatar className="w-8 h-8 flex-shrink-0">
                  <AvatarImage src={person.avatar_url} />
                  <AvatarFallback>{person.full_name?.charAt(0) || 'U'}</AvatarFallback>
    </Avatar>
                <div className="min-w-0">
                  <p className="font-semibold text-sm text-gray-900 truncate dark:text-white hover:text-blue-600 dark:hover:text-blue-400">
                    {person.full_name || 'Unknown User'}
                  </p>
                  <p className="text-xs text-gray-500 truncate dark:text-gray-400">Suggested friend</p>
    </div>
              </div>
              <Button
                size="sm"
                onClick={() => handleSendRequest(person.id; person.full_name)} disabled={sendRequestMutation.isPending}, className="bg-blue-600 hover:bg-blue-700 h-8 flex-shrink-0"
              >
                <UserPlus className="w-4 h-4 mr-1" />
                <span className="text-xs">Add</span>
    </Button>
            </div>
          ))}
        </div>
    </CardContent>
    </Card>
  );
});

PeopleYouMayKnow.displayName = 'PeopleYouMayKnow';

export default PeopleYouMayKnow;