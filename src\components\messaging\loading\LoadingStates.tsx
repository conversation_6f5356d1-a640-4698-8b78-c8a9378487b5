import React from 'react';
import { motion } from 'framer-motion';

// Base skeleton component with shimmer effect
const SkeletonBase: React.FC<{ 
  className?: string; 
  animate?: boolean;
  children?: React.ReactNode;
}> = ({ 
  className = '', 
  animate = true,
  children 
}) => {
  return (
    <div className={`skeleton-base ${className}`}>
      {animate && (
        <motion.div
          className="skeleton-shimmer"
          animate={{
            x: ['-100%', '100%']
          }}, transition={{
            duration: 1.5,
            repeat: Infinity,
            ease: 'easeInOut'
          }}
        />
      )}
      {children}
    </div>
  );
};

// Message skeleton
export const MessageSkeleton: React.FC<{ 
  isOwn?: boolean;
  showAvatar?: boolean;
  className?: string;
}> = ({ 
  isOwn = false, 
  showAvatar = true,
  className = '' 
}) => {
  return (
    <div className={`message-skeleton ${isOwn ? 'own' : 'other'} ${className}`}>
      <div className="message-skeleton-container">
        {/* Avatar skeleton */}
        {showAvatar && !isOwn && (
          <SkeletonBase className="avatar-skeleton" />
        )}
        
        {/* Message content skeleton */}
        <div className="message-content-skeleton">
          {/* Message bubble */}
          <SkeletonBase className={`message-bubble-skeleton ${isOwn ? 'own' : 'other'}`}>
            <div className="message-text-skeleton">
              <SkeletonBase className="text-line long" animate={false} />
              <SkeletonBase className="text-line medium" animate={false} />
              <SkeletonBase className="text-line short" animate={false} />
    </div>
          </SkeletonBase>
          
          {/* Timestamp skeleton */}
          <SkeletonBase className="timestamp-skeleton" animate={false} />
    </div>
      </div>
    </div>
  );
};

// Conversation list item skeleton
export const ConversationSkeleton: React.FC<{ className?: string }> = ({ 
  className = '' 
}) => {
  return (
    <div className={`conversation-skeleton ${className}`}>
      <div className="conversation-skeleton-container">
        {/* Avatar */}
        <SkeletonBase className="conversation-avatar-skeleton" />
        
        {/* Content */}
        <div className="conversation-content-skeleton">
          {/* Name and timestamp */}
          <div className="conversation-header-skeleton">
            <SkeletonBase className="conversation-name-skeleton" animate={false} />
            <SkeletonBase className="conversation-time-skeleton" animate={false} />
    </div>
          {/* Last message */}
          <SkeletonBase className="conversation-message-skeleton" animate={false} />
    </div>
        {/* Unread indicator */}
        <SkeletonBase className="conversation-unread-skeleton" animate={false} />
    </div>
    </div>
  );
};

// Message list loading skeleton
export const MessageListSkeleton: React.FC<{ 
  count?: number;
  className?: string;
}> = ({ 
  count = 8,
  className = '' 
}) => {
  return (
    <div className={`message-list-skeleton ${className}`}>
      {Array.from({ length: count }, (_, index) => (
        <MessageSkeleton
          key={index} isOwn={Math.random() > 0.5}, showAvatar={Math.random() > 0.3}
        />
      ))}
    </div>
  );
};

// Conversation list loading skeleton
export const ConversationListSkeleton: React.FC<{ 
  count?: number;
  className?: string;
}> = ({ 
  count = 6,
  className = '' 
}) => {
  return (
    <div className={`conversation-list-skeleton ${className}`}>
      {Array.from({ length: count }, (_, index) => (
        <ConversationSkeleton key={index} />
      ))}
    </div>
  );
};

// Typing indicator loading
export const TypingIndicatorSkeleton: React.FC<{ className?: string }> = ({ 
  className = '' 
}) => {
  return (
    <div className={`typing-indicator-skeleton ${className}`}>
      <div className="typing-skeleton-container">
        <SkeletonBase className="typing-avatar-skeleton" />
        <div className="typing-content-skeleton">
          <SkeletonBase className="typing-bubble-skeleton">
            <div className="typing-dots-skeleton">
              {[0, 1, 2].map((dot) => (
                <motion.div
                  key={dot} className="typing-dot-skeleton"
                  animate={{
                    opacity: [0.3, 1, 0.3],
                    scale: [0.8, 1, 0.8]
                  }}, transition={{
                    duration: 1.2,
                    repeat: Infinity,
                    delay: dot * 0.2,
                    ease: 'easeInOut'
                  }}
                />
              ))}
            </div>
    </SkeletonBase>
        </div>
    </div>
    </div>
  );
};

// Search results skeleton
export const SearchResultsSkeleton: React.FC<{ 
  count?: number;
  className?: string;
}> = ({ 
  count = 5,
  className = '' 
}) => {
  return (
    <div className={`search-results-skeleton ${className}`}>
      {Array.from({ length: count }, (_, index) => (
        <div key={index} className="search-result-skeleton">
          <SkeletonBase className="search-result-avatar-skeleton" />
          <div className="search-result-content-skeleton">
            <SkeletonBase className="search-result-title-skeleton" animate={false} />
            <SkeletonBase className="search-result-snippet-skeleton" animate={false} />
            <SkeletonBase className="search-result-meta-skeleton" animate={false} />
    </div>
        </div>
      ))}
    </div>
  );
};

// Media upload skeleton
export const MediaUploadSkeleton: React.FC<{ 
  type?: 'image' | 'video' | 'file';
  className?: string;
}> = ({ 
  type = 'image',
  className = '' 
}) => {
  return (
    <div className={`media-upload-skeleton ${type} ${className}`}>
      <SkeletonBase className="media-preview-skeleton">
        <div className="media-icon-skeleton">
          {type === 'image' && '🖼️'}
          {type === 'video' && '🎥'}
          {type === 'file' && '📄'}
        </div>
    </SkeletonBase>
      <div className="media-info-skeleton">
        <SkeletonBase className="media-name-skeleton" animate={false} />
        <SkeletonBase className="media-size-skeleton" animate={false} />
        <div className="media-progress-skeleton">
          <motion.div
            className="media-progress-bar-skeleton"
            animate={{
              width: ['0%', '100%']
            }}, transition={{
              duration: 2,
              repeat: Infinity,
              ease: 'easeInOut'
            }}
          />
    </div>
      </div>
    </div>
  );
};

// Reaction picker skeleton
export const ReactionPickerSkeleton: React.FC<{ className?: string }> = ({ 
  className = '' 
}) => {
  return (
    <div className={`reaction-picker-skeleton ${className}`}>
      <div className="reaction-picker-container-skeleton">
        {Array.from({ length: 8 }, (_, index) => (
          <SkeletonBase 
            key={index} className="reaction-item-skeleton"
            animate={false}
          />
        ))}
      </div>
    </div>
  );
};

// Thread skeleton
export const ThreadSkeleton: React.FC<{ 
  messageCount?: number;
  className?: string;
}> = ({ 
  messageCount = 3,
  className = '' 
}) => {
  return (
    <div className={`thread-skeleton ${className}`}>
      {/* Thread header */}
      <div className="thread-header-skeleton">
        <SkeletonBase className="thread-title-skeleton" animate={false} />
        <SkeletonBase className="thread-count-skeleton" animate={false} />
    </div>
      {/* Thread messages */}
      <div className="thread-messages-skeleton">
        {Array.from({ length: messageCount }, (_, index) => (
          <MessageSkeleton
            key={index} isOwn={Math.random() > 0.5}, showAvatar={true}
          />
        ))}
      </div>
    </div>
  );
};

// Loading spinner with message
export const LoadingSpinner: React.FC<{ 
  message?: string;
  size?: 'small' | 'medium' | 'large';
  className?: string;
}> = ({ 
  message = 'Loading...',
  size = 'medium',
  className = '' 
}) => {
  return (
    <div className={`loading-spinner ${size} ${className}`}>
      <motion.div
        className="spinner"
        animate={{ rotate: 360 }}, transition={{
          duration: 1,
          repeat: Infinity,
          ease: 'linear'
        }}
      />
      {message && (
        <motion.p
          className="loading-message"
          animate={{ opacity: [0.5, 1, 0.5] }}, transition={{
            duration: 1.5,
            repeat: Infinity,
            ease: 'easeInOut'
          }}
        >
          {message}
        </motion.p>
      )}
    </div>
  );
};

// Pulse loading indicator
export const PulseLoader: React.FC<{ 
  count?: number;
  className?: string;
}> = ({ 
  count = 3,
  className = '' 
}) => {
  return (
    <div className={`pulse-loader ${className}`}>
      {Array.from({ length: count }, (_, index) => (
        <motion.div
          key={index} className="pulse-dot"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.5, 1, 0.5]
          }}, transition={{
            duration: 1.2,
            repeat: Infinity,
            delay: index * 0.2,
            ease: 'easeInOut'
          }}
        />
      ))}
    </div>
  );
};

// Progressive loading component
export const ProgressiveLoader: React.FC<{
  progress: number;
  message?: string;
  className?: string;
}> = ({
  progress,
  message,
  className = ''
}) => {
  return (
    <div className={`progressive-loader ${className}`}>
      <div className="progress-container">
        <motion.div
          className="progress-bar"
          initial={{ width: 0 }}, animate={{ width: `${Math.min(100, Math.max(0, progress))}%` }}, transition={{
            type: 'spring',
            stiffness: 400,
            damping: 25
          }}
        />
    </div>
      {message && (
        <p className="progress-message">{message}</p>
      )}
      <span className="progress-percentage">{Math.round(progress)}%</span>
    </div>
  );
};

// Lazy loading placeholder
export const LazyLoadPlaceholder: React.FC<{
  height?: number;
  className?: string;
  children?: React.ReactNode;
}> = ({
  height = 200,
  className = '',
  children
}) => {
  return (
    <div 
      className={`lazy-load-placeholder ${className}`}, style={{ height: `${height}px` }}
    >
      <SkeletonBase className="lazy-content">
        {children || (
          <div className="lazy-default-content">
            <LoadingSpinner size="small" message="Loading content..." />
    </div>
        )}
      </SkeletonBase>
    </div>
  );
};

export default {
  SkeletonBase,
  MessageSkeleton,
  ConversationSkeleton,
  MessageListSkeleton,
  ConversationListSkeleton,
  TypingIndicatorSkeleton,
  SearchResultsSkeleton,
  MediaUploadSkeleton,
  ReactionPickerSkeleton,
  ThreadSkeleton,
  LoadingSpinner,
  PulseLoader,
  ProgressiveLoader,
  LazyLoadPlaceholder
};