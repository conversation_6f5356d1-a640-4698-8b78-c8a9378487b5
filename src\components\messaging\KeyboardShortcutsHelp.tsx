import React, { useState, useEffect } from 'react';
import { X, Keyboard, Search } from 'lucide-react';
import { KeyboardShortcut } from '../../services/messaging/KeyboardNavigationService';

interface KeyboardShortcutsHelpProps {
  isOpen: boolean, onClose: () => void;
  shortcuts?: KeyboardShortcut[];
  className?: string;
}

export const KeyboardShortcutsHelp: React.FC<KeyboardShortcutsHelpProps> = ({
  isOpen,
  onClose,
  shortcuts = [],
  className = ''
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  // Close on Escape key
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown'; handleKeyDown);
  }, [isOpen, onClose]);

  // Focus management
  useEffect(() => {
    if (isOpen) {
      const firstFocusable = document.querySelector('#shortcuts-modal [tabindex="0"]') as HTMLElement;
      firstFocusable?.focus();
    }
  }, [isOpen]);

  if (!isOpen) return null;

  const categories = ['all', ...Array.from(new Set(shortcuts.map(s => s.category)))];
  
  const filteredShortcuts = shortcuts.filter(shortcut => {
    const matchesSearch = searchTerm === '' || 
      shortcut.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      shortcut.key.toLowerCase().includes(searchTerm.toLowerCase()) ||
      shortcut.action.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesCategory = selectedCategory === 'all' || shortcut.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  const formatShortcut = (shortcut: KeyboardShortcut) => {
    const keys = [];
    if (shortcut.ctrlKey) keys.push('Ctrl');
    if (shortcut.shiftKey) keys.push('Shift');
    if (shortcut.altKey) keys.push('Alt');
    if (shortcut.metaKey) keys.push('Cmd');
    keys.push(shortcut.key);
    
    return keys.join(' + ');
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'navigation': return '🧭';
      case 'messaging': return '💬';
      case 'reactions': return '😊';
      case 'threads': return '🧵';
      case 'general': return '⚙️';
      default: return '📋';
    }
  };

  const groupedShortcuts = filteredShortcuts.reduce((acc, shortcut) => {
    if (!acc[shortcut.category]) {
      acc[shortcut.category] = [];
    }
    acc[shortcut.category].push(shortcut);
    return acc;
  }, {} as Record<string, KeyboardShortcut[]>);

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div
        id="shortcuts-modal"
        className={`bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden ${className}`}, role="dialog"
        aria-labelledby="shortcuts-title"
        aria-describedby="shortcuts-description"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center gap-3">
            <Keyboard className="w-6 h-6 text-blue-600" />
            <div>
              <h2 id="shortcuts-title" className="text-xl font-semibold text-gray-900">
                Keyboard Shortcuts
              </h2>
              <p id="shortcuts-description" className="text-sm text-gray-600">
                Navigate and interact with messages using keyboard shortcuts
              </p>
    </div>
          </div>
          
          <button
            onClick={onClose} className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors"
            aria-label="Close shortcuts help"
            tabIndex={0}
          >
            <X className="w-5 h-5" />
    </button>
        </div>

        {/* Search and Filter */}
        <div className="p-6 border-b border-gray-200 space-y-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search shortcuts..."
              value={searchTerm} onChange={(e) => setSearchTerm(e.target.value)}, className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              aria-label="Search keyboard shortcuts"
            />
    </div>
          {/* Category Filter */}
          <div className="flex flex-wrap gap-2">
            {categories.map(category => (
              <button
                key={category} onClick={() => setSelectedCategory(category)}, className={`px-3 py-1 text-sm rounded-full transition-colors ${
                  selectedCategory === category
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
                aria-label={`Filter by ${category}, shortcuts`}
              >
                {getCategoryIcon(category)} {category.charAt(0).toUpperCase() + category.slice(1)}
              </button>
            ))}
          </div>
    </div>
        {/* Shortcuts List */}
        <div className="p-6 overflow-y-auto max-h-96">
          {Object.keys(groupedShortcuts).length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Keyboard className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>No shortcuts found matching your search.</p>
    </div>
          ) : (
            <div className="space-y-6">
              {Object.entries(groupedShortcuts).map(([category, categoryShortcuts]) => (
                <div key={category}>
                  <h3 className="text-lg font-medium text-gray-900 mb-3 flex items-center gap-2">
                    <span>{getCategoryIcon(category)}</span>
                    {category.charAt(0).toUpperCase() + category.slice(1)}
                  </h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {categoryShortcuts.map((shortcut, index) => (
                      <div
                        key={`${shortcut.action}-${index}`}, className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                      >
                        <div className="flex-1">
                          <p className="text-sm font-medium text-gray-900">
                            {shortcut.description}
                          </p>
                          <p className="text-xs text-gray-500 mt-1">
                            Action: {shortcut.action}
                          </p>
    </div>
                        <div className="ml-4">
                          <kbd className="inline-flex items-center px-2 py-1 text-xs font-mono bg-white border border-gray-300 rounded shadow-sm">
                            {formatShortcut(shortcut)}
                          </kbd>
    </div>
                      </div>
                    ))}
                  </div>
    </div>
              ))}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-gray-200 bg-gray-50">
          <div className="flex items-center justify-between text-sm text-gray-600">
            <div className="flex items-center gap-4">
              <span>Total shortcuts: {filteredShortcuts.length}</span>
              <span>•</span>
              <span>Press <kbd className="px-1 py-0.5 bg-white border rounded text-xs">Esc</kbd> to close</span>
    </div>
            <div className="flex items-center gap-2">
              <span>Need help?</span>
              <button
                onClick={() => {
                  // In a real app, this might open documentation
                  alert('For more help, visit our documentation or contact support.');
                }}, className="text-blue-600 hover:text-blue-800 underline"
              >
                View Documentation
              </button>
    </div>
          </div>
    </div>
      </div>
    </div>
  );
};

/**
 * Hook to manage keyboard shortcuts help modal
 */
export function useKeyboardShortcutsHelp() {
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    const handleShowShortcuts = () => {
      setIsOpen(true);
    };

    document.addEventListener('show-keyboard-shortcuts', handleShowShortcuts);

    return () => {
      document.removeEventListener('show-keyboard-shortcuts', handleShowShortcuts);
    };
  }, []);

  const open = () => setIsOpen(true);
  const close = () => setIsOpen(false);
  const toggle = () => setIsOpen(prev => !prev);

  return {
    isOpen,
    open,
    close,
    toggle
  };
}

export default KeyboardShortcutsHelp;
