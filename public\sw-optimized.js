/**
 * Optimized Service Worker with Advanced Caching Strategies
 * Provides intelligent caching, background sync, and performance optimizations
 */

const CACHE_NAME = 'facebook-clone-v1';
const DYNAMIC_CACHE = 'facebook-clone-dynamic-v1';
const IMAGE_CACHE = 'facebook-clone-images-v1';
const API_CACHE = 'facebook-clone-api-v1';

// Cache configuration
const CACHE_CONFIG = {
  maxAge: {
    static: 7 * 24 * 60 * 60 * 1000, // 7 days
    dynamic: 24 * 60 * 60 * 1000,     // 1 day
    images: 30 * 24 * 60 * 60 * 1000, // 30 days
    api: 5 * 60 * 1000                // 5 minutes
  },
  maxEntries: {
    dynamic: 100,
    images: 200,
    api: 50
  }
};

// Static assets to precache
const STATIC_ASSETS = [
  '/',
  '/manifest.json',
  '/offline.html',
  // Core JS and CSS will be added dynamically
];

// URL patterns for different caching strategies
const URL_PATTERNS = {
  static: /\.(js|css|woff2?|ttf|eot)$/,
  images: /\.(png|jpg|jpeg|gif|webp|svg|ico)$/,
  api: /\/api\//,
  html: /\.html$/
};

// Performance tracking
let performanceMetrics = {
  cacheHits: 0,
  cacheMisses: 0,
  networkRequests: 0,
  backgroundSyncs: 0
};

/**
 * Install event - precache static assets
 */
self.addEventListener('install', (event) => {
  console.log('SW: Installing optimized service worker');
  
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        console.log('SW: Precaching static assets');
        return cache.addAll(STATIC_ASSETS);
      })
      .then(() => {
        // Skip waiting to activate immediately
        return self.skipWaiting();
      })
      .catch((error) => {
        console.error('SW: Install failed:', error);
      })
  );
});

/**
 * Activate event - cleanup old caches
 */
self.addEventListener('activate', (event) => {
  console.log('SW: Activating optimized service worker');
  
  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        const deletePromises = cacheNames
          .filter((cacheName) => {
            return cacheName !== CACHE_NAME && 
                   cacheName !== DYNAMIC_CACHE && 
                   cacheName !== IMAGE_CACHE && 
                   cacheName !== API_CACHE;
          })
          .map((cacheName) => {
            console.log('SW: Deleting old cache:', cacheName);
            return caches.delete(cacheName);
          });
        
        return Promise.all(deletePromises);
      })
      .then(() => {
        // Take control immediately
        return self.clients.claim();
      })
  );
});

/**
 * Fetch event - intelligent caching strategies
 */
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);
  
  // Skip non-GET requests and chrome-extension requests
  if (request.method !== 'GET' || url.protocol === 'chrome-extension:') {
    return;
  }
  
  performanceMetrics.networkRequests++;
  
  // Route to appropriate caching strategy
  if (URL_PATTERNS.static.test(url.pathname)) {
    event.respondWith(handleStaticAssets(request));
  } else if (URL_PATTERNS.images.test(url.pathname)) {
    event.respondWith(handleImages(request));
  } else if (URL_PATTERNS.api.test(url.pathname)) {
    event.respondWith(handleAPI(request));
  } else if (URL_PATTERNS.html.test(url.pathname) || url.pathname === '/') {
    event.respondWith(handleHTML(request));
  } else {
    event.respondWith(handleDynamic(request));
  }
});

/**
 * Static assets - Cache First strategy
 */
async function handleStaticAssets(request) {
  try {
    const cachedResponse = await caches.match(request);
    
    if (cachedResponse) {
      performanceMetrics.cacheHits++;
      
      // Check if cache is expired
      const cacheDate = new Date(cachedResponse.headers.get('date') || Date.now());
      const isExpired = Date.now() - cacheDate.getTime() > CACHE_CONFIG.maxAge.static;
      
      if (!isExpired) {
        return cachedResponse;
      }
    }
    
    // Fetch from network and update cache
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      const cache = await caches.open(CACHE_NAME);
      await cache.put(request, networkResponse.clone());
    }
    
    performanceMetrics.cacheMisses++;
    return networkResponse;
    
  } catch (error) {
    console.error('SW: Static asset fetch failed:', error);
    
    // Try to return cached version even if expired
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    throw error;
  }
}

/**
 * Images - Cache First with cleanup
 */
async function handleImages(request) {
  try {
    const cachedResponse = await caches.match(request);
    
    if (cachedResponse) {
      performanceMetrics.cacheHits++;
      return cachedResponse;
    }
    
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      const cache = await caches.open(IMAGE_CACHE);
      
      // Cleanup old entries if needed
      await cleanupCache(IMAGE_CACHE, CACHE_CONFIG.maxEntries.images);
      
      await cache.put(request, networkResponse.clone());
    }
    
    performanceMetrics.cacheMisses++;
    return networkResponse;
    
  } catch (error) {
    console.error('SW: Image fetch failed:', error);
    
    // Return placeholder image or cached version
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // Return placeholder image
    return new Response(
      '<svg width="200" height="200" xmlns="http://www.w3.org/2000/svg"><rect width="100%" height="100%" fill="#f0f0f0"/><text x="50%" y="50%" text-anchor="middle" dy=".3em" fill="#999">Image unavailable</text></svg>',
      {
        headers: {
          'Content-Type': 'image/svg+xml',
          'Cache-Control': 'no-cache'
        }
      }
    );
  }
}

/**
 * API requests - Network First with background update
 */
async function handleAPI(request) {
  try {
    // Try network first
    const networkResponse = await Promise.race([
      fetch(request),
      new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Network timeout')), 3000)
      )
    ]);
    
    if (networkResponse.ok) {
      const cache = await caches.open(API_CACHE);
      
      // Only cache GET requests
      if (request.method === 'GET') {
        await cleanupCache(API_CACHE, CACHE_CONFIG.maxEntries.api);
        await cache.put(request, networkResponse.clone());
      }
    }
    
    return networkResponse;
    
  } catch (error) {
    console.log('SW: API network failed, trying cache:', error.message);
    
    const cachedResponse = await caches.match(request);
    
    if (cachedResponse) {
      performanceMetrics.cacheHits++;
      
      // Schedule background update if cache is old
      const cacheDate = new Date(cachedResponse.headers.get('date') || Date.now());
      const isOld = Date.now() - cacheDate.getTime() > CACHE_CONFIG.maxAge.api;
      
      if (isOld) {
        scheduleBackgroundUpdate(request);
      }
      
      return cachedResponse;
    }
    
    performanceMetrics.cacheMisses++;
    
    // Return offline response for API
    return new Response(
      JSON.stringify({
        error: 'Offline',
        message: 'This feature is not available offline',
        cached: false
      }),
      {
        status: 503,
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache'
        }
      }
    );
  }
}

/**
 * HTML pages - Network First with fallback
 */
async function handleHTML(request) {
  try {
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      const cache = await caches.open(DYNAMIC_CACHE);
      await cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
    
  } catch (error) {
    console.log('SW: HTML network failed, trying cache');
    
    const cachedResponse = await caches.match(request);
    
    if (cachedResponse) {
      performanceMetrics.cacheHits++;
      return cachedResponse;
    }
    
    performanceMetrics.cacheMisses++;
    
    // Return offline page
    const offlineResponse = await caches.match('/offline.html');
    if (offlineResponse) {
      return offlineResponse;
    }
    
    // Fallback offline HTML
    return new Response(
      `<!DOCTYPE html>
      <html>
        <head>
          <title>Offline - Facebook Clone</title>
          <meta name="viewport" content="width=device-width, initial-scale=1">
          <style>
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; text-align: center; padding: 2rem; }
            .offline { color: #666; max-width: 400px; margin: 0 auto; }
            .icon { font-size: 4rem; margin-bottom: 1rem; }
          </style>
        </head>
        <body>
          <div class="offline">
            <div class="icon">📱</div>
            <h1>You're offline</h1>
            <p>Check your internet connection and try again.</p>
            <button onclick="location.reload()">Retry</button>
          </div>
        </body>
      </html>`,
      {
        headers: {
          'Content-Type': 'text/html',
          'Cache-Control': 'no-cache'
        }
      }
    );
  }
}

/**
 * Dynamic content - Stale While Revalidate
 */
async function handleDynamic(request) {
  try {
    const cache = await caches.open(DYNAMIC_CACHE);
    const cachedResponse = await cache.match(request);
    
    // Return cached version immediately if available
    if (cachedResponse) {
      performanceMetrics.cacheHits++;
      
      // Update in background
      fetch(request)
        .then((networkResponse) => {
          if (networkResponse.ok) {
            cache.put(request, networkResponse.clone());
          }
        })
        .catch(() => {
          // Ignore background update failures
        });
      
      return cachedResponse;
    }
    
    // No cache, fetch from network
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      await cleanupCache(DYNAMIC_CACHE, CACHE_CONFIG.maxEntries.dynamic);
      await cache.put(request, networkResponse.clone());
    }
    
    performanceMetrics.cacheMisses++;
    return networkResponse;
    
  } catch (error) {
    console.error('SW: Dynamic fetch failed:', error);
    throw error;
  }
}

/**
 * Background sync for failed requests
 */
self.addEventListener('sync', (event) => {
  console.log('SW: Background sync triggered:', event.tag);
  
  if (event.tag === 'background-update') {
    event.waitUntil(processBackgroundUpdates());
  }
  
  performanceMetrics.backgroundSyncs++;
});

/**
 * Message handling for cache management
 */
self.addEventListener('message', (event) => {
  const { type, payload } = event.data;
  
  switch (type) {
    case 'GET_METRICS':
      event.ports[0].postMessage(performanceMetrics);
      break;
      
    case 'CLEAR_CACHE':
      event.waitUntil(clearAllCaches().then(() => {
        event.ports[0].postMessage({ success: true });
      }));
      break;
      
    case 'PRELOAD_ROUTES':
      event.waitUntil(preloadRoutes(payload.routes).then(() => {
        event.ports[0].postMessage({ success: true });
      }));
      break;
      
    default:
      console.log('SW: Unknown message type:', type);
  }
});

/**
 * Utility functions
 */

// Clean up cache entries
async function cleanupCache(cacheName, maxEntries) {
  const cache = await caches.open(cacheName);
  const keys = await cache.keys();
  
  if (keys.length > maxEntries) {
    const sortedKeys = keys.sort((a, b) => {
      // Sort by URL to have consistent cleanup
      return a.url.localeCompare(b.url);
    });
    
    const keysToDelete = sortedKeys.slice(0, keys.length - maxEntries);
    
    await Promise.all(
      keysToDelete.map(key => cache.delete(key))
    );
    
    console.log(`SW: Cleaned up ${keysToDelete.length} entries from ${cacheName}`);
  }
}

// Schedule background update
function scheduleBackgroundUpdate(request) {
  // Store request for background processing
  self.registration.sync.register('background-update')
    .catch((error) => {
      console.log('SW: Background sync registration failed:', error);
    });
}

// Process background updates
async function processBackgroundUpdates() {
  // This would process stored requests that need updating
  console.log('SW: Processing background updates');
  
  // Implementation would depend on how requests are stored
  // For now, just log the action
}

// Clear all caches
async function clearAllCaches() {
  const cacheNames = await caches.keys();
  
  await Promise.all(
    cacheNames.map(cacheName => caches.delete(cacheName))
  );
  
  console.log('SW: Cleared all caches');
}

// Preload routes
async function preloadRoutes(routes) {
  const cache = await caches.open(DYNAMIC_CACHE);
  
  const preloadPromises = routes.map(async (route) => {
    try {
      const response = await fetch(route);
      if (response.ok) {
        await cache.put(route, response);
      }
    } catch (error) {
      console.log(`SW: Failed to preload ${route}:`, error);
    }
  });
  
  await Promise.all(preloadPromises);
  console.log(`SW: Preloaded ${routes.length} routes`);
}

// Error handling
self.addEventListener('error', (event) => {
  console.error('SW: Service Worker error:', event.error);
});

self.addEventListener('unhandledrejection', (event) => {
  console.error('SW: Unhandled promise rejection:', event.reason);
});

console.log('SW: Optimized service worker loaded successfully');