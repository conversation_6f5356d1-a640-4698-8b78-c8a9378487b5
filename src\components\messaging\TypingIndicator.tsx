/**
 * Typing Indicator Component
 * Shows when users are typing in conversations with automatic timeout and cleanup
 */

import React, { useMemo, useEffect, useRef, useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui';
import { cn } from '@/lib/utils';
import { SIZE_CONFIG, TIMING_CONSTANTS, SizeVariant } from './constants';

interface TypingIndicatorProps {
  typingUsers: string[];
  getUserName?: (userId: string) => string;
  getUserAvatar?: (userId: string) => string;
  maxVisible?: number;
  showAvatars?: boolean;
  className?: string;
  size?: SizeVariant;
  autoTimeout?: number; // Auto-hide timeout in milliseconds (default from constants)
  onTypingTimeout?: (userId: string) => void; // Callback when user times out
  enableAutoCleanup?: boolean; // Enable automatic cleanup of stale typing indicators
}

interface TypingUser {
  userId: string, timestamp: number;
  timeoutId?: NodeJS.Timeout;
}

// Using consolidated size configuration
const sizeConfig = SIZE_CONFIG;

// Memoized animated typing dots for performance
const TypingDots: React.FC<{ size: SizeVariant }> = React.memo(({ size }) => {
  const dotSize = sizeConfig[size].dot;
  
  return (
    <div className="flex items-center space-x-1">
      {[0, 1, 2].map((index) => (
        <motion.div
          key={index} className={cn("bg-gray-400 rounded-full", dotSize)}, animate={{
            scale: [1, 1.2, 1],
            opacity: [0.5, 1, 0.5]
          }}, transition={{
            duration: 1.4,
            repeat: Infinity,
            delay: index * 0.2,
            ease: "easeInOut"
          }}
        />
      ))}
    </div>
  );
});

TypingDots.displayName = 'TypingDots';

const TypingIndicatorComponent: React.FC<TypingIndicatorProps> = ({
  typingUsers,
  getUserName = (id) => `User ${id.slice(-4)}`,
  getUserAvatar,
  maxVisible = 3,
  showAvatars = true,
  className,
  size = 'md',
  autoTimeout = TIMING_CONSTANTS.TYPING_TIMEOUT, // Use constant from consolidated config
  onTypingTimeout,
  enableAutoCleanup = true
}) => {
  const sizeClasses = sizeConfig[size];
  const [activeTypingUsers, setActiveTypingUsers] = useState<TypingUser[]>([]);
  const typingTimeoutsRef = useRef<Map<string, NodeJS.Timeout>>(new Map());
  const lastUpdateRef = useRef<number>(Date.now());

  // Update active typing users with timeout management
  useEffect(() => {
    const now = Date.now();
    lastUpdateRef.current = now;

    // Clear existing timeouts
    typingTimeoutsRef.current.forEach(timeout => clearTimeout(timeout));
    typingTimeoutsRef.current.clear();

    // Create new typing user entries with timeouts
    const newActiveUsers: TypingUser[] = typingUsers.map(userId => {
      const timeoutId = setTimeout(() => {
        if (enableAutoCleanup) {
          setActiveTypingUsers(prev => prev.filter(user => user.userId !== userId));
          onTypingTimeout?.(userId);
        }
      }, autoTimeout);

      typingTimeoutsRef.current.set(userId, timeoutId);

      return {
        userId,
        timestamp: now,
        timeoutId
      };
    });

    setActiveTypingUsers(newActiveUsers);

    // Cleanup function
    return () => {
      typingTimeoutsRef.current.forEach(timeout => clearTimeout(timeout));
      typingTimeoutsRef.current.clear();
    };
  }, [typingUsers, autoTimeout, enableAutoCleanup, onTypingTimeout]);

  // Get current typing user IDs for display
  const currentTypingUsers = useMemo(() => {
    return activeTypingUsers.map(user => user.userId);
  }, [activeTypingUsers]);

  // Format typing message
  const typingMessage = useMemo(() => {
    if (currentTypingUsers.length === 0) return '';
    
    const visibleUsers = currentTypingUsers.slice(0, maxVisible);
    const hiddenCount = Math.max(0, currentTypingUsers.length - maxVisible);
    
    if (visibleUsers.length === 1) {
      return `${getUserName(visibleUsers[0])} is typing...`;
    } else if (visibleUsers.length === 2) {
      return `${getUserName(visibleUsers[0])} and ${getUserName(visibleUsers[1])} are typing...`;
    } else if (visibleUsers.length === 3 && hiddenCount === 0) {
      return `${getUserName(visibleUsers[0])}, ${getUserName(visibleUsers[1])}, and ${getUserName(visibleUsers[2])} are typing...`;
    } else {
      const names = visibleUsers.slice(0, 2).map(getUserName).join(', ');
      const totalOthers = visibleUsers.length - 2 + hiddenCount;
      return `${names} and ${totalOthers} others are typing...`;
    }
  }, [currentTypingUsers, maxVisible, getUserName]);

  // Calculate time since last activity for visual feedback
  const timeSinceLastUpdate = useMemo(() => {
    return Date.now() - lastUpdateRef.current;
  }, [activeTypingUsers]);

  if (currentTypingUsers.length === 0) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: 10, height: 0 }}, animate={{ opacity: 1, y: 0, height: 'auto' }}, exit={{ opacity: 0, y: -10, height: 0 }}, transition={{ duration: 0.2, ease: "easeOut" }}, className={cn(
          "flex items-center py-2 px-4",
          sizeClasses.spacing,
          className
        )}
      >
        {/* User Avatars */}
        {showAvatars && (
          <div className="flex -space-x-2">
            {currentTypingUsers.slice(0, maxVisible).map((userId) => {
              const typingUser = activeTypingUsers.find(user => user.userId === userId);
              const timeSinceTyping = typingUser ? Date.now() - typingUser.timestamp : 0;
              const isStale = timeSinceTyping > autoTimeout * 0.8; // Show warning at 80% of timeout
              
              return (
                <motion.div
                  key={userId} initial={{ scale: 0, opacity: 0 }}, animate={{ scale: 1, opacity: 1 }}, exit={{ scale: 0, opacity: 0 }}, transition={{ duration: 0.2 }}, className="relative"
                >
                  <Avatar className={cn(
                    sizeClasses.avatar, 
                    "border-2 border-white dark:border-gray-800",
                    isStale && "opacity-60" // Fade out stale indicators
                  )}>
                    {getUserAvatar && (
                      <AvatarImage src={getUserAvatar(userId)} alt={getUserName(userId)} />
                    )}
                    <AvatarFallback className="text-xs font-medium">
                      {getUserName(userId).charAt(0).toUpperCase()}
                    </AvatarFallback>
    </Avatar>
                  {/* Typing indicator dot with timeout visualization */}
                  <motion.div
                    className={cn(
                      "absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-white dark:border-gray-800",
                      isStale ? "bg-yellow-500" : "bg-green-500"
                    )} animate={{ 
                      scale: [1, 1.2, 1],
                      opacity: isStale ? [1, 0.5, 1] : 1
                    }}, transition={{ 
                      duration: isStale ? 0.5 : 1, 
                      repeat: Infinity 
                    }}
                  />
                </motion.div>
              );
            })}
            
            {/* Show count if more users are typing */}
            {currentTypingUsers.length > maxVisible && (
              <motion.div
                initial={{ scale: 0, opacity: 0 }}, animate={{ scale: 1, opacity: 1 }}, className={cn(
                  "flex items-center justify-center bg-gray-200 dark:bg-gray-700 rounded-full border-2 border-white dark:border-gray-800",
                  sizeClasses.avatar
                )}
              >
                <span className={cn("font-medium text-gray-600 dark:text-gray-300", sizeClasses.text)}>
                  +{currentTypingUsers.length - maxVisible}
                </span>
              </motion.div>
            )}
          </div>
        )}

        {/* Typing Message */}
        <div className="flex items-center space-x-2">
          <motion.span
            className={cn(
              "text-gray-500 dark:text-gray-400 italic",
              sizeClasses.text
            )} initial={{ opacity: 0 }}, animate={{ opacity: 1 }}, transition={{ delay: 0.1 }}
          >
            {typingMessage}
          </motion.span>
          
          <TypingDots size={size} />
    </div>
      </motion.div>
    </AnimatePresence>
  );
};

// Compact typing indicator for inline use
interface CompactTypingIndicatorProps {
  isTyping: boolean;
  userName?: string;
  className?: string;
}

const CompactTypingIndicatorComponent: React.FC<CompactTypingIndicatorProps> = ({
  isTyping,
  userName,
  className
}) => {
  if (!isTyping) return null;

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}, animate={{ opacity: 1, scale: 1 }}, exit={{ opacity: 0, scale: 0.8 }}, className={cn("flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400", className)}
    >
      <span className="italic">
        {userName ? `${userName} is typing` : 'Typing'}...
      </span>
      <TypingDots size="sm" />
    </motion.div>
  );
};

// Typing indicator for message input
interface InputTypingIndicatorProps {
  conversationId: string, typingUsers: string[];
  getUserName?: (userId: string) => string;
  className?: string;
}

const InputTypingIndicatorComponent: React.FC<InputTypingIndicatorProps> = ({
  conversationId,
  typingUsers,
  getUserName = (id) => `User ${id.slice(-4)}`,
  className
}) => {
  const filteredUsers = typingUsers.filter(userId => userId !== 'current-user'); // Filter out current user
  
  if (filteredUsers.length === 0) return null;

  return (
    <motion.div
      initial={{ opacity: 0, height: 0 }}, animate={{ opacity: 1, height: 'auto' }}, exit={{ opacity: 0, height: 0 }}, className={cn("px-4 py-2 border-t border-gray-100 dark:border-gray-700", className)}
    >
      <div className="flex items-center space-x-2">
        <TypingDots size="sm" />
        <span className="text-xs text-gray-500 dark:text-gray-400 italic">
          {filteredUsers.length === 1
            ? `${getUserName(filteredUsers[0])} is typing...`
            : `${filteredUsers.length} people are typing...`
          }
        </span>
    </div>
    </motion.div>
  );
};

// Memoized exports for performance
export const TypingIndicator = React.memo(TypingIndicatorComponent, (prevProps, nextProps) => {
  return (
    JSON.stringify(prevProps.typingUsers) === JSON.stringify(nextProps.typingUsers) &&
    prevProps.maxVisible === nextProps.maxVisible &&
    prevProps.showAvatars === nextProps.showAvatars &&
    prevProps.size === nextProps.size &&
    prevProps.autoTimeout === nextProps.autoTimeout &&
    prevProps.enableAutoCleanup === nextProps.enableAutoCleanup
  );
});

export const CompactTypingIndicator = React.memo(CompactTypingIndicatorComponent);
export const InputTypingIndicator = React.memo(InputTypingIndicatorComponent);

TypingIndicator.displayName = 'TypingIndicator';
CompactTypingIndicator.displayName = 'CompactTypingIndicator';
InputTypingIndicator.displayName = 'InputTypingIndicator';

export default TypingIndicator;