import React, { memo } from 'react';
import { motion } from 'framer-motion';
import { MapPin, Heart, Truck } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui';
import { MarketplaceListing } from '@/services/SocialFeaturesService';
import { formatPrice, isRecentListing } from '@/utils/marketplace';
import { formatDistanceToNow } from 'date-fns';
import { OptimizedImage } from '@/components/OptimizedImage';

interface MarketplaceListingCardProps {
  listing: MarketplaceListing, isSaved: boolean, onListingClick: (listing: MarketplaceListing) => void; onToggleSaved: (listingId: string) => void;
}

const MarketplaceListingCard: React.FC<MarketplaceListingCardProps> = memo(({
  listing,
  isSaved,
  onListingClick,
  onToggleSaved
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}, animate={{ opacity: 1, scale: 1 }}, exit={{ opacity: 0, scale: 0.95 }}, transition={{ duration: 0.2 }}, className="w-full"
    >
      <Card
        className="group hover:shadow-xl hover:shadow-blue-100 dark:hover:shadow-blue-900/20 transition-all duration-200 cursor-pointer overflow-hidden h-full border-2 hover:border-blue-300 dark:hover:border-blue-700 hover:scale-[1.02] active:scale-[0.98]"
        onClick={() => onListingClick(listing)} role="button"
        tabIndex={0} onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            onListingClick(listing);
          }
        }}, style={{ userSelect: 'none' }}
      >
        {/* Image Section */}
        <div className="aspect-square relative overflow-hidden bg-gray-100">
          {listing.photos.length > 0 ? (
            <OptimizedImage
              src={listing.photos[0]} alt={listing.title}, className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              <div className="text-gray-400">No Image</div>
    </div>
          )}
          
          {/* Overlay buttons */}
          <div className="absolute top-2 right-2 flex space-x-2">
            <Button
              variant="secondary"
              size="sm"
              className="h-8 w-8 p-0 bg-white/80 hover:bg-white"
              onClick={(e) => {
                e.stopPropagation();
                onToggleSaved(listing.id);
              }}
            >
              <Heart className={`w-4 h-4 ${isSaved ? 'text-red-500 fill-red-500' : 'text-gray-600'}`} />
    </Button>
            {isRecentListing(listing.createdAt) && (
              <Badge variant="secondary" className="bg-green-100 text-green-800 text-xs">
                New
              </Badge>
            )}
          </div>
    </div>
        {/* Content Section */}
        <CardContent className="p-3 flex-1">
          <div className="space-y-2">
            {/* Title and Price */}
            <div className="flex items-start justify-between">
              <h3 className="font-semibold text-sm line-clamp-2 flex-1 mr-2">
                {listing.title}
              </h3>
              <span className="text-lg font-bold text-green-600 whitespace-nowrap">
                {formatPrice(listing.price)}
              </span>
    </div>
            {/* Location */}
            <div className="flex items-center text-gray-500 text-xs">
              <MapPin className="w-3 h-3 mr-1" />
              <span className="truncate">{listing.location.name}</span>
    </div>
            {/* Seller */}
            <div className="flex items-center space-x-2">
              <Avatar className="w-6 h-6">
                <AvatarImage src={listing.seller.avatar} />
                <AvatarFallback className="text-xs">
                  {listing.seller.name.charAt(0)}
                </AvatarFallback>
    </Avatar>
              <span className="text-xs text-gray-600 truncate">
                {listing.seller.name}
              </span>
    </div>
            {/* Condition and Time */}
            <div className="flex items-center justify-between text-xs text-gray-500">
              <Badge variant="outline" className="text-xs">
                {listing.condition}
              </Badge>
              <span>{formatDistanceToNow(listing.createdAt, { addSuffix: true })}</span>
    </div>
            {/* Delivery Options */}
            <div className="flex flex-wrap gap-1">
              {listing.deliveryOptions.map((option) => (
                <Badge key={option} variant="outline" className="text-xs flex items-center">
                  {option === 'pickup' ? <MapPin className="w-3 h-3 mr-1" /> : <Truck className="w-3 h-3 mr-1" />}
                  {option}
                </Badge>
              ))}
            </div>
    </div>
        </CardContent>
    </Card>
    </motion.div>
  );
});

MarketplaceListingCard.displayName = 'MarketplaceListingCard';

export default MarketplaceListingCard;
