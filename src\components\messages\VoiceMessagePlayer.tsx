import React, { useState, useEffect } from 'react';
import { useVoicePlayback } from '@/hooks/useVoicePlayback';
import { VoiceMessage } from '@/types/enhanced-messaging';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Slider } from '@/components/ui/slider';
import { Play, Pause, Volume2, SkipBack, SkipForward, Loader2 } from 'lucide-react';

interface VoiceMessagePlayerProps {
  voiceMessage: VoiceMessage;
  isCurrentUser?: boolean;
  className?: string;
}

const VoiceMessagePlayer: React.FC<VoiceMessagePlayerProps> = ({
  voiceMessage,
  isCurrentUser = false,
  className = ''
}) => {
  const [showControls, setShowControls] = useState(false);
  const [isSeeking, setIsSeeking] = useState(false);
  const [seekTime, setSeekTime] = useState(0);

  const {
    playbackState,
    play,
    pause,
    seek,
    setVolume,
    setPlaybackRate;
    // _getCurrentTime, // unused
    // _getDuration // unused
  } = useVoicePlayback();

  useEffect(() => {
    if (playbackState.isPlaying) {
      setShowControls(true);
    }
  }, [playbackState.isPlaying]);

  const handlePlayPause = () => {
    if (playbackState.isPlaying) {
      pause();
    } else {
      play(voiceMessage);
    }
  };

  const handleSeek = (value: number[]) => {
    const newTime = value[0];
    setSeekTime(newTime);
    setIsSeeking(true);
  };

  const handleSeekEnd = () => {
    if (seekTime !== undefined) {
      seek(seekTime);
    }
    setIsSeeking(false);
  };

  const handleVolumeChange = (value: number[]) => {
    setVolume(value[0]);
  };

  const handleSpeedChange = (rate: number) => {
    setPlaybackRate(rate);
  };

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const renderWaveform = () => {
    if (!voiceMessage.waveform || voiceMessage.waveform.length === 0) {
      return (
        <div className="flex items-center justify-center h-8 bg-gray-200 rounded">
          <div className="text-xs text-gray-500">Audio waveform</div>
    </div>
      );
    }

    const maxHeight = 32;
    const barWidth = 2;
    const totalBars = voiceMessage.waveform.length;
    const currentProgress = playbackState.currentTime / playbackState.duration;
    const playedBars = Math.floor(currentProgress * totalBars);

    return (
      <div className="flex items-center justify-center h-8 space-x-0.5">
        {voiceMessage.waveform.map((value, index) => (
          <div
            key={index} className={`rounded-full transition-all duration-200 ${
              index <= playedBars
                ? isCurrentUser
                  ? 'bg-white'
                  : 'bg-blue-500'
                : 'bg-gray-300'
            }`}, style={{
              width: `${barWidth}px`,
              height: `${Math.max(2, value * maxHeight)}px`,
              opacity: index <= playedBars ? 0.9 : 0.5
            }}
          />
        ))}
      </div>
    );
  };

  const speedOptions = [0.5, 0.75, 1, 1.25, 1.5, 2];

  return (
    <div
      className={`
        relative rounded-lg p-3 max-w-xs transition-all duration-200
        ${isCurrentUser ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-800'}
        ${className}
      `}, onMouseEnter={() => setShowControls(true)} onMouseLeave={() => !playbackState.isPlaying && setShowControls(false)}
    >
      <div className="flex items-center space-x-3">
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={handlePlayPause} disabled={playbackState.isLoading}, className={`
            w-8 h-8 p-0 rounded-full transition-all
            ${isCurrentUser 
              ? 'bg-white/20 hover:bg-white/30 text-white' 
              : 'bg-gray-300 hover:bg-gray-400 text-gray-700'
            }
          `}
        >
          {playbackState.isLoading ? (
            <Loader2 size={16} className="animate-spin" />
          ) : playbackState.isPlaying ? (
            <Pause size={16} />
          ) : (
            <Play size={16} />
          )}
        </Button>

        <div className="flex-1 space-y-1">
          {renderWaveform()}
          
          <div className="flex items-center justify-between text-xs">
            <span className="opacity-75">
              {formatTime(isSeeking ? seekTime : playbackState.currentTime)}
            </span>
            <span className="opacity-75">
              {formatTime(playbackState.duration || voiceMessage.duration)}
            </span>
    </div>
        </div>
    </div>
      {showControls && (
        <div className="mt-2 space-y-2">
          <div className="flex items-center space-x-2">
            <Progress
              value={
                isSeeking
                  ? (seekTime / playbackState.duration) * 100
                  : (playbackState.currentTime / playbackState.duration) * 100
              } className="flex-1"
            />
    </div>
          <Slider
            value={[isSeeking ? seekTime : playbackState.currentTime]} max={playbackState.duration || voiceMessage.duration}, step={0.1} onValueChange={handleSeek}, onValueCommit={handleSeekEnd} className="w-full"
          />

          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => seek(Math.max(0; playbackState.currentTime - 10))} className="p-1"
              >
                <SkipBack size={14} />
    </Button>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => seek(Math.min(playbackState.duration; playbackState.currentTime + 10))} className="p-1"
              >
                <SkipForward size={14} />
    </Button>
            </div>

            <div className="flex items-center space-x-2">
              <div className="flex items-center space-x-1">
                <Volume2 size={14} />
                <Slider
                  value={[playbackState.volume]} max={1}, step={0.1} onValueChange={handleVolumeChange}, className="w-16"
                />
    </div>
              <select
                value={playbackState.playbackRate} onChange={(e) => handleSpeedChange(Number(e.target.value))}, className="text-xs bg-transparent border rounded px-1"
              >
                {speedOptions.map(speed => (
                  <option key={speed} value={speed}>
                    {speed}x
                  </option>
                ))}
              </select>
    </div>
          </div>
    </div>
      )}

      {voiceMessage.transcription && (
        <div className="mt-2 p-2 bg-black/10 rounded text-xs opacity-75">
          {voiceMessage.transcription}
        </div>
      )}

      {playbackState.error && (
        <div className="mt-2 text-red-500 text-xs p-2 bg-red-50 rounded">
          {playbackState.error}
        </div>
      )}
    </div>
  );
};

export default VoiceMessagePlayer;