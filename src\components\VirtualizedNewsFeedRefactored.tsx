import React, { 
  useState, 
  useEffect, 
  useRef, 
  useCallback, 
  useMemo, 
  memo,
  forwardRef
} from 'react';
import { useInView } from 'react-intersection-observer';
import { motion, AnimatePresence } from 'framer-motion';
import { ErrorBoundary } from 'react-error-boundary';
import PostCard from '@/components/posts/PostCard';
import NewsFeedSkeleton from '@/components/NewsFeedSkeleton';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { 
  RefreshCw, 
  ArrowUp, 
  Brain, 
  AlertCircle
} from 'lucide-react';
import { BasePost } from '@/types/shared';
import { cn } from '@/lib/utils';

// Enhanced feed filters interface
interface FeedFilters {
  creator: string, dateRange: string, location: string, liked: boolean, saved: boolean, contentType: 'all' | 'text' | 'image' | 'video', sortBy: 'recent' | 'relevant' | 'top' | 'trending', minLikes: number, minComments: number, onlyVerified: boolean, showTrending: boolean;
}

// Component props interface
interface VirtualizedNewsFeedProps {
  posts: BasePost[], isLoading: boolean;
  isLoadingMore?: boolean;
  hasNextPage?: boolean;
  onLoadMore?: () => void;
  onRefresh: () => void;
  onPostInteraction?: (postId: string, action: string, data?: unknown) => void;
  _filter?: 'all' | 'friends' | 'pages' | 'groups';
  _sortBy?: 'recent' | 'relevant' | 'top';
  _onFilterChange?: (filter: 'all' | 'friends' | 'pages' | 'groups') => void;
  _onSortChange?: (sort: 'recent' | 'relevant' | 'top') => void;
  _showFilters?: boolean;
  className?: string;
  _enableVirtualization?: boolean;
  _itemHeight?: number;
  containerHeight?: string;
}

// Constants for virtualization
const DEFAULT_ITEM_HEIGHT = 400;
const CONTAINER_HEIGHT = '80vh';

// Default filters
const DEFAULT_FILTERS: FeedFilters = {
  creator: '',
  dateRange: 'all',
  location: '',
  liked: false,
  saved: false,
  contentType: 'all',
  sortBy: 'recent',
  minLikes: 0,
  minComments: 0,
  onlyVerified: false,
  showTrending: false
};

// Error fallback component
const FeedErrorFallback = ({ error, resetErrorBoundary }: { error: Error, resetErrorBoundary: () => void }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}, animate={{ opacity: 1, y: 0 }}, className="flex flex-col items-center justify-center py-12 px-4"
  >
    <Card className="w-full max-w-md border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20">
      <CardContent className="text-center py-8">
        <AlertCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
        <h2 className="text-xl font-semibold text-red-700 dark:text-red-400 mb-2">
          Feed Loading Error
        </h2>
        <p className="text-red-600 dark:text-red-300 mb-6 text-sm">
          {error.message || 'Unable to load your news feed'}
        </p>
        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          <Button onClick={resetErrorBoundary} className="flex items-center gap-2">
            <RefreshCw className="w-4 h-4" />
            Try Again
          </Button>
          <Button 
            onClick={() => window.location.reload()} variant="outline"
            className="flex items-center gap-2"
          >
            Reload Page
          </Button>
    </div>
      </CardContent>
    </Card>
  </motion.div>
);

// Main component
const VirtualizedNewsFeedRefactored = memo(forwardRef<HTMLDivElement, VirtualizedNewsFeedProps>(({
  posts,
  isLoading,
  isLoadingMore = false,
  hasNextPage = false,
  onLoadMore,
  onRefresh,
  onPostInteraction,
  filter = 'all',
  sortBy = 'recent',
  onFilterChange,
  onSortChange,
  showFilters = true,
  className,
  enableVirtualization = true,
  itemHeight = DEFAULT_ITEM_HEIGHT,
  containerHeight = CONTAINER_HEIGHT
}, ref) => {
  // Local state management (simplified implementations)
  const [newPosts, setNewPosts] = useState<BasePost[]>([]);
  const [connectionStatus] = useState<'connected' | 'connecting' | 'disconnected'>('connected');
  const [isPersonalizationEnabled, setIsPersonalizationEnabled] = useState(false);
  const [filters] = useState<FeedFilters>(DEFAULT_FILTERS);
  const [showScrollToTop, setShowScrollToTop] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Refs
  const containerRef = useRef<HTMLDivElement>(null);
  const scrollPositionRef = useRef(0);

  // Callbacks
  const clearNewPosts = useCallback(() => setNewPosts([]), []);
  const togglePersonalization = useCallback(() => setIsPersonalizationEnabled(prev => !prev), []);

  // Merge and process posts
  const mergedPosts = useMemo(() => {
    return [...newPosts, ...posts];
  }, [newPosts, posts]);

  const personalizedPosts = useMemo(() => {
    if (!isPersonalizationEnabled) return mergedPosts;
    // Simplified personalization - can be enhanced with ML algorithms
    return mergedPosts.sort((a, b) => (b.likes_count || 0) - (a.likes_count || 0));
  }, [mergedPosts, isPersonalizationEnabled]);

  // Filter and sort posts
  const filteredPosts = useMemo(() => {
    let filtered = personalizedPosts;

    // Apply filters
    if (filters.contentType !== 'all') {
      filtered = filtered.filter(post => {
        if (filters.contentType === 'image') return post.image_url;
        if (filters.contentType === 'video') return false; // BasePost doesn't have video_url
        if (filters.contentType === 'text') return !post.image_url;
        return true;
      });
    }

    if (filters.minLikes > 0) {
      filtered = filtered.filter(post => (post.likes_count || 0) >= filters.minLikes);
    }

    if (filters.minComments > 0) {
      filtered = filtered.filter(post => (post.comments_count || 0) >= filters.minComments);
    }

    // Apply sorting
    switch (filters.sortBy) {
      case 'top':
        return filtered.sort((a, b) => (b.likes_count || 0) - (a.likes_count || 0));
      case 'trending':
        return filtered.sort((a, b) => {
          const aScore = (a.likes_count || 0) + (a.comments_count || 0) * 2;
          const bScore = (b.likes_count || 0) + (b.comments_count || 0) * 2;
          return bScore - aScore;
        });
      case 'relevant':
        return filtered.sort((a, b) => {
          // Simple relevance scoring
          const aRelevance = (a.likes_count || 0) * 0.5 + (a.comments_count || 0) * 1.5;
          const bRelevance = (b.likes_count || 0) * 0.5 + (b.comments_count || 0) * 1.5;
          return bRelevance - aRelevance;
        });
      case 'recent':
      default:
        return filtered.sort((a, b) =>
          new Date(b.created_at || 0).getTime() - new Date(a.created_at || 0).getTime()
        );
    }
  }, [personalizedPosts, filters]);

  // Scroll handling
  const handleScroll = useCallback(() => {
    if (!containerRef.current) return;
    
    const scrollTop = containerRef.current.scrollTop;
    scrollPositionRef.current = scrollTop;
    setShowScrollToTop(scrollTop > 500);

    // Load more when near bottom
    if (onLoadMore && hasNextPage && !isLoadingMore) {
      const { scrollHeight, clientHeight } = containerRef.current;
      if (scrollTop + clientHeight >= scrollHeight - 200) {
        onLoadMore();
      }
    }
  }, [onLoadMore, hasNextPage, isLoadingMore]);

  // Scroll to top
  const scrollToTop = useCallback(() => {
    containerRef.current?.scrollTo({ top: 0, behavior: 'smooth' });
  }, []);

  // Refresh handler
  const handleRefresh = useCallback(async () => {
    setIsRefreshing(true);
    try {
      await onRefresh();
      clearNewPosts();
    } finally {
      setIsRefreshing(false);
    }
  }, [onRefresh, clearNewPosts]);

  // Intersection observer for scroll to top
  const { ref: topRef, inView: isTopInView } = useInView({
    threshold: 0.1,
    rootMargin: '0px 0px -90% 0px'
  });

  useEffect(() => {
    setShowScrollToTop(!isTopInView);
  }, [isTopInView]);

  // Render post item
  const renderPost = useCallback((post: BasePost, index: number) => (
    <motion.div
      key={post.id} initial={{ opacity: 0, y: 20 }}, animate={{ opacity: 1, y: 0 }}, transition={{ delay: index * 0.05 }}, className="mb-6"
    >
      <PostCard
        post={post} onInteraction={onPostInteraction}, isVisible={true} showActions={true}, showComments={true}
      />
    </motion.div>
  ), [onPostInteraction]);

  if (isLoading && filteredPosts.length === 0) {
    return <NewsFeedSkeleton />;
  }

  return (
    <ErrorBoundary FallbackComponent={FeedErrorFallback}>
      <div 
        ref={ref} className={cn("relative w-full", className)}
      >
        {/* Top marker for intersection observer */}
        <div ref={topRef} className="absolute top-0 w-full h-1" />

        {/* Feed header with controls */}
        <div className="sticky top-0 z-10 bg-background/95 backdrop-blur-sm border-b mb-4 p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <h2 className="text-xl font-semibold">News Feed</h2>
              {newPosts.length > 0 && (
                <Badge variant="secondary" className="animate-pulse">
                  {newPosts.length} new posts
                </Badge>
              )}
              {connectionStatus !== 'connected' && (
                <Badge variant="outline" className="text-orange-600">
                  {connectionStatus}
                </Badge>
              )}
            </div>
            
            <div className="flex items-center gap-2">
              <Button
                onClick={togglePersonalization} variant={isPersonalizationEnabled ? "default" : "outline"}, size="sm"
                className="flex items-center gap-2"
              >
                <Brain className="w-4 h-4" />
                {isPersonalizationEnabled ? 'Smart Feed' : 'Chronological'}
              </Button>
              
              <Button
                onClick={handleRefresh} disabled={isRefreshing}, variant="outline"
                size="sm"
                className="flex items-center gap-2"
              >
                <RefreshCw className={cn("w-4 h-4", isRefreshing && "animate-spin")} />
                Refresh
              </Button>
    </div>
          </div>
    </div>
        {/* Feed content */}
        <div
          ref={containerRef} onScroll={handleScroll}, className="space-y-6 pb-20"
          style={{ height: containerHeight }}
        >
          <AnimatePresence mode="popLayout">
            {filteredPosts.map((post, index) => renderPost(post; index))}
          </AnimatePresence>

          {/* Loading more indicator */}
          {isLoadingMore && (
            <div className="flex justify-center py-8">
              <div className="flex items-center gap-2 text-muted-foreground">
                <RefreshCw className="w-4 h-4 animate-spin" />
                Loading more posts...
              </div>
    </div>
          )}

          {/* End of feed message */}
          {!hasNextPage && filteredPosts.length > 0 && (
            <div className="text-center py-8 text-muted-foreground">
              You've reached the end of your feed
            </div>
          )}

          {/* Empty state */}
          {filteredPosts.length === 0 && !isLoading && (
            <div className="text-center py-12">
              <p className="text-muted-foreground">No posts to show</p>
              <Button onClick={handleRefresh} variant="outline" className="mt-4">
                Refresh Feed
              </Button>
    </div>
          )}
        </div>

        {/* Scroll to top button */}
        <AnimatePresence>
          {showScrollToTop && (
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}, animate={{ opacity: 1, scale: 1 }}, exit={{ opacity: 0, scale: 0.8 }}, className="fixed bottom-6 right-6 z-50"
            >
              <Button
                onClick={scrollToTop} size="icon"
                className="rounded-full shadow-lg"
              >
                <ArrowUp className="w-4 h-4" />
    </Button>
            </motion.div>
          )}
        </AnimatePresence>
    </div>
    </ErrorBoundary>
  );
}));

VirtualizedNewsFeedRefactored.displayName = 'VirtualizedNewsFeedRefactored';

export default VirtualizedNewsFeedRefactored;