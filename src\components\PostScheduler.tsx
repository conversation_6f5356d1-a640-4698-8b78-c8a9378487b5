import React, { useState, useEffect } from 'react';
import { Calendar, Clock, X, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import type { BasePost } from '@/types/shared';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'sonner';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { uniqueToast } from '@/utils/toastManager';
import { storage } from '@/lib/storage';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui';
import { getSafeImage } from '@/lib/constants';

interface ScheduledPost {
  id: string, content: string, scheduledFor: string, privacy: 'public' | 'friends' | 'only_me', status: 'scheduled' | 'published' | 'failed', createdAt: string, author: {
    name: string, avatar: string;
  };
  media?: {
    type: 'image' | 'video', url: string;
  }[];
}

interface PostSchedulerProps {
  isOpen?: boolean;
  onClose?: () => void;
  initialContent?: string;
  onCreatePost?: (post: Partial<BasePost>) => void;
  mode?: 'inline' | 'dialog'; // 'inline' for instant posting, 'dialog' for scheduled posting
}

const PostScheduler: React.FC<PostSchedulerProps> = ({ 
  isOpen = false, 
  onClose = () => {}, 
  initialContent = '',
  onCreatePost,
  mode = 'dialog'
}) => {
  const [content, setContent] = useState(initialContent);
  const [scheduledDate, setScheduledDate] = useState('');
  const [scheduledTime, setScheduledTime] = useState('');
  const [privacy, setPrivacy] = useState<'public' | 'friends' | 'only_me'>('friends');
  const [scheduledPosts, setScheduledPosts] = useState<ScheduledPost[]>([]);
  const [showScheduledPosts, setShowScheduledPosts] = useState(false);

  // Load scheduled posts from storage
  useEffect(() => {
    const saved = storage.get<ScheduledPost[]>('scheduled_posts', []);
    setScheduledPosts(saved);
  }, []);

  // Check and publish scheduled posts
  useEffect(() => {
    const checkScheduledPosts = () => {
      const now = new Date();
      const updated = scheduledPosts.map(post => {
        if (post.status === 'scheduled' && new Date(post.scheduledFor) <= now) {
          // Simulate publishing the post
          uniqueToast.success(`Post "${post.content.substring(0, 30)}..." has been published!`);
          return { ...post, status: 'published' as const };
        }
        return post;
      });
      
      if (updated.some((post, index) => post.status !== scheduledPosts[index]?.status)) {
        setScheduledPosts(updated);
        storage.set('scheduled_posts', updated);
      }
    };

    const interval = setInterval(checkScheduledPosts, 60000); // Check every minute
    return () => clearInterval(interval);
  }, [scheduledPosts]);

  const handleInstantPost = () => {
    if (!content.trim()) {
      toast.error('Post content is required');
      return;
    }

    if (onCreatePost) {
      const newPost = {
        id: `post_${Date.now()}`,
        content,
        privacy,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      
      onCreatePost(newPost);
      setContent('');
      setPrivacy('friends');
    }
  };

  const handleSchedulePost = () => {
    if (!content.trim()) {
      toast.error('Post content is required');
      return;
    }

    if (!scheduledDate || !scheduledTime) {
      toast.error('Please select date and time for scheduling');
      return;
    }

    const scheduledDateTime = new Date(`${scheduledDate}T${scheduledTime}`);
    const now = new Date();

    if (scheduledDateTime <= now) {
      toast.error('Scheduled time must be in the future');
      return;
    }

    const newPost: ScheduledPost = {
      id: `scheduled_${Date.now()}`,
      content,
      scheduledFor: scheduledDateTime.toISOString(),
      privacy,
      status: 'scheduled',
      createdAt: new Date().toISOString(),
      author: {
        name: 'You',
        avatar: getSafeImage('AVATARS', 0)
      }
    };

    const updated = [...scheduledPosts, newPost];
    setScheduledPosts(updated);
    storage.set('scheduled_posts', updated);

    // Reset form
    setContent('');
    setScheduledDate('');
    setScheduledTime('');
    setPrivacy('friends');
    
    uniqueToast.success('Post scheduled successfully!');
    onClose();
  };

  const handleDeleteScheduledPost = (postId: string) => {
    const updated = scheduledPosts.filter(post => post.id !== postId);
    setScheduledPosts(updated);
    storage.set('scheduled_posts', updated);
    uniqueToast.success('Scheduled post deleted');
  };

  const getMinDateTime = () => {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  const getMinTime = () => {
    const now = new Date();
    const today = now.toDateString();
    const selectedDay = scheduledDate ? new Date(scheduledDate).toDateString() : '';
    
    if (selectedDay === today) {
      const hours = String(now.getHours()).padStart(2, '0');
      const minutes = String(now.getMinutes() + 1).padStart(2, '0');
      return `${hours}:${minutes}`;
    }
    return '00:00';
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'scheduled':
        return <Badge variant="secondary" className="bg-blue-100 text-blue-800">Scheduled</Badge>;
      case 'published':
        return <Badge variant="secondary" className="bg-green-100 text-green-800">Published</Badge>;
      case 'failed':
        return <Badge variant="destructive">Failed</Badge>;
      default:
        return <Badge variant="secondary">Unknown</Badge>;
    }
  };

  const formatDateTime = (dateTime: string) => {
    const date = new Date(dateTime);
    return date.toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Inline mode for instant posting
  if (mode === 'inline' && onCreatePost) {
    return (
      <Card>
        <CardContent className="p-4">
          <div className="space-y-4">
            <Textarea
              placeholder="What's on your mind?"
              value={content} onChange={(e) => setContent(e.target.value)}, rows={3} className="resize-none"
            />
            
            <div className="flex items-center justify-between">
              <Select value={privacy} onValueChange={(value: 'public' | 'friends' | 'only_me') => setPrivacy(value)}>
                <SelectTrigger className="w-[140px]">
                  <SelectValue />
    </SelectTrigger>
                <SelectContent>
                  <SelectItem value="public">🌍 Public</SelectItem>
                  <SelectItem value="friends">👥 Friends</SelectItem>
                  <SelectItem value="only_me">🔒 Only Me</SelectItem>
    </SelectContent>
              </Select>
              
              <div className="flex gap-2">
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => {
                    setShowScheduledPosts(true);
                  }}
                >
                  <Calendar className="w-4 h-4 mr-1" />
                  Schedule
                </Button>
                <Button 
                  onClick={handleInstantPost} disabled={!content.trim()}, size="sm"
                >
                  Post
                </Button>
    </div>
            </div>
    </div>
        </CardContent>
    </Card>
    );
  }

  // Dialog mode for scheduled posting
  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="sm:max-w-lg">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Calendar className="w-5 h-5" />
              Schedule Post
            </DialogTitle>
    </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="content">Post Content</Label>
              <Textarea
                id="content"
                placeholder="What's on your mind?"
                value={content} onChange={(e) => setContent(e.target.value)}, rows={4} className="mt-1"
              />
    </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="date">Date</Label>
                <Input
                  id="date"
                  type="date"
                  value={scheduledDate} onChange={(e) => setScheduledDate(e.target.value)}, min={getMinDateTime()} className="mt-1"
                />
    </div>
              <div>
                <Label htmlFor="time">Time</Label>
                <Input
                  id="time"
                  type="time"
                  value={scheduledTime} onChange={(e) => setScheduledTime(e.target.value)}, min={getMinTime()} className="mt-1"
                />
    </div>
            </div>

            <div>
              <Label htmlFor="privacy">Privacy</Label>
              <Select value={privacy} onValueChange={(value: 'public' | 'friends' | 'only_me') => setPrivacy(value)}>
                <SelectTrigger className="mt-1">
                  <SelectValue />
    </SelectTrigger>
                <SelectContent>
                  <SelectItem value="public">🌍 Public</SelectItem>
                  <SelectItem value="friends">👥 Friends</SelectItem>
                  <SelectItem value="only_me">🔒 Only Me</SelectItem>
    </SelectContent>
              </Select>
    </div>
            <div className="flex gap-2 pt-4">
              <Button onClick={handleSchedulePost} className="flex-1">
                <Clock className="w-4 h-4 mr-2" />
                Schedule Post
              </Button>
              <Button variant="outline" onClick={() => setShowScheduledPosts(true)}>
                View Scheduled
              </Button>
    </div>
          </div>
    </DialogContent>
      </Dialog>

      {/* Scheduled Posts Dialog */}
      <Dialog open={showScheduledPosts} onOpenChange={setShowScheduledPosts}>
        <DialogContent className="sm:max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Calendar className="w-5 h-5" />
              Scheduled Posts ({scheduledPosts.length})
            </DialogTitle>
    </DialogHeader>
          <div className="space-y-4">
            {scheduledPosts.length === 0 ? (
              <div className="text-center py-8">
                <Calendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">No scheduled posts</p>
    </div>
            ) : (
              scheduledPosts.map((post) => (
                <Card key={post.id}>
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-3">
                        <Avatar className="w-8 h-8">
                          <AvatarImage src={post.author.avatar} />
                          <AvatarFallback>{post.author.name[0]}</AvatarFallback>
    </Avatar>
                        <div>
                          <p className="font-medium text-sm">{post.author.name}</p>
                          <p className="text-xs text-gray-500">
                            Scheduled for {formatDateTime(post.scheduledFor)}
                          </p>
    </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {getStatusBadge(post.status)}
                        {post.status === 'scheduled' && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteScheduledPost(post.id)}
                          >
                            <X className="w-4 h-4" />
    </Button>
                        )}
                      </div>
    </div>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <p className="text-sm text-gray-700 dark:text-gray-300">
                      {post.content}
                    </p>
                    {post.status === 'scheduled' && new Date(post.scheduledFor) <= new Date() && (
                      <div className="flex items-center gap-2 mt-2 text-amber-600">
                        <AlertCircle className="w-4 h-4" />
                        <span className="text-xs">Ready to publish</span>
    </div>
                    )}
                  </CardContent>
    </Card>
              ))
            )}
          </div>
    </DialogContent>
      </Dialog>
    </>
  );
};

export default PostScheduler;