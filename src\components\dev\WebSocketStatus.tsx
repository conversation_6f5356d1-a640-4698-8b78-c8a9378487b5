import React from 'react';
import { useWebSocketStatus } from '@/utils/websocketFallback';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Wifi, WifiOff, RefreshCw } from 'lucide-react';

/**
 * Development-only component to show WebSocket connection status
 * Helps developers understand HMR connection issues
 */
const WebSocketStatus: React.FC = () => {
  const { connectionStatus, retryCount } = useWebSocketStatus();

  // Only show in development mode
  if (process.env.NODE_ENV === 'production') {
    return null;
  }

  const getStatusInfo = () => {
    switch (connectionStatus) {
      case 'connected':
        return {
          icon: <Wifi className="w-3 h-3" />
        label: 'HMR Connected',
          variant: 'default' as const,
          color: 'text-green-600'
        };
      case 'retrying':
        return {
          icon: <RefreshCw className="w-3 h-3 animate-spin" />
        label: `Reconnecting... (${retryCount}/10)`,
          variant: 'secondary' as const,
          color: 'text-yellow-600'
        };
      case 'disconnected':
        return {
          icon: <WifiOff className="w-3 h-3" />
        label: 'HMR Disconnected',
          variant: 'destructive' as const,
          color: 'text-red-600'
        };
      default:
        return {
          icon: <Wifi className="w-3 h-3" />
        label: 'HMR Status Unknown',
          variant: 'outline' as const,
          color: 'text-gray-600'
        };
    }
  };

  const statusInfo = getStatusInfo();

  return (
    <div className="fixed top-4 right-4 z-50">
      <Card className="shadow-lg">
        <CardContent className="p-2">
          <Badge variant={statusInfo.variant} className="text-xs">
            <div className="flex items-center gap-1">
              <span className={statusInfo.color}>{statusInfo.icon}</span>
              <span>{statusInfo.label}</span>
    </div>
          </Badge>
    </CardContent>
      </Card>
    </div>
  );
};

export default WebSocketStatus;