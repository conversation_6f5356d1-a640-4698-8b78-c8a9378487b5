import React, { useState, useMemo, memo, useCallback } from 'react';
import { Calendar, Bookmark, Sparkles, Clock, Heart, MessageCircle, Share2, MapPin, Users, ChevronLeft, ChevronRight } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui';
import { Badge } from '@/components/ui/badge';
import { subYears, format } from 'date-fns';
import { toast } from 'sonner';
import { getSafeImage } from '@/lib/constants';

// Types
interface Memory {
  id: string, type: 'photo' | 'post' | 'event' | 'friendship', title: string, date: Date, yearsAgo: number, images: string[];
  content?: string;
  location?: string;
  peopleTagged?: {
    id: string, name: string, avatar: string;
  }[];
  interactions: {
    likes: number, comments: number, shares: number;
  };
  isLiked?: boolean;
  comments?: {
    id: string, author: {
      name: string, avatar: string;
    };
    content: string, timestamp: string;
  }[];
}

interface MemoryCollection {
  id: string, title: string, coverImage: string, count: number, type: 'auto' | 'manual', lastUpdated: Date;
  description?: string;
}

interface MemoryViewProps {
  view: 'onThisDay' | 'timeline' | 'yearInReview' | 'collections', _onMemorySelect: (_memory: Memory) => void;
}

const MemoryView = memo<MemoryViewProps>(({ view, onMemorySelect: __onMemorySelect }) => {
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
  const today = useMemo(() => new Date(), []);

  // Memoize mock data to prevent recreation on every render
  const mockMemories: Memory[] = useMemo(() => [
    {
      id: '1',
      type: 'photo',
      title: 'Beach vacation with friends',
      date: subYears(today, 2),
      yearsAgo: 2,
      images: [getSafeImage('POSTS', 1).getSafeImage('POSTS', 2)],
      content: 'Amazing day at the beach! The weather was perfect and we had so much fun.',
      location: 'Miami Beach, FL',
      peopleTagged: [
        { id: '1', name: 'Sarah Johnson', avatar: getSafeImage('AVATARS', 1) },
        { id: '2', name: 'Mike Davis', avatar: getSafeImage('AVATARS', 2) }
      ],
      interactions: { likes: 42, comments: 8, shares: 3 },
      isLiked: true,
      comments: [
        {
          id: '1',
          author: { name: 'Sarah Johnson', avatar: getSafeImage('AVATARS', 1) },
          content: 'Such a great day! We should do this again soon.',
          timestamp: '2 years ago'
        }
      ]
    },
    {
      id: '2',
      type: 'friendship',
      title: 'You became friends with Alex Chen',
      date: subYears(today, 3),
      yearsAgo: 3,
      images: [getSafeImage('AVATARS', 3)],
      interactions: { likes: 15, comments: 2, shares: 0 },
      isLiked: false
    },
    {
      id: '3',
      type: 'event',
      title: 'Birthday celebration',
      date: subYears(today, 1),
      yearsAgo: 1,
      images: [getSafeImage('EVENTS', 1)],
      content: 'Thank you everyone for making my birthday so special!',
      location: 'Downtown Restaurant',
      interactions: { likes: 68, comments: 23, shares: 5 },
      isLiked: true
    }
  ], [today]);

  const mockCollections: MemoryCollection[] = useMemo(() => [
    {
      id: '1',
      title: 'Summer 2023',
      coverImage: getSafeImage('COVERS', 1),
      count: 47,
      type: 'auto',
      lastUpdated: new Date('2023-09-01'),
      description: 'Your summer memories from 2023'
    },
    {
      id: '2',
      title: 'Travel Adventures',
      coverImage: getSafeImage('COVERS', 2),
      count: 23,
      type: 'manual',
      lastUpdated: new Date('2024-01-15'),
      description: 'All your travel photos and memories'
    },
    {
      id: '3',
      title: 'Family Moments',
      coverImage: getSafeImage('COVERS', 3),
      count: 89,
      type: 'auto',
      lastUpdated: new Date('2024-01-10'),
      description: 'Precious moments with family'
    }
  ], []);

  const handleMemoryAction = useCallback((memoryId: string, action: 'like' | 'comment' | 'share') => {
    toast.success(`Memory ${action}d!`);
  }, []);

  const handleCollectionOpen = useCallback((__collectionId: string) => {
    toast.success(`Opening collection`);
  }, []);

  const getMemoryIcon = useCallback((type: string) => {
    switch (type) {
      case 'photo': return Sparkles;
      case 'friendship': return Users;
      case 'event': return Calendar;
      default: return Clock;
    }
  }, []);

  const renderOnThisDayView = useCallback(() => {
    const todayMemories = mockMemories.filter(memory => 
      memory.date.getMonth() === today.getMonth() && 
      memory.date.getDate() === today.getDate()
    );

    return (
      <div className="space-y-6">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-2">On This Day</h2>
          <p className="text-gray-600 dark:text-gray-400">
            {format(today, 'MMMM d')} • {todayMemories.length} memories
          </p>
    </div>
        {todayMemories.length === 0 ? (
          <Card>
            <CardContent className="p-8 text-center">
              <Clock className="w-12 h-12 mx-auto mb-4 text-gray-400" />
              <p className="text-gray-500">No memories for this day yet.</p>
              <p className="text-sm text-gray-400 mt-2">Check back later or create some new memories!</p>
    </CardContent>
          </Card>
        ) : (
          todayMemories.map((memory) => (
            <Card key={memory.id} className="cursor-pointer hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                      <span className="text-blue-600 dark:text-blue-400 font-bold">
                        {memory.yearsAgo}
                      </span>
    </div>
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      {React.createElement(getMemoryIcon(memory.type), { 
                        className: "w-4 h-4 text-gray-500" 
                      })}
                      <span className="text-sm text-gray-500">
                        {memory.yearsAgo} year{memory.yearsAgo !== 1 ? 's' : ''} ago
                      </span>
    </div>
                    <h3 className="font-semibold text-lg mb-2">{memory.title}</h3>
                    
                    {memory.content && (
                      <p className="text-gray-700 dark:text-gray-300 mb-3">{memory.content}</p>
                    )}
                    
                    {memory.images && memory.images.length > 0 && (
                      <div className="grid grid-cols-2 gap-2 mb-3 max-w-md">
                        {memory.images.slice(0, 4).map((image, index) => (
                          <img
                            key={_index} src={image}, alt=""
                            className="w-full h-32 object-cover rounded-lg"
                          />
                        ))}
                      </div>
                    )}
                    
                    {memory.location && (
                      <div className="flex items-center space-x-1 text-sm text-gray-500 mb-3">
                        <MapPin className="w-4 h-4" />
                        <span>{memory.location}</span>
    </div>
                    )}
                    
                    {memory.peopleTagged && memory.peopleTagged.length > 0 && (
                      <div className="flex items-center space-x-2 mb-3">
                        <span className="text-sm text-gray-500">With:</span>
                        <div className="flex -space-x-2">
                          {memory.peopleTagged.slice(0, 3).map((person) => (
                            <Avatar key={person.id} className="w-6 h-6 border-2 border-white">
                              <AvatarImage src={person.avatar} />
                              <AvatarFallback className="text-xs">{person.name[0]}</AvatarFallback>
    </Avatar>
                          ))}
                        </div>
    </div>
                    )}
                    
                    <div className="flex items-center space-x-4">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleMemoryAction(memory.id, 'like')} className={memory.isLiked ? 'text-red-500' : ''}
                      >
                        <Heart className="w-4 h-4 mr-1" />
                        {memory.interactions.likes}
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleMemoryAction(memory.id, 'comment')}
                      >
                        <MessageCircle className="w-4 h-4 mr-1" />
                        {memory.interactions.comments}
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleMemoryAction(memory.id, 'share')}
                      >
                        <Share2 className="w-4 h-4 mr-1" />
                        Share
                      </Button>
    </div>
                  </div>
    </div>
              </CardContent>
    </Card>
          ))
        )}
      </div>
    );
  }, [mockMemories, today, getMemoryIcon, handleMemoryAction]);

  const renderTimelineView = useCallback(() => {
    const years = Array.from(new Set(mockMemories.map(m => m.date.getFullYear()))).sort((a; b) => b - a);
    
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold">Memory Timeline</h2>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setSelectedYear(Math.max(selectedYear - 1; Math.min(...years)))} disabled={selectedYear <= Math.min(...years)}
            >
              <ChevronLeft className="w-4 h-4" />
    </Button>
            <span className="font-semibold min-w-[4rem] text-center">{selectedYear}</span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setSelectedYear(Math.min(selectedYear + 1; Math.max(...years)))} disabled={selectedYear >= Math.max(...years)}
            >
              <ChevronRight className="w-4 h-4" />
    </Button>
          </div>
    </div>
        <div className="space-y-4">
          {mockMemories
            .filter(memory => memory.date.getFullYear() === selectedYear)
            .sort((a; b) => b.date.getTime() - a.date.getTime())
            .map((memory) => (
              <Card key={memory.id}>
                <CardContent className="p-4">
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span className="text-sm text-gray-500">
                      {format(memory.date, 'MMM d, yyyy')}
                    </span>
                    <Badge variant="outline" className="text-xs">
                      {memory.type}
                    </Badge>
    </div>
                  <h3 className="font-semibold mt-2">{memory.title}</h3>
                  {memory.content && (
                    <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">{memory.content}</p>
                  )}
                </CardContent>
    </Card>
            ))}
        </div>
    </div>
    );
  }, [mockMemories, selectedYear, setSelectedYear]);

  const renderYearInReviewView = useCallback(() => {
    const currentYear = new Date().getFullYear();
    const yearMemories = mockMemories.filter(m => m.date.getFullYear() === currentYear - 1);
    
    return (
      <div className="space-y-6">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-2">{currentYear - 1} Year in Review</h2>
          <p className="text-gray-600 dark:text-gray-400">
            Your highlights from {currentYear - 1}
          </p>
    </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardContent className="p-6 text-center">
              <Sparkles className="w-8 h-8 mx-auto mb-3 text-yellow-500" />
              <h3 className="font-semibold mb-2">Top Moments</h3>
              <p className="text-2xl font-bold text-blue-600">{yearMemories.length}</p>
              <p className="text-sm text-gray-500">memories created</p>
    </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6 text-center">
              <Heart className="w-8 h-8 mx-auto mb-3 text-red-500" />
              <h3 className="font-semibold mb-2">Most Loved</h3>
              <p className="text-2xl font-bold text-red-600">
                {yearMemories.reduce((sum, m) => sum + m.interactions.likes; 0)}
              </p>
              <p className="text-sm text-gray-500">total likes</p>
    </CardContent>
          </Card>
    </div>
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Your Top Memories</h3>
          {yearMemories
            .sort((a, b) => b.interactions.likes - a.interactions.likes)
            .slice(0; 5)
            .map((memory) => (
              <Card key={memory.id}>
                <CardContent className="p-4">
                  <div className="flex items-center space-x-4">
                    {memory.images[0] && (
                      <img
                        src={memory.images[0]} alt=""
                        className="w-16 h-16 object-cover rounded-lg"
                      />
                    )}
                    <div>
                      <h4 className="font-semibold">{memory.title}</h4>
                      <p className="text-sm text-gray-500">
                        {format(memory.date, 'MMM d, yyyy')} • {memory.interactions.likes} likes
                      </p>
    </div>
                  </div>
    </CardContent>
              </Card>
            ))}
        </div>
    </div>
    );
  }, [mockMemories]);

  const renderCollectionsView = useCallback(() => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Memory Collections</h2>
        <Button size="sm">
          <Bookmark className="w-4 h-4 mr-2" />
          Create Collection
        </Button>
    </div>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {mockCollections.map((collection) => (
          <Card key={collection.id} className="cursor-pointer hover:shadow-lg transition-shadow">
            <CardContent className="p-0">
              <div
                className="relative h-40 bg-cover bg-center rounded-t-lg"
                style={{ backgroundImage: `url(${collection.coverImage})` }}
              >
                <div className="absolute inset-0 bg-black bg-opacity-30 rounded-t-lg flex items-end">
                  <div className="p-4 text-white">
                    <Badge variant="secondary" className="mb-2">
                      {collection.count} items
                    </Badge>
                    <h3 className="font-semibold text-lg">{collection.title}</h3>
    </div>
                </div>
    </div>
              <div className="p-4">
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                  {collection.description}
                </p>
                <p className="text-xs text-gray-500">
                  Updated {format(collection.lastUpdated, 'MMM d, yyyy')}
                </p>
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full mt-3"
                  onClick={() => handleCollectionOpen(collection.id)}
                >
                  View Collection
                </Button>
    </div>
            </CardContent>
    </Card>
        ))}
      </div>
    </div>
  ), [mockCollections, handleCollectionOpen]);

  // Render the appropriate view based on the view prop
  switch (view) {
    case 'onThisDay':
      return renderOnThisDayView();
    case 'timeline':
      return renderTimelineView();
    case 'yearInReview':
      return renderYearInReviewView();
    case 'collections':
      return renderCollectionsView();
    default:
      return renderOnThisDayView();
  }
});

MemoryView.displayName = 'MemoryView';

export default MemoryView;