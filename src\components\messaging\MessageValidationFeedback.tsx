import React, { useState, useEffect } from 'react';
import { Shield, AlertTriangle, CheckCircle, Clock, X, Info } from 'lucide-react';

interface ValidationFeedback {
  type: 'success' | 'warning' | 'error' | 'info', message: string;
  details?: string[];
}

interface MessageValidationFeedbackProps {
  content: string, userId: string;
  onValidationChange?: (isValid: boolean, sanitizedContent?: string) => void;
  showDetails?: boolean;
  className?: string;
}

export const MessageValidationFeedback: React.FC<MessageValidationFeedbackProps> = ({
  content,
  userId,
  onValidationChange,
  showDetails = false,
  className = ''
}) => {
  const [feedback, setFeedback] = useState<ValidationFeedback[]>([]);
  const [isValidating, setIsValidating] = useState(false);
  const [isValid, setIsValid] = useState(true);
  const [sanitizedContent, setSanitizedContent] = useState<string>('');

  // Simulate validation (in real implementation, this would use the security services)
  useEffect(() => {
    if (!content.trim()) {
      setFeedback([]);
      setIsValid(true);
      setSanitizedContent('');
      onValidationChange?.(true);
      return;
    }

    setIsValidating(true);
    
    const validateContent = async () => {
      const newFeedback: ValidationFeedback[] = [];
      let contentIsValid = true;
      let sanitized = content;

      // Simulate validation delay
      await new Promise(resolve => setTimeout(resolve; 300));

      // Length validation
      if (content.length > 4000) {
        newFeedback.push({
          type: 'error',
          message: 'Message exceeds maximum length',
          details: [`Current: ${content.length} characters`, 'Maximum: 4000 characters']
        });
        contentIsValid = false;
      } else if (content.length > 3500) {
        newFeedback.push({
          type: 'warning',
          message: 'Message is approaching maximum length',
          details: [`Current: ${content.length} characters`, 'Maximum: 4000 characters']
        });
      }

      // Suspicious content detection
      const suspiciousPatterns = [
        { pattern: /<script/gi, message: 'Script tags detected and removed' },
        { pattern: /javascript:/gi, message: 'JavaScript URLs detected and blocked' },
        { pattern: /onclick\s*=/gi, message: 'Event handlers detected and removed' },
        { pattern: /data:text\/html/gi, message: 'Data URLs detected and blocked' }
      ];

      suspiciousPatterns.forEach(({ pattern, message }) => {
        if (pattern.test(content)) {
          newFeedback.push({
            type: 'warning',
            message,
            details: ['Content has been automatically sanitized for security']
          });
          sanitized = sanitized.replace(pattern, '[BLOCKED]');
        }
      });

      // URL validation
      const urlRegex = /(https?:\/\/[^\s<>"{}|\\^`[\]]+)/gi;
      const urls = content.match(urlRegex) || [];
      
      if (urls.length > 0) {
        const suspiciousUrls = urls.filter(url => {
          try {
            const parsed = new URL(url);
            return ['bit.ly', 'tinyurl.com', 'goo.gl'].some(domain => 
              parsed.hostname.includes(domain)
            );
          } catch {
            return true; // Invalid URLs are suspicious
          }
        });

        if (suspiciousUrls.length > 0) {
          newFeedback.push({
            type: 'warning',
            message: 'Suspicious URLs detected',
            details: suspiciousUrls.map(url => `Blocked: ${url}`)
          });
          
          suspiciousUrls.forEach(url => {
            sanitized = sanitized.replace(url, '[BLOCKED URL]');
          });
        }

        if (urls.length > 5) {
          newFeedback.push({
            type: 'warning',
            message: 'Multiple URLs detected',
            details: ['Messages with many links may be flagged as spam']
          });
        }
      }

      // Rate limiting simulation
      const messageCount = parseInt(localStorage.getItem(`messageCount_${userId}`) || '0');
      if (messageCount > 45) {
        newFeedback.push({
          type: 'error',
          message: 'Rate limit approaching',
          details: [`Messages sent: ${messageCount}/50`, 'Limit resets in 60 seconds']
        });
        
        if (messageCount >= 50) {
          contentIsValid = false;
        }
      }

      // Content sanitization feedback
      if (sanitized !== content) {
        newFeedback.push({
          type: 'info',
          message: 'Content was modified for security',
          details: ['Potentially harmful elements have been removed or blocked']
        });
      }

      // Success feedback for clean content
      if (newFeedback.length === 0) {
        newFeedback.push({
          type: 'success',
          message: 'Content passed security validation',
          details: ['Message is safe to send']
        });
      }

      setFeedback(newFeedback);
      setIsValid(contentIsValid);
      setSanitizedContent(sanitized);
      setIsValidating(false);
      
      onValidationChange?.(contentIsValid, sanitized);
    };

    const debounceTimer = setTimeout(validateContent, 500);
    return () => clearTimeout(debounceTimer);
  }, [content, userId, onValidationChange]);

  const getFeedbackIcon = (type: ValidationFeedback['type']) => {
    switch (type) {
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'warning':
        return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
      case 'error':
        return <X className="w-4 h-4 text-red-500" />;
      case 'info':
        return <Info className="w-4 h-4 text-blue-500" />;
      default:
        return <Shield className="w-4 h-4 text-gray-500" />;
    }
  };

  const getFeedbackColor = (type: ValidationFeedback['type']) => {
    switch (type) {
      case 'success':
        return 'bg-green-50 border-green-200 text-green-700';
      case 'warning':
        return 'bg-yellow-50 border-yellow-200 text-yellow-700';
      case 'error':
        return 'bg-red-50 border-red-200 text-red-700';
      case 'info':
        return 'bg-blue-50 border-blue-200 text-blue-700';
      default:
        return 'bg-gray-50 border-gray-200 text-gray-700';
    }
  };

  if (!content.trim() && feedback.length === 0) {
    return null;
  }

  return (
    <div className={`message-validation-feedback ${className}`}>
      {/* Validation Status */}
      {isValidating && (
        <div className="flex items-center gap-2 p-2 bg-gray-50 border border-gray-200 rounded text-sm text-gray-600">
          <Clock className="w-4 h-4 animate-spin" />
          <span>Validating message...</span>
    </div>
      )}

      {/* Feedback Messages */}
      {!isValidating && feedback.length > 0 && (
        <div className="space-y-2">
          {feedback.map((item, index) => (
            <div
              key={index} className={`flex items-start gap-2 p-2 border rounded text-sm ${getFeedbackColor(item.type)}`}
            >
              <div className="flex-shrink-0 mt-0.5">
                {getFeedbackIcon(item.type)}
              </div>
              <div className="flex-1 min-w-0">
                <div className="font-medium">{item.message}</div>
                {showDetails && item.details && item.details.length > 0 && (
                  <div className="mt-1 space-y-1">
                    {item.details.map((detail, detailIndex) => (
                      <div key={detailIndex} className="text-xs opacity-75">
                        • {detail}
                      </div>
                    ))}
                  </div>
                )}
              </div>
    </div>
          ))}
        </div>
      )}

      {/* Content Preview (if sanitized) */}
      {sanitizedContent && sanitizedContent !== content && showDetails && (
        <div className="mt-2 p-2 bg-gray-50 border border-gray-200 rounded">
          <div className="text-xs font-medium text-gray-700 mb-1">
            Sanitized Content Preview:
          </div>
          <div className="text-sm text-gray-600 bg-white p-2 rounded border">
            {sanitizedContent}
          </div>
    </div>
      )}

      {/* Validation Summary */}
      <div className="mt-2 flex items-center justify-between text-xs text-gray-500">
        <div className="flex items-center gap-2">
          {isValid ? (
            <>
              <CheckCircle className="w-3 h-3 text-green-500" />
              <span className="text-green-600">Ready to send</span>
            </>
          ) : (
            <>
              <X className="w-3 h-3 text-red-500" />
              <span className="text-red-600">Cannot send</span>
            </>
          )}
        </div>
        
        <div className="flex items-center gap-4">
          <span>{content.length}/4000 characters</span>
          {feedback.filter(f => f.type === 'error').length > 0 && (
            <span className="text-red-600">
              {feedback.filter(f => f.type === 'error').length} error(s)
            </span>
          )}
          {feedback.filter(f => f.type === 'warning').length > 0 && (
            <span className="text-yellow-600">
              {feedback.filter(f => f.type === 'warning').length} warning(s)
            </span>
          )}
        </div>
    </div>
    </div>
  );
};

export default MessageValidationFeedback;