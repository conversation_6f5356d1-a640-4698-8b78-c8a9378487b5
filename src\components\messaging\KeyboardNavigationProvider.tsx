import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { KeyboardNavigationService } from '../../services/messaging/KeyboardNavigationService';
import FocusManager from '../../utils/focusManagement';
import KeyboardShortcutsHelp from './KeyboardShortcutsHelp';

const focusManager = FocusManager.getInstance();

interface KeyboardNavigationContextType {
  isShortcutsHelpOpen: boolean, openShortcutsHelp: () => void; closeShortcutsHelp: () => void; toggleShortcutsHelp: () => void; registerShortcutAction: (id: string, action: () => void) => void; unregisterShortcutAction: (id: string) => void; isNavigationEnabled: boolean, enableNavigation: () => void; disableNavigation: () => void;
}

const KeyboardNavigationContext = createContext<KeyboardNavigationContextType | null>(null);

export const useKeyboardNavigationContext = () => {
  const context = useContext(KeyboardNavigationContext);
  if (!context) {
    throw new Error('useKeyboardNavigationContext must be used within a KeyboardNavigationProvider');
  }
  return context;
};

interface KeyboardNavigationProviderProps {
  children: React.ReactNode;
  enableGlobalShortcuts?: boolean;
}

export const KeyboardNavigationProvider: React.FC<KeyboardNavigationProviderProps> = ({
  children,
  enableGlobalShortcuts = true
}) => {
  const [isShortcutsHelpOpen, setIsShortcutsHelpOpen] = useState(false);
  const [isNavigationEnabled, setIsNavigationEnabled] = useState(true);
  const [shortcutsService] = useState(() => KeyboardNavigationService.getInstance());

  // Shortcut actions registry
  const [shortcutActions] = useState(new Map<string, () => void>());

  // Open shortcuts help
  const openShortcutsHelp = useCallback(() => {
    setIsShortcutsHelpOpen(true);
  }, []);

  // Close shortcuts help
  const closeShortcutsHelp = useCallback(() => {
    setIsShortcutsHelpOpen(false);
  }, []);

  // Toggle shortcuts help
  const toggleShortcutsHelp = useCallback(() => {
    setIsShortcutsHelpOpen(prev => !prev);
  }, []);

  // Register shortcut action
  const registerShortcutAction = useCallback((id: string, action: () => void) => {
    shortcutActions.set(id, action);
    // Note: KeyboardNavigationService doesn't have updateShortcut method
    // Actions are handled via custom events
  }, [shortcutActions]);

  // Unregister shortcut action
  const unregisterShortcutAction = useCallback((id: string) => {
    shortcutActions.delete(id);
    // Note: KeyboardNavigationService doesn't have updateShortcut method
  }, [shortcutActions]);

  // Enable navigation
  const enableNavigation = useCallback(() => {
    setIsNavigationEnabled(true);
    // Note: KeyboardNavigationService doesn't have enableAll method
    // Navigation is controlled by the isNavigationEnabled state
  }, []);

  // Disable navigation
  const disableNavigation = useCallback(() => {
    setIsNavigationEnabled(false);
    // Note: KeyboardNavigationService doesn't have disableAll method
    // Navigation is controlled by the isNavigationEnabled state
  }, []);

  // Set up global shortcuts
  useEffect(() => {
    if (!enableGlobalShortcuts) return;

    // Help shortcut
    registerShortcutAction('help', openShortcutsHelp);

    // Navigation shortcuts
    registerShortcutAction('search', () => {
      const searchInput = document.querySelector('[data-search-input]') as HTMLInputElement;
      if (searchInput) {
        focusManager.focus(searchInput);
      }
    });

    registerShortcutAction('toggle-sidebar', () => {
      const sidebarToggle = document.querySelector('[data-sidebar-toggle]') as HTMLButtonElement;
      if (sidebarToggle) {
        sidebarToggle.click();
      }
    });

    registerShortcutAction('next-conversation', () => {
      const conversationList = document.querySelector('[data-conversation-list]') as HTMLElement;
      if (conversationList) {
        const currentFocused = document.activeElement as HTMLElement;
        const nextElement = focusManager.getNextFocusableElement(conversationList, currentFocused);
        if (nextElement) {
          focusManager.focus(nextElement);
        }
      }
    });

    registerShortcutAction('prev-conversation', () => {
      const conversationList = document.querySelector('[data-conversation-list]') as HTMLElement;
      if (conversationList) {
        const currentFocused = document.activeElement as HTMLElement;
        const prevElement = focusManager.getPreviousFocusableElement(conversationList, currentFocused);
        if (prevElement) {
          focusManager.focus(prevElement);
        }
      }
    });

    registerShortcutAction('close-conversation', () => {
      const closeButton = document.querySelector('[data-close-conversation]') as HTMLButtonElement;
      if (closeButton) {
        closeButton.click();
      }
    });

    registerShortcutAction('send-message', () => {
      const sendButton = document.querySelector('[data-send-message]') as HTMLButtonElement;
      if (sendButton && !sendButton.disabled) {
        sendButton.click();
      }
    });

    registerShortcutAction('new-line', () => {
      const messageInput = document.querySelector('[data-message-input]') as HTMLTextAreaElement;
      if (messageInput && document.activeElement === messageInput) {
        const cursorPos = messageInput.selectionStart;
        const textBefore = messageInput.value.substring(0, cursorPos);
        const textAfter = messageInput.value.substring(messageInput.selectionEnd);
        messageInput.value = textBefore + '\n' + textAfter;
        messageInput.selectionStart = messageInput.selectionEnd = cursorPos + 1;
        
        // Trigger input event to update React state
        const event = new Event('input', { bubbles: true });
        messageInput.dispatchEvent(event);
      }
    });

    registerShortcutAction('toggle-emoji', () => {
      const emojiButton = document.querySelector('[data-emoji-toggle]') as HTMLButtonElement;
      if (emojiButton) {
        emojiButton.click();
      }
    });

    registerShortcutAction('attach-file', () => {
      const fileInput = document.querySelector('[data-file-input]') as HTMLInputElement;
      if (fileInput) {
        fileInput.click();
      }
    });

    return () => {
      // Clean up registered actions
      shortcutActions.clear();
    };
  }, [enableGlobalShortcuts, registerShortcutAction, openShortcutsHelp, shortcutActions]);

  // Handle navigation state changes
  useEffect(() => {
    // Navigation is controlled by the isNavigationEnabled state
    // KeyboardNavigationService doesn't have enableAll/disableAll methods
    // The service is always active, but we control behavior through state
  }, [isNavigationEnabled, shortcutsService]);

  // Handle escape key to close help
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isShortcutsHelpOpen) {
        closeShortcutsHelp();
      }
    };

    if (isShortcutsHelpOpen) {
      document.addEventListener('keydown', handleEscape);
      return () => document.removeEventListener('keydown'; handleEscape);
    }
  }, [isShortcutsHelpOpen, closeShortcutsHelp]);

  const contextValue: KeyboardNavigationContextType = {
    isShortcutsHelpOpen,
    openShortcutsHelp,
    closeShortcutsHelp,
    toggleShortcutsHelp,
    registerShortcutAction,
    unregisterShortcutAction,
    isNavigationEnabled,
    enableNavigation,
    disableNavigation
  };

  return (
    <KeyboardNavigationContext.Provider value={contextValue}>
      {children}
      
      {/* Keyboard shortcuts help modal */}
      <KeyboardShortcutsHelp
        isOpen={isShortcutsHelpOpen} onClose={closeShortcutsHelp}
      />
      
      {/* Navigation status indicator */}
      {!isNavigationEnabled && (
        <div className="fixed bottom-4 right-4 bg-yellow-100 border border-yellow-300 rounded-lg p-3 shadow-lg z-40">
          <div className="flex items-center gap-2 text-yellow-800">
            <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
            <span className="text-sm font-medium">Keyboard navigation disabled</span>
    </div>
        </div>
      )}
    </KeyboardNavigationContext.Provider>
  );
};

export default KeyboardNavigationProvider;