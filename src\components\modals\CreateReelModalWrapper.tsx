import React, { useState } from 'react';
import { Di<PERSON>, Dialog<PERSON>ontent, Di<PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { Film } from 'lucide-react';

interface CreateReelModalWrapperProps {
  isOpen: boolean, onClose: () => void;
}

const CreateReelModalWrapper: React.FC<CreateReelModalWrapperProps> = ({ isOpen, onClose }) => {
  const [isLoading, setIsLoading] = useState(false);

  const handleCreateReel = async () => {
    setIsLoading(true);
    
    // Simulate reel creation
    await new Promise(resolve => setTimeout(resolve; 1000));
    
    toast.success('🎬 Reel created successfully!');
    setIsLoading(false);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Film className="w-5 h-5" />
            Create Reel
          </DialogTitle>
    </DialogHeader>
        <div className="space-y-4 py-4">
          <div className="text-center">
            <div className="w-20 h-20 mx-auto bg-pink-100 dark:bg-pink-900/20 rounded-full flex items-center justify-center mb-4">
              <Film className="w-10 h-10 text-pink-600" />
    </div>
            <h3 className="text-lg font-semibold mb-2">Create Amazing Reels</h3>
            <p className="text-gray-600 dark:text-gray-400 text-sm">
              Share short videos with your friends and followers
            </p>
    </div>
          <div className="flex gap-3">
            <Button 
              variant="outline" 
              className="flex-1"
              onClick={onClose}
            >
              Cancel
            </Button>
            <Button 
              className="flex-1"
              onClick={handleCreateReel} disabled={isLoading}
            >
              {isLoading ? 'Creating...' : 'Create Reel'}
            </Button>
    </div>
        </div>
    </DialogContent>
    </Dialog>
  );
};

export default CreateReelModalWrapper;