import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  MessageCircle,
  ChevronDown,
  ChevronUp,
  Users,
  Send,
  X
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { formatDistanceToNow } from 'date-fns';
import { Message, MessageThread, User } from '@/types/enhanced-messaging';
import { toast } from 'sonner';

interface MessageThreadProps {
  thread: MessageThread, parentMessage: Message, isOpen: boolean, onToggle: () => void; onReply: (content: string) => Promise<void>; onClose: () => void; currentUserId: string, users: { [key: string]: User };
  className?: string;
}

const MessageThreadComponent: React.FC<MessageThreadProps> = ({
  thread,
  parentMessage,
  isOpen,
  onToggle,
  onReply,
  onClose,
  currentUserId,
  users,
  className = ''
}) => {
  const [replyContent, setReplyContent] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const threadEndRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new replies are added
  useEffect(() => {
    if (isOpen && threadEndRef.current) {
      threadEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [thread.replies.length, isOpen]);

  const handleSubmitReply = async () => {
    if (!replyContent.trim() || isSubmitting) return;

    setIsSubmitting(true);
    try {
      await onReply(replyContent);
      setReplyContent('');
      toast.success('Reply sent');
    } catch (error) {
      console.error('Failed to send reply:', error);
      toast.error('Failed to send reply');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmitReply();
    }
  };

  const formatMessageTime = (timestamp: Date) => {
    return formatDistanceToNow(timestamp, { addSuffix: true });
  };

  const getUser = (userId: string): User => {
    return users[userId] || {
      id: userId,
      name: 'Unknown User',
      avatar: '',
      isOnline: false,
      lastActive: new Date().toISOString(),
      username: userId
    };
  };

  return (
    <div className={`bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg ${className}`}>
      {/* Thread Header */}
      <div className="flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center space-x-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={onToggle} className="flex items-center space-x-2"
          >
            {isOpen ? (
              <ChevronUp className="w-4 h-4" />
            ) : (
              <ChevronDown className="w-4 h-4" />
            )}
            <MessageCircle className="w-4 h-4" />
            <span className="text-sm font-medium">
              {thread.replies.length} {thread.replies.length === 1 ? 'reply' : 'replies'}
            </span>
    </Button>
          {thread.participantCount > 1 && (
            <Badge variant="secondary" className="flex items-center space-x-1">
              <Users className="w-3 h-3" />
              <span>{thread.participantCount}</span>
    </Badge>
          )}
        </div>

        {isOpen && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose} className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            <X className="w-4 h-4" />
    </Button>
        )}
      </div>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}, animate={{ height: 'auto', opacity: 1 }}, exit={{ height: 0, opacity: 0 }}, transition={{ duration: 0.2 }}, className="overflow-hidden"
          >
            {/* Parent Message Context */}
            <div className="p-3 bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-start space-x-3">
                <Avatar className="w-8 h-8">
                  <AvatarImage src={getUser(parentMessage.senderId).avatar} />
                  <AvatarFallback>
                    {getUser(parentMessage.senderId).name.charAt(0)}
                  </AvatarFallback>
    </Avatar>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium text-gray-900 dark:text-white">
                      {getUser(parentMessage.senderId).name}
                    </span>
                    <span className="text-xs text-gray-500 dark:text-gray-400">
                      {formatMessageTime(parentMessage.timestamp)}
                    </span>
    </div>
                  <p className="text-sm text-gray-700 dark:text-gray-300 mt-1 line-clamp-2">
                    {parentMessage.content}
                  </p>
    </div>
              </div>
    </div>
            {/* Thread Replies */}
            <ScrollArea className="max-h-96">
              <div className="p-3 space-y-4">
                {thread.replies.length === 0 ? (
                  <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                    <MessageCircle className="w-8 h-8 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">No replies yet. Start the conversation!</p>
    </div>
                ) : (
                  thread.replies.map((reply) => {
                    const user = getUser(reply.senderId);
                    const isOwnMessage = reply.senderId === currentUserId;

                    return (
                      <motion.div
                        key={reply.id} initial={{ opacity: 0, y: 10 }}, animate={{ opacity: 1, y: 0 }}, className="flex items-start space-x-3"
                      >
                        <Avatar className="w-8 h-8">
                          <AvatarImage src={user.avatar} />
                          <AvatarFallback>
                            {user.name.charAt(0)}
                          </AvatarFallback>
    </Avatar>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center space-x-2">
                            <span className="text-sm font-medium text-gray-900 dark:text-white">
                              {user.name}
                            </span>
                            <span className="text-xs text-gray-500 dark:text-gray-400">
                              {formatMessageTime(reply.timestamp)}
                            </span>
                            {isOwnMessage && (
                              <Badge variant="secondary" className="text-xs">
                                You
                              </Badge>
                            )}
                          </div>
                          <div className={`mt-1 p-2 rounded-lg max-w-xs ${
                            isOwnMessage
                              ? 'bg-blue-500 text-white ml-auto'
                              : 'bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-white'
                          }`}>
                            <p className="text-sm break-words">
                              {reply.content}
                            </p>
    </div>
                        </div>
                      </motion.div>
                    );
                  })
                )}
                <div ref={threadEndRef} />
    </div>
            </ScrollArea>

            {/* Reply Input */}
            <div className="p-3 border-t border-gray-200 dark:border-gray-700">
              <div className="flex items-end space-x-2">
                <Avatar className="w-8 h-8">
                  <AvatarImage src={getUser(currentUserId).avatar} />
                  <AvatarFallback>
                    {getUser(currentUserId).name.charAt(0)}
                  </AvatarFallback>
    </Avatar>
                <div className="flex-1">
                  <Textarea
                    ref={textareaRef} placeholder="Reply to thread..."
                    value={replyContent} onChange={(e) => setReplyContent(e.target.value)}, onKeyPress={handleKeyPress} disabled={isSubmitting}, className="min-h-[40px] max-h-24 resize-none border-gray-200 dark:border-gray-700 focus:border-blue-500 dark:focus:border-blue-400"
                    rows={1}
                  />
    </div>
                <Button
                  onClick={handleSubmitReply} disabled={!replyContent.trim() || isSubmitting}, size="sm"
                  className="h-10"
                >
                  <Send className="w-4 h-4" />
    </Button>
              </div>
    </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default MessageThreadComponent;
