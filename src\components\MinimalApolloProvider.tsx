import React from 'react';

// Ultra-minimal Apollo Provider bypass
// This component provides no Apollo functionality and just renders children
// Hooks removed to prevent React dispatcher corruption
interface MinimalApolloProviderProps {
  children: React.ReactNode;
}

const MinimalApolloProvider: React.FC<MinimalApolloProviderProps> = ({ children }) => {
  // No React hooks to prevent dispatcher corruption
  console.log('✅ MinimalApolloProvider: Rendering without Apollo hooks');
  
  return React.createElement(React.Fragment, null, children);
};

export default MinimalApolloProvider;
