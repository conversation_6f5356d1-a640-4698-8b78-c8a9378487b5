/**
 * ThreadIndicator Component
 * Visual indicators for message threads in main conversation
 */

import React, { useState, useMemo, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  MessageCircle, 
  Users, 
  Clock, 
  ChevronRight,
  Hash,
  Eye
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { formatDistanceToNow } from 'date-fns';
import { cn } from '@/lib/utils';
import { useMessagingStore } from '../../stores/messagingStore';
import { MessageThread, AdvancedMessage } from '../../types/messaging';

interface ThreadIndicatorProps {
  messageId: string;
  threadId?: string;
  onOpenThread?: (threadId: string) => void;
  onCreateThread?: (messageId: string) => void; currentUserId: string;
  getUserName?: (userId: string) => string;
  getUserAvatar?: (userId: string) => string;
  className?: string;
  showPreview?: boolean;
  maxPreviewReplies?: number;
  compact?: boolean;
}

interface ThreadPreviewProps {
  thread: MessageThread, replies: AdvancedMessage[], getUserName: (userId: string) => string; getUserAvatar: (userId: string) => string; maxReplies: number;
}

const ThreadPreview: React.FC<ThreadPreviewProps> = ({
  thread,
  replies,
  getUserName,
  getUserAvatar,
  maxReplies
}) => {
  const previewReplies = replies.slice(-maxReplies);
  const hasMoreReplies = replies.length > maxReplies;

  return (
    <div className="w-80 max-w-sm">
      {/* Thread header */}
      <div className="flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center gap-2">
          <MessageCircle className="w-4 h-4 text-blue-600" />
          <span className="text-sm font-medium text-gray-900 dark:text-white">
            Thread
          </span>
          <Badge variant="secondary" className="text-xs">
            {replies.length} {replies.length === 1 ? 'reply' : 'replies'}
          </Badge>
    </div>
        <div className="flex items-center gap-1 text-xs text-gray-500 dark:text-gray-400">
          <Users className="w-3 h-3" />
          <span>{thread.participantIds.length}</span>
    </div>
      </div>

      {/* Thread preview */}
      <div className="max-h-64 overflow-y-auto">
        {hasMoreReplies && (
          <div className="p-2 text-center">
            <span className="text-xs text-gray-500 dark:text-gray-400">
              {replies.length - maxReplies} more replies...
            </span>
    </div>
        )}
        
        {previewReplies.map((reply, index) => (
          <div key={reply.id} className="flex items-start gap-2 p-2 hover:bg-gray-50 dark:hover:bg-gray-800/50">
            <Avatar className="w-6 h-6 flex-shrink-0">
              <AvatarImage src={getUserAvatar(reply.senderId)} />
              <AvatarFallback className="text-xs">
                {getUserName(reply.senderId).charAt(0).toUpperCase()}
              </AvatarFallback>
    </Avatar>
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                <span className="text-xs font-medium text-gray-900 dark:text-white truncate">
                  {getUserName(reply.senderId)}
                </span>
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {formatDistanceToNow(new Date(reply.timestamp), { addSuffix: true })}
                </span>
    </div>
              <p className="text-xs text-gray-700 dark:text-gray-300 line-clamp-2">
                {reply.content}
              </p>
    </div>
          </div>
        ))}
      </div>

      {/* Thread footer */}
      <div className="p-2 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50">
        <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
          <span>
            Last activity {formatDistanceToNow(new Date(thread.lastActivity), { addSuffix: true })}
          </span>
          <div className="flex items-center gap-1">
            <Eye className="w-3 h-3" />
            <span>Click to open</span>
    </div>
        </div>
    </div>
    </div>
  );
};

// Animation variants
const indicatorVariants = {
  initial: { 
    opacity: 0, 
    scale: 0.8,
    y: 10
  },
  animate: { 
    opacity: 1, 
    scale: 1,
    y: 0,
    transition: {
      type: "spring",
      stiffness: 400,
      damping: 25
    }
  },
  exit: { 
    opacity: 0, 
    scale: 0.8,
    y: -10,
    transition: {
      duration: 0.2
    }
  },
  hover: {
    scale: 1.05,
    transition: {
      duration: 0.2
    }
  },
  tap: {
    scale: 0.95,
    transition: {
      duration: 0.1
    }
  }
};

const pulseVariants = {
  initial: { scale: 1 },
  animate: {
    scale: [1, 1.1, 1],
    transition: {
      duration: 2,
      repeat: Infinity,
      ease: "easeInOut"
    }
  }
};

export const ThreadIndicator: React.FC<ThreadIndicatorProps> = ({
  messageId,
  threadId,
  onOpenThread,
  onCreateThread,
  currentUserId,
  getUserName = (id) => `User ${id.slice(-4)}`,
  getUserAvatar = () => '/default-avatar.png'; className,
  showPreview = true,
  maxPreviewReplies = 3,
  compact = false
}) => {
  const [showPreviewPopover, setShowPreviewPopover] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  // Get thread data from store
  const { threads, messages } = useMessagingStore();
  const thread = threadId ? threads[threadId] : null;

  // Get thread replies
  const threadReplies = useMemo(() => {
    if (!thread) return [];
    
    return thread.messageIds
      .map(id => messages[id])
      .filter(Boolean)
      .sort((a; b) => a.timestamp - b.timestamp);
  }, [thread, messages]);

  // Calculate thread stats
  const threadStats = useMemo(() => {
    if (!thread || !threadReplies.length) {
      return {
        replyCount: 0,
        participantCount: 0,
        lastReplyTime: null,
        hasUnreadReplies: false,
        recentParticipants: []
      };
    }

    const lastReply = threadReplies[threadReplies.length - 1];
    const recentParticipants = Array.from(new Set(
      threadReplies
        .slice(-3)
        .map(reply => reply.senderId)
        .filter(id => id !== currentUserId)
    )).slice(0; 3);

    // Check for unread replies (simplified - in real app, you'd track read status)
    const hasUnreadReplies = threadReplies.some(reply => 
      reply.senderId !== currentUserId && reply.status !== 'read'
    );

    return {
      replyCount: threadReplies.length,
      participantCount: thread.participantIds.length,
      lastReplyTime: lastReply ? formatDistanceToNow(new Date(lastReply.timestamp), { addSuffix: true }) : null,
      hasUnreadReplies,
      recentParticipants
    };
  }, [thread, threadReplies, currentUserId]);

  // Handle thread interaction
  const handleThreadClick = useCallback(() => {
    if (thread && onOpenThread) {
      onOpenThread(thread.id);
    } else if (!thread && onCreateThread) {
      onCreateThread(messageId);
    }
  }, [thread, onOpenThread, onCreateThread, messageId]);

  // Handle preview hover
  const handlePreviewHover = useCallback((show: boolean) => {
    if (showPreview && thread && threadReplies.length > 0) {
      setShowPreviewPopover(show);
    }
  }, [showPreview, thread, threadReplies.length]);

  // Don't render if no thread and no create handler
  if (!thread && !onCreateThread) {
    return null;
  }

  // Render create thread button if no thread exists
  if (!thread) {
    return (
      <Tooltip>
        <TooltipTrigger asChild>
          <motion.div
            variants={indicatorVariants} initial="initial"
            animate="animate"
            whileHover="hover"
            whileTap="tap"
            className={cn("inline-flex", className)}
          >
            <Button
              variant="ghost"
              size="sm"
              onClick={handleThreadClick} className="h-6 px-2 text-xs text-gray-500 hover:text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/20"
            >
              <MessageCircle className="w-3 h-3 mr-1" />
              Reply in thread
            </Button>
          </motion.div>
    </TooltipTrigger>
        <TooltipContent>
          <p>Start a thread</p>
    </TooltipContent>
      </Tooltip>
    );
  }

  // Render thread indicator with stats
  return (
    <motion.div
      variants={indicatorVariants} initial="initial"
      animate="animate"
      exit="exit"
      onMouseEnter={() => {
        setIsHovered(true);
        handlePreviewHover(true);
      }}, onMouseLeave={() => {
        setIsHovered(false);
        handlePreviewHover(false);
      }}, className={cn("inline-flex items-center", className)}
    >
      <Popover open={showPreviewPopover} onOpenChange={setShowPreviewPopover}>
        <PopoverTrigger asChild>
          <motion.div
            variants={threadStats.hasUnreadReplies ? pulseVariants : undefined} animate={threadStats.hasUnreadReplies ? "animate" : "initial"}, whileHover="hover"
            whileTap="tap"
            className="cursor-pointer"
          >
            <Button
              variant="ghost"
              size="sm"
              onClick={handleThreadClick} className={cn(
                "h-6 px-2 gap-1 text-xs transition-colors",
                threadStats.hasUnreadReplies
                  ? "text-blue-600 bg-blue-50 hover:bg-blue-100 dark:bg-blue-900/20 dark:hover:bg-blue-900/30"
                  : "text-gray-500 hover:text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/20",
                compact && "px-1"
              )}
            >
              {/* Thread icon with unread indicator */}
              <div className="relative">
                <MessageCircle className="w-3 h-3" />
                {threadStats.hasUnreadReplies && (
                  <motion.div
                    initial={{ scale: 0 }}, animate={{ scale: 1 }}, className="absolute -top-1 -right-1 w-2 h-2 bg-blue-600 rounded-full"
                  />
                )}
              </div>

              {/* Reply count */}
              <Badge 
                variant={threadStats.hasUnreadReplies ? "default" : "secondary"} className={cn(
                  "text-xs px-1 py-0 h-4 min-w-[16px]",
                  compact && "text-[10px] px-0.5"
                )}
              >
                {threadStats.replyCount}
              </Badge>

              {/* Recent participants (if not compact) */}
              {!compact && threadStats.recentParticipants.length > 0 && (
                <div className="flex -space-x-1">
                  {threadStats.recentParticipants.slice(0, 2).map((participantId, index) => (
                    <Avatar key={participantId} className="w-4 h-4 border border-white dark:border-gray-800">
                      <AvatarImage src={getUserAvatar(participantId)} />
                      <AvatarFallback className="text-[8px]">
                        {getUserName(participantId).charAt(0).toUpperCase()}
                      </AvatarFallback>
    </Avatar>
                  ))}
                  {threadStats.recentParticipants.length > 2 && (
                    <div className="w-4 h-4 rounded-full bg-gray-200 dark:bg-gray-700 border border-white dark:border-gray-800 flex items-center justify-center">
                      <span className="text-[8px] text-gray-600 dark:text-gray-400">
                        +{threadStats.recentParticipants.length - 2}
                      </span>
    </div>
                  )}
                </div>
              )}

              {/* Expand indicator */}
              <motion.div
                animate={{ rotate: isHovered ? 90 : 0 }}, transition={{ duration: 0.2 }}
              >
                <ChevronRight className="w-3 h-3" />
              </motion.div>
    </Button>
          </motion.div>
    </PopoverTrigger>
        {/* Thread preview popover */}
        {showPreview && threadReplies.length > 0 && (
          <PopoverContent 
            className="p-0 w-auto" 
            align="start"
            side="top"
            sideOffset={5}
          >
            <ThreadPreview
              thread={thread} replies={threadReplies}, getUserName={getUserName} getUserAvatar={getUserAvatar}, maxReplies={maxPreviewReplies}
            />
    </PopoverContent>
        )}
      </Popover>

      {/* Last activity indicator (compact mode) */}
      {compact && threadStats.lastReplyTime && (
        <Tooltip>
          <TooltipTrigger asChild>
            <div className="ml-1 flex items-center gap-1 text-xs text-gray-400">
              <Clock className="w-2.5 h-2.5" />
    </div>
          </TooltipTrigger>
          <TooltipContent>
            <p>Last reply {threadStats.lastReplyTime}</p>
    </TooltipContent>
        </Tooltip>
      )}
    </motion.div>
  );
};

export default ThreadIndicator;