import React, { useState, useMemo, useCallback, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { useEnhancedMessaging } from '@/hooks/useMessaging';
import UniversalErrorBoundary from '@/components/ui/UniversalErrorBoundary';
import KeyboardNavigationProvider from './KeyboardNavigationProvider';
import MessagingSettingsModal from './MessagingSettingsModal';
import { OptimizedNotificationManager } from '../../services/OptimizedNotificationManager';
import MessagingMigrationService from '../../utils/messagingMigration';
import { Message, Conversation } from '../../types/enhanced-messaging';

// Import existing components
import ConversationPane from '../messages/ConversationPane';
import ConversationSidebar from '../messages/ConversationSidebar';

// Empty state component
const EmptyState = () => (
  <div className="flex-1 flex items-center justify-center bg-gray-50 dark:bg-gray-900">
    <div className="text-center">
      <div className="text-gray-400 mb-4">
        <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
    </svg>
      </div>
      <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
        No conversation selected
      </h3>
      <p className="text-gray-500 dark:text-gray-400">
        Choose a conversation from the sidebar to start messaging
      </p>
    </div>
  </div>
);

// Import new advanced components - these can be implemented later or use existing components as fallback
// import KeyboardNavigableConversationList from './KeyboardNavigableConversationList';
// import KeyboardNavigableMessageList from './KeyboardNavigableMessageList';

interface AdvancedMessagingContainerProps {
  enableAdvancedFeatures?: boolean;
  enableKeyboardNavigation?: boolean;
  enableNotifications?: boolean;
  migrationMode?: 'auto' | 'manual' | 'disabled';
}

const AdvancedMessagingContainer: React.FC<AdvancedMessagingContainerProps> = ({
  enableAdvancedFeatures = true,
  enableKeyboardNavigation: _enableKeyboardNavigation = true,
  enableNotifications = true,
  migrationMode = 'auto'
}) => {
  const { user } = useAuth();
  const currentUserId = user?.id || 'user-1';
  
  const {
    conversations,
    activeConversation,
    messages,
    isConnected,
    selectConversation,
    sendMessage,
    addReaction: _addReaction, markAsRead: _markAsRead
  } = useEnhancedMessaging(currentUserId);
  
  const [showSidebar, setShowSidebar] = useState(true);
  const [showSettingsModal, setShowSettingsModal] = useState(false);
  const [messagingSettings, setMessagingSettings] = useState({
    theme: 'system' as const,
    fontSize: 'medium' as const,
    enterToSend: true,
    soundEnabled: true,
    notificationPreview: true,
    activeStatus: true,
    readReceipts: true,
    autoDownloadMedia: 'wifi' as const,
    dataUsage: 'medium' as const
  });
  const [migrationStatus, setMigrationStatus] = useState<'pending' | 'in-progress' | 'completed' | 'error'>('pending');
  const [_migrationError, setMigrationError] = useState<string | null>(null);
  const [advancedConversations, setAdvancedConversations] = useState<Conversation[]>([]);
  const [_advancedMessages, setAdvancedMessages] = useState<Message[]>([]);
  const [notificationService] = useState(() => OptimizedNotificationManager.getInstance());
  const [migrationService] = useState(() => MessagingMigrationService.getInstance());

  // Use state for mobile detection to avoid direct window access during render
  const [isMobile, setIsMobile] = useState(() => window.innerWidth < 768);
  
  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize'; handleResize);
  }, []);

  // Handle migration on component mount
  useEffect(() => {
    if (migrationMode === 'disabled') {
      setMigrationStatus('completed');
      // For disabled migration, directly use the standard conversations/messages
      setAdvancedConversations(conversations);
      setAdvancedMessages(messages);
      return;
    }

    const performMigration = async () => {
      if (!migrationService.needsMigration()) {
        setMigrationStatus('completed');
        // If no migration needed, directly use conversations/messages
        setAdvancedConversations(conversations);
        setAdvancedMessages(messages);
        return;
      }

      if (migrationMode === 'manual') {
        // Wait for manual trigger
        return;
      }

      setMigrationStatus('in-progress');

      try {
        const result = await migrationService.performMigration(
          conversations as unknown as Parameters<typeof migrationService.performMigration>[0],
          messages as unknown as Parameters<typeof migrationService.performMigration>[1]
        );

        if (result.success) {
          setAdvancedConversations(result.conversations as unknown as Conversation[]);
          setAdvancedMessages(result.messages as unknown as Message[]);
          setMigrationStatus('completed');
        } else {
          setMigrationError(result.errors?.join(', ') || 'Unknown migration error');
          setMigrationStatus('error');
          // Fallback to regular conversations if migration fails
          setAdvancedConversations(conversations);
          setAdvancedMessages(messages);
        }
      } catch (error) {
        console.error('Migration failed:', error);
        const errorMessage = error instanceof Error ? error.message : 'Migration failed';
        setMigrationError(errorMessage);
        setMigrationStatus('error');
        if (window.toast) {
          window.toast.error(`Failed to migrate messaging data: ${errorMessage}`);
        }
        // Fallback to regular conversations if migration fails
        setAdvancedConversations(conversations);
        setAdvancedMessages(messages);
      }
    };

    // Only perform migration if we have conversations and migrationStatus is still pending
    if (conversations.length > 0 && migrationStatus === 'pending') {
      performMigration();
    }
  }, [conversations, messages, migrationMode, migrationStatus, migrationService]);

  // Set up notification listeners
  useEffect(() => {
    if (!enableNotifications) return;

    const handleNewMessageNotification = (message: Message, conversation: Conversation) => {
      if (message.senderId !== currentUserId) {
        notificationService.notifyNewMessage(message, conversation);
      }
    };

    const handleReactionNotification = (message: Message, conversation: Conversation, reaction: string, userId: string) => {
      if (userId !== currentUserId && message.senderId === currentUserId) {
        notificationService.notifyReaction(message, conversation, reaction, userId);
      }
    };

    // Listen for messaging events
    const handleNewMessageEvent = (event: CustomEvent) => {
      const { message, conversation } = event.detail;
      handleNewMessageNotification(message, conversation);
    };

    const handleReactionEvent = (event: CustomEvent) => {
      const { message, conversation, reaction, userId } = event.detail;
      handleReactionNotification(message, conversation, reaction, userId);
    };

    window.addEventListener('new-message', handleNewMessageEvent as EventListener);
    window.addEventListener('message-reaction', handleReactionEvent as EventListener);

    return () => {
      window.removeEventListener('new-message', handleNewMessageEvent as EventListener);
      window.removeEventListener('message-reaction', handleReactionEvent as EventListener);
    };
  }, [enableNotifications, currentUserId, notificationService]);

  // Filter online users from conversations
  const onlineUsers = useMemo(() => {
    const currentConversations = enableAdvancedFeatures ? advancedConversations : conversations;
    return currentConversations.filter(conv =>
      conv.type === 'direct' && 'user' in conv && conv.user?.isOnline
    );
  }, [conversations, advancedConversations, enableAdvancedFeatures]);

  // Handler functions
  const handleSendMessage = useCallback(async (content: string, options?: {
    replyTo?: string;
    threadId?: string;
    mentions?: string[];
    attachments?: File[];
  }) => {
    if (!activeConversation) return;

    try {
      await sendMessage(content, {
        type: 'text',
        replyTo: options?.replyTo,
        mentions: options?.mentions,
        attachments: options?.attachments
      });
    } catch (error) {
      console.error('Failed to send message:', error);
      if (window.toast) {
        window.toast.error('Failed to send message');
      }
    }
  }, [activeConversation, sendMessage]);

  const handleCloseSidebar = useCallback(() => {
    setShowSidebar(false);
  }, []);

  const handleCreateGroup = useCallback(() => {
    // Enhanced group creation with advanced features
    if (window.toast) {
      window.toast.info('Advanced group creation feature coming soon!');
    }
  }, []);



  const handleShowNotificationSettings = useCallback(() => {
    setShowSettingsModal(true);
  }, []);

  const handleStartVideoCall = useCallback((_conversationId: string) => {
    // Enhanced video calling with WebRTC
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
      if (window.toast) {
        window.toast.error('Video calls are not supported in your browser');
      }
      return;
    }

    if (window.toast) {
      window.toast.info('Advanced video call feature coming soon!');
    }
  }, []);

  const handleStartAudioCall = useCallback((_conversationId: string) => {
    // Enhanced audio calling with WebRTC
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
      if (window.toast) {
        window.toast.error('Audio calls are not supported in your browser');
      }
      return;
    }

    if (window.toast) {
      window.toast.info('Advanced audio call feature coming soon!');
    }
  }, []);



  // Memoized messaging interface to prevent unnecessary re-renders
  const messagingInterface = useMemo(() => (
    <div className="flex h-[calc(100vh-4rem)] bg-gray-50 dark:bg-gray-900">
      <ConversationSidebar
        conversations={enableAdvancedFeatures ? advancedConversations : conversations} activeConversationId={activeConversation?.id || null}, onlineUsers={onlineUsers} isConnected={isConnected}, isMobile={isMobile} showSidebar={showSidebar}, pushNotificationsEnabled={notificationService.getSettings().enabled} onSelectConversation={selectConversation}, onCloseSidebar={handleCloseSidebar} onCreateGroup={handleCreateGroup}, onShowNotificationSettings={handleShowNotificationSettings}
      />
      <div className="flex-1 flex flex-col min-w-0">
        {activeConversation ? (
          <ConversationPane
            selectedConversation={{
              ...activeConversation,
              title: activeConversation.name || 'Conversation',
              lastActivity: Date.now(),
              participants: activeConversation.participants?.map(p => typeof p === 'string' ? p : p.id) || []; createdAt: typeof activeConversation.createdAt === 'number' ? activeConversation.createdAt : Date.now(),
              updatedAt: typeof activeConversation.updatedAt === 'number' ? activeConversation.updatedAt : Date.now()
            }, as unknown as import('@/types/messaging').Conversation}, messages={messages} showConversation={true}, isMobile={isMobile} isConnected={isConnected}, onBackToList={() => selectConversation('')} onSendMessage={(content: string) => handleSendMessage(content)}, onStartVideoCall={handleStartVideoCall} onStartAudioCall={handleStartAudioCall}
          />
        ) : (
          <EmptyState />
        )}
      </div>
    </div>
  ), [
    enableAdvancedFeatures,
    advancedConversations,
    conversations,
    activeConversation,
    onlineUsers,
    isConnected,
    isMobile,
    showSidebar,
    selectConversation,
    handleCloseSidebar,
    handleCreateGroup,
    handleShowNotificationSettings,
    messages,
    handleSendMessage,
    handleStartVideoCall,
    handleStartAudioCall,
    notificationService
  ]);

  const handleSettingsChange = useCallback((newSettings: Record<string, unknown>) => {
    setMessagingSettings(prev => ({ ...prev, ...newSettings }));
    // Save to localStorage or send to service
    localStorage.setItem('messagingSettings', JSON.stringify({ ...messagingSettings, ...newSettings }));
  }, [messagingSettings]);

  return (
    <UniversalErrorBoundary>
      <KeyboardNavigationProvider>
        {messagingInterface}

        {/* Settings Modal */}
        <MessagingSettingsModal
          isOpen={showSettingsModal} onClose={() => setShowSettingsModal(false)}, currentSettings={messagingSettings} onSettingsChange={handleSettingsChange}
        />
    </KeyboardNavigationProvider>
    </UniversalErrorBoundary>
  );
};

export default AdvancedMessagingContainer;