import React from 'react';
import { AlertTriangle, Video, RefreshCw } from 'lucide-react';
import { Button } from '../ui/button';
import UniversalErrorBoundary from '../ui/UniversalErrorBoundary';

interface ReelsErrorFallbackProps {
  error?: Error;
  onRetry?: () => void;
}

const ReelsErrorFallback: React.FC<ReelsErrorFallbackProps> = ({ error, onRetry }) => {
  return (
    <div className="min-h-[400px] flex items-center justify-center p-8 bg-black text-white">
      <div className="text-center max-w-sm">
        <div className="relative mb-6">
          <Video className="w-16 h-16 mx-auto text-gray-500" />
          <AlertTriangle className="w-8 h-8 absolute -top-2 -right-2 text-red-500" />
    </div>
        <h3 className="text-lg font-semibold mb-2">
          Reels Unavailable
        </h3>
        
        <p className="text-gray-400 text-sm mb-6">
          {error?.message || 'Something went wrong while loading Reels. Please try again.'}
        </p>
        
        <div className="space-y-3">
          <Button 
            onClick={onRetry || (() => window.location.reload())} className="w-full bg-purple-600 hover:bg-purple-700"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Try Again
          </Button>
          
          <Button 
            variant="outline" 
            onClick={() => window.history.back()} className="w-full border-gray-600 text-gray-300 hover:bg-gray-800"
          >
            Go Back
          </Button>
    </div>
      </div>
    </div>
  );
};

interface ReelsErrorBoundaryProps {
  children: React.ReactNode;
  onError?: (error: Error) => void;
}

const ReelsErrorBoundary: React.FC<ReelsErrorBoundaryProps> = ({ children, onError }) => {
  const [retryKey, setRetryKey] = React.useState(0);

  const handleRetry = () => {
    setRetryKey(prev => prev + 1);
  };

  return (
    <UniversalErrorBoundary
      key={retryKey} level="reels"
      onError={onError} fallback={<ReelsErrorFallback onRetry={handleRetry} />}
    >
      {children}
    </UniversalErrorBoundary>
  );
};

export default ReelsErrorBoundary;
