import React, { useState, useEffect, useRef, CSSProperties } from 'react';
import { cn } from '@/lib/utils';
import ImageOptimizationService from '@/services/ImageOptimizationService';

interface OptimizedImageProps {
  src: string, alt: string;
  width?: number;
  height?: number;
  className?: string;
  priority?: boolean;
  quality?: number;
  fit?: 'cover' | 'contain' | 'fill' | 'inside' | 'outside';
  onLoad?: () => void;
  onError?: () => void;
  placeholder?: 'blur' | 'empty' | 'shimmer';
  aspectRatio?: number;
  sizes?: string;
  style?: CSSProperties;
}

const imageService = ImageOptimizationService.getInstance();

export const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  width,
  height,
  className,
  priority = false,
  quality = 85,
  fit = 'cover',
  onLoad,
  onError,
  placeholder = 'blur',
  aspectRatio,
  sizes,
  style
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [blurDataUrl, setBlurDataUrl] = useState<string>('');
  const imgRef = useRef<HTMLImageElement>(null);

  // Calculate aspect ratio if not provided
  const calculatedAspectRatio = aspectRatio || (width && height ? width / height : 16 / 9);

  // Generate optimized URLs
  const optimizedSrc = imageService.getOptimizedImageUrl(src, {
    width,
    height,
    quality,
    format: 'auto',
    fit
  });


  const srcSet = width ? imageService.generateSrcSet(src) : undefined;
  
  const defaultSizes = sizes || imageService.generateSizes([
    { maxWidth: 640, size: '100vw' },
    { maxWidth: 768, size: '80vw' },
    { maxWidth: 1024, size: '60vw' },
    { maxWidth: 1280, size: `${width || 800}px` }
  ]);

  // Get blur placeholder
  useEffect(() => {
    if (placeholder === 'blur' && !priority) {
      imageService.getBlurDataUrl(src, 20).then(setBlurDataUrl);
    }
  }, [src, placeholder, priority]);

  // Set up lazy loading
  useEffect(() => {
    const img = imgRef.current;
    if (!img || priority) return;

    imageService.observeImage(img);

    return () => {
      imageService.unobserveImage(img);
    };
  }, [priority]);

  const handleLoad = () => {
    setIsLoaded(true);
    setHasError(false);
    onLoad?.();
  };

  const handleError = () => {
    setHasError(true);
    setIsLoaded(true);
    onError?.();
  };

  // Placeholder styles
  const placeholderStyles: CSSProperties = {
    position: 'absolute',
    inset: 0,
    width: '100%',
    height: '100%',
    ...(!isLoaded && placeholder === 'blur' && blurDataUrl
      ? {
          backgroundImage: `url(${blurDataUrl})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          filter: 'blur(20px)',
          transform: 'scale(1.1)'
        }
      : {})
  };

  return (
    <div
      className={cn('relative overflow-hidden', className)} style={{
        aspectRatio: calculatedAspectRatio,
        ...style
      }}
    >
      {/* Placeholder */}
      {!isLoaded && (
        <div
          className={cn(
            'absolute inset-0',
            placeholder === 'shimmer' && 'animate-pulse bg-gray-200 dark:bg-gray-800',
            placeholder === 'empty' && 'bg-gray-100 dark:bg-gray-900'
          )} style={placeholder === 'blur' ? placeholderStyles : undefined}
        />
      )}

      {/* Error state */}
      {hasError && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100 dark:bg-gray-900">
          <div className="text-center">
            <svg
              className="mx-auto h-12 w-12 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
              />
    </svg>
            <p className="mt-2 text-sm text-gray-500">Failed to load image</p>
    </div>
        </div>
      )}

      {/* Main image */}
      <img
        ref={imgRef} src={optimizedSrc}, data-src={optimizedSrc} srcSet={priority ? srcSet : undefined}, data-srcset={!priority ? srcSet : undefined} sizes={defaultSizes}, alt={alt} width={width}, height={height} loading={priority ? 'eager' : 'lazy'}, fetchpriority={priority ? 'high' : 'auto'} onLoad={handleLoad}, onError={handleError} className={cn(
          'transition-opacity duration-300',
          isLoaded ? 'opacity-100' : 'opacity-0',
          hasError && 'hidden'
        )}, style={{
          objectFit: fit === 'inside' ? 'contain' : fit === 'outside' ? 'cover' : fit as React.CSSProperties['objectFit']
        }}
      />

      {/* Loading indicator for priority images */}
      {priority && !isLoaded && !hasError && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="h-8 w-8 animate-spin rounded-full border-4 border-gray-300 border-t-blue-600" />
    </div>
      )}
    </div>
  );
};

// Hooks for image optimization
export function useOptimizedImage(src: string, options?: {
  width?: number;
  height?: number;
  quality?: number;
}) {
  const [optimizedUrl, setOptimizedUrl] = useState<string>('');
  const [srcSet, setSrcSet] = useState<string>('');

  useEffect(() => {
    const service = ImageOptimizationService.getInstance();
    
    const url = service.getOptimizedImageUrl(src, {
      width: options?.width,
      height: options?.height,
      quality: options?.quality,
      format: 'auto'
    });
    
    const set = options?.width ? service.generateSrcSet(src) : '';
    
    setOptimizedUrl(url);
    setSrcSet(set);
  }, [src, options?.width, options?.height, options?.quality]);

  return { optimizedUrl, srcSet };
}

export default OptimizedImage;
