/**
 * Accessible Message Input Component
 * Fully accessible message input with keyboard navigation and screen reader support
 */

import React, { useState, useRef, useCallback, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { 
  Send, 
  Paperclip, 
  Smile, 
  Mic, 
  Image, 
  FileText,
  X,
  AlertCircle
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface AccessibleMessageInputProps {
  value: string, onChange: (value: string) => void; onSend: () => void;
  onTypingStart?: () => void;
  onTypingStop?: () => void;
  onFileUpload?: (files: FileList) => void;
  onEmojiSelect?: () => void;
  onVoiceRecord?: () => void;
  placeholder?: string;
  disabled?: boolean;
  maxLength?: number;
  showCharacterCount?: boolean;
  showAttachments?: boolean;
  showEmoji?: boolean;
  showVoiceRecord?: boolean;
  error?: string;
  className?: string;
  'aria-label'?: string;
  'aria-describedby'?: string;
}

export const AccessibleMessageInput: React.FC<AccessibleMessageInputProps> = ({
  value,
  onChange,
  onSend,
  onTypingStart,
  onTypingStop,
  onFileUpload,
  onEmojiSelect,
  onVoiceRecord,
  placeholder = 'Type a message...',
  disabled = false,
  maxLength = 2000,
  showCharacterCount = true,
  showAttachments = true,
  showEmoji = true,
  showVoiceRecord = true,
  error,
  className,
  'aria-label': ariaLabel = 'Message input',
  'aria-describedby': ariaDescribedBy
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [showAttachmentMenu, setShowAttachmentMenu] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Character count and validation
  const characterCount = value.length;
  const isOverLimit = characterCount > maxLength;
  const charactersRemaining = maxLength - characterCount;

  // Auto-resize textarea
  const adjustTextareaHeight = useCallback(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      textarea.style.height = `${Math.min(textarea.scrollHeight, 120)}px`;
    }
  }, []);

  useEffect(() => {
    adjustTextareaHeight();
  }, [value, adjustTextareaHeight]);

  // Handle input change with typing indicators
  const handleInputChange = useCallback((newValue: string) => {
    onChange(newValue);

    // Typing indicators
    if (newValue.trim() && !value.trim()) {
      onTypingStart?.();
    } else if (!newValue.trim() && value.trim()) {
      onTypingStop?.();
    }

    // Debounced typing stop
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }
    
    if (newValue.trim()) {
      typingTimeoutRef.current = setTimeout(() => {
        onTypingStop?.();
      }, 3000);
    }
  }, [value, onChange, onTypingStart, onTypingStop]);

  // Handle keyboard shortcuts
  const handleKeyDown = useCallback((event: React.KeyboardEvent) => {
    // Send message on Enter (without Shift)
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      if (value.trim() && !disabled && !isOverLimit) {
        onSend();
      }
    }

    // Escape to clear input
    if (event.key === 'Escape') {
      if (showAttachmentMenu) {
        setShowAttachmentMenu(false);
      } else if (value) {
        onChange('');
      }
    }

    // Ctrl/Cmd + A to select all
    if ((event.ctrlKey || event.metaKey) && event.key === 'a') {
      event.preventDefault();
      textareaRef.current?.select();
    }
  }, [value, disabled, isOverLimit, onSend, onChange, showAttachmentMenu]);

  // Handle file selection
  const handleFileSelect = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      onFileUpload?.(files);
      setShowAttachmentMenu(false);
    }
    // Reset input
    event.target.value = '';
  }, [onFileUpload]);

  // Handle voice recording
  const handleVoiceRecord = useCallback(() => {
    setIsRecording(!isRecording);
    onVoiceRecord?.();
  }, [isRecording, onVoiceRecord]);

  // Focus management
  const handleFocus = useCallback(() => {
    setIsFocused(true);
  }, []);

  const handleBlur = useCallback(() => {
    setIsFocused(false);
    // Stop typing indicator when losing focus
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }, onTypingStop?.();
  }, [onTypingStop]);

  // Cleanup
  useEffect(() => {
    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    };
  }, []);

  const inputId = `message-input-${Math.random().toString(36).substr(2, 9)}`;
  const errorId = error ? `${inputId}-error` : undefined;
  const countId = showCharacterCount ? `${inputId}-count` : undefined;
  const describedBy = [ariaDescribedBy, errorId, countId].filter(Boolean).join(' ');

  return (
    <div className={cn("relative", className)}>
      {/* Main input container */}
      <div
        className={cn(
          "flex items-end gap-2 p-3 border rounded-lg transition-colors",
          isFocused ? "border-blue-500 ring-2 ring-blue-200" : "border-gray-300",
          error ? "border-red-500" : "",
          disabled ? "opacity-50 cursor-not-allowed" : "",
          "bg-white dark:bg-gray-800 dark:border-gray-600"
        )}
      >
        {/* Attachment button */}
        {showAttachments && (
          <div className="relative">
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowAttachmentMenu(!showAttachmentMenu)} disabled={disabled}, className="h-8 w-8 p-0 hover:bg-gray-100 dark:hover:bg-gray-700"
                  aria-label="Attach file"
                  aria-expanded={showAttachmentMenu}, aria-haspopup="menu"
                >
                  <Paperclip className="w-4 h-4" />
    </Button>
              </TooltipTrigger>
              <TooltipContent>Attach file (Ctrl+Shift+A)</TooltipContent>
    </Tooltip>
            {/* Attachment menu */}
            <AnimatePresence>
              {showAttachmentMenu && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.95, y: 10 }}, animate={{ opacity: 1, scale: 1, y: 0 }}, exit={{ opacity: 0, scale: 0.95, y: 10 }}, className="absolute bottom-full left-0 mb-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg shadow-lg p-2 z-50"
                  role="menu"
                  aria-label="Attachment options"
                >
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => fileInputRef.current?.click()} className="w-full justify-start gap-2"
                    role="menuitem"
                  >
                    <Image className="w-4 h-4" />
                    Photo or Video
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => fileInputRef.current?.click()} className="w-full justify-start gap-2"
                    role="menuitem"
                  >
                    <FileText className="w-4 h-4" />
                    Document
                  </Button>
                </motion.div>
              )}
            </AnimatePresence>
    </div>
        )}

        {/* Text input */}
        <div className="flex-1 relative">
          <Textarea
            ref={textareaRef} id={inputId}, value={value} onChange={(e) => handleInputChange(e.target.value)}, onKeyDown={handleKeyDown} onFocus={handleFocus}, onBlur={handleBlur} placeholder={placeholder}, disabled={disabled} maxLength={maxLength}, rows={1} className={cn(
              "min-h-[40px] max-h-[120px] resize-none border-0 focus:ring-0 p-0",
              "bg-transparent placeholder:text-gray-500",
              isOverLimit ? "text-red-600" : ""
            )}, aria-label={ariaLabel}, aria-describedby={describedBy || undefined}, aria-invalid={error ? 'true' : 'false'}, aria-required="false"
          />

          {/* Character count */}
          {showCharacterCount && (
            <div
              id={countId} className={cn(
                "absolute bottom-1 right-1 text-xs",
                isOverLimit ? "text-red-500" : "text-gray-400"
              )}, aria-live="polite"
            >
              {charactersRemaining < 100 && (
                <span className={isOverLimit ? "font-semibold" : ""}>
                  {charactersRemaining}
                </span>
              )}
            </div>
          )}
        </div>

        {/* Emoji button */}
        {showEmoji && (
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={onEmojiSelect} disabled={disabled}, className="h-8 w-8 p-0 hover:bg-gray-100 dark:hover:bg-gray-700"
                aria-label="Insert emoji"
              >
                <Smile className="w-4 h-4" />
    </Button>
            </TooltipTrigger>
            <TooltipContent>Insert emoji (Ctrl+;)</TooltipContent>
    </Tooltip>
        )}

        {/* Voice record button */}
        {showVoiceRecord && (
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={handleVoiceRecord} disabled={disabled}, className={cn(
                  "h-8 w-8 p-0 hover:bg-gray-100 dark:hover:bg-gray-700",
                  isRecording ? "bg-red-100 text-red-600 dark:bg-red-900/20" : ""
                )}, aria-label={isRecording ? "Stop recording" : "Start voice recording"}, aria-pressed={isRecording}
              >
                <Mic className={cn("w-4 h-4", isRecording && "animate-pulse")} />
    </Button>
            </TooltipTrigger>
            <TooltipContent>
              {isRecording ? "Stop recording" : "Voice message (Ctrl+Shift+V)"}
            </TooltipContent>
    </Tooltip>
        )}

        {/* Send button */}
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              type="button"
              onClick={onSend} disabled={disabled || !value.trim() || isOverLimit}, size="sm"
              className={cn(
                "h-8 w-8 p-0",
                value.trim() && !isOverLimit
                  ? "bg-blue-500 hover:bg-blue-600 text-white"
                  : "bg-gray-200 text-gray-400 cursor-not-allowed"
              )}, aria-label="Send message"
            >
              <Send className="w-4 h-4" />
    </Button>
          </TooltipTrigger>
          <TooltipContent>Send message (Enter)</TooltipContent>
    </Tooltip>
      </div>

      {/* Error message */}
      {error && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}, animate={{ opacity: 1, height: 'auto' }}, exit={{ opacity: 0, height: 0 }}, id={errorId} className="flex items-center gap-2 mt-2 text-sm text-red-600"
          role="alert"
          aria-live="polite"
        >
          <AlertCircle className="w-4 h-4 flex-shrink-0" />
          <span>{error}</span>
        </motion.div>
      )}

      {/* Hidden file input */}
      <input
        ref={fileInputRef} type="file"
        multiple
        accept="image/*,video/*,.pdf,.doc,.docx,.txt"
        onChange={handleFileSelect} className="hidden"
        aria-hidden="true"
      />

      {/* Recording indicator */}
      <AnimatePresence>
        {isRecording && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}, animate={{ opacity: 1, y: 0 }}, exit={{ opacity: 0, y: 10 }}, className="absolute top-0 left-0 right-0 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-2 flex items-center justify-between"
            role="status"
            aria-live="polite"
          >
            <div className="flex items-center gap-2 text-red-600">
              <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse" />
              <span className="text-sm font-medium">Recording voice message...</span>
    </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsRecording(false)} className="h-6 w-6 p-0 text-red-600 hover:bg-red-100"
              aria-label="Cancel recording"
            >
              <X className="w-4 h-4" />
    </Button>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Keyboard shortcuts help */}
      <div className="sr-only" aria-live="polite">
        Keyboard shortcuts: Enter to send, Shift+Enter for new line, Escape to clear, Ctrl+A to select all
      </div>
    </div>
  );
};

export default AccessibleMessageInput;