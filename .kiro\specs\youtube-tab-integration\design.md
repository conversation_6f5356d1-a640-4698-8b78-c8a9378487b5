# Design Document

## Overview

The YouTube tab integration will enhance and complete the existing YouTube2 functionality already present in the Facebook clone application. The current implementation includes basic YouTube pages (Home, Watch, Search, Trending, Shorts, Subscriptions, Library, History, and Playlist) with video and playlist contexts.

This design will expand the existing foundation by:
1. Migrating and adapting components from the external YouTube clone (ytmain-v6)
2. Enhancing the current YouTube2 implementation with missing features
3. Ensuring complete visual consistency with Facebook's design system
4. Adding advanced features like Creator Studio, Live Streaming, and Analytics

The integration builds upon the existing modular architecture while adding comprehensive YouTube functionality including content creation, channel management, and creator tools.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    A[Facebook Clone App] --> B[App Router]
    B --> C[YouTube Route Handler]
    C --> D[YouTube Layout Wrapper]
    D --> E[YouTube Components]
    
    E --> F[Video Player]
    E --> G[Channel Management]
    E --> H[Creator Studio]
    E --> I[Live Streaming]
    E --> J[Search & Discovery]
    
    K[Shared Services] --> L[Theme Provider]
    K --> M[Auth Context]
    K --> N[Notification System]
    K --> O[Performance Monitoring]
    
    L --> D
    M --> D
    N --> D
    O --> D
```

### Integration Strategy

The YouTube functionality will be enhanced using a **Migration and Enhancement** approach:

1. **Current State**: YouTube2 is already integrated with route prefix `/youtube2/*` and includes basic pages
2. **Migration Path**: Gradually migrate from `youtube2` to `youtube` while preserving existing functionality
3. **Component Enhancement**: Enhance existing YouTube2 components with features from ytmain-v6
4. **Layout Preservation**: Continue using Facebook's `AppLayout` component for consistency
5. **Service Integration**: Extend existing VideoContext and PlaylistContext with additional services

### Current File Structure (Existing)

```
src/
├── pages/
│   ├── YouTube2.tsx (Main router component)
│   └── youtube2/
│       ├── HomePage.tsx ✓
│       ├── WatchPage.tsx ✓
│       ├── SearchResultsPage.tsx ✓
│       ├── TrendingPage.tsx ✓
│       ├── ShortsPage.tsx ✓
│       ├── SubscriptionsPage.tsx ✓
│       ├── LibraryPage.tsx ✓
│       ├── HistoryPage.tsx ✓
│       └── PlaylistPage.tsx ✓
├── components/
│   └── youtube2/
│       ├── AddToPlaylistButton.tsx ✓
│       ├── EnhancedVideoPlayer.tsx ✓
│       ├── VideoUploadModal.tsx ✓
│       └── utils.ts ✓
├── contexts/
│   ├── VideoContext.tsx ✓
│   └── PlaylistContext.tsx ✓
```

### Target File Structure (Enhanced)

```
src/
├── pages/
│   ├── YouTube.tsx (Renamed from YouTube2.tsx)
│   └── youtube/ (Renamed from youtube2/)
│       ├── HomePage.tsx (Enhanced)
│       ├── WatchPage.tsx (Enhanced)
│       ├── SearchResultsPage.tsx (Enhanced)
│       ├── TrendingPage.tsx (Enhanced)
│       ├── ShortsPage.tsx (Enhanced)
│       ├── SubscriptionsPage.tsx (Enhanced)
│       ├── LibraryPage.tsx (Enhanced)
│       ├── HistoryPage.tsx (Enhanced)
│       ├── PlaylistPage.tsx (Enhanced)
│       ├── ChannelPage.tsx (New)
│       ├── StudioPage.tsx (New)
│       ├── AnalyticsPage.tsx (New)
│       ├── LiveStreamPage.tsx (New)
│       └── UploadPage.tsx (New)
├── components/
│   └── youtube/ (Renamed from youtube2/)
│       ├── layout/
│       │   ├── YouTubeSidebar.tsx (New)
│       │   └── YouTubeHeader.tsx (New)
│       ├── video/
│       │   ├── EnhancedVideoPlayer.tsx (Enhanced)
│       │   ├── VideoCard.tsx (New)
│       │   ├── VideoGrid.tsx (New)
│       │   ├── VideoDetails.tsx (New)
│       │   └── VideoRecommendations.tsx (New)
│       ├── channel/
│       │   ├── ChannelHeader.tsx (New)
│       │   ├── ChannelTabs.tsx (New)
│       │   └── ChannelContent.tsx (New)
│       ├── studio/
│       │   ├── StudioDashboard.tsx (New)
│       │   ├── VideoUpload.tsx (Enhanced from VideoUploadModal)
│       │   ├── Analytics.tsx (New)
│       │   └── ContentManager.tsx (New)
│       ├── shared/
│       │   ├── CategoryChips.tsx (New)
│       │   ├── SearchBar.tsx (New)
│       │   ├── AddToPlaylistButton.tsx (Moved)
│       │   └── utils.ts (Enhanced)
│       └── live/
│           ├── LiveStreamSetup.tsx (New)
│           ├── LiveStreamPlayer.tsx (New)
│           └── LiveChat.tsx (New)
├── services/
│   └── youtube/
│       ├── videoService.ts (New)
│       ├── channelService.ts (New)
│       ├── searchService.ts (New)
│       ├── analyticsService.ts (New)
│       ├── liveStreamService.ts (New)
│       └── uploadService.ts (New)
├── hooks/
│   └── youtube/
│       ├── useVideoPlayer.ts (New)
│       ├── useChannelData.ts (New)
│       ├── useVideoSearch.ts (New)
│       ├── useLiveStream.ts (New)
│       └── useAnalytics.ts (New)
├── types/
│   └── youtube/
│       ├── video.ts (New)
│       ├── channel.ts (New)
│       ├── user.ts (New)
│       ├── analytics.ts (New)
│       └── livestream.ts (New)
└── contexts/
    ├── VideoContext.tsx (Enhanced)
    ├── PlaylistContext.tsx (Enhanced)
    ├── ChannelContext.tsx (New)
    └── YouTubeAuthContext.tsx (New)
```

## Components and Interfaces

### Core Components

#### 1. YouTubeLayout Component

```typescript
interface YouTubeLayoutProps {
  children: React.ReactNode;
  showYouTubeSidebar?: boolean;
  pageTitle?: string;
}

const YouTubeLayout: React.FC<YouTubeLayoutProps> = ({
  children,
  showYouTubeSidebar = true,
  pageTitle
}) => {
  // Wraps content in Facebook's AppLayout while providing YouTube-specific navigation
}
```

#### 2. VideoPlayer Component

```typescript
interface VideoPlayerProps {
  videoId: string;
  autoplay?: boolean;
  controls?: boolean;
  onTimeUpdate?: (currentTime: number) => void;
  onEnded?: () => void;
  className?: string;
}

const VideoPlayer: React.FC<VideoPlayerProps> = ({
  videoId,
  autoplay = false,
  controls = true,
  onTimeUpdate,
  onEnded,
  className
}) => {
  // Implements video playback with Facebook-styled controls
}
```

#### 3. YouTubeSidebar Component

```typescript
interface YouTubeSidebarProps {
  collapsed?: boolean;
  onNavigate?: (path: string) => void;
}

const YouTubeSidebar: React.FC<YouTubeSidebarProps> = ({
  collapsed = false,
  onNavigate
}) => {
  // YouTube-specific navigation using Facebook's sidebar styling
}
```

### Service Interfaces

#### Video Service

```typescript
interface VideoService {
  getTrendingVideos(category?: string): Promise<Video[]>;
  getVideoById(id: string): Promise<Video>;
  searchVideos(query: string, filters?: SearchFilters): Promise<Video[]>;
  getRelatedVideos(videoId: string): Promise<Video[]>;
  updateVideoMetrics(videoId: string, action: 'view' | 'like' | 'dislike'): Promise<void>;
}
```

#### Channel Service

```typescript
interface ChannelService {
  getChannelById(id: string): Promise<Channel>;
  getChannelVideos(channelId: string, options?: PaginationOptions): Promise<Video[]>;
  subscribeToChannel(channelId: string): Promise<void>;
  unsubscribeFromChannel(channelId: string): Promise<void>;
  getSubscriptions(): Promise<Channel[]>;
}
```

## Data Models

### Video Model

```typescript
interface Video {
  id: string;
  title: string;
  description: string;
  thumbnailUrl: string;
  videoUrl: string;
  duration: number;
  viewCount: number;
  likeCount: number;
  dislikeCount: number;
  uploadDate: Date;
  category: string;
  tags: string[];
  channel: {
    id: string;
    name: string;
    avatarUrl: string;
    subscriberCount: number;
  };
  isLive?: boolean;
  isShort?: boolean;
  quality: VideoQuality[];
}

interface VideoQuality {
  resolution: string;
  url: string;
  bitrate: number;
}
```

### Channel Model

```typescript
interface Channel {
  id: string;
  name: string;
  description: string;
  avatarUrl: string;
  bannerUrl: string;
  subscriberCount: number;
  videoCount: number;
  joinDate: Date;
  isVerified: boolean;
  socialLinks: SocialLink[];
  playlists: Playlist[];
}

interface SocialLink {
  platform: string;
  url: string;
}
```

### User Model (Extended)

```typescript
interface YouTubeUser extends User {
  youtube: {
    subscriptions: string[]; // Channel IDs
    watchHistory: WatchHistoryItem[];
    likedVideos: string[]; // Video IDs
    playlists: Playlist[];
    uploadedVideos: string[]; // Video IDs
    channelId?: string;
  };
}

interface WatchHistoryItem {
  videoId: string;
  watchedAt: Date;
  watchTime: number; // seconds watched
  completed: boolean;
}
```

## Error Handling

### Error Boundary Strategy

```typescript
interface YouTubeErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

class YouTubeErrorBoundary extends Component<
  { children: ReactNode },
  YouTubeErrorBoundaryState
> {
  // Handles YouTube-specific errors while maintaining Facebook's error UI
}
```

### Error Types

```typescript
enum YouTubeErrorType {
  VIDEO_NOT_FOUND = 'VIDEO_NOT_FOUND',
  CHANNEL_NOT_FOUND = 'CHANNEL_NOT_FOUND',
  PLAYBACK_ERROR = 'PLAYBACK_ERROR',
  UPLOAD_ERROR = 'UPLOAD_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR',
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR'
}

interface YouTubeError extends Error {
  type: YouTubeErrorType;
  videoId?: string;
  channelId?: string;
  retryable: boolean;
}
```

## Testing Strategy

### Component Testing

```typescript
// Example test structure
describe('VideoPlayer Component', () => {
  it('should render with Facebook theme styling', () => {
    // Test Facebook theme integration
  });
  
  it('should handle video playback controls', () => {
    // Test video functionality
  });
  
  it('should integrate with Facebook notification system', () => {
    // Test notification integration
  });
});
```

### Integration Testing

```typescript
describe('YouTube Route Integration', () => {
  it('should maintain Facebook layout structure', () => {
    // Test layout preservation
  });
  
  it('should handle navigation between Facebook and YouTube', () => {
    // Test cross-navigation
  });
  
  it('should preserve authentication state', () => {
    // Test auth integration
  });
});
```

### Performance Testing

- **Bundle Size**: Ensure YouTube integration doesn't increase bundle size by more than 30%
- **Load Time**: YouTube pages should load within 3 seconds
- **Memory Usage**: Monitor memory consumption during video playback
- **Scroll Performance**: Maintain 60fps during infinite scroll

## Theme Integration

### Color Scheme Mapping

```typescript
const youtubeThemeMapping = {
  // Map YouTube colors to Facebook theme
  primary: 'var(--fb-primary)', // Facebook blue
  secondary: 'var(--fb-secondary)',
  background: 'var(--fb-background)',
  surface: 'var(--fb-surface)',
  text: 'var(--fb-text)',
  textSecondary: 'var(--fb-text-secondary)',
  border: 'var(--fb-border)',
  hover: 'var(--fb-hover)',
  accent: 'var(--fb-accent)'
};
```

### Component Styling

```scss
// YouTube components will use Facebook's design tokens
.youtube-video-card {
  @apply bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700;
  // Matches Facebook's card styling
}

.youtube-button {
  @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg;
  // Uses Facebook's button styling
}
```

## Performance Optimizations

### Code Splitting

```typescript
// Lazy load YouTube components
const YouTubeHome = lazy(() => import('@/pages/youtube/YouTubeHome'));
const YouTubeWatch = lazy(() => import('@/pages/youtube/YouTubeWatch'));
const YouTubeStudio = lazy(() => import('@/pages/youtube/YouTubeStudio'));

// Route-based code splitting
const youtubeRoutes = [
  {
    path: '/youtube',
    element: <Suspense fallback={<YouTubeLoadingSkeleton />}><YouTubeHome /></Suspense>
  },
  // ... other routes
];
```

### Caching Strategy

```typescript
// Video data caching
const videoCache = new Map<string, Video>();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

const getCachedVideo = (videoId: string): Video | null => {
  const cached = videoCache.get(videoId);
  if (cached && Date.now() - cached.cachedAt < CACHE_DURATION) {
    return cached;
  }
  return null;
};
```

### Virtual Scrolling

```typescript
// Implement virtual scrolling for video grids
const VirtualizedVideoGrid: React.FC<{
  videos: Video[];
  itemHeight: number;
  containerHeight: number;
}> = ({ videos, itemHeight, containerHeight }) => {
  // Use react-window for efficient rendering
};
```

## Security Considerations

### Content Security Policy

```typescript
// CSP headers for video content
const cspDirectives = {
  'media-src': ['self', '*.youtube.com', '*.googlevideo.com'],
  'img-src': ['self', '*.ytimg.com', 'data:'],
  'script-src': ['self', '*.youtube.com'],
  'frame-src': ['self', '*.youtube.com']
};
```

### Input Validation

```typescript
// Validate video IDs and user inputs
const validateVideoId = (videoId: string): boolean => {
  return /^[a-zA-Z0-9_-]{11}$/.test(videoId);
};

const sanitizeSearchQuery = (query: string): string => {
  return query.replace(/[<>\"']/g, '').trim().substring(0, 100);
};
```

## Accessibility

### ARIA Labels and Roles

```typescript
// Video player accessibility
<div
  role="application"
  aria-label="Video player"
  aria-describedby="video-description"
>
  <video
    aria-label={`Playing: ${videoTitle}`}
    controls
    preload="metadata"
  />
</div>
```

### Keyboard Navigation

```typescript
// Implement keyboard shortcuts
const useKeyboardShortcuts = () => {
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      switch (event.key) {
        case ' ': // Space - play/pause
          event.preventDefault();
          togglePlayPause();
          break;
        case 'f': // F - fullscreen
          toggleFullscreen();
          break;
        case 'm': // M - mute
          toggleMute();
          break;
      }
    };
    
    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, []);
};
```

## Migration Strategy

### Current State Assessment
- ✅ Basic routing structure exists (`/youtube2/*`)
- ✅ Core pages implemented (Home, Watch, Search, Trending, Shorts, etc.)
- ✅ Basic video and playlist contexts
- ✅ Some YouTube components (VideoPlayer, VideoUploadModal, AddToPlaylistButton)
- ❌ Missing Creator Studio functionality
- ❌ Missing channel management
- ❌ Missing live streaming
- ❌ Missing analytics
- ❌ Limited theme integration with Facebook design system

### Phase 1: Foundation Enhancement (Week 1-2)
- Migrate from `/youtube2` to `/youtube` routes
- Enhance existing VideoContext and PlaylistContext
- Improve theme integration with Facebook design tokens
- Add missing TypeScript interfaces and types
- Implement comprehensive error handling

### Phase 2: Component Migration (Week 3-4)
- Migrate key components from ytmain-v6 project
- Enhance existing video player with advanced features
- Implement video grid and card components
- Add category chips and improved search functionality
- Create YouTube-specific sidebar navigation

### Phase 3: Channel & Content Management (Week 5-6)
- Implement channel pages and channel management
- Add subscription functionality
- Create content management tools
- Implement video upload enhancements
- Add comment system integration

### Phase 4: Creator Studio (Week 7-8)
- Implement YouTube Studio dashboard
- Add analytics and metrics tracking
- Create content manager interface
- Implement video editing tools
- Add monetization features

### Phase 5: Advanced Features (Week 9-10)
- Implement live streaming functionality
- Add advanced search and discovery
- Create recommendation engine
- Implement YouTube Shorts enhancements
- Add community features

### Phase 6: Integration & Polish (Week 11-12)
- Complete Facebook theme integration
- Performance optimization and bundle analysis
- Accessibility improvements
- Mobile responsiveness enhancements
- Testing and bug fixes

### Migration Checklist

#### Immediate Tasks
- [ ] Update route constants from `YOUTUBE2` to `YOUTUBE`
- [ ] Rename `YouTube2.tsx` to `YouTube.tsx`
- [ ] Rename `youtube2/` directory to `youtube/`
- [ ] Update all import paths
- [ ] Add YouTube tab to main navigation sidebar

#### Component Enhancement Tasks
- [ ] Enhance existing video player with ytmain-v6 features
- [ ] Migrate CategoryChips component
- [ ] Migrate HoverAutoplayVideoCard component
- [ ] Implement YouTube-specific layout components
- [ ] Add comprehensive video metadata support

#### New Feature Tasks
- [ ] Implement channel pages
- [ ] Add Creator Studio functionality
- [ ] Implement live streaming
- [ ] Add analytics dashboard
- [ ] Create advanced search features