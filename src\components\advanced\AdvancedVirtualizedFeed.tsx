import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { VariableSizeList, ListChildComponentProps } from 'react-window';
import AutoSizer from 'react-virtualized-auto-sizer';
import { motion } from 'framer-motion';
import { Heart, MessageCircle, Share, Bookmark, MoreHorizontal, Play } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui';
import { Badge } from '@/components/ui/badge';

interface PostData {
  id: string, author: {
    id: string, name: string, username: string, avatar: string, isVerified: boolean;
  };
  content: string;
  mediaUrls?: string[];
  timestamp: Date, metrics: {
    likes: number, comments: number, shares: number;
    views?: number;
  };
  userInteractions: {
    hasLiked: boolean, hasShared: boolean, hasBookmarked: boolean;
  };
  tags: string[];
  isPromoted?: boolean;
  category: string, type: 'text' | 'image' | 'video' | 'poll' | 'event';
  estimatedHeight?: number;
}

interface AdvancedVirtualizedFeedProps {
  posts: PostData[];
  onPostInteraction?: (postId: string, action: string) => void;
  onLoadMore?: () => void;
  isLoading?: boolean;
  className?: string;
}

interface ItemData {
  posts: PostData[];
  onPostInteraction?: (postId: string, action: string) => void; itemHeights: Map<number, number>;
  setItemHeight: (index: number, height: number) => void;
}

// Dynamic post renderer with height measurement
const PostItem: React.FC<ListChildComponentProps<ItemData>> = ({ 
  index, 
  style, 
  data 
}) => {
  const { posts, onPostInteraction, setItemHeight } = data;
  const post = posts[index];
  const itemRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (itemRef.current) {
      const height = itemRef.current.getBoundingClientRect().height;
      setItemHeight(index, height);
    }
  }, [index, setItemHeight, post]);

  const handleInteraction = useCallback((action: string) => {
    onPostInteraction?.(post.id, action);
  }, [post.id, onPostInteraction]);

  const formatTimestamp = useCallback((timestamp: Date) => {
    const now = new Date();
    const diff = now.getTime() - timestamp.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return 'now';
    if (minutes < 60) return `${minutes}m`;
    if (hours < 24) return `${hours}h`;
    return `${days}d`;
  }, []);

  return (
    <div style={style}>
      <motion.div
        ref={itemRef}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: index * 0.02 }}
        className="p-2"
      >
        <Card className="overflow-hidden hover:shadow-md transition-shadow">
          <CardContent className="p-4">
            {/* Post Header */}
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center gap-3">
                <Avatar className="h-10 w-10">
                  <AvatarImage src={post.author.avatar} alt={post.author.name} />
                  <AvatarFallback>{post.author.name.slice(0, 2)}</AvatarFallback>
    </Avatar>
                <div>
                  <div className="flex items-center gap-2">
                    <span className="font-semibold text-sm">{post.author.name}</span>
                    {post.author.isVerified && (
                      <Badge variant="secondary" className="text-xs px-1 py-0">✓</Badge>
                    )}
                    {post.isPromoted && (
                      <Badge variant="outline" className="text-xs px-1 py-0">Promoted</Badge>
                    )}
                  </div>
                  <div className="flex items-center gap-2 text-xs text-muted-foreground">
                    <span>@{post.author.username}</span>
                    <span>•</span>
                    <span>{formatTimestamp(post.timestamp)}</span>
                    <span>•</span>
                    <span className="capitalize">{post.category}</span>
    </div>
                </div>
    </div>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
    </Button>
            </div>

            {/* Post Content */}
            <div className="mb-3">
              <p className="text-sm leading-relaxed">
                {post.content}
              </p>
              
              {/* Tags */}
              {post.tags.length > 0 && (
                <div className="flex flex-wrap gap-1 mt-2">
                  {post.tags.slice(0, 3).map((tag, i) => (
                    <span key={i} className="text-blue-600 text-xs hover:underline cursor-pointer">
                      #{tag}
                    </span>
                  ))}
                  {post.tags.length > 3 && (
                    <span className="text-xs text-muted-foreground">
                      +{post.tags.length - 3} more
                    </span>
                  )}
                </div>
              )}
            </div>

            {/* Media Content */}
            {post.mediaUrls && post.mediaUrls.length > 0 && (
              <div className="mb-3 rounded-lg overflow-hidden bg-muted">
                {post.type === 'video' ? (
                  <div className="relative aspect-video bg-black flex items-center justify-center">
                    <Button variant="ghost" size="lg" className="text-white">
                      <Play className="h-8 w-8" />
    </Button>
                    <div className="absolute bottom-2 right-2">
                      <Badge variant="secondary" className="text-xs">
                        {Math.floor(Math.random() * 5) + 1}:30
                      </Badge>
    </div>
                  </div>
                ) : (
                  <div className="grid grid-cols-2 gap-1">
                    {post.mediaUrls.slice(0, 4).map((url, i) => (
                      <img
                        key={i}
                        src={url}
                        alt="Post media"
                        className="w-full h-24 object-cover"
                        loading="lazy"
                      />
                    ))}
                  </div>
                )}
              </div>
            )}

            {/* Special Post Types */}
            {post.type === 'poll' && (
              <div className="mb-3 p-3 bg-muted rounded-lg">
                <p className="text-sm font-medium mb-2">Quick Poll</p>
                <div className="space-y-2">
                  <div className="flex justify-between items-center p-2 bg-background rounded border">
                    <span className="text-sm">Option A</span>
                    <span className="text-xs text-muted-foreground">45%</span>
    </div>
                  <div className="flex justify-between items-center p-2 bg-background rounded border">
                    <span className="text-sm">Option B</span>
                    <span className="text-xs text-muted-foreground">55%</span>
    </div>
                </div>
    </div>
            )}

            {post.type === 'event' && (
              <div className="mb-3 p-3 bg-blue-50 rounded-lg border border-blue-200">
                <div className="flex items-center gap-2 mb-1">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span className="text-sm font-medium text-blue-700">Event</span>
    </div>
                <p className="text-sm text-blue-600">Tech Conference 2024</p>
                <p className="text-xs text-blue-500">Tomorrow at 9:00 AM</p>
    </div>
            )}

            {/* Post Actions */}
            <div className="flex items-center justify-between pt-2 border-t">
              <div className="flex items-center gap-4">
                <Button
                  variant="ghost"
                  size="sm"
                  className={`gap-2 h-8 ${post.userInteractions.hasLiked ? 'text-red-500' : ''}`}
                  onClick={() => handleInteraction('like')}
                >
                  <Heart className={`h-4 w-4 ${post.userInteractions.hasLiked ? 'fill-current' : ''}`} />
                  <span className="text-xs">{post.metrics.likes}</span>
    </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="gap-2 h-8"
                  onClick={() => handleInteraction('comment')}
                >
                  <MessageCircle className="h-4 w-4" />
                  <span className="text-xs">{post.metrics.comments}</span>
    </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className={`gap-2 h-8 ${post.userInteractions.hasShared ? 'text-green-500' : ''}`}
                  onClick={() => handleInteraction('share')}
                >
                  <Share className="h-4 w-4" />
                  <span className="text-xs">{post.metrics.shares}</span>
    </Button>
              </div>
              
              <Button
                variant="ghost"
                size="sm"
                className={`h-8 w-8 p-0 ${post.userInteractions.hasBookmarked ? 'text-blue-500' : ''}`}
                onClick={() => handleInteraction('bookmark')}
              >
                <Bookmark className={`h-4 w-4 ${post.userInteractions.hasBookmarked ? 'fill-current' : ''}`} />
    </Button>
            </div>

            {/* View Count for promoted posts */}
            {post.metrics.views && post.isPromoted && (
              <div className="mt-2 pt-2 border-t">
                <span className="text-xs text-muted-foreground">
                  {post.metrics.views.toLocaleString()} views
                </span>
    </div>
            )}
          </CardContent>
    </Card>
      </motion.div>
    </div>
  );
};

const AdvancedVirtualizedFeed: React.FC<AdvancedVirtualizedFeedProps> = ({
  posts,
  onPostInteraction,
  onLoadMore,
  isLoading = false,
  className = ''
}) => {
  const [itemHeights, setItemHeights] = useState<Map<number, number>>(new Map());
  const listRef = useRef<VariableSizeList>(null);

  // Estimate initial heights based on post type
  const estimateItemHeight = useCallback((index: number) => {
    const post = posts[index];
    if (!post) return 200;

    let height = 140; // Base height
    
    // Content length factor
    height += Math.min(Math.ceil(post.content.length / 60) * 20, 80);
    
    // Media factor
    if (post.mediaUrls?.length) {
      height += post.type === 'video' ? 200 : 150;
    }
    
    // Special types
    if (post.type === 'poll') height += 80;
    if (post.type === 'event') height += 60;
    
    return height;
  }, [posts]);

  const getItemHeight = useCallback((index: number) => {
    return itemHeights.get(index) || estimateItemHeight(index);
  }, [itemHeights, estimateItemHeight]);

  const setItemHeight = useCallback((index: number, height: number) => {
    setItemHeights(prev => {
      const newMap = new Map(prev);
      if (newMap.get(index) !== height) {
        newMap.set(index, height);
        // Defer list cache reset to avoid render during state update
        queueMicrotask(() => {
          if (listRef.current) {
            listRef.current.resetAfterIndex(index);
          }
        });
      }
      return newMap;
    });
  }, []);

  const itemData: ItemData = useMemo(() => ({
    posts,
    onPostInteraction,
    itemHeights,
    setItemHeight
  }), [posts, onPostInteraction, itemHeights, setItemHeight]);

  // Handle infinite scroll
  const handleItemsRendered = useCallback(({
    visibleStopIndex
  }: {
    visibleStartIndex: number, visibleStopIndex: number;
  }) => {
    // Load more when near the end
    if (
      visibleStopIndex >= posts.length - 5 && 
      onLoadMore && 
      !isLoading
    ) {
      onLoadMore();
    }
  }, [posts.length, onLoadMore, isLoading]);

  // Performance optimizations
  useEffect(() => {
    // Preload visible range heights
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const element = entry.target as HTMLElement;
            const index = parseInt(element.dataset.index || '0');
            const height = element.getBoundingClientRect().height;
            setItemHeight(index, height);
          }
        });
      },
      { threshold: 0.1 }
    );

    return () => observer.disconnect();
  }, [setItemHeight]);

  return (
    <div className={`h-full ${className}`}>
      <AutoSizer>
        {({ height, width }) => (
          <VariableSizeList
            ref={listRef}
            height={height}
            width={width}
            itemCount={posts.length}
            itemSize={getItemHeight}
            itemData={itemData}
            onItemsRendered={handleItemsRendered}
            overscanCount={5}
            estimatedItemSize={200}
          >
            {PostItem}
          </VariableSizeList>
        )}
      </AutoSizer>
      
      {/* Loading indicator */}
      {isLoading && (
        <div className="flex justify-center p-4">
          <div className="animate-spin h-6 w-6 border-2 border-primary border-t-transparent rounded-full"></div>
    </div>
      )}
    </div>
  );
};

export default AdvancedVirtualizedFeed;