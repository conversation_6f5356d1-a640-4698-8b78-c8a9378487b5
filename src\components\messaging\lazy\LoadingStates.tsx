/**
 * Loading States for Lazy Messaging Components
 * Optimized skeleton screens and loading indicators
 */

import React from 'react';
import { motion } from 'framer-motion';
import { Loader2, MessageSquare, Phone, Settings, Shield, Search } from 'lucide-react';
import { cn } from '@/lib/utils';
import { SIZE_CONFIG, SizeVariant } from '../constants';

interface LoadingStateProps {
  className?: string;
  size?: SizeVariant;
  animated?: boolean;
  description?: string;
}

// Base skeleton component
const Skeleton: React.FC<{
  className?: string;
  animated?: boolean;
}> = ({ className, animated = true }) => (
  <div
    className={cn(
      "bg-gray-200 dark:bg-gray-700 rounded",
      animated && "animate-pulse",
      className
    )}
  />
);

// Message bubble skeleton
export const MessageBubbleSkeleton: React.FC<LoadingStateProps> = ({
  className,
  size = 'md',
  animated = true
}) => {
  const sizeConfig = SIZE_CONFIG[size];
  
  return (
    <div className={cn("flex gap-3 p-4", className)}>
      {/* Avatar */}
      <Skeleton 
        className={cn(sizeConfig.avatar, "rounded-full")} animated={animated}
      />
      
      {/* Message content */}
      <div className="flex-1 space-y-2">
        {/* Sender name */}
        <Skeleton 
          className="h-4 w-20" 
          animated={animated}
        />
        
        {/* Message bubble */}
        <div className="space-y-2">
          <Skeleton 
            className="h-6 w-3/4" 
            animated={animated}
          />
          <Skeleton 
            className="h-6 w-1/2" 
            animated={animated}
          />
    </div>
        {/* Timestamp */}
        <Skeleton 
          className="h-3 w-16" 
          animated={animated}
        />
    </div>
    </div>
  );
};

// Message list skeleton
export const MessageListSkeleton: React.FC<{
  count?: number;
  className?: string;
  animated?: boolean;
}> = ({ count = 5, className, animated = true }) => (
  <div className={cn("space-y-4", className)}>
    {Array.from({ length: count }, (_, i) => (
      <MessageBubbleSkeleton 
        key={i} animated={animated}, className={i % 2 === 0 ? "flex-row-reverse" : ""}
      />
    ))}
  </div>
);

// Typing indicator skeleton
export const TypingIndicatorSkeleton: React.FC<LoadingStateProps> = ({
  className,
  animated = true
}) => (
  <div className={cn("flex items-center gap-2 px-4 py-2", className)}>
    <Skeleton 
      className="w-6 h-6 rounded-full" 
      animated={animated}
    />
    <div className="flex gap-1">
      {[0, 1, 2].map(i => (
        <motion.div
          key={i} className="w-2 h-2 bg-gray-400 rounded-full"
          animate={animated ? {
            scale: [1, 1.2, 1],
            opacity: [0.5, 1, 0.5]
          } : {}}, transition={{
            duration: 1.4,
            repeat: Infinity,
            delay: i * 0.2,
            ease: "easeInOut"
          }}
        />
      ))}
    </div>
    </div>
);

// Connection status skeleton
export const ConnectionStatusSkeleton: React.FC<LoadingStateProps> = ({
  className,
  animated = true
}) => (
  <div className={cn("flex items-center gap-2 px-3 py-2 rounded-lg border", className)}>
    <Skeleton 
      className="w-4 h-4 rounded" 
      animated={animated}
    />
    <Skeleton 
      className="h-4 w-20" 
      animated={animated}
    />
    </div>
);

// Generic component loading state
export const ComponentLoadingSkeleton: React.FC<{
  icon?: React.ReactNode;
  title?: string;
  description?: string;
  className?: string;
  animated?: boolean;
}> = ({
  icon = <MessageSquare className="w-8 h-8" />
        title = "Loading component...",
  description,
  className,
  animated = true
}) => (
  <div className={cn(
    "flex flex-col items-center justify-center p-8 text-center",
    className
  )}>
    <motion.div
      className="mb-4 text-gray-400"
      animate={animated ? {
        scale: [1, 1.1, 1],
        opacity: [0.5, 1, 0.5]
      } : {}}, transition={{
        duration: 2,
        repeat: Infinity,
        ease: "easeInOut"
      }}
    >
      {icon}
    </motion.div>
    
    <div className="space-y-2">
      <div className="text-sm font-medium text-gray-600 dark:text-gray-400">
        {title}
      </div>
      {description && (
        <div className="text-xs text-gray-500">
          {description}
        </div>
      )}
    </div>
    </div>
);

// Specific component loading states
export const MessageReactionsLoading: React.FC<LoadingStateProps> = (props) => (
  <ComponentLoadingSkeleton
    icon={<span className="text-2xl">😊</span>} title="Loading reactions..."
    {...props}
  />
);

export const CallInterfaceLoading: React.FC<LoadingStateProps> = (props) => (
  <ComponentLoadingSkeleton
    icon={<Phone className="w-8 h-8" />} title="Starting call..."
    description="Initializing audio/video components"
    {...props}
  />
);

export const SecurityDashboardLoading: React.FC<LoadingStateProps> = (props) => (
  <ComponentLoadingSkeleton
    icon={<Shield className="w-8 h-8" />} title="Loading security settings..."
    description="Fetching encryption status"
    {...props}
  />
);

export const PrivacySettingsLoading: React.FC<LoadingStateProps> = (props) => (
  <ComponentLoadingSkeleton
    icon={<Settings className="w-8 h-8" />} title="Loading privacy settings..."
    {...props}
  />
);

export const MessageSearchLoading: React.FC<LoadingStateProps> = (props) => (
  <ComponentLoadingSkeleton
    icon={<Search className="w-8 h-8" />} title="Loading search..."
    description="Indexing messages"
    {...props}
  />
);

// Advanced loading state with progress
export const ProgressiveLoadingState: React.FC<{
  progress: number, stage: string;
  className?: string;
}> = ({ progress, stage, className }) => (
  <div className={cn("flex flex-col items-center justify-center p-8", className)}>
    <motion.div
      className="mb-4"
      animate={{ rotate: 360 }}, transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
    >
      <Loader2 className="w-8 h-8 text-blue-500" />
    </motion.div>
    
    <div className="w-full max-w-xs space-y-3">
      <div className="text-center">
        <div className="text-sm font-medium text-gray-700 dark:text-gray-300">
          {stage}
        </div>
        <div className="text-xs text-gray-500 mt-1">
          {Math.round(progress)}% complete
        </div>
    </div>
      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
        <motion.div
          className="bg-blue-500 h-2 rounded-full"
          initial={{ width: 0 }}, animate={{ width: `${progress}%` }}, transition={{ duration: 0.5, ease: "easeOut" }}
        />
    </div>
    </div>
    </div>
);

// Error state for failed lazy loading
export const LazyLoadingError: React.FC<{
  error?: Error;
  retry?: () => void;
  className?: string;
}> = ({ error, retry, className }) => (
  <div className={cn(
    "flex flex-col items-center justify-center p-8 text-center",
    className
  )}>
    <div className="mb-4 text-red-500">
      <MessageSquare className="w-8 h-8" />
    </div>
    <div className="space-y-3">
      <div className="text-sm font-medium text-gray-700 dark:text-gray-300">
        Failed to load component
      </div>
      
      {error && (
        <div className="text-xs text-gray-500 max-w-xs">
          {error.message}
        </div>
      )}
      
      {retry && (
        <button
          onClick={retry} className="px-4 py-2 text-sm bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
        >
          Try Again
        </button>
      )}
    </div>
    </div>
);

// Shimmer effect utility
export const ShimmerEffect: React.FC<{
  children: React.ReactNode;
  className?: string;
}> = ({ children, className }) => (
  <div className={cn("relative overflow-hidden", className)}>
    {children}
    <div className="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/30 to-transparent" />
    </div>
);

// Optimized loading state selector
export const getLoadingStateForComponent = (componentName: string) => {
  const componentLoadingStates: Record<string, React.ComponentType<LoadingStateProps>> = {
    'MessageReactions': MessageReactionsLoading,
    'CallInterface': CallInterfaceLoading,
    'SecurityDashboard': SecurityDashboardLoading,
    'PrivacySettings': PrivacySettingsLoading,
    'OptimizedMessageSearch': MessageSearchLoading,
    'VirtualizedMessageList': () => <MessageListSkeleton />,
    'EnhancedMessageBubble': MessageBubbleSkeleton,
    'TypingIndicator': TypingIndicatorSkeleton,
    'ConnectionStatusIndicator': ConnectionStatusSkeleton
  };

  return componentLoadingStates[componentName] || ComponentLoadingSkeleton;
};

export default {
  MessageBubbleSkeleton,
  MessageListSkeleton,
  TypingIndicatorSkeleton,
  ConnectionStatusSkeleton,
  ComponentLoadingSkeleton,
  MessageReactionsLoading,
  CallInterfaceLoading,
  SecurityDashboardLoading,
  PrivacySettingsLoading,
  MessageSearchLoading,
  ProgressiveLoadingState,
  LazyLoadingError,
  ShimmerEffect,
  getLoadingStateForComponent
};