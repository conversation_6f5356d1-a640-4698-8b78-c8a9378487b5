/**
 * ConnectionStatusIndicator Component
 * Visual indicator for WebSocket connection status and quality
 */

import React, { useState } from 'react';
import { motion, AnimatePresence, Variants } from 'framer-motion';
import { 
  Wifi, 
  WifiOff, 
  Loader2, 
  AlertTriangle, 
  CheckCircle, 
  Clock,
  Activity,
  Signal,
  MessageSquare,
  RefreshCw,
  X
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Progress } from '@/components/ui/progress';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import { useConnectionStatus } from '../../hooks/useConnectionStatus';
import { ConnectionStatus } from '../../services/messaging/ConnectionManager';

interface ConnectionStatusIndicatorProps {
  className?: string;
  showDetails?: boolean;
  showMetrics?: boolean;
  showNetworkQuality?: boolean;
  compact?: boolean;
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
}

const getStatusIcon = (status: ConnectionStatus, isOnline: boolean) => {
  if (!isOnline) {
    return <WifiOff className="w-4 h-4" />;
  }

  switch (status) {
    case 'connected':
      return <CheckCircle className="w-4 h-4" />;
    case 'connecting':
      return <Loader2 className="w-4 h-4 animate-spin" />;
    case 'disconnected':
      return <AlertTriangle className="w-4 h-4" />;
    default:
      return <WifiOff className="w-4 h-4" />;
  }
};

const getStatusColor = (status: ConnectionStatus, isOnline: boolean) => {
  if (!isOnline) {
    return 'text-gray-400';
  }

  switch (status) {
    case 'connected':
      return 'text-green-500';
    case 'connecting':
      return 'text-yellow-500';
    case 'disconnected':
      return 'text-red-500';
    default:
      return 'text-gray-400';
  }
};

const getStatusText = (status: ConnectionStatus, isOnline: boolean) => {
  if (!isOnline) {
    return 'Offline';
  }

  switch (status) {
    case 'connected':
      return 'Connected';
    case 'connecting':
      return 'Connecting...';
    case 'disconnected':
      return 'Disconnected';
    default:
      return 'Unknown';
  }
};

const getNetworkQualityColor = (quality: string) => {
  switch (quality) {
    case 'excellent':
      return 'text-green-500';
    case 'good':
      return 'text-blue-500';
    case 'fair':
      return 'text-yellow-500';
    case 'poor':
      return 'text-red-500';
    default:
      return 'text-gray-400';
  }
};

const formatLatency = (latency: number) => {
  if (latency < 100) return 'Excellent';
  if (latency < 300) return 'Good';
  if (latency < 600) return 'Fair';
  return 'Poor';
};

const formatUptime = (uptime: number) => {
  const seconds = Math.floor(uptime / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);

  if (hours > 0) {
    return `${hours}h ${minutes % 60}m`;
  } else if (minutes > 0) {
    return `${minutes}m ${seconds % 60}s`;
  } else {
    return `${seconds}s`;
  }
};

const ConnectionStatusIndicatorComponent: React.FC<ConnectionStatusIndicatorProps> = ({
  className,
  showDetails = true,
  showMetrics = true,
  showNetworkQuality = true,
  compact = false,
  position = 'bottom-right'
}) => {
  const [showPopover, setShowPopover] = useState(false);
  
  const {
    connectionInfo,
    reconnect,
    disconnect,
    clearQueue,
    isOnline;
    connectionHistory
  } = useConnectionStatus({
    enableMetrics: showMetrics,
    enableNetworkQuality: showNetworkQuality,
    enableUserFeedback: true
  });

  const { status, metrics, networkQuality, queuedMessages } = connectionInfo;

  // Animation variants
  const indicatorVariants: Variants = {
    initial: { scale: 0, opacity: 0 },
    animate: { 
      scale: 1, 
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 400,
        damping: 25
      }
    },
    exit: { 
      scale: 0, 
      opacity: 0,
      transition: { duration: 0.2 }
    }
  };

  const pulseVariants: Variants = {
    animate: {
      scale: [1, 1.1, 1],
      opacity: [1, 0.8, 1],
      transition: {
        duration: 2,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }
  };

  const positionClasses = {
    'top-left': 'top-4 left-4',
    'top-right': 'top-4 right-4',
    'bottom-left': 'bottom-4 left-4',
    'bottom-right': 'bottom-4 right-4'
  };

  if (compact) {
    return (
      <Tooltip>
        <TooltipTrigger asChild>
          <motion.div
            variants={indicatorVariants} initial="initial"
            animate="animate"
            className={cn(
              "flex items-center gap-1 px-2 py-1 rounded-full bg-white dark:bg-gray-800 shadow-sm border",
              getStatusColor(status, isOnline),
              className
            )}
          >
            <motion.div
              variants={status === 'connecting' ? pulseVariants : undefined} animate={status === 'connecting' ? 'animate' : undefined}
            >
              {getStatusIcon(status, isOnline)}
            </motion.div>
            
            {queuedMessages > 0 && (
              <Badge variant="secondary" className="text-xs px-1 py-0 h-4">
                {queuedMessages}
              </Badge>
            )}
          </motion.div>
    </TooltipTrigger>
        <TooltipContent>
          <p>{getStatusText(status, isOnline)}</p>
          {networkQuality && (
            <p className="text-xs">
              Quality: {networkQuality.quality} ({networkQuality.latency}ms)
            </p>
          )}
        </TooltipContent>
    </Tooltip>
    );
  }

  return (
    <motion.div
      variants={indicatorVariants} initial="initial"
      animate="animate"
      className={cn(
        "fixed z-50",
        positionClasses[position],
        className
      )}
    >
      <Popover open={showPopover} onOpenChange={setShowPopover}>
        <PopoverTrigger asChild>
          <motion.button
            whileHover={{ scale: 1.05 }}, whileTap={{ scale: 0.95 }}, className={cn(
              "flex items-center gap-2 px-3 py-2 rounded-lg bg-white dark:bg-gray-800 shadow-lg border transition-colors",
              "hover:bg-gray-50 dark:hover:bg-gray-700",
              getStatusColor(status, isOnline)
            )}
          >
            <motion.div
              variants={status === 'connecting' ? pulseVariants : undefined} animate={status === 'connecting' ? 'animate' : undefined}
            >
              {getStatusIcon(status, isOnline)}
            </motion.div>
            
            {!compact && (
              <span className="text-sm font-medium">
                {getStatusText(status, isOnline)}
              </span>
            )}
            
            {queuedMessages > 0 && (
              <Badge variant="secondary" className="text-xs">
                {queuedMessages} queued
              </Badge>
            )}
          </motion.button>
    </PopoverTrigger>
        {showDetails && (
          <PopoverContent className="w-80 p-4" align="end">
            <div className="space-y-4">
              {/* Header */}
              <div className="flex items-center justify-between">
                <h3 className="text-sm font-semibold">Connection Status</h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowPopover(false)} className="h-6 w-6 p-0"
                >
                  <X className="w-4 h-4" />
    </Button>
              </div>

              {/* Status Overview */}
              <div className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <div className={getStatusColor(status, isOnline)}>
                  {getStatusIcon(status, isOnline)}
                </div>
                <div className="flex-1">
                  <div className="text-sm font-medium">
                    {getStatusText(status, isOnline)}
                  </div>
                  <div className="text-xs text-gray-500">
                    {isOnline ? 'Internet connection available' : 'No internet connection'}
                  </div>
    </div>
              </div>

              {/* Network Quality */}
              {showNetworkQuality && networkQuality && (
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Signal className="w-4 h-4 text-gray-400" />
                    <span className="text-sm font-medium">Network Quality</span>
    </div>
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-gray-500">
                      {formatLatency(networkQuality.latency)}
                    </span>
                    <span className={cn("text-xs font-medium", getNetworkQualityColor(networkQuality.quality))}>
                      {networkQuality.quality.toUpperCase()}
                    </span>
    </div>
                  <div className="text-xs text-gray-500">
                    Latency: {networkQuality.latency}ms
                  </div>
    </div>
              )}

              {/* Metrics */}
              {showMetrics && metrics && (
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Activity className="w-4 h-4 text-gray-400" />
                    <span className="text-sm font-medium">Connection Metrics</span>
    </div>
                  <div className="grid grid-cols-2 gap-3 text-xs">
                    <div>
                      <div className="text-gray-500">Uptime</div>
                      <div className="font-medium">{formatUptime(metrics.uptime)}</div>
    </div>
                    <div>
                      <div className="text-gray-500">Messages Sent</div>
                      <div className="font-medium">{metrics.messagesSent}</div>
    </div>
                    <div>
                      <div className="text-gray-500">Messages Received</div>
                      <div className="font-medium">{metrics.messagesReceived}</div>
    </div>
                    <div>
                      <div className="text-gray-500">Reconnects</div>
                      <div className="font-medium">{metrics.totalReconnects}</div>
    </div>
                  </div>
    </div>
              )}

              {/* Queued Messages */}
              {queuedMessages > 0 && (
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <MessageSquare className="w-4 h-4 text-yellow-500" />
                    <span className="text-sm font-medium">Queued Messages</span>
    </div>
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-gray-500">
                      {queuedMessages} messages waiting to be sent
                    </span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={clearQueue} className="h-6 text-xs"
                    >
                      Clear Queue
                    </Button>
    </div>
                </div>
              )}

              {/* Connection History */}
              {connectionHistory.length > 0 && (
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Clock className="w-4 h-4 text-gray-400" />
                    <span className="text-sm font-medium">Recent Activity</span>
    </div>
                  <div className="space-y-1 max-h-20 overflow-y-auto">
                    {connectionHistory.slice(-3).map((entry, index) => (
                      <div key={index} className="flex items-center justify-between text-xs">
                        <span className={getStatusColor(entry.status, true)}>
                          {getStatusText(entry.status, true)}
                        </span>
                        <span className="text-gray-500">
                          {entry.duration ? formatUptime(entry.duration) : 'Active'}
                        </span>
    </div>
                    ))}
                  </div>
    </div>
              )}

              {/* Actions */}
              <div className="flex gap-2 pt-2 border-t">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={reconnect} disabled={status === 'connecting'}, className="flex-1"
                >
                  <RefreshCw className="w-3 h-3 mr-1" />
                  Reconnect
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={disconnect} disabled={status === 'disconnected'}, className="flex-1"
                >
                  Disconnect
                </Button>
    </div>
            </div>
    </PopoverContent>
        )}
      </Popover>
    </motion.div>
  );
};

// Memoized component for performance optimization
export const ConnectionStatusIndicator = React.memo(ConnectionStatusIndicatorComponent, (prevProps, nextProps) => {
  return (
    prevProps.showDetails === nextProps.showDetails &&
    prevProps.showMetrics === nextProps.showMetrics &&
    prevProps.showNetworkQuality === nextProps.showNetworkQuality &&
    prevProps.compact === nextProps.compact &&
    prevProps.position === nextProps.position
  );
});

ConnectionStatusIndicator.displayName = 'ConnectionStatusIndicator';

export default ConnectionStatusIndicator;