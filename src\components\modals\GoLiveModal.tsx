import React, { useState, useRef, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Camera, CameraOff, Settings, Users, Globe, Lock, Wifi, WifiOff, MessageSquare, Video } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { toast } from 'sonner';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useNavigate } from 'react-router-dom';

interface StreamSettings {
  videoEnabled: boolean, audioEnabled: boolean, camera: string, microphone: string, quality: string, chatEnabled: boolean, donationsEnabled: boolean, autoSaveEnabled: boolean;
}

interface StreamData {
  id: string, title: string, description: string, privacy: 'public' | 'friends' | 'private', category: string, streamKey: string, settings: StreamSettings, startedAt: string, viewers: number, likes: number, comments: number;
}


interface GoLiveModalProps {
  isOpen: boolean, onClose: () => void;
  onStreamStarted?: (streamData: StreamData) => void;
}

const GoLiveModal: React.FC<GoLiveModalProps> = ({ isOpen, onClose, onStreamStarted }) => {
  const navigate = useNavigate();
  const [streamTitle, setStreamTitle] = useState('');
  const [streamDescription, setStreamDescription] = useState('');
  const [privacy, setPrivacy] = useState<'public' | 'friends' | 'private'>('public');
  const [category, setCategory] = useState('');
  const [isVideoEnabled, setIsVideoEnabled] = useState(true);
  const [isAudioEnabled, setIsAudioEnabled] = useState(true);
  const [selectedCamera, setSelectedCamera] = useState('default');
  const [selectedMicrophone, setSelectedMicrophone] = useState('default');
  const [streamKey, setStreamKey] = useState('');
  const [isGeneratingKey, setIsGeneratingKey] = useState(false);
  const [isStartingStream, setIsStartingStream] = useState(false);
  const [showAdvancedSettings, setShowAdvancedSettings] = useState(false);
  const [enableChat, setEnableChat] = useState(true);
  const [enableDonations, setEnableDonations] = useState(false);
  const [enableAutoSave, setEnableAutoSave] = useState(true);
  const [streamQuality, setStreamQuality] = useState('720p');
  const [isTestingConnection, setIsTestingConnection] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'idle' | 'testing' | 'good' | 'poor'>('idle');
  

  const videoRef = useRef<HTMLVideoElement>(null);

  // Auto-generate stream key when modal opens
  useEffect(() => {
    if (isOpen && !streamKey) {
      const key = `live_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      setStreamKey(key);
    }
  }, [isOpen, streamKey]);

  // Initialize camera preview
  useEffect(() => {
    let stream: MediaStream | null = null;

    if (isOpen && isVideoEnabled) {
      navigator.mediaDevices.getUserMedia({ video: true, audio: false })
        .then(mediaStream => {
          stream = mediaStream;
          if (videoRef.current) {
            videoRef.current.srcObject = stream;
          }
        })
        .catch(err => {
          console.error('Error accessing camera:', err);
          toast.error('Unable to access camera');
        });
    }

    return () => {
      // Cleanup camera stream
      const video = videoRef.current;
      if (stream) {
        stream.getTracks().forEach(track => {
          track.stop();
        });
      }
      // Access ref in cleanup function
      if (video) {
        video.srcObject = null;
      }
    };
  }, [isOpen, isVideoEnabled]);


  const generateStreamKey = () => {
    setIsGeneratingKey(true);
    setTimeout(() => {
      const key = `live_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      setStreamKey(key);
      setIsGeneratingKey(false);
      toast.success('Stream key regenerated successfully');
    }, 1000);
  };

  const testConnection = () => {
    setIsTestingConnection(true);
    setConnectionStatus('testing');
    
    setTimeout(() => {
      const isGood = Math.random() > 0.3; // 70% chance of good connection
      setConnectionStatus(isGood ? 'good' : 'poor');
      setIsTestingConnection(false);
      
      if (isGood) {
        toast.success('Connection test passed! Ready to go live.');
      } else {
        toast.error('Poor connection detected. Check your internet.');
      }
    }, 2000);
  };

  const handleStartStream = () => {
    if (!streamTitle.trim()) {
      toast.error('Please add a title for your stream');
      return;
    }

    if (!streamKey) {
      toast.error('Please generate a stream key');
      return;
    }

    if (connectionStatus === 'poor') {
      toast.warning('Your connection might be unstable');
    }

    setIsStartingStream(true);

    // Simulate stream setup
    setTimeout(() => {
      const streamData: StreamData = {
        id: `stream_${Date.now()}`,
        title: streamTitle.trim(),
        description: streamDescription.trim(),
        privacy,
        category,
        streamKey,
        settings: {
          videoEnabled: isVideoEnabled,
          audioEnabled: isAudioEnabled,
          camera: selectedCamera,
          microphone: selectedMicrophone,
          quality: streamQuality,
          chatEnabled: enableChat,
          donationsEnabled: enableDonations,
          autoSaveEnabled: enableAutoSave
        },
        startedAt: new Date().toISOString(),
        viewers: 0,
        likes: 0,
        comments: 0
      };

      // Save to local storage
      const existingStreams = JSON.parse(localStorage.getItem('user_streams') || '[]');
      const updatedStreams = [streamData, ...existingStreams].slice(0, 50);
      localStorage.setItem('user_streams', JSON.stringify(updatedStreams));
      
      // Store current stream data for the live page
      localStorage.setItem('current_stream', JSON.stringify(streamData));

      if (onStreamStarted) {
        onStreamStarted(streamData);
      }

      setIsStartingStream(false);
      
      // Navigate to live stream page
      navigate('/live');
      
      // Close the modal after navigation
      setTimeout(() => {
        onClose();
      }, 100);
    }, 2000);
  };



  const handleClose = (skipCleanup = false) => {
    // Clean up only if not transitioning to live stream
    if (!skipCleanup && videoRef.current && videoRef.current.srcObject) {
      const stream = videoRef.current.srcObject as MediaStream;
      stream.getTracks().forEach(track => {
        track.stop();
        stream.removeTrack(track);
      });
      videoRef.current.srcObject = null;
    }
    
    // Reset form
    setStreamTitle('');
    setStreamDescription('');
    setPrivacy('public');
    setCategory('');
    setStreamKey('');
    setConnectionStatus('idle');
    setShowAdvancedSettings(false);
    onClose();
  };

  const categories = [
    'Gaming',
    'Music',
    'Talk Shows',
    'Sports',
    'Education',
    'Creative',
    'Cooking',
    'Fitness',
    'Travel',
    'Other'
  ];

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && handleClose()}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden p-0">
        <DialogHeader className="p-4 border-b flex items-center justify-between">
          <DialogTitle>Go Live</DialogTitle>
    </DialogHeader>
        <div className="flex h-[80vh]">
          {/* Preview Section */}
          <div className="w-1/2 bg-black relative">
            {isVideoEnabled ? (
              <video
                ref={videoRef}, autoPlay
                muted
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center">
                <div className="text-center">
                  <CameraOff className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-400">Camera is off</p>
    </div>
              </div>
            )}

            {/* Stream Status Overlay */}
            <div className="absolute top-4 left-4 flex items-center space-x-2">
              {connectionStatus === 'good' && (
                <Badge className="bg-green-500 text-white">
                  <Wifi className="w-3 h-3 mr-1" />
                  Good connection
                </Badge>
              )}
              {connectionStatus === 'poor' && (
                <Badge className="bg-red-500 text-white">
                  <WifiOff className="w-3 h-3 mr-1" />
                  Poor connection
                </Badge>
              )}
              {connectionStatus === 'testing' && (
                <Badge className="bg-yellow-500 text-white animate-pulse">
                  Testing connection...
                </Badge>
              )}
            </div>

            {/* Camera/Mic Controls */}
            <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex items-center space-x-3">
              <Button
                size="icon"
                variant={isVideoEnabled ? "secondary" : "destructive"} onClick={() => setIsVideoEnabled(!isVideoEnabled)}, className="bg-white/20 backdrop-blur"
              >
                {isVideoEnabled ? <Camera className="w-4 h-4" /> : <CameraOff className="w-4 h-4" />}
              </Button>
              <Button
                size="icon"
                variant={isAudioEnabled ? "secondary" : "destructive"} onClick={() => setIsAudioEnabled(!isAudioEnabled)}, className="bg-white/20 backdrop-blur"
              >
                {isAudioEnabled ? <Mic className="w-4 h-4" /> : <MicOff className="w-4 h-4" />}
              </Button>
              <Button
                size="icon"
                variant="secondary"
                onClick={() => setShowAdvancedSettings(!showAdvancedSettings)} className="bg-white/20 backdrop-blur"
              >
                <Settings className="w-4 h-4" />
    </Button>
            </div>
    </div>
          {/* Settings Section */}
          <div className="w-1/2 flex flex-col">
            <div className="flex-1 p-6 overflow-y-auto">
              <Tabs defaultValue="basic" className="w-full">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="basic">Basic Info</TabsTrigger>
                  <TabsTrigger value="settings">Settings</TabsTrigger>
    </TabsList>
              <TabsContent value="basic" className="space-y-4 mt-4">
                {/* Title */}
                <div>
                  <Label htmlFor="title">Stream Title *</Label>
                  <Input
                    id="title"
                    value={streamTitle} onChange={(e) => setStreamTitle(e.target.value)}, placeholder="What are you streaming about?"
                    className="mt-1"
                  />
    </div>
                {/* Description */}
                <div>
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={streamDescription} onChange={(e) => setStreamDescription(e.target.value)}, placeholder="Tell viewers what to expect..."
                    rows={3} className="mt-1 resize-none"
                  />
    </div>
                {/* Category */}
                <div>
                  <Label htmlFor="category">Category</Label>
                  <Select value={category} onValueChange={setCategory}>
                    <SelectTrigger id="category" className="mt-1">
                      <SelectValue placeholder="Select a category" />
    </SelectTrigger>
                    <SelectContent>
                      {categories.map((cat) => (
                        <SelectItem key={cat} value={cat}>{cat}</SelectItem>
                      ))}
                    </SelectContent>
    </Select>
                </div>

                {/* Privacy */}
                <div>
                  <Label htmlFor="privacy">Privacy</Label>
                  <Select value={privacy} onValueChange={(value) => setPrivacy(value as 'public' | 'friends' | 'private')}>
                    <SelectTrigger id="privacy" className="mt-1">
                      <SelectValue />
    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="public">
                        <div className="flex items-center">
                          <Globe className="w-4 h-4 mr-2" />
                          Public
                        </div>
    </SelectItem>
                      <SelectItem value="friends">
                        <div className="flex items-center">
                          <Users className="w-4 h-4 mr-2" />
                          Friends
                        </div>
    </SelectItem>
                      <SelectItem value="private">
                        <div className="flex items-center">
                          <Lock className="w-4 h-4 mr-2" />
                          Only Me
                        </div>
    </SelectItem>
                    </SelectContent>
    </Select>
                </div>

                {/* Stream Key */}
                <div>
                  <Label>Stream Key (Auto-generated)</Label>
                  <div className="flex items-center space-x-2 mt-1">
                    <Input
                      value={streamKey}, readOnly
                      placeholder="Stream key will be auto-generated"
                      type="password"
                    />
                    <Button
                      onClick={generateStreamKey} disabled={isGeneratingKey}, size="sm"
                      variant="outline"
                    >
                      {isGeneratingKey ? 'Regenerating...' : 'Regenerate'}
                    </Button>
    </div>
                  <p className="text-xs text-gray-500 mt-1">
                    Stream key has been auto-generated. Keep it private.
                  </p>
    </div>
                {/* Connection Test */}
                <div className="pt-4">
                  <Button
                    variant="outline"
                    onClick={testConnection} disabled={isTestingConnection}, className="w-full"
                  >
                    {isTestingConnection ? (
                      <>
                        <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2" />
                        Testing Connection...
                      </>
                    ) : (
                      <>
                        <Wifi className="w-4 h-4 mr-2" />
                        Test Connection
                      </>
                    )}
                  </Button>
    </div>
              </TabsContent>

              <TabsContent value="settings" className="space-y-4 mt-4">
                {/* Stream Quality */}
                <div>
                  <Label htmlFor="quality">Stream Quality</Label>
                  <Select value={streamQuality} onValueChange={setStreamQuality}>
                    <SelectTrigger id="quality" className="mt-1">
                      <SelectValue />
    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1080p">1080p (Full HD)</SelectItem>
                      <SelectItem value="720p">720p (HD)</SelectItem>
                      <SelectItem value="480p">480p (SD)</SelectItem>
                      <SelectItem value="360p">360p (Low)</SelectItem>
    </SelectContent>
                  </Select>
    </div>
                {/* Device Selection */}
                <div>
                  <Label htmlFor="camera">Camera</Label>
                  <Select value={selectedCamera} onValueChange={setSelectedCamera}>
                    <SelectTrigger id="camera" className="mt-1">
                      <SelectValue />
    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="default">Default Camera</SelectItem>
                      <SelectItem value="external">External Webcam</SelectItem>
    </SelectContent>
                  </Select>
    </div>
                <div>
                  <Label htmlFor="microphone">Microphone</Label>
                  <Select value={selectedMicrophone} onValueChange={setSelectedMicrophone}>
                    <SelectTrigger id="microphone" className="mt-1">
                      <SelectValue />
    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="default">Default Microphone</SelectItem>
                      <SelectItem value="headset">Headset</SelectItem>
    </SelectContent>
                  </Select>
    </div>
                {/* Stream Features */}
                <div className="space-y-3 pt-4">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="chat" className="flex items-center cursor-pointer">
                      <MessageSquare className="w-4 h-4 mr-2" />
                      Enable Live Chat
                    </Label>
                    <Switch
                      id="chat"
                      checked={enableChat} onCheckedChange={setEnableChat}
                    />
    </div>
                  <div className="flex items-center justify-between">
                    <Label htmlFor="donations" className="flex items-center cursor-pointer">
                      <span className="mr-2">💰</span>
                      Enable Donations
                    </Label>
                    <Switch
                      id="donations"
                      checked={enableDonations} onCheckedChange={setEnableDonations}
                    />
    </div>
                  <div className="flex items-center justify-between">
                    <Label htmlFor="autosave" className="flex items-center cursor-pointer">
                      <span className="mr-2">💾</span>
                      Auto-save Stream
                    </Label>
                    <Switch
                      id="autosave"
                      checked={enableAutoSave} onCheckedChange={setEnableAutoSave}
                    />
    </div>
                </div>

                {/* Tips */}
                <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg mt-4">
                  <h4 className="font-medium text-blue-800 dark:text-blue-300 mb-2">
                    Tips for great streams
                  </h4>
                  <ul className="text-sm text-blue-700 dark:text-blue-200 space-y-1">
                    <li>• Ensure good lighting and clear audio</li>
                    <li>• Interact with your viewers regularly</li>
                    <li>• Test your connection before going live</li>
                    <li>• Have a plan for your stream content</li>
    </ul>
                </div>
    </TabsContent>
              </Tabs>
    </div>
            {/* Action Buttons - Always visible at bottom */}
            <div className="p-6 pt-0">
              <div className="flex justify-end space-x-3 pt-4 border-t">
                <Button
                  variant="outline"
                  onClick={handleClose} disabled={isStartingStream}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleStartStream} disabled={isStartingStream || !streamTitle.trim() || !streamKey}, className="bg-red-600 hover:bg-red-700"
                >
                  {isStartingStream ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                      Starting Stream...
                    </>
                  ) : (
                    <>
                      <Video className="w-4 h-4 mr-2" />
                      Go Live
                    </>
                  )}
                </Button>
    </div>
            </div>
    </div>
        </div>
    </DialogContent>
    </Dialog>
  );
};

export default GoLiveModal;
