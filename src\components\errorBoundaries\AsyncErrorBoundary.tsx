import React from 'react';
import { Alert<PERSON>riangle, Refresh<PERSON>w, Wifi, WifiOff } from 'lucide-react';
import { Button } from '../ui/button';
import UniversalErrorBoundary from '../ui/UniversalErrorBoundary';

interface AsyncErrorFallbackProps {
  error?: Error;
  onRetry?: () => void;
  isNetworkError?: boolean;
}

const AsyncErrorFallback: React.FC<AsyncErrorFallbackProps> = ({ 
  error, 
  onRetry,
  isNetworkError = false 
}) => {
  const isOffline = !navigator.onLine;
  const shouldShowNetworkError = isNetworkError || isOffline;

  return (
    <div className="p-8 text-center bg-gray-50 dark:bg-gray-900 rounded-lg">
      <div className="mb-4">
        {shouldShowNetworkError ? (
          <WifiOff className="w-12 h-12 text-orange-500 mx-auto" />
        ) : (
          <AlertTriangle className="w-12 h-12 text-red-500 mx-auto" />
        )}
      </div>
      
      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
        {shouldShowNetworkError ? 'Connection Error' : 'Loading Error'}
      </h3>
      
      <p className="text-gray-600 dark:text-gray-400 text-sm mb-6 max-w-sm mx-auto">
        {shouldShowNetworkError 
          ? 'Unable to connect. Please check your internet connection and try again.'
          : error?.message || 'Something went wrong while loading this content.'
        }
      </p>
      
      <div className="space-y-3">
        <Button onClick={onRetry || (() => window.location.reload())}>
          <RefreshCw className="w-4 h-4 mr-2" />
          {shouldShowNetworkError ? 'Reconnect' : 'Try Again'}
        </Button>
        
        {shouldShowNetworkError && (
          <div className="flex items-center justify-center gap-2 text-sm text-gray-500">
            {isOffline ? (
              <>
                <WifiOff className="w-4 h-4" />
                <span>You're offline</span>
              </>
            ) : (
              <>
                <Wifi className="w-4 h-4" />
                <span>Connection restored</span>
              </>
            )}
          </div>
        )}
      </div>
      
      {process.env.NODE_ENV === 'development' && error && (
        <details className="mt-6 text-left">
          <summary className="text-xs text-gray-600 cursor-pointer">Technical Details</summary>
          <pre className="text-xs text-gray-500 mt-2 p-2 bg-gray-100 dark:bg-gray-800 rounded overflow-auto">
            {error.stack}
          </pre>
    </details>
      )}
    </div>
  );
};

interface AsyncErrorBoundaryProps {
  children: React.ReactNode;
  onError?: (error: Error) => void;
}

const AsyncErrorBoundary: React.FC<AsyncErrorBoundaryProps> = ({ children, onError }) => {
  const [retryKey, setRetryKey] = React.useState(0);
  const [isNetworkError, setIsNetworkError] = React.useState(false);

  const handleRetry = () => {
    setRetryKey(prev => prev + 1);
    setIsNetworkError(false);
  };

  const handleError = (error: Error) => {
    // Detect network-related errors
    const networkErrorPatterns = [
      /network/i,
      /fetch/i,
      /connection/i,
      /timeout/i,
      /offline/i
    ];
    
    const isNetwork = networkErrorPatterns.some(pattern => 
      pattern.test(error.message) || pattern.test(error.name)
    );
    
    setIsNetworkError(isNetwork);
    onError?.(error);
  };

  // Listen for online/offline events
  React.useEffect(() => {
    const handleOnline = () => setIsNetworkError(false);
    const handleOffline = () => setIsNetworkError(true);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  return (
    <UniversalErrorBoundary
      key={retryKey} level="component"
      onError={handleError} fallback={
        <AsyncErrorFallback 
          isNetworkError={isNetworkError} onRetry={handleRetry}
        />
      }
    >
      {children}
    </UniversalErrorBoundary>
  );
};

export default AsyncErrorBoundary;
