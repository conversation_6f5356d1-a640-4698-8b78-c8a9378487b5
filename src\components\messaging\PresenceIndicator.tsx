/**
 * Presence Indicator Component
 * Shows user online/offline status and activity
 */

import React, { useMemo } from 'react';
import { motion } from 'framer-motion';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { 
  Circle, 
  Smartphone, 
  Monitor, 
  Tablet,
  MessageCircle,
  Eye,
  Phone,
  Video
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { UserPresence } from '@/types/messaging';
import { formatDistanceToNow } from 'date-fns';

interface PresenceIndicatorProps {
  presence: UserPresence;
  showActivity?: boolean;
  showDevice?: boolean;
  showTooltip?: boolean;
  size?: 'sm' | 'md' | 'lg';
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
  className?: string;
}

const statusConfig = {
  online: {
    color: 'bg-green-500',
    label: 'Online',
    pulse: true
  },
  away: {
    color: 'bg-yellow-500',
    label: 'Away',
    pulse: false
  },
  busy: {
    color: 'bg-red-500',
    label: 'Busy',
    pulse: false
  },
  offline: {
    color: 'bg-gray-400',
    label: 'Offline',
    pulse: false
  }
};

const deviceIcons = {
  web: Monitor,
  mobile: Smartphone,
  desktop: Monitor,
  tablet: Tablet
};

const activityIcons = {
  typing: MessageCircle,
  viewing_profile: Eye,
  in_call: Phone,
  video_call: Video
};

const sizeConfig = {
  sm: {
    indicator: 'w-2 h-2',
    avatar: 'w-6 h-6',
    badge: 'text-xs px-1'
  },
  md: {
    indicator: 'w-3 h-3',
    avatar: 'w-8 h-8',
    badge: 'text-xs px-2'
  },
  lg: {
    indicator: 'w-4 h-4',
    avatar: 'w-10 h-10',
    badge: 'text-sm px-2'
  }
};

const positionConfig = {
  'bottom-right': 'bottom-0 right-0',
  'bottom-left': 'bottom-0 left-0',
  'top-right': 'top-0 right-0',
  'top-left': 'top-0 left-0'
};

export const PresenceIndicator: React.FC<PresenceIndicatorProps> = ({
  presence,
  showActivity = true,
  showDevice = false,
  showTooltip = true,
  size = 'md',
  position = 'bottom-right',
  className
}) => {
  const config = statusConfig[presence.status];
  const sizeClasses = sizeConfig[size];
  const positionClasses = positionConfig[position];
  
  const DeviceIcon = deviceIcons[presence.device];
  const ActivityIcon = presence.currentActivity ? activityIcons[presence.currentActivity] : null;

  // Generate tooltip content
  const tooltipContent = useMemo(() => {
    let content = config.label;
    
    if (presence.status === 'offline' && presence.lastSeen) {
      const timeAgo = formatDistanceToNow(new Date(presence.lastSeen), { addSuffix: true });
      content += ` • Last seen ${timeAgo}`;
    }
    
    if (showActivity && presence.currentActivity) {
      const activityLabels = {
        typing: 'Typing',
        viewing_profile: 'Viewing profile',
        in_call: 'In a call',
        video_call: 'In a video call'
      };
      content += ` • ${activityLabels[presence.currentActivity]}`;
    }
    
    if (showDevice) {
      const deviceLabels = {
        web: 'Web',
        mobile: 'Mobile',
        desktop: 'Desktop',
        tablet: 'Tablet'
      };
      content += ` • ${deviceLabels[presence.device]}`;
    }
    
    return content;
  }, [config.label, presence, showActivity, showDevice]);

  const indicator = (
    <div className={cn("relative inline-block", className)}>
      {/* Main presence indicator */}
      <motion.div
        className={cn(
          "absolute rounded-full border-2 border-white dark:border-gray-800",
          sizeClasses.indicator,
          config.color,
          positionClasses
        )} animate={config.pulse ? {
          scale: [1, 1.2, 1],
          opacity: [1, 0.8, 1]
        } : {}}, transition={config.pulse ? {
          duration: 2,
          repeat: Infinity,
          ease: "easeInOut"
        } : {}}
      />
      
      {/* Activity indicator */}
      {showActivity && ActivityIcon && presence.status === 'online' && (
        <motion.div
          initial={{ scale: 0, opacity: 0 }}, animate={{ scale: 1, opacity: 1 }}, className={cn(
            "absolute rounded-full bg-blue-500 border border-white dark:border-gray-800 flex items-center justify-center",
            size === 'sm' ? 'w-3 h-3 -top-1 -left-1' : 
            size === 'md' ? 'w-4 h-4 -top-1 -left-1' : 'w-5 h-5 -top-1 -left-1'
          )}
        >
          <ActivityIcon className={cn(
            "text-white",
            size === 'sm' ? 'w-2 h-2' : 
            size === 'md' ? 'w-2.5 h-2.5' : 'w-3 h-3'
          )} />
        </motion.div>
      )}
      
      {/* Device indicator */}
      {showDevice && DeviceIcon && (
        <motion.div
          initial={{ scale: 0, opacity: 0 }}, animate={{ scale: 1, opacity: 1 }}, className={cn(
            "absolute rounded bg-gray-600 text-white flex items-center justify-center",
            size === 'sm' ? 'w-3 h-2 -bottom-1 -right-1' : 
            size === 'md' ? 'w-4 h-3 -bottom-1 -right-1' : 'w-5 h-4 -bottom-1 -right-1'
          )}
        >
          <DeviceIcon className={cn(
            size === 'sm' ? 'w-1.5 h-1.5' : 
            size === 'md' ? 'w-2 h-2' : 'w-2.5 h-2.5'
          )} />
        </motion.div>
      )}
    </div>
  );

  if (!showTooltip) {
    return indicator;
  }

  return (
    <Tooltip>
      <TooltipTrigger asChild>
        {indicator}
      </TooltipTrigger>
      <TooltipContent side="top" className="text-xs">
        {tooltipContent}
      </TooltipContent>
    </Tooltip>
  );
};

// User avatar with presence
interface UserAvatarWithPresenceProps {
  userId: string, presence: UserPresence;
  getUserName?: (userId: string) => string;
  getUserAvatar?: (userId: string) => string;
  size?: 'sm' | 'md' | 'lg';
  showActivity?: boolean;
  showDevice?: boolean;
  className?: string;
}

export const UserAvatarWithPresence: React.FC<UserAvatarWithPresenceProps> = ({
  userId,
  presence,
  getUserName = (id) => `User ${id.slice(-4)}`,
  getUserAvatar,
  size = 'md',
  showActivity = true,
  showDevice = false,
  className
}) => {
  const sizeClasses = sizeConfig[size];

  return (
    <div className={cn("relative", className)}>
      <Avatar className={sizeClasses.avatar}>
        {getUserAvatar && (
          <AvatarImage src={getUserAvatar(userId)} alt={getUserName(userId)} />
        )}
        <AvatarFallback className="text-xs font-medium">
          {getUserName(userId).charAt(0).toUpperCase()}
        </AvatarFallback>
    </Avatar>
      <PresenceIndicator
        presence={presence} showActivity={showActivity}, showDevice={showDevice} size={size}, position="bottom-right"
      />
    </div>
  );
};

// Presence list for multiple users
interface PresenceListProps {
  presences: Array<{ userId: string, presence: UserPresence }>;
  getUserName?: (userId: string) => string;
  getUserAvatar?: (userId: string) => string;
  maxVisible?: number;
  size?: 'sm' | 'md' | 'lg';
  layout?: 'horizontal' | 'vertical';
  className?: string;
}

export const PresenceList: React.FC<PresenceListProps> = ({
  presences,
  getUserName = (id) => `User ${id.slice(-4)}`,
  getUserAvatar,
  maxVisible = 5,
  size = 'md',
  layout = 'horizontal',
  className
}) => {
  const visiblePresences = presences.slice(0, maxVisible);
  const hiddenCount = Math.max(0, presences.length - maxVisible);

  const containerClasses = layout === 'horizontal' 
    ? 'flex items-center space-x-2' 
    : 'flex flex-col space-y-2';

  return (
    <div className={cn(containerClasses, className)}>
      {visiblePresences.map(({ userId, presence }) => (
        <UserAvatarWithPresence
          key={userId} userId={userId}, presence={presence} getUserName={getUserName}, getUserAvatar={getUserAvatar} size={size}, showActivity={true} showDevice={false}
        />
      ))}
      
      {hiddenCount > 0 && (
        <div className={cn("flex items-center justify-center bg-gray-200 dark:bg-gray-700 rounded-full", sizeConfig[size].avatar)}>
          <span className={cn("font-medium text-gray-600 dark:text-gray-300", sizeConfig[size].badge)}>
            +{hiddenCount}
          </span>
    </div>
      )}
    </div>
  );
};

// Simple status badge
interface StatusBadgeProps {
  status: UserPresence['status'];
  showLabel?: boolean;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export const StatusBadge: React.FC<StatusBadgeProps> = ({
  status,
  showLabel = true,
  size = 'md',
  className
}) => {
  const config = statusConfig[status];
  
  return (
    <Badge 
      variant="outline" 
      className={cn(
        "flex items-center space-x-1",
        sizeConfig[size].badge,
        className
      )}
    >
      <Circle className={cn(
        "fill-current",
        config.color.replace('bg-', 'text-'),
        size === 'sm' ? 'w-2 h-2' : size === 'md' ? 'w-2.5 h-2.5' : 'w-3 h-3'
      )} />
      {showLabel && <span>{config.label}</span>}
    </Badge>
  );
};

export default PresenceIndicator;