import React from 'react';
import MinimalApolloProvider from '@/components/MinimalApolloProvider';

interface ApolloProviderWrapperProps {
  children: React.ReactNode;
}

// Using minimal provider to completely bypass Apollo and avoid React hook context corruption
export const ApolloProviderWrapper: React.FC<ApolloProviderWrapperProps> = ({ children }) => {
  return (
    <MinimalApolloProvider>
      {children}
    </MinimalApolloProvider>
  );
};

export default ApolloProviderWrapper;
