/**
 * Messaging Error Boundary
 * Specialized error boundary for messaging components
 */

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { <PERSON>ert<PERSON><PERSON>gle, RefreshCw, MessageCircle } from 'lucide-react';
import { toast } from 'sonner';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  showRetry?: boolean;
  context?: string;
}

interface State {
  hasError: boolean, error: Error | null, errorInfo: ErrorInfo | null, retryCount: number;
}

export class MessagingErrorBoundary extends Component<Props, State> {
  private maxRetries = 3;

  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: 0
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Messaging Error Boundary caught an error:', error, errorInfo);
    
    this.setState({
      error,
      errorInfo
    });

    // Call custom error handler
    this.props.onError?.(error, errorInfo);

    // Log to error reporting service
    this.logError(error, errorInfo);

    // Show user notification
    toast.error('Something went wrong with messaging', {
      description: 'We\'re working to fix this issue.',
      action: {
        label: 'Retry',
        onClick: () => this.handleRetry()
      }
    });
  }

  private logError = (error: Error, errorInfo: ErrorInfo) => {
    // In a real app, this would send to an error reporting service
    const errorReport = {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      context: this.props.context || 'messaging',
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    };

    console.error('Error Report:', errorReport);
    
    // TODO: Send to error reporting service
    // errorReportingService.report(errorReport);
  };

  private handleRetry = () => {
    if (this.state.retryCount < this.maxRetries) {
      this.setState(prevState => ({
        hasError: false,
        error: null,
        errorInfo: null,
        retryCount: prevState.retryCount + 1
      }));

      toast.success('Retrying...');
    } else {
      toast.error('Maximum retry attempts reached. Please refresh the page.');
    }
  };

  private handleRefresh = () => {
    window.location.reload();
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default error UI
      return (
        <Card className="max-w-md mx-auto mt-8 border-red-200 bg-red-50 dark:bg-red-900/20 dark:border-red-800">
          <CardHeader className="text-center">
            <div className="flex justify-center mb-4">
              <AlertTriangle className="w-12 h-12 text-red-500" />
    </div>
            <CardTitle className="text-red-700 dark:text-red-300">
              Messaging Error
            </CardTitle>
    </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-center text-sm text-red-600 dark:text-red-400">
              <p>Something went wrong with the messaging system.</p>
              {this.state.error && (
                <details className="mt-2 text-xs">
                  <summary className="cursor-pointer hover:underline">
                    Error Details
                  </summary>
                  <pre className="mt-2 p-2 bg-red-100 dark:bg-red-900/40 rounded text-left overflow-auto">
                    {this.state.error.message}
                  </pre>
    </details>
              )}
            </div>

            <div className="flex flex-col space-y-2">
              {this.props.showRetry !== false && this.state.retryCount < this.maxRetries && (
                <Button
                  onClick={this.handleRetry} variant="outline"
                  className="w-full"
                >
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Retry ({this.maxRetries - this.state.retryCount} attempts left)
                </Button>
              )}
              
              <Button
                onClick={this.handleRefresh} variant="default"
                className="w-full"
              >
                <MessageCircle className="w-4 h-4 mr-2" />
                Refresh Page
              </Button>
    </div>
            <div className="text-xs text-gray-500 text-center">
              If this problem persists, please contact support.
            </div>
    </CardContent>
        </Card>
      );
    }

    return this.props.children;
  }
}

export default MessagingErrorBoundary;