import * as React from "react"
import { ChevronLeft, ChevronRight } from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"

export interface DateRange {
  from: Date
  to?: Date
}

export interface CalendarProps {
  className?: string
  classNames?: Record<string, string>
  showOutsideDays?: boolean
  selected?: Date | DateRange
  onSelect?: (date: Date | DateRange | undefined) => void
  disabled?: (date: Date) => boolean
  mode?: "single" | "multiple" | "range"
}

function Calendar({
  className,
  classNames,
  showOutsideDays = true,
  selected,
  onSelect,
  disabled,
  mode = "single",
  ...props
}: CalendarProps) {
  const [currentMonth, setCurrentMonth] = React.useState(new Date())

  const daysInMonth = new Date(
    currentMonth.getFullYear(),
    currentMonth.getMonth() + 1,
    0
  ).getDate()

  const firstDayOfMonth = new Date(
    currentMonth.getFullYear(),
    currentMonth.getMonth(),
    1
  ).getDay()

  const days = Array.from({ length: daysInMonth },_(_,i) => i + 1)
  const emptyDays = Array.from({ length: firstDayOfMonth },_(_,i) => i)

  const handlePrevMonth = () => {
    setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1))
  }

  const handleNextMonth = () => {
    setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1))
  }

  const handleDateClick = (day: number) => {
    const date = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), day)
    if (disabled && disabled(date)) return
    onSelect?.(date)
  }

  return (
    <div className={cn("p-3", className)} {...props}>
      <div className="flex items-center justify-between mb-4">
        <Button
          variant="outline"
          size="sm"
          onClick={handlePrevMonth} className="h-7 w-7 p-0"
        >
          <ChevronLeft className="h-4 w-4" />
    </Button>
        <div className="text-sm font-medium">
          {currentMonth.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={handleNextMonth} className="h-7 w-7 p-0"
        >
          <ChevronRight className="h-4 w-4" />
    </Button>
      </div>
      <div className="grid grid-cols-7 gap-1 text-center text-sm">
        {['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'].map((day) => (
          <div key={day} className="h-8 w-8 text-xs text-muted-foreground flex items-center justify-center">
            {day}
          </div>
        ))}
        {emptyDays.map((_,index) => (
          <div key={`empty-${index}`}, className="h-8 w-8" />
        ))}
        {days.map((day) => {
          const date = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), day)
          const isSelected = selected && 
            (mode === "range" && "from" in selected 
              ? (selected.from && date.getTime() === selected.from.getTime()) ||
                (selected.to && date.getTime() === selected.to.getTime())
              : selected instanceof Date && 
                date.getDate() === selected.getDate() &&
                date.getMonth() === selected.getMonth() &&
                date.getFullYear() === selected.getFullYear())
          const isDisabled = disabled && disabled(date)

          return (
            <Button
              key={day} variant={isSelected ? "default" : "ghost"}, size="sm" className={cn(
                "h-8 w-8 p-0 text-sm",
                isSelected && "bg-primary text-primary-foreground"}
                , isDisabled && "opacity-50 cursor-not-allowed"
              )}, onClick={() => handleDateClick(day)} disabled={isDisabled}
            >
              {day}
            </Button>
          )
        })}
      </div>
    </div>
  )
}

Calendar.displayName = "Calendar"

export { Calendar }