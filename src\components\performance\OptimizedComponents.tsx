// High-performance React component optimizations
import React from 'react';
import { ErrorBoundary } from 'react-error-boundary';

// Optimized component wrapper with memoization
export const withOptimization = <P extends Record<string, any>>(
  Component: React.ComponentType<P>,
  options: {
    memo?: boolean;
    displayName?: string;
    shouldUpdate?: (prevProps: P, nextProps: P) => boolean;
  } = {}
) => {
  const { memo = true, displayName, shouldUpdate } = options;

  let OptimizedComponent = Component;

  // Apply React.memo with custom comparison
  if (memo) {
    OptimizedComponent = React.memo(Component, shouldUpdate);
  }

  // Set display name for debugging
  if (displayName) {
    OptimizedComponent.displayName = displayName;
  }

  return OptimizedComponent;
};

// Optimized list component with virtualization support
export const OptimizedList = React.memo<{
  items: unknown[], renderItem: (item: unknown, index: number) => React.ReactNode;
  getItemKey?: (item: unknown, index: number) => string | number;
  className?: string;
}>(({ items, renderItem, getItemKey, className }) => {
  // Memoize the rendered items to prevent unnecessary re-renders
  const renderedItems = React.useMemo(() => {
    return items.map((item, index) => {
      const key = getItemKey ? getItemKey(item, index) : index;
      return (
        <React.Fragment key={key}>
          {renderItem(item, index)}
        </React.Fragment>
      );
    });
  }, [items, renderItem, getItemKey]);

  return (
    <div className={className}>
      {renderedItems}
    </div>
  );
});

OptimizedList.displayName = 'OptimizedList';

// Optimized form component with controlled updates
export const OptimizedFormField = React.memo<{
  value: string, onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  debounceMs?: number;
}>(({ value, onChange, placeholder, className, debounceMs = 300 }) => {
  const [localValue, setLocalValue] = React.useState(value);
  const timeoutRef = React.useRef<NodeJS.Timeout>();

  // Update local value when prop changes
  React.useEffect(() => {
    setLocalValue(value);
  }, [value]);

  // Debounced change handler
  const handleChange = React.useCallback((newValue: string) => {
    setLocalValue(newValue);
    
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    timeoutRef.current = setTimeout(() => {
      onChange(newValue);
    }, debounceMs);
  }, [onChange, debounceMs]);

  // Cleanup timeout on unmount
  React.useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return (
    <input
      type="text"
      value={localValue} onChange={(e) => handleChange(e.target.value)}, placeholder={placeholder} className={className}
    />
  );
});

OptimizedFormField.displayName = 'OptimizedFormField';

// Optimized image component with lazy loading and error handling
export const OptimizedImage = React.memo<{
  src: string, alt: string;
  className?: string;
  fallbackSrc?: string;
  loading?: 'lazy' | 'eager';
}>(({ src, alt, className, fallbackSrc, loading = 'lazy' }) => {
  const [imageSrc, setImageSrc] = React.useState(src);
  const [isLoading, setIsLoading] = React.useState(true);
  const [hasError, setHasError] = React.useState(false);

  const handleLoad = React.useCallback(() => {
    setIsLoading(false);
    setHasError(false);
  }, []);

  const handleError = React.useCallback(() => {
    setIsLoading(false);
    setHasError(true);
    if (fallbackSrc && imageSrc !== fallbackSrc) {
      setImageSrc(fallbackSrc);
    }
  }, [fallbackSrc, imageSrc]);

  // Update src when prop changes
  React.useEffect(() => {
    if (src !== imageSrc) {
      setImageSrc(src);
      setIsLoading(true);
      setHasError(false);
    }
  }, [src, imageSrc]);

  return (
    <div className={`relative ${className || ''}`}>
      {isLoading && (
        <div className="absolute inset-0 bg-gray-200 animate-pulse" />
      )}
      
      <img
        src={imageSrc} alt={alt}, loading={loading} onLoad={handleLoad}, onError={handleError} className={`${className || ''} ${isLoading ? 'opacity-0' : 'opacity-100'} transition-opacity`}
      />
      
      {hasError && !fallbackSrc && (
        <div className="absolute inset-0 bg-gray-100 flex items-center justify-center text-gray-500 text-sm">
          Failed to load
        </div>
      )}
    </div>
  );
});

OptimizedImage.displayName = 'OptimizedImage';

// Optimized modal component with portal and focus management
export const OptimizedModal = React.memo<{
  isOpen: boolean, onClose: () => void; children: React.ReactNode;
  className?: string;
}>(({ isOpen, onClose, children, className }) => {
  const modalRef = React.useRef<HTMLDivElement>(null);
  const previousActiveElement = React.useRef<Element | null>(null);

  // Handle escape key
  React.useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      previousActiveElement.current = document.activeElement;
      
      // Focus the modal
      setTimeout(() => {
        modalRef.current?.focus();
      }, 0);
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      
      // Restore focus
      if (previousActiveElement.current instanceof HTMLElement) {
        previousActiveElement.current.focus();
      }
    };
  }, [isOpen, onClose]);

  // Handle backdrop click
  const handleBackdropClick = React.useCallback((e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  }, [onClose]);

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 z-50 bg-black/50 flex items-center justify-center p-4"
      onClick={handleBackdropClick}
    >
      <div
        ref={modalRef} className={`bg-white rounded-lg shadow-xl max-w-lg w-full max-h-[90vh] overflow-auto ${className || ''}`}, tabIndex={-1} role="dialog"
        aria-modal="true"
      >
        {children}
      </div>
    </div>
  );
});

OptimizedModal.displayName = 'OptimizedModal';

// Performance monitoring HOC
export const withPerformanceMonitoring = <P extends Record<string, any>>(
  Component: React.ComponentType<P>,
  componentName: string
) => {
  return React.memo<P>((props) => {
    const renderStart = React.useRef<number>();
    const [renderTime, setRenderTime] = React.useState<number>(0);

    // Measure render time
    renderStart.current = performance.now();

    React.useEffect(() => {
      if (renderStart.current) {
        const duration = performance.now() - renderStart.current;
        setRenderTime(duration);
        
        // Log slow renders in development
        if (process.env.NODE_ENV === 'development' && duration > 16) {
          console.warn(`Slow render detected in ${componentName}: ${duration.toFixed(2)}ms`);
        }
      }
    });

    return (
      <ErrorBoundary
        fallback={<div>Error in {componentName}</div>}, onError={(error) => {
          console.error(`Error in ${componentName}:`, error);
        }}
      >
        <Component {...props} />
        {process.env.NODE_ENV === 'development' && renderTime > 0 && (
          <div className="text-xs text-gray-500 border-t pt-1 mt-1">
            Render: {renderTime.toFixed(2)}ms
          </div>
        )}
      </ErrorBoundary>
    );
  });
};

// Optimized context provider
export const createOptimizedContext = <T,>() => {
  const Context = React.createContext<T | undefined>(undefined);
  
  const Provider = React.memo<{
    value: T, children: React.ReactNode;
  }>(({ value, children }) => {
    // Memoize the value to prevent unnecessary re-renders
    const memoizedValue = React.useMemo(() => value, [value]);
    
    return (
      <Context.Provider value={memoizedValue}>
        {children}
      </Context.Provider>
    );
  });
  
  const useContext = () => {
    const context = React.useContext(Context);
    if (context === undefined) {
      throw new Error('useContext must be used within Provider');
    }
    return context;
  };
  
  return { Provider, useContext };
};
