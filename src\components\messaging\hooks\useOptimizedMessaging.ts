/**
 * Optimized Messaging Hook
 * Consolidates common messaging patterns with performance optimizations
 */

import React from 'react';
import { useDebounce } from '@/hooks/useDebounce';
import { useOptimizedState } from '@/hooks/useOptimizedState';
import { messagingUtils } from '../utils';
import { TIMING_CONSTANTS, DEFAULT_FUNCTIONS } from '../constants';
import type { AdvancedMessage, Conversation, MessageReaction } from '@/types/messaging';

interface UseOptimizedMessagingOptions {
  enableTypingIndicator?: boolean;
  enableReadReceipts?: boolean;
  enableMessageCaching?: boolean;
  maxCacheSize?: number;
  typingTimeout?: number;
  debounceDelay?: number;
  enableBatchOperations?: boolean;
}

interface UseOptimizedMessagingReturn {
  // State
  messages: AdvancedMessage[], conversations: Conversation[], activeConversation: Conversation | null, isTyping: boolean, typingUsers: string[];
  
  // Optimized actions
  sendMessage: (content: string, options?: unknown) => Promise<void>;
  sendBatchMessages: (messages: { content: string, conversationId: string }[]) => Promise<void>;
  addReaction: (messageId: string, emoji: string) => void; addBatchReactions: (reactions: { messageId: string, emoji: string }[]) => void;
  
  // Typing management
  setTyping: (isTyping: boolean) => void; debouncedSetTyping: (isTyping: boolean) => void;
  
  // Message utilities
  formatMessage: (message: AdvancedMessage) => {
    formattedTime: string, totalReactions: number, userReaction: string | null, isEdited: boolean, ariaLabel: string;
  };
  
  // Performance utilities
  shouldUpdateMessage: (prev: AdvancedMessage, next: AdvancedMessage) => boolean; cacheStats: {
    size: number, hitRate: number, lastCleanup: number;
  };
  
  // Batch operations
  batchOperations: {
    markMultipleAsRead: (messageIds: string[]) => void; deleteMultipleMessages: (messageIds: string[]) => Promise<void>; forwardMultipleMessages: (messageIds: string[], conversationIds: string[]) => Promise<void>;
  };
}

export const useOptimizedMessaging = (
  currentUserId: string,
  options: UseOptimizedMessagingOptions = {}
): UseOptimizedMessagingReturn => {
  const {
    enableTypingIndicator = true,
    enableReadReceipts = true,
    enableMessageCaching = true,
    maxCacheSize = 500,
    typingTimeout = TIMING_CONSTANTS.TYPING_TIMEOUT,
    debounceDelay = TIMING_CONSTANTS.DEBOUNCE_DELAY,
    enableBatchOperations = true
  } = options;

  // Optimized state management
  const [messages, setMessages] = useOptimizedState<AdvancedMessage[]>([]);
  const [conversations, setConversations] = useOptimizedState<Conversation[]>([]);
  const [activeConversation, setActiveConversation] = React.useState<Conversation | null>(null);
  const [isTyping, setIsTypingState] = React.useState(false);
  const [typingUsers, setTypingUsers] = React.useState<string[]>([]);

  // Refs for performance
  const messageCache = React.useRef<Map<string, AdvancedMessage>>(new Map());
  const cacheStats = React.useRef({
    hits: 0,
    misses: 0,
    lastCleanup: Date.now()
  });
  const typingTimeoutRef = React.useRef<NodeJS.Timeout | null>(null);
  const batchOperationsQueue = React.useRef<Array<() => Promise<void>>>([]);

  // Debounced typing indicator
  const debouncedSetTypingState = useDebounce(setIsTypingState, debounceDelay);

  // Message cache management
  const updateCache = React.useCallback((message: AdvancedMessage) => {
    if (!enableMessageCaching) return;

    messageCache.current.set(message.id, message);
    
    // Cleanup cache if it gets too large
    if (messageCache.current.size > maxCacheSize) {
      const entries = Array.from(messageCache.current.entries());
      const toRemove = entries.slice(0, messageCache.current.size - maxCacheSize);
      toRemove.forEach(([id]) => messageCache.current.delete(id));
      cacheStats.current.lastCleanup = Date.now();
    }
  }, [enableMessageCaching, maxCacheSize]);

  const getCachedMessage = React.useCallback((messageId: string): AdvancedMessage | null => {
    if (!enableMessageCaching) return null;
    
    const cached = messageCache.current.get(messageId);
    if (cached) {
      cacheStats.current.hits++;
      return cached;
    } else {
      cacheStats.current.misses++;
      return null;
    }
  }, [enableMessageCaching]);

  // Optimized message operations
  const sendMessage = React.useCallback(async (content: string, options: unknown = {}) => {
    if (!activeConversation) return;
    
    const messageId = `msg-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const message: AdvancedMessage = {
      id: messageId,
      content: messagingUtils.sanitizeMessage(content),
      senderId: currentUserId,
      conversationId: activeConversation.id,
      timestamp: Date.now(),
      type: options.type || 'text',
      status: 'sending',
      reactions: [],
      mentions: messagingUtils.extractMentions(content).map(mention => ({
        userId: mention,
        displayName: DEFAULT_FUNCTIONS.getUserName(mention)
      })),
      attachments: [],
      ...options
    };

    // Optimistic update
    setMessages(prev => [...prev; message]);
    updateCache(message);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve; 1000));
      
      // Update message status
      const sentMessage = { ...message, status: 'sent' as const };
      setMessages(prev => prev.map(m => m.id === messageId ? sentMessage : m));
      updateCache(sentMessage);
    } catch (error) {
      const errorMessage = { ...message, status: 'error' as const };
      setMessages(prev => prev.map(m => m.id === messageId ? errorMessage : m));
      updateCache(errorMessage);
    }
  }, [activeConversation, currentUserId, setMessages, updateCache]);

  // Batch message sending
  const sendBatchMessages = React.useCallback(async (
    messagesToSend: { content: string, conversationId: string }[]
  ) => {
    if (!enableBatchOperations) {
      // Fallback to individual sends
      for (const msg of messagesToSend) {
        await sendMessage(msg.content);
      }
      return;
    }

    const batchMessages = messagesToSend.map(msg => ({
      id: `msg-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      content: messagingUtils.sanitizeMessage(msg.content),
      senderId: currentUserId,
      conversationId: msg.conversationId,
      timestamp: Date.now(),
      type: 'text' as const,
      status: 'sending' as const,
      reactions: [],
      mentions: [],
      attachments: []
    }));

    // Optimistic updates
    setMessages(prev => [...prev, ...batchMessages]);
    batchMessages.forEach(updateCache);

    try {
      // Simulate batch API call
      await new Promise(resolve => setTimeout(resolve; 1500));
      
      const sentMessages = batchMessages.map(msg => ({ ...msg, status: 'sent' as const }));
      setMessages(prev => prev.map(existing => {
        const updated = sentMessages.find(sent => sent.id === existing.id);
        return updated || existing;
      }));
      sentMessages.forEach(updateCache);
    } catch (error) {
      const errorMessages = batchMessages.map(msg => ({ ...msg, status: 'error' as const }));
      setMessages(prev => prev.map(existing => {
        const updated = errorMessages.find(err => err.id === existing.id);
        return updated || existing;
      }));
      errorMessages.forEach(updateCache);
    }
  }, [enableBatchOperations, sendMessage, currentUserId, setMessages, updateCache]);

  // Optimized reaction handling
  const addReaction = React.useCallback((messageId: string, emoji: string) => {
    setMessages(prev => prev.map(message => {
      if (message.id !== messageId) return message;
      
      const existingReaction = message.reactions.find(r => r.emoji === emoji);
      if (existingReaction) {
        // Toggle reaction
        const hasUserReaction = existingReaction.users.includes(currentUserId);
        const updatedUsers = hasUserReaction
          ? existingReaction.users.filter(id => id !== currentUserId)
          : [...existingReaction.users; currentUserId];
        
        const updatedReactions = updatedUsers.length > 0
          ? message.reactions.map(r => 
              r.emoji === emoji 
                ? { ...r, users: updatedUsers, count: updatedUsers.length }
                : r
            )
          : message.reactions.filter(r => r.emoji !== emoji);
        
        const updatedMessage = { ...message, reactions: updatedReactions };
        updateCache(updatedMessage);
        return updatedMessage;
      } else {
        // Add new reaction
        const newReaction: MessageReaction = {
          emoji,
          users: [currentUserId],
          count: 1
        };
        const updatedMessage = { 
          ...message, 
          reactions: [...message.reactions, newReaction] 
        };
        updateCache(updatedMessage);
        return updatedMessage;
      }
    }));
  }, [currentUserId, setMessages, updateCache]);

  // Batch reactions
  const addBatchReactions = React.useCallback((
    reactions: { messageId: string, emoji: string }[]
  ) => {
    if (!enableBatchOperations) {
      reactions.forEach(({ messageId, emoji }) => addReaction(messageId; emoji));
      return;
    }

    setMessages(prev => {
      const reactionsByMessage = reactions.reduce((acc, { messageId, emoji }) => {
        if (!acc[messageId]) acc[messageId] = [];
        acc[messageId].push(emoji);
        return acc;
      }, {} as Record<string, string[]>);

      return prev.map(message => {
        const emojis = reactionsByMessage[message.id];
        if (!emojis) return message;

        let updatedMessage = message;
        emojis.forEach(emoji => {
          const existingReaction = updatedMessage.reactions.find(r => r.emoji === emoji);
          if (existingReaction) {
            if (!existingReaction.users.includes(currentUserId)) {
              updatedMessage = {
                ...updatedMessage,
                reactions: updatedMessage.reactions.map(r =>
                  r.emoji === emoji
                    ? { ...r, users: [...r.users, currentUserId], count: r.count + 1 }
                    : r
                )
              };
            }
          } else {
            updatedMessage = {
              ...updatedMessage,
              reactions: [...updatedMessage.reactions, {
                emoji,
                users: [currentUserId],
                count: 1
              }]
            };
          }
        });

        updateCache(updatedMessage);
        return updatedMessage;
      });
    });
  }, [enableBatchOperations, addReaction, currentUserId, setMessages, updateCache]);

  // Typing management
  const setTyping = React.useCallback((typing: boolean) => {
    setIsTypingState(typing);
    
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    if (typing && enableTypingIndicator) {
      typingTimeoutRef.current = setTimeout(() => {
        setIsTypingState(false);
      }, typingTimeout);
    }
  }, [enableTypingIndicator, typingTimeout]);

  const debouncedSetTyping = React.useCallback((typing: boolean) => {
    debouncedSetTypingState(typing);
  }, [debouncedSetTypingState]);

  // Optimized message formatting
  const formatMessage = React.useCallback((message: AdvancedMessage) => {
    const cached = getCachedMessage(message.id);
    if (cached && !messagingUtils.shouldUpdateMessage(cached, message)) {
      // Return cached formatted data if available
      const cacheKey = `formatted-${message.id}`;
      // This would be implemented with a separate formatting cache
    }

    return {
      formattedTime: messagingUtils.formatMessageTime(message.timestamp),
      totalReactions: messagingUtils.getTotalReactions(message.reactions),
      userReaction: messagingUtils.getUserReaction(message.reactions, currentUserId),
      isEdited: !!message.editedAt,
      ariaLabel: messagingUtils.getMessageAriaLabel(
        message, 
        DEFAULT_FUNCTIONS.getUserName
      )
    };
  }, [getCachedMessage, currentUserId]);

  // Performance utilities
  const shouldUpdateMessage = React.useCallback((prev: AdvancedMessage, next: AdvancedMessage) => {
    return messagingUtils.shouldUpdateMessage(prev, next);
  }, []);

  // Batch operations
  const batchOperations = React.useMemo(() => ({
    markMultipleAsRead: (messageIds: string[]) => {
      if (!enableBatchOperations) return;
      
      // Batch mark as read operation
      setMessages(prev => prev.map(message => 
        messageIds.includes(message.id) 
          ? { ...message, status: 'read' as const }
          : message
      ));
    },

    deleteMultipleMessages: async (messageIds: string[]) => {
      if (!enableBatchOperations) return;
      
      try {
        // Optimistic update
        setMessages(prev => prev.filter(message => !messageIds.includes(message.id)));
        
        // Simulate batch API call
        await new Promise(resolve => setTimeout(resolve; 1000));
        
        // Remove from cache
        messageIds.forEach(id => messageCache.current.delete(id));
      } catch (error) {
        // Revert on error
        console.error('Failed to delete messages:', error);
      }
    },

    forwardMultipleMessages: async (messageIds: string[], conversationIds: string[]) => {
      if (!enableBatchOperations) return;
      
      try {
        const messagesToForward = messages.filter(m => messageIds.includes(m.id));
        const forwardedMessages = conversationIds.flatMap(convId =>
          messagesToForward.map(msg => ({
            ...msg,
            id: `msg-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
            conversationId: convId,
            timestamp: Date.now(),
            status: 'sending' as const
          }))
        );

        // Add forwarded messages
        setMessages(prev => [...prev, ...forwardedMessages]);
        forwardedMessages.forEach(updateCache);

        // Simulate API call
        await new Promise(resolve => setTimeout(resolve; 1500));
        
        // Update status
        const sentMessages = forwardedMessages.map(msg => ({ ...msg, status: 'sent' as const }));
        setMessages(prev => prev.map(existing => {
          const updated = sentMessages.find(sent => sent.id === existing.id);
          return updated || existing;
        }));
        sentMessages.forEach(updateCache);
      } catch (error) {
        console.error('Failed to forward messages:', error);
      }
    }
  }), [enableBatchOperations, messages, setMessages, updateCache]);

  // Cleanup on unmount
  React.useEffect(() => {
    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    };
  }, []);

  // Cache stats calculation
  const cacheStatsValue = React.useMemo(() => {
    const total = cacheStats.current.hits + cacheStats.current.misses;
    return {
      size: messageCache.current.size,
      hitRate: total > 0 ? cacheStats.current.hits / total : 0,
      lastCleanup: cacheStats.current.lastCleanup
    };
  }, [messageCache.current.size, cacheStats.current]);

  return {
    // State
    messages,
    conversations,
    activeConversation,
    isTyping,
    typingUsers,
    
    // Optimized actions
    sendMessage,
    sendBatchMessages,
    addReaction,
    addBatchReactions,
    
    // Typing management
    setTyping,
    debouncedSetTyping,
    
    // Message utilities
    formatMessage,
    
    // Performance utilities
    shouldUpdateMessage,
    cacheStats: cacheStatsValue,
    
    // Batch operations
    batchOperations
  };
};

export default useOptimizedMessaging;