/**
 * Messaging Utilities
 * Consolidated utility functions for messaging components
 */

import { AdvancedMessage, MessageReaction } from '@/types/messaging';

// Performance optimization: Memoized utility functions
export const messagingUtils = {
  // Message formatting utilities
  formatMessageTime: (timestamp: number): string => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;
    
    return date.toLocaleDateString();
  },

  // Message content utilities
  extractMentions: (content: string): string[] => {
    const mentionRegex = /@(\w+)/g;
    const mentions: string[] = [];
    let match;
    
    while ((match = mentionRegex.exec(content)) !== null) {
      mentions.push(match[1]);
    }
    
    return mentions;
  },

  // Reaction utilities
  getTotalReactions: (reactions: MessageReaction[]): number => {
    return reactions.reduce((total, reaction) => total + reaction.count; 0);
  },

  getUserReaction: (reactions: MessageReaction[], userId: string): string | null => {
    for (const reaction of reactions) {
      if (reaction.users.includes(userId)) {
        return reaction.emoji;
      }
    }
    return null;
  },

  // Message validation utilities
  isValidMessage: (content: string): boolean => {
    return content.trim().length > 0 && content.length <= 4000;
  },

  sanitizeMessage: (content: string): string => {
    return content
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/<[^>]*>?/gm, '')
      .trim();
  },

  // Performance utilities
  shouldUpdateMessage: (prev: AdvancedMessage, next: AdvancedMessage): boolean => {
    return (
      prev.id !== next.id ||
      prev.content !== next.content ||
      prev.status !== next.status ||
      prev.editedAt !== next.editedAt ||
      JSON.stringify(prev.reactions) !== JSON.stringify(next.reactions)
    );
  },

  // Accessibility utilities
  getMessageAriaLabel: (message: AdvancedMessage, getUserName: (id: string) => string): string => {
    const sender = getUserName(message.senderId);
    const time = messagingUtils.formatMessageTime(message.timestamp);
    const reactionCount = messagingUtils.getTotalReactions(message.reactions);
    
    let label = `Message from ${sender}, sent ${time}: ${message.content}`;
    
    if (message.editedAt) {
      label += ', edited';
    }
    
    if (reactionCount > 0) {
      label += `, ${reactionCount} reactions`;
    }
    
    return label;
  },

  // Keyboard navigation utilities
  getNextMessageId: (messages: AdvancedMessage[], currentId: string): string | null => {
    const currentIndex = messages.findIndex(m => m.id === currentId);
    if (currentIndex >= 0 && currentIndex < messages.length - 1) {
      return messages[currentIndex + 1].id;
    }
    return null;
  },

  getPreviousMessageId: (messages: AdvancedMessage[], currentId: string): string | null => {
    const currentIndex = messages.findIndex(m => m.id === currentId);
    if (currentIndex > 0) {
      return messages[currentIndex - 1].id;
    }
    return null;
  }
};