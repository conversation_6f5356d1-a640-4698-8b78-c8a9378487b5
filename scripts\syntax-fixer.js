#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

class SyntaxErrorFixer {
  constructor() {
    this.fixedFiles = 0;
    this.fixedErrors = 0;
    this.errorPatterns = [
      // Function parameter fixes
      {
        pattern: /(\w+)\s*:\s*\{\s*([^}]+)\s*\}\s*,?\s*\)/g,
        fix: (match, paramName, types) => {
          // Fix incomplete destructured parameters
          const cleanTypes = types.replace(/,$/, '').trim();
          return `${paramName}: { ${cleanTypes} })`;
        },
        description: 'Fix incomplete destructured parameters'
      },
      // Generic type fixes
      {
        pattern: /\w+<[^>]*$/gm,
        fix: (match) => {
          // Add missing closing > for generic types
          return match + '>';
        },
        description: 'Fix incomplete generic types'
      },
      // Object type fixes
      {
        pattern: /:\s*\{\s*([^}]*),\s*$/gm,
        fix: (match, content) => {
          // Remove trailing comma in object types
          return `: { ${content.trim()} }`;
        },
        description: 'Fix trailing commas in object types'
      },
      // Interface/type comma fixes
      {
        pattern: /^\s*(\w+)\s*:\s*([^,;]+),\s*$/gm,
        fix: (match, key, value) => {
          // Fix incomplete interface properties
          return `  ${key}: ${value.trim()};`;
        },
        description: 'Fix interface property syntax'
      },
      // Array/tuple fixes
      {
        pattern: /\[\s*([^[\]]*),\s*$/gm,
        fix: (match, content) => {
          // Remove trailing comma in arrays
          return `[${content.trim()}]`;
        },
        description: 'Fix trailing commas in arrays'
      },
      // React component prop fixes
      {
        pattern: /React\.FC<\s*\{\s*([^}]*),\s*$/gm,
        fix: (match, props) => {
          // Fix React.FC props
          return `React.FC<{ ${props.trim()} }>`;
        },
        description: 'Fix React.FC prop types'
      },
      // Function return type fixes
      {
        pattern: /\)\s*:\s*([^{;=]+),\s*$/gm,
        fix: (match, returnType) => {
          // Fix function return types
          return `): ${returnType.trim()}`;
        },
        description: 'Fix function return types'
      },
      // Hook dependency fixes
      {
        pattern: /\[\s*([^[\]]*),\s*\]/g,
        fix: (match, deps) => {
          // Fix hook dependencies
          const cleanDeps = deps.replace(/,\s*$/, '').trim();
          return `[${cleanDeps}]`;
        },
        description: 'Fix hook dependencies'
      },
      // JSX prop fixes
      {
        pattern: /\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s*=\s*\{([^}]*),\s*$/gm,
        fix: (match, prop, value) => {
          // Fix JSX props
          return ` ${prop}={${value.trim()}}`;
        },
        description: 'Fix JSX props'
      },
      // Import/export fixes
      {
        pattern: /^(import|export)\s*\{\s*([^}]*),\s*\}\s*from/gm,
        fix: (match, keyword, imports) => {
          // Fix import/export statements
          const cleanImports = imports.replace(/,\s*$/, '').trim();
          return `${keyword} { ${cleanImports} } from`;
        },
        description: 'Fix import/export statements'
      },
      // Variable declaration fixes
      {
        pattern: /^(\s*)(const|let|var)\s+([^=]+),\s*$/gm,
        fix: (match, indent, keyword, declaration) => {
          // Fix variable declarations
          return `${indent}${keyword} ${declaration.trim()};`;
        },
        description: 'Fix variable declarations'
      },
      // Method/function fixes
      {
        pattern: /(\w+)\s*\(\s*([^)]*),\s*\)\s*\{/g,
        fix: (match, name, params) => {
          // Fix method parameters
          const cleanParams = params.replace(/,\s*$/, '').trim();
          return `${name}(${cleanParams}) {`;
        },
        description: 'Fix method parameters'
      },
      // Type annotation fixes
      {
        pattern: /:\s*([^,;{}]+),\s*;/g,
        fix: (match, type) => {
          // Fix type annotations
          return `: ${type.trim()};`;
        },
        description: 'Fix type annotations'
      },
      // Switch/case fixes
      {
        pattern: /case\s+([^:]+),\s*:/g,
        fix: (match, caseValue) => {
          // Fix switch cases
          return `case ${caseValue.trim()}:`;
        },
        description: 'Fix switch cases'
      },
      // Conditional fixes
      {
        pattern: /\?\s*([^:]+),\s*:/g,
        fix: (match, trueValue) => {
          // Fix conditional expressions
          return `? ${trueValue.trim()} :`;
        },
        description: 'Fix conditional expressions'
      },
      // Function call fixes
      {
        pattern: /(\w+)\s*\(\s*([^)]*),\s*\)/g,
        fix: (match, funcName, args) => {
          // Fix function calls
          const cleanArgs = args.replace(/,\s*$/, '').trim();
          return `${funcName}(${cleanArgs})`;
        },
        description: 'Fix function calls'
      }
    ];
  }

  fixFile(filePath) {
    try {
      let content = fs.readFileSync(filePath, 'utf8');
      let modified = false;
      let localFixes = 0;

      // Apply all fix patterns
      for (const pattern of this.errorPatterns) {
        const originalContent = content;
        content = content.replace(pattern.pattern, pattern.fix);
        if (content !== originalContent) {
          modified = true;
          localFixes++;
        }
      }

      // Additional specific fixes
      content = this.applySpecificFixes(content);

      if (modified || content !== fs.readFileSync(filePath, 'utf8')) {
        fs.writeFileSync(filePath, content);
        this.fixedFiles++;
        this.fixedErrors += localFixes;
        console.log(`✅ Fixed ${localFixes} syntax errors in ${path.basename(filePath)}`);
        return true;
      }

      return false;
    } catch (error) {
      console.error(`❌ Error fixing ${filePath}:`, error.message);
      return false;
    }
  }

  applySpecificFixes(content) {
    // Fix specific common errors
    
    // Fix incomplete React.FC definitions
    content = content.replace(
      /React\.FC<\s*\{\s*([^}]*)\s*,\s*$/gm,
      'React.FC<{ $1 }>'
    );

    // Fix incomplete interface definitions
    content = content.replace(
      /interface\s+(\w+)\s*\{\s*([^}]*),\s*$/gm,
      'interface $1 {\n  $2\n}'
    );

    // Fix incomplete type definitions
    content = content.replace(
      /type\s+(\w+)\s*=\s*\{\s*([^}]*),\s*$/gm,
      'type $1 = {\n  $2\n}'
    );

    // Fix incomplete arrow functions
    content = content.replace(
      /=>\s*\{\s*([^}]*),\s*$/gm,
      '=> {\n  $1\n}'
    );

    // Fix incomplete array destructuring
    content = content.replace(
      /\[\s*([^[\]]*),\s*\]\s*=/g,
      '[$1] ='
    );

    // Fix incomplete object destructuring
    content = content.replace(
      /\{\s*([^{}]*),\s*\}\s*=/g,
      '{ $1 } ='
    );

    // Fix hook dependencies with trailing commas
    content = content.replace(
      /(useEffect|useMemo|useCallback)\s*\([^,]+,\s*\[\s*([^[\]]*),\s*\]\s*\)/g,
      '$1($2, [$3])'
    );

    // Fix JSX expressions with trailing commas
    content = content.replace(
      /\{\s*([^{}]*),\s*\}/g,
      '{ $1 }'
    );

    // Fix template literals with trailing commas
    content = content.replace(
      /`([^`]*),\s*`/g,
      '`$1`'
    );

    // Fix console statements
    content = content.replace(
      /console\.(log|error|warn|info)\s*\(\s*([^)]*),\s*\)/g,
      'console.$1($2)'
    );

    // Fix return statements
    content = content.replace(
      /return\s*\{\s*([^}]*),\s*\}/g,
      'return { $1 }'
    );

    // Fix async function definitions
    content = content.replace(
      /async\s+(\w+)\s*\(\s*([^)]*),\s*\)/g,
      'async $1($2)'
    );

    // Fix generic function calls
    content = content.replace(
      /(\w+)<([^>]*)>\s*\(\s*([^)]*),\s*\)/g,
      '$1<$2>($3)'
    );

    return content;
  }

  processDirectory(dirPath) {
    const entries = fs.readdirSync(dirPath, { withFileTypes: true });

    for (const entry of entries) {
      const fullPath = path.join(dirPath, entry.name);

      if (entry.isDirectory() && !entry.name.startsWith('.') && entry.name !== 'node_modules') {
        this.processDirectory(fullPath);
      } else if (entry.isFile() && /\.(ts|tsx|js|jsx)$/.test(entry.name)) {
        this.fixFile(fullPath);
      }
    }
  }

  run() {
    console.log('🔧 Starting comprehensive syntax error fixing...\n');

    const srcPath = path.join(process.cwd(), 'src');
    if (fs.existsSync(srcPath)) {
      this.processDirectory(srcPath);
    }

    console.log('\n📊 Syntax Fix Summary:');
    console.log(`✅ Files processed: ${this.fixedFiles}`);
    console.log(`🔧 Syntax errors fixed: ${this.fixedErrors}`);
    console.log('\n🎉 Syntax fixing complete!');

    return {
      filesFixed: this.fixedFiles,
      errorsFixed: this.fixedErrors
    };
  }
}

// Run the fixer
const fixer = new SyntaxErrorFixer();
fixer.run();
