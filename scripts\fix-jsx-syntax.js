#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Common JSX syntax fixes
const fixes = [
  // Fix comma placement between JSX props
  {
    pattern: /(\w+)=\{([^}]+)\}\s+(\w+)=\{/g,
    replacement: '$1={$2}\n                      $3={'
  },
  // Fix semicolons that should be commas in function calls
  {
    pattern: /setTimeout\(([^,]+);\s*(\d+)\)/g,
    replacement: 'setTimeout($1, $2)'
  },
  // Fix Promise resolve calls
  {
    pattern: /resolve\s*;\s*(\d+)/g,
    replacement: 'resolve, $1'
  },
  // Fix JSX prop syntax with commas
  {
    pattern: /(\w+)=\{([^}]+)\}\s+(\w+)=/g,
    replacement: '$1={$2}\n                      $3='
  }
];

function fixFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    fixes.forEach(fix => {
      const newContent = content.replace(fix.pattern, fix.replacement);
      if (newContent !== content) {
        content = newContent;
        modified = true;
      }
    });
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`Fixed: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`Error fixing ${filePath}:`, error.message);
    return false;
  }
}

function walkDirectory(dir, extensions = ['.tsx', '.ts', '.jsx', '.js']) {
  const files = [];
  
  function walk(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        walk(fullPath);
      } else if (stat.isFile() && extensions.some(ext => item.endsWith(ext))) {
        files.push(fullPath);
      }
    }
  }
  
  walk(dir);
  return files;
}

function main() {
  const srcDir = path.join(__dirname, '..', 'src');
  const files = walkDirectory(srcDir);
  
  console.log(`Found ${files.length} files to check...`);
  
  let fixedCount = 0;
  
  files.forEach(file => {
    if (fixFile(file)) {
      fixedCount++;
    }
  });
  
  console.log(`\nFixed ${fixedCount} files out of ${files.length} total files.`);
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}
