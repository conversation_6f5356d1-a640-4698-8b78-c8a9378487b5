import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation, useParams } from 'react-router-dom';
import MessagingIntegrationService from '../../services/messaging/MessagingIntegrationService';
import UnifiedMessagingContainer from './UnifiedMessagingContainer';

interface MessagingRouterProps {
  // Allow customization of routing behavior
  basePath?: string;
  enableDeepLinking?: boolean;
  onNavigationChange?: (path: string) => void;
}

const MessagingRouter: React.FC<MessagingRouterProps> = ({
  basePath = '/messages',
  enableDeepLinking = true,
  onNavigationChange
}) => {
  const navigate = useNavigate();
  const location = useLocation();
  const params = useParams();
  
  const [integrationService] = useState(() => MessagingIntegrationService.getInstance());
  const [isInitialized, setIsInitialized] = useState(false);

  // Initialize routing integration
  useEffect(() => {
    // Set up navigation change listener
    if (onNavigationChange) {
      onNavigationChange(location.pathname);
    }

    // Handle deep linking for conversations
    if (enableDeepLinking && params.conversationId) {
      // Emit event to select conversation
      const event = new CustomEvent('messaging:select-conversation', {
        detail: { conversationId: params.conversationId }
      });
      window.dispatchEvent(event);
    }

    setIsInitialized(true);
  }, [location.pathname, params.conversationId, enableDeepLinking, onNavigationChange]);

  // Handle migration completion
  const handleMigrationComplete = () => {
    // Optionally navigate to a specific route after migration
    if (location.pathname === basePath && params.conversationId) {
      navigate(`${basePath}/${params.conversationId}`, { replace: true });
    }
  };

  // Handle migration error
  const handleMigrationError = (error: string) => {
    console.error('Migration error in router:', error);
    
    // Optionally show a toast or navigate to an error page
    if (window.toast) {
      window.toast.error('Failed to upgrade messaging system');
    }
  };

  // Set up conversation navigation listeners
  useEffect(() => {
    const handleConversationSelect = (event: CustomEvent) => {
      const { conversationId } = event.detail;
      
      if (enableDeepLinking && conversationId) {
        const newPath = `${basePath}/${conversationId}`;
        if (location.pathname !== newPath) {
          navigate(newPath, { replace: false });
        }
      }
    };

    const handleConversationClose = () => {
      if (enableDeepLinking && location.pathname !== basePath) {
        navigate(basePath, { replace: false });
      }
    };

    // Listen for messaging events
    window.addEventListener('messaging:select-conversation', handleConversationSelect as EventListener);
    window.addEventListener('messaging:close-conversation', handleConversationClose);

    return () => {
      window.removeEventListener('messaging:select-conversation', handleConversationSelect as EventListener);
      window.removeEventListener('messaging:close-conversation', handleConversationClose);
    };
  }, [navigate, location.pathname, basePath, enableDeepLinking]);

  // Set up integration service navigation handlers
  useEffect(() => {
    const config = integrationService.getConfig();
    
    // Update integration service with routing preferences
    integrationService.updateConfig({
      ...config,
      // Add any routing-specific config here
    });
  }, [integrationService]);

  if (!isInitialized) {
    return (
      <div className="flex h-[calc(100vh-4rem)] bg-gray-50 dark:bg-gray-900 items-center justify-center">
        <div className="animate-spin w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full"></div>
    </div>
    );
  }

  return (
    <UnifiedMessagingContainer
      enableMigrationUI={true} onMigrationComplete={handleMigrationComplete}, onMigrationError={handleMigrationError}
    />
  );
};

export default MessagingRouter;