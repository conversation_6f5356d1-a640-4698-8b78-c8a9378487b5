/**
 * Bundle Optimization Configuration for Messaging Components
 * Webpack optimization settings for efficient code splitting
 */

// Bundle analysis and optimization utilities
export const messagingBundleConfig = {
  // Chunk splitting configuration
  chunks: {
    // Core messaging functionality (always loaded)
    'messaging-core': [
      './src/components/messaging/EnhancedMessageBubble',
      './src/components/messaging/TypingIndicator',
      './src/components/messaging/constants',
      './src/components/messaging/utils'
    ],
    
    // Messaging UI components (loaded on messaging route)
    'messaging-ui': [
      './src/components/messaging/VirtualizedMessageList',
      './src/components/messaging/MessageReactions',
      './src/components/messaging/ConnectionStatusIndicator'
    ],
    
    // Advanced messaging features (loaded on demand)
    'messaging-advanced': [
      './src/components/messaging/AdvancedMessageThread',
      './src/components/messaging/MessageSearch',
      './src/components/messaging/MessagingRouter'
    ],
    
    // Security and privacy features (loaded on demand)
    'messaging-security': [
      './src/components/messaging/SecurityDashboard',
      './src/components/messaging/PrivacySettings',
      './src/components/messaging/SecureMessageInput'
    ],
    
    // Call and media features (loaded on demand)
    'messaging-media': [
      './src/components/messaging/CallInterface',
      './src/components/messaging/VideoCallInterface',
      './src/components/messaging/FileUploadManager'
    ],
    
    // Accessibility features (loaded on demand)
    'messaging-a11y': [
      './src/components/messaging/AccessibleMessageInput',
      './src/components/messaging/KeyboardNavigationProvider',
      './src/components/messaging/ScreenReaderSupport'
    ]
  },

  // Webpack optimization configuration
  optimization: {
    splitChunks: {
      chunks: 'all',
      cacheGroups: {
        // Messaging core - always loaded
        messagingCore: {
          name: 'messaging-core',
          test: /[\\/]src[\\/]components[\\/]messaging[\\/](EnhancedMessageBubble|TypingIndicator|constants|utils)/,
          priority: 30,
          chunks: 'all',
          enforce: true
        },
        
        // Messaging UI - loaded on messaging route
        messagingUI: {
          name: 'messaging-ui',
          test: /[\\/]src[\\/]components[\\/]messaging[\\/](VirtualizedMessageList|MessageReactions|ConnectionStatusIndicator)/,
          priority: 25,
          chunks: 'async',
          minChunks: 1
        },
        
        // Advanced features - loaded on demand
        messagingAdvanced: {
          name: 'messaging-advanced',
          test: /[\\/]src[\\/]components[\\/]messaging[\\/](AdvancedMessageThread|MessageSearch|MessagingRouter)/,
          priority: 20,
          chunks: 'async',
          minChunks: 1
        },
        
        // Security features - loaded on demand
        messagingSecurity: {
          name: 'messaging-security',
          test: /[\\/]src[\\/]components[\\/]messaging[\\/](SecurityDashboard|PrivacySettings|SecureMessageInput)/,
          priority: 20,
          chunks: 'async',
          minChunks: 1
        },
        
        // Media features - loaded on demand
        messagingMedia: {
          name: 'messaging-media',
          test: /[\\/]src[\\/]components[\\/]messaging[\\/](CallInterface|VideoCallInterface|FileUploadManager)/,
          priority: 20,
          chunks: 'async',
          minChunks: 1
        },
        
        // Accessibility features - loaded on demand
        messagingA11y: {
          name: 'messaging-a11y',
          test: /[\\/]src[\\/]components[\\/]messaging[\\/](AccessibleMessageInput|KeyboardNavigationProvider|ScreenReaderSupport)/,
          priority: 15,
          chunks: 'async',
          minChunks: 1
        },
        
        // Messaging hooks - shared across features
        messagingHooks: {
          name: 'messaging-hooks',
          test: /[\\/]src[\\/]hooks[\\/](useMessaging|useMessageStatus|useConnectionStatus|useMessageValidation)/,
          priority: 25,
          chunks: 'all',
          minChunks: 2
        },
        
        // Messaging services - shared across features
        messagingServices: {
          name: 'messaging-services',
          test: /[\\/]src[\\/]services[\\/](UnifiedMessagingService|messaging[\\/])/,
          priority: 25,
          chunks: 'all',
          minChunks: 2
        },
        
        // Third-party libraries used by messaging
        messagingVendor: {
          name: 'messaging-vendor',
          test: /[\\/]node_modules[\\/](framer-motion|date-fns|socket\.io-client)[\\/]/,
          priority: 10,
          chunks: 'all',
          minChunks: 1
        }
      }
    },
    
    // Module concatenation for better tree shaking
    concatenateModules: true,
    
    // Minimize bundles in production
    minimize: true,
    
    // Remove unused code
    usedExports: true,
    
    // Side effects configuration
    sideEffects: false
  },

  // Bundle analysis utilities
  analysis: {
    // Expected chunk sizes (in KB)
    expectedSizes: {
      'messaging-core': 50,
      'messaging-ui': 75,
      'messaging-advanced': 40,
      'messaging-security': 30,
      'messaging-media': 60,
      'messaging-a11y': 25,
      'messaging-hooks': 20,
      'messaging-services': 35,
      'messaging-vendor': 100
    },
    
    // Critical path chunks (should be preloaded)
    criticalChunks: ['messaging-core', 'messaging-ui'],
    
    // Preload conditions
    preloadConditions: {
      'messaging-ui': 'route:messages',
      'messaging-advanced': 'feature:search',
      'messaging-security': 'route:settings',
      'messaging-media': 'feature:call',
      'messaging-a11y': 'accessibility:enabled'
    }
  }
};

// Dynamic import optimization
export const createOptimizedImport = (
  componentPath: string,
  chunkName: string,
  preloadCondition?: string
) => {
  return () => {
    console.log(`📦 Loading chunk: ${chunkName}`);
    
    return import(
      /* webpackChunkName: "[chunkName]" */
      /* webpackPreload: true */
      componentPath
    ).then(module => {
      console.log(`✅ Loaded chunk: ${chunkName}`);
      return module;
    }).catch(error => {
      console.error(`❌ Failed to load chunk ${chunkName}:`, error);
      throw error;
    });
  };
};

// Resource hints generation
export const generateResourceHints = () => {
  const hints: Array<{ rel: string, href: string; as?: string; crossorigin?: string }> = [];
  
  // Preload critical messaging chunks
  messagingBundleConfig.analysis.criticalChunks.forEach(chunk => {
    hints.push({
      rel: 'preload',
      href: `/chunks/${chunk}.js`,
      as: 'script',
      crossorigin: 'anonymous'
    });
  });
  
  // Prefetch non-critical chunks
  Object.keys(messagingBundleConfig.chunks).forEach(chunk => {
    if (!messagingBundleConfig.analysis.criticalChunks.includes(chunk)) {
      hints.push({
        rel: 'prefetch',
        href: `/chunks/${chunk}.js`,
        as: 'script'
      });
    }
  });
  
  return hints;
};

// Bundle performance monitoring
export class BundlePerformanceMonitor {
  private static loadTimes = new Map<string, number>();
  private static chunkSizes = new Map<string, number>();
  
  static recordChunkLoad(chunkName: string, loadTime: number, size?: number) {
    this.loadTimes.set(chunkName, loadTime);
    if (size) {
      this.chunkSizes.set(chunkName, size);
    }
    
    // Log performance metrics
    console.log(`📊 Chunk ${chunkName} loaded in ${loadTime}ms${size ? ` (${size}KB)` : ''}`);
    
    // Check against expected sizes
    const expectedSize = messagingBundleConfig.analysis.expectedSizes[chunkName];
    if (expectedSize && size && size > expectedSize * 1.2) {
      console.warn(`⚠️ Chunk ${chunkName} is larger than expected: ${size}KB > ${expectedSize}KB`);
    }
  }
  
  static getMetrics() {
    return {
      loadTimes: Object.fromEntries(this.loadTimes),
      chunkSizes: Object.fromEntries(this.chunkSizes),
      totalChunks: this.loadTimes.size,
      averageLoadTime: Array.from(this.loadTimes.values()).reduce((a, b) => a + b; 0) / this.loadTimes.size || 0,
      totalSize: Array.from(this.chunkSizes.values()).reduce((a, b) => a + b; 0)
    };
  }
  
  static generateReport() {
    const metrics = this.getMetrics();
    const report = {
      summary: {
        totalChunks: metrics.totalChunks,
        totalSize: `${metrics.totalSize}KB`,
        averageLoadTime: `${metrics.averageLoadTime.toFixed(2)}ms`
      },
      chunks: Object.entries(metrics.loadTimes).map(([chunk, loadTime]) => ({
        name: chunk,
        loadTime: `${loadTime}ms`,
        size: metrics.chunkSizes.get(chunk) ? `${metrics.chunkSizes.get(chunk)}KB` : 'Unknown',
        expected: messagingBundleConfig.analysis.expectedSizes[chunk] ? `${messagingBundleConfig.analysis.expectedSizes[chunk]}KB` : 'Unknown'
      })),
      recommendations: this.generateRecommendations()
    };
    
    return report;
  }
  
  private static generateRecommendations() {
    const recommendations: string[] = [];
    const metrics = this.getMetrics();
    
    // Check for oversized chunks
    Object.entries(metrics.chunkSizes).forEach(([chunk, size]) => {
      const expected = messagingBundleConfig.analysis.expectedSizes[chunk];
      if (expected && size > expected * 1.2) {
        recommendations.push(`Consider optimizing ${chunk} - currently ${size}KB, expected ${expected}KB`);
      }
    });
    
    // Check for slow loading chunks
    Object.entries(metrics.loadTimes).forEach(([chunk, loadTime]) => {
      if (loadTime > 1000) {
        recommendations.push(`${chunk} is loading slowly (${loadTime}ms) - consider further splitting or optimization`);
      }
    });
    
    // General recommendations
    if (metrics.totalSize > 500) {
      recommendations.push('Total messaging bundle size is large - consider more aggressive code splitting');
    }
    
    if (metrics.averageLoadTime > 500) {
      recommendations.push('Average chunk load time is high - check network conditions and CDN performance');
    }
    
    return recommendations;
  }
}

// Webpack plugin for messaging optimization
export const createMessagingOptimizationPlugin = () => {
  return {
    name: 'MessagingOptimizationPlugin',
    setup(build: unknown) {
      // Mark messaging chunks for splitting
      build.onResolve({ filter: /src\/components\/messaging/ }, (args: unknown) => {
        return {
          path: args.path,
          namespace: 'messaging-component'
        };
      });
      
      // Add chunk names and optimization hints
      build.onLoad({ filter: /.*/, namespace: 'messaging-component' }, (args: unknown) => {
        // This would be implemented based on the specific bundler
        return null;
      });
    }
  };
};

// Runtime chunk loading utilities
export const chunkLoader = {
  loadedChunks: new Set<string>(),
  
  async loadChunk(chunkName: string): Promise<void> {
    if (this.loadedChunks.has(chunkName)) {
      return Promise.resolve();
    }
    
    const startTime = performance.now();
    
    try {
      // Dynamic import with chunk name
      const module = await import(
        /* webpackChunkName: "[chunkName]" */
        `../chunks/${chunkName}`
      );
      
      const loadTime = performance.now() - startTime;
      BundlePerformanceMonitor.recordChunkLoad(chunkName, loadTime);
      
      this.loadedChunks.add(chunkName);
      console.log(`✅ Successfully loaded chunk: ${chunkName}`);
      
      return module;
    } catch (error) {
      console.error(`❌ Failed to load chunk ${chunkName}:`, error);
      throw error;
    }
  },
  
  preloadChunk(chunkName: string): Promise<void> {
    // Use link prefetch for non-critical chunks
    const link = document.createElement('link');
    link.rel = 'prefetch';
    link.href = `/chunks/${chunkName}.js`;
    link.as = 'script';
    document.head.appendChild(link);
    
    return Promise.resolve();
  },
  
  getLoadedChunks(): string[] {
    return Array.from(this.loadedChunks);
  }
};

export default messagingBundleConfig;