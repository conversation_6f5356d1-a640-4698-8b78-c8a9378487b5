import React, { useCallback, useMemo } from 'react';
import { MessageCircle, Video, Phone, Shield, ShieldOff } from 'lucide-react';
import OptimizedMessageBubble from '../messaging/OptimizedMessageBubble';
import MessageComposer from './MessageComposer';
import ConversationHeader from './ConversationHeader';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { MOCK_IMAGES, getSafeImage } from '@/lib/constants';
import { toast } from 'sonner';

import { Message, Conversation, FileAttachment } from '@/types/messaging';

interface ConversationPaneProps {
  selectedConversation: Conversation | null, messages: Message[], showConversation: boolean, isMobile: boolean, isConnected: boolean, onBackToList: () => void; onSendMessage: (content: string) => void;
  onStartVideoCall?: (conversationId: string) => void;
  onStartAudioCall?: (conversationId: string) => void;
  onMarkAsRead?: (conversationId: string, messageId: string) => void;
  onAddReaction?: (messageId: string, emoji: string) => void;
}

const ConversationPane: React.FC<ConversationPaneProps> = React.memo(({
  selectedConversation,
  messages,
  isMobile,
  isConnected,
  onBackToList,
  onSendMessage,
  onStartVideoCall,
  onStartAudioCall,
  onMarkAsRead,
  onAddReaction
}) => {
  // All hooks must be called before any conditional returns
  const handleVideoCall = useCallback(() => {
    if (onStartVideoCall && selectedConversation) {
      onStartVideoCall(selectedConversation.id);
    } else {
      toast.info('Video call feature available when connected');
    }
  }, [onStartVideoCall, selectedConversation]);

  const handleAudioCall = useCallback(() => {
    if (onStartAudioCall && selectedConversation) {
      onStartAudioCall(selectedConversation.id);
    } else {
      toast.info('Audio call feature available when connected');
    }
  }, [onStartAudioCall, selectedConversation]);

  const handleReaction = useCallback((messageId: string, emoji: string) => {
    if (onAddReaction) {
      onAddReaction(messageId, emoji);
    } else {
      toast.info('Reactions available when connected');
    }
  }, [onAddReaction]);

  // Memoize toast handlers
  const handleReply = useCallback(() => {
    toast.info('Reply feature coming soon');
  }, []);

  const handleEdit = useCallback(() => {
    toast.info('Edit feature coming soon');
  }, []);

  const handleDelete = useCallback(() => {
    toast.info('Delete feature coming soon');
  }, []);

  const handleAttach = useCallback(() => {
    toast.info('Attachment feature coming soon');
  }, []);

  const handleEmoji = useCallback(() => {
    toast.info('Emoji picker coming soon');
  }, []);

  const handleAudio = useCallback(() => {
    toast.info('Audio feature coming soon');
  }, []);

  const handleVoiceMessage = useCallback((attachment: FileAttachment) => {
    // Handle voice message here - you can convert it to a regular message
    toast.success('Voice message sent!');
    // For now, let's just send a text representation
    onSendMessage(`🎤 Voice message (${Math.round(attachment.file.size / 1024)}KB)`);
  }, [onSendMessage]);

  // Check if there are users typing
  const typingUsers = useMemo(() => {
    if (!selectedConversation?.isTyping) return [];
    
    return Object.entries(selectedConversation.isTyping)
      .filter(([userId, isTyping]) => 
        userId !== 'currentUser' && userId !== 'current-user' && isTyping
      )
      .map(([userId]) => userId);
  }, [selectedConversation?.isTyping]);

  // Early return if no conversation is selected
  if (!selectedConversation) {
    return (
      <div className="flex-1 flex items-center justify-center h-full bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <MessageCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500 dark:text-gray-400">Select a conversation to start messaging</p>
    </div>
      </div>
    );
  }

  return (
    <div className="flex-1 flex flex-col h-full overflow-hidden">
      {/* Enhanced Conversation Header */}
      <div className="border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
        <ConversationHeader
          user={selectedConversation.user || (selectedConversation.participants?.[0] ? {
            id: selectedConversation.participants[0].id,
            name: selectedConversation.name || selectedConversation.participants[0].name,
            avatar: selectedConversation.participants[0].avatar,
            isOnline: selectedConversation.participants[0].isOnline,
            lastActive: selectedConversation.participants[0].lastSeen?.toString() || new Date().toISOString()
          } : {
            id: 'unknown',
            name: selectedConversation.name || 'Unknown User',
            avatar: '',
            isOnline: false,
            lastActive: new Date().toISOString()
          })}, isMobile={isMobile} onBackToList={onBackToList}
        />
        
        {/* Call Actions and Status */}
        <div className="px-4 pb-3 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {isConnected ? (
              <Badge variant="default" className="flex items-center space-x-1 bg-green-500 hover:bg-green-600 text-xs">
                <Shield className="w-3 h-3" />
                <span>Encrypted</span>
    </Badge>
            ) : (
              <Badge variant="secondary" className="flex items-center space-x-1 text-xs">
                <ShieldOff className="w-3 h-3" />
                <span>Offline</span>
    </Badge>
            )}
          </div>
          
          {selectedConversation?.type === 'direct' && (
            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleAudioCall} className="text-gray-600 hover:text-blue-600 dark:text-gray-400 dark:hover:text-blue-400"
              >
                <Phone className="w-4 h-4" />
    </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleVideoCall} className="text-gray-600 hover:text-blue-600 dark:text-gray-400 dark:hover:text-blue-400"
              >
                <Video className="w-4 h-4" />
    </Button>
            </div>
          )}
        </div>
    </div>
      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-3 space-y-4 bg-gray-50 dark:bg-gray-900">
        {messages.length === 0 ? (
          <div className="text-center py-8">
            <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-3 dark:bg-gray-700">
              <MessageCircle className="w-6 h-6 text-gray-400 dark:text-gray-500" />
    </div>
            <p className="text-gray-500 dark:text-gray-400">No messages yet</p>
            <p className="text-sm text-gray-400 dark:text-gray-500 mt-1">Start the conversation!</p>
    </div>
        ) : (
          messages.map((message, index) => {
            const isOwn = message.senderId === 'currentUser' || message.senderId === 'current-user';
            const sender = isOwn ? {
              id: 'currentUser',
              name: 'You',
              avatar: getSafeImage('AVATARS', 7),
              isOnline: true,
              lastSeen: undefined
            } : {
              id: message.senderId,
              name: selectedConversation?.user?.name || selectedConversation?.participants?.find(p => p.id === message.senderId)?.name || 'Unknown'; avatar: selectedConversation?.user?.avatar || selectedConversation?.participants?.find(p => p.id === message.senderId)?.avatar || ''; isOnline: selectedConversation?.user?.isOnline || selectedConversation?.participants?.find(p => p.id === message.senderId)?.isOnline || false; lastSeen: selectedConversation?.user?.lastActive || undefined
            };

            return (
              <div key={message.id}>
                <OptimizedMessageBubble
                  message={{
                    ...message,
                    conversationId: selectedConversation?.id || '',
                    reactions: message.reactions || [],
                    sender: sender
                  }}, currentUserId={'currentUser'} isOwn={isOwn}, showActions={true} onReply={handleReply}, onEdit={handleEdit} onDelete={handleDelete}, onReaction={isConnected ? handleReaction : () => {}}, onCopy={(content) => {
                    navigator.clipboard.writeText(content);
                    toast.success('Message copied to clipboard');
                  }}
                />
    </div>
            );
          })
        )}
        
        {/* Typing indicators */}
        {typingUsers.length > 0 && (
          <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
            <div className="flex space-x-1">
              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
    </div>
            <span>
              {selectedConversation?.user?.name || 'Someone'} is typing...
            </span>
    </div>
        )}
      </div>
      
      {/* Enhanced Message Input */}
      <MessageComposer
        onSend={onSendMessage} onAttach={handleAttach}, onEmoji={handleEmoji} onAudio={handleAudio}, onVoiceMessage={handleVoiceMessage}
      />
    </div>
  );
});

ConversationPane.displayName = 'ConversationPane';

export default ConversationPane;
