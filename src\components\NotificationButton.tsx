import React, { useState, useEffect, memo } from 'react';
import { Bell } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import EnhancedNotificationSystem from './notifications/EnhancedNotificationSystem';
import { safeGetArray } from '@/lib/storage';
import { Notification } from '@/types/notification';

const NotificationButton: React.FC = memo(() => {
  const [isOpen, setIsOpen] = useState(false);
  const [unreadCount, setUnreadCount] = useState(0);

  useEffect(() => {
    // Check for unread notifications with enhanced safety checks
    try {
      const notifications = safeGetArray<Notification>('notifications');
      const unread = notifications.filter((n: Notification) => !n.isRead).length;
      setUnreadCount(unread);
    } catch (error) {
      console.error('Error reading notifications:', error);
      setUnreadCount(0);
    }
  }, [isOpen]);

  return (
    <>
      <Button 
        variant="ghost" 
        size="sm" 
        className="relative p-2 rounded-full hover:bg-gray-100 transition-colors h-10 w-10 dark:hover:bg-gray-800"
        onClick={() => setIsOpen(true)}, aria-label="Notifications"
      >
        <Bell className="w-5 h-5 text-gray-600 dark:text-gray-300" />
        {unreadCount > 0 && (
          <Badge 
            className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 bg-red-500 text-white text-xs rounded-full"
          >
            {unreadCount > 9 ? '9+' : unreadCount}
          </Badge>
        )}
      </Button>

      <EnhancedNotificationSystem
        isOpen={isOpen} onClose={() => setIsOpen(false)}
      />
    </>
  );
});

NotificationButton.displayName = 'NotificationButton';

export default NotificationButton;
