import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { AlertCircle, Construction } from 'lucide-react';

// Fallback component for missing components
export const ComponentFallback: React.FC<{ componentName: string }> = ({ componentName }) => (
  <Card>
    <CardHeader>
      <CardTitle className="flex items-center gap-2">
        <Construction className="w-5 h-5 text-orange-500" />
        {componentName}
      </CardTitle>
    </CardHeader>
    <CardContent>
      <div className="flex items-center gap-2 text-gray-600 dark:text-gray-400">
        <AlertCircle className="w-4 h-4" />
        <span>This component is being developed and will be available soon.</span>
    </div>
    </CardContent>
    </Card>
);

// Safe component wrapper with error boundary
export class SafeComponentWrapper extends React.Component<
  { children: React.ReactNode, fallbackName: string },
  { hasError: boolean }
> {
  constructor(props: { children: React.ReactNode, fallbackName: string }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(): { hasError: boolean } {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.warn(`Component ${this.props.fallbackName} failed to render:`, error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return <ComponentFallback componentName={this.props.fallbackName} />;
    }

    return this.props.children;
  }
}