import React, { memo, useCallback, useMemo, useRef, useEffect, useState } from 'react';
import { VariableSizeList as List } from 'react-window';
import { motion } from 'framer-motion';
import { Heart, MessageCircle, Share2, Bookmark, MoreHorizontal, Eye, TrendingUp } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { formatTimeAgo } from '@/utils/timeUtils';
import { useAdvancedPerformanceMonitor } from '@/utils/advancedPerformanceMonitor';

interface FeedPost {
  id: string, author: {
    id: string, name: string, username: string, avatar: string, isVerified: boolean;
  };
  content: string, mediaUrls: string[], timestamp: Date, metrics: {
    likes: number, comments: number, shares: number, views: number;
  };
  userInteractions: {
    hasLiked: boolean, hasShared: boolean, hasBookmarked: boolean;
  };
  tags: string[], isPromoted: boolean, category: 'trending' | 'recent' | 'following' | 'recommended';
}

interface VirtualFeedItemProps {
  index: number, style: React.CSSProperties, data: {
    posts: FeedPost[], onLike: (postId: string) => void; onUnlike: (postId: string) => void; onComment: (postId: string) => void; onShare: (postId: string) => void; onBookmark: (postId: string) => void; onUserClick: (userId: string) => void;
  };
}

// Optimized individual post component with memoization
const VirtualFeedItem: React.FC<VirtualFeedItemProps> = memo(({ index, style, data }) => {
  const { recordComponentRender } = useAdvancedPerformanceMonitor();
  const renderStart = useRef(performance.now());
  const [imageLoaded, setImageLoaded] = useState(false);
  const [inView, setInView] = useState(false);
  
  const post = data.posts[index];
  
  // Record render performance
  useEffect(() => {
    const renderTime = performance.now() - renderStart.current;
    recordComponentRender(`FeedItem-${post.id}`, renderTime, true);
  });

  // Intersection observer for lazy loading
  const itemRef = useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        setInView(entry.isIntersecting);
      },
      { threshold: 0.1 }
    );

    if (itemRef.current) {
      observer.observe(itemRef.current);
    }

    return () => observer.disconnect();
  }, []);

  const handleInteraction = useCallback((action: string) => {
    const actionMap = {
      like: post.userInteractions.hasLiked ? data.onUnlike : data.onLike,
      comment: data.onComment,
      share: data.onShare,
      bookmark: data.onBookmark
    };

    actionMap[action as keyof typeof actionMap]?.(post.id);
  }, [post.id, post.userInteractions.hasLiked, data]);

  const handleUserClick = useCallback(() => {
    data.onUserClick(post.author.id);
  }, [post.author.id, data.onUserClick]);

  if (!post) return null;

  return (
    <div style={style} className="px-2 sm:px-4">
      <div className="pb-8"> {/* Explicit bottom margin */}
        <motion.div
          ref={itemRef} initial={{ opacity: 0, y: 20 }}, animate={{ opacity: inView ? 1 : 0.3, y: 0 }}, transition={{ duration: 0.3 }}, className="h-full"
        >
          <Card className="bg-white/95 backdrop-blur-sm border border-gray-200 shadow-sm hover:shadow-md transition-shadow h-full">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="relative">
                  <Avatar 
                    className="w-8 h-8 sm:w-10 sm:h-10 cursor-pointer hover:scale-105 transition-transform"
                    onClick={handleUserClick}
                  >
                    <AvatarImage src={post.author.avatar} alt={post.author.name} />
                    <AvatarFallback className="text-xs">{post.author.name.slice(0, 2).toUpperCase()}</AvatarFallback>
    </Avatar>
                  {post.author.isVerified && (
                    <div className="absolute -bottom-1 -right-1 w-3 h-3 sm:w-4 sm:h-4 bg-blue-500 rounded-full flex items-center justify-center">
                      <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-white rounded-full"></div>
    </div>
                  )}
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2">
                    <h3 
                      className="font-semibold text-gray-900 cursor-pointer hover:underline truncate text-sm sm:text-base"
                      onClick={handleUserClick}
                    >
                      {post.author.name}
                    </h3>
                    {post.isPromoted && (
                      <Badge variant="outline" className="text-xs bg-blue-50 text-blue-600 border-blue-200">
                        <TrendingUp className="w-2 h-2 sm:w-3 sm:h-3 mr-1" />
                        <span className="hidden sm:inline">Promoted</span>
                        <span className="sm:hidden">Pro</span>
    </Badge>
                    )}
                  </div>
                  <p className="text-xs sm:text-sm text-gray-500">
                    @{post.author.username} • {formatTimeAgo(post.timestamp)}
                  </p>
    </div>
              </div>
              
              <Button variant="ghost" size="sm" className="h-6 w-6 sm:h-8 sm:w-8 p-0">
                <MoreHorizontal className="w-3 h-3 sm:w-4 sm:h-4" />
    </Button>
            </div>
    </CardHeader>
          <CardContent className="pt-0 space-y-3">
            {/* Post Content */}
            <div>
              <p className="text-gray-800 leading-relaxed whitespace-pre-wrap text-sm sm:text-base">
                {post.content}
              </p>
              
              {/* Tags */}
              {post.tags.length > 0 && (
                <div className="flex flex-wrap gap-1 mt-2">
                  {post.tags.map((tag, tagIndex) => (
                    <Badge 
                      key={tagIndex} variant="secondary" 
                      className="text-xs bg-gray-100 hover:bg-gray-200 cursor-pointer px-1 py-0.5"
                    >
                      #{tag}
                    </Badge>
                  ))}
                </div>
              )}
            </div>

            {/* Media Content - Lazy Loaded */}
            {post.mediaUrls.length > 0 && inView && (
              <div className="rounded-lg overflow-hidden bg-gray-100">
                <img
                  src={post.mediaUrls[0]} alt="Post media"
                  className={`w-full h-auto max-h-64 sm:max-h-96 object-cover transition-opacity duration-300 ${
                    imageLoaded ? 'opacity-100' : 'opacity-0'
                  }`}, onLoad={() => setImageLoaded(true)} loading="lazy"
                />
                {!imageLoaded && (
                  <div className="h-32 sm:h-48 bg-gray-200 animate-pulse flex items-center justify-center">
                    <div className="w-6 h-6 sm:w-8 sm:h-8 bg-gray-300 rounded animate-spin"></div>
    </div>
                )}
              </div>
            )}

            {/* Engagement Metrics */}
            <div className="flex items-center justify-between py-2 border-t border-gray-100">
              <div className="flex items-center space-x-2 sm:space-x-4 text-xs sm:text-sm text-gray-500">
                <div className="flex items-center space-x-1">
                  <Eye className="w-3 h-3 sm:w-4 sm:h-4" />
                  <span className="hidden sm:inline">{post.metrics.views.toLocaleString()}</span>
                  <span className="sm:hidden">{(post.metrics.views / 1000).toFixed(1)}k</span>
    </div>
                <div className="flex items-center space-x-1">
                  <Heart className="w-3 h-3 sm:w-4 sm:h-4 text-red-500" />
                  <span className="hidden sm:inline">{post.metrics.likes.toLocaleString()}</span>
                  <span className="sm:hidden">{post.metrics.likes}</span>
    </div>
                <div className="flex items-center space-x-1">
                  <MessageCircle className="w-3 h-3 sm:w-4 sm:h-4" />
                  <span>{post.metrics.comments}</span>
    </div>
                <div className="flex items-center space-x-1">
                  <Share2 className="w-3 h-3 sm:w-4 sm:h-4" />
                  <span>{post.metrics.shares}</span>
    </div>
              </div>
    </div>
            {/* Action Buttons */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-1">
                <Button
                  variant="ghost"
                  size="sm"
                  className={`flex items-center space-x-1 sm:space-x-2 hover:bg-red-50 px-2 py-1 h-auto ${
                    post.userInteractions.hasLiked ? 'text-red-500' : 'text-gray-600'
                  }`}, onClick={() => handleInteraction('like')}
                >
                  <Heart className={`w-3 h-3 sm:w-4 sm:h-4 ${post.userInteractions.hasLiked ? 'fill-current' : ''}`} />
                  <span className="text-xs sm:text-sm hidden sm:inline">Like</span>
    </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="flex items-center space-x-1 sm:space-x-2 text-gray-600 hover:bg-blue-50 px-2 py-1 h-auto"
                  onClick={() => handleInteraction('comment')}
                >
                  <MessageCircle className="w-3 h-3 sm:w-4 sm:h-4" />
                  <span className="text-xs sm:text-sm hidden sm:inline">Comment</span>
    </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="flex items-center space-x-1 sm:space-x-2 text-gray-600 hover:bg-green-50 px-2 py-1 h-auto"
                  onClick={() => handleInteraction('share')}
                >
                  <Share2 className="w-3 h-3 sm:w-4 sm:h-4" />
                  <span className="text-xs sm:text-sm hidden sm:inline">Share</span>
    </Button>
              </div>
              
              <Button
                variant="ghost"
                size="sm"
                className={`hover:bg-yellow-50 px-2 py-1 h-auto ${
                  post.userInteractions.hasBookmarked ? 'text-yellow-600' : 'text-gray-600'
                }`}, onClick={() => handleInteraction('bookmark')}
              >
                <Bookmark className={`w-3 h-3 sm:w-4 sm:h-4 ${post.userInteractions.hasBookmarked ? 'fill-current' : ''}`} />
    </Button>
            </div>
    </CardContent>
        </Card>
      </motion.div>
    </div>
  </div>
  );
});

VirtualFeedItem.displayName = 'VirtualFeedItem';

// Enhanced virtual feed component
interface EnhancedVirtualFeedProps {
  posts: FeedPost[], onLike: (postId: string) => void; onUnlike: (postId: string) => void; onComment: (postId: string) => void; onShare: (postId: string) => void; onBookmark: (postId: string) => void; onUserClick: (userId: string) => void; onLoadMore: () => void; hasMore: boolean;
  onPostInteraction?: (postId: string, action: string) => void; // Optional backward compatibility
  isLoading: boolean;
  className?: string;
}

const EnhancedVirtualFeed: React.FC<EnhancedVirtualFeedProps> = memo(({
  posts,
  onLike,
  onUnlike,
  onComment,
  onShare,
  onBookmark,
  onUserClick,
  onLoadMore,
  hasMore,
  isLoading,
  className = ''
}) => {
  const { recordOptimization } = useAdvancedPerformanceMonitor();
  const listRef = useRef<List>(null);
  const [visibleRange, setVisibleRange] = useState({ start: 0, end: 10 });

  // Memoized data for virtual list
  const itemData = useMemo(() => ({
    posts,
    onLike,
    onUnlike,
    onComment,
    onShare,
    onBookmark,
    onUserClick
  }), [posts, onLike, onUnlike, onComment, onShare, onBookmark, onUserClick]);

  // Record optimization metrics
  useEffect(() => {
    const totalPosts = posts.length;
    const renderedPosts = Math.min(visibleRange.end - visibleRange.start, 10);
    const virtualScrollEfficiency = totalPosts > 0 ? (renderedPosts / totalPosts) * 100 : 0;
    const memoizationHitRate = 85; // Simulated based on React.memo usage
    const lazyLoadingSavings = posts.filter(p => p.mediaUrls.length > 0).length * 0.1; // Simulated savings
    
    recordOptimization(memoizationHitRate, virtualScrollEfficiency, lazyLoadingSavings);
  }, [posts.length, visibleRange, recordOptimization]);

  const handleItemsRendered = useCallback(({ visibleStartIndex, visibleStopIndex }: { visibleStartIndex: number, visibleStopIndex: number }) => {
    setVisibleRange({ start: visibleStartIndex, end: visibleStopIndex });
    
    // Load more when approaching the end
    if (!isLoading && hasMore && visibleStopIndex >= posts.length - 5) {
      onLoadMore();
    }
  }, [isLoading, hasMore, posts.length, onLoadMore]);

  // Ultra-safe item size calculation with very generous heights
  const getItemSize = useCallback((index: number) => {
    // Handle loading indicator item
    if (index >= posts.length) {
      return 150; // Fixed height for loading indicator
    }
    
    const post = posts[index];
    if (!post) return 600;
    
    // Start with very generous base height to prevent overlap
    let height = 400; // Very safe base height
    
    // Add substantial height based on content length  
    const contentWords = post.content.split(' ').length;
    const estimatedLines = Math.ceil(contentWords / 6); // Even more conservative - 6 words per line
    height += estimatedLines * 30; // 30px per line (very generous)
    
    // Add very generous height for media
    if (post.mediaUrls.length > 0) {
      height += 400; // Very generous media height
    }
    
    // Add generous height for tags
    if (post.tags.length > 0) {
      height += 80; // Very generous tag height
    }
    
    // Add extra buffer based on content complexity
    if (post.content.length > 150) {
      height += 80; // Extra for long content
    }
    
    // Add final safety buffer
    height += 80; // Large safety buffer
    
    // Ensure extremely generous minimum height
    const finalHeight = Math.max(height, 550);
    
    return finalHeight;
  }, [posts]);

  // Reset list when posts change to recalculate all heights
  useEffect(() => {
    if (listRef.current) {
      // Force complete recalculation
      listRef.current.resetAfterIndex(0, true);
    }
  }, [posts.length, posts]);

  // Handle window resize for responsive recalculation with better debouncing
  useEffect(() => {
    const handleResize = () => {
      if (listRef.current) {
        // Reset all calculations on resize
        listRef.current.resetAfterIndex(0, true);
      }
    };

    const debouncedResize = debounce(handleResize, 100);
    window.addEventListener('resize', debouncedResize);
    return () => {
      window.removeEventListener('resize', debouncedResize);
    };
  }, []);

  // Force initial recalculation on mount
  useEffect(() => {
    const timer = setTimeout(() => {
      if (listRef.current) {
        listRef.current.resetAfterIndex(0, true);
      }
    }, 100);
    return () => clearTimeout(timer);
  }, []);

  // Improved debounce utility with proper cleanup
  function debounce<T extends (...args: unknown[]) => any>(func: T; wait: number) {
    let timeout: NodeJS.Timeout;
    return function executedFunction(...args: Parameters<T>) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }

  // Add a ref to track if component is mounted
  const isMountedRef = useRef(true);
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  if (posts.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 text-gray-500">
        <div className="text-center">
          <div className="w-16 h-16 bg-gray-200 rounded-full mx-auto mb-4 animate-pulse"></div>
          <p>No posts to display</p>
    </div>
      </div>
    );
  }

  return (
    <div className={`h-full ${className}`}>
      <List
        ref={listRef} height={800} // Increased height for better visibility
        width="100%"
        itemCount={posts.length + (hasMore ? 1 : 0)} itemSize={getItemSize}, itemData={itemData} onItemsRendered={handleItemsRendered}, overscanCount={2} // Reduced even more for performance
        className="scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100"
      >
        {({ index, style }: { index: number, style: React.CSSProperties }) => {
          if (index >= posts.length) {
            // Loading indicator with proper height
            return (
              <div style={{ ...style, height: 150 }}, className="flex items-center justify-center">
                <div className="flex items-center space-x-2 text-gray-500">
                  <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                  <span className="text-sm">Loading more posts...</span>
    </div>
              </div>
            );
          }

          return (
            <VirtualFeedItem
              index={index} style={style}, data={itemData}
            />
          );
        }}
      </List>
    </div>
  );
});

EnhancedVirtualFeed.displayName = 'EnhancedVirtualFeed';

export default EnhancedVirtualFeed;
