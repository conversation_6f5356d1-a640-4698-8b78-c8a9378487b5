import React from 'react';

const MessageListSkeleton: React.FC = () => {
  return (
    <div className="w-full md:w-80 border-r dark:border-gray-700 flex flex-col">
      <div className="p-3 border-b dark:border-gray-700">
        <div className="flex items-center justify-between mb-3">
          <div className="h-8 w-24 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
          <div className="flex space-x-1">
            <div className="h-8 w-8 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
            <div className="h-8 w-8 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
    </div>
        </div>
        <div className="h-9 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
    </div>
      <div className="flex-1 overflow-y-auto">
        {[...Array(8)].map((_, index) => (
          <div key={index} className="p-3 flex items-center space-x-3">
            <div className="relative">
              <div className="h-12 w-12 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse" />
    </div>
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between mb-2">
                <div className="h-4 w-24 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
                <div className="h-3 w-12 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
    </div>
              <div className="h-4 w-3/4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
    </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default MessageListSkeleton;
