import React, { memo } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { 
  Settings, 
  Bell, 
  RefreshCw, 
  Download, 
  Monitor,
  Clock,
  Zap,
  Save
} from 'lucide-react';
import { toast } from 'sonner';
import { useOptimizationSuite } from '@/hooks/useOptimizationSuite';

const OptimizationSettings: React.FC = memo(() => {
  const { 
    settings, 
    updateSettings, 
    metrics, 
    performanceStatus, 
    isMonitoring;
    lastUpdate 
  } = useOptimizationSuite();

  const handleSaveSettings = () => {
    toast.success('Settings saved successfully');
  };

  const handleResetSettings = () => {
    updateSettings({
      autoRefresh: true,
      refreshInterval: 5000,
      enableNotifications: true,
      exportFormat: 'json',
      monitoringEnabled: true
    });
    toast.success('Settings reset to defaults');
  };

  const formatLastUpdate = () => {
    if (!lastUpdate) return 'Never';
    const now = new Date();
    const diff = now.getTime() - lastUpdate.getTime();
    const seconds = Math.floor(diff / 1000);
    const minutes = Math.floor(seconds / 60);
    
    if (minutes > 0) return `${minutes}m ago`;
    return `${seconds}s ago`;
  };

  return (
    <div className="space-y-6">
      {/* Settings Header */}
      <Card className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-xl font-semibold mb-2 flex items-center gap-2">
                <Settings className="w-6 h-6 text-blue-500" />
                Optimization Settings
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                Configure monitoring, notifications, and export preferences
              </p>
    </div>
            <div className="flex gap-2">
              <Button onClick={handleSaveSettings} className="flex items-center gap-2">
                <Save className="w-4 h-4" />
                Save
              </Button>
              <Button onClick={handleResetSettings} variant="outline">
                Reset
              </Button>
    </div>
          </div>
    </CardContent>
      </Card>

      {/* Current Status */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Monitoring Status</p>
                <p className="text-lg font-semibold">
                  {isMonitoring ? 'Active' : 'Paused'}
                </p>
    </div>
              <div className={`w-3 h-3 rounded-full ${isMonitoring ? 'bg-green-500 animate-pulse' : 'bg-gray-400'}`} />
    </div>
          </CardContent>
    </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Performance</p>
                <p className="text-lg font-semibold">
                  {metrics?.performance.score || 0}/100
                </p>
    </div>
              <Badge 
                variant="outline" 
                className={`${performanceStatus.color === 'green' ? 'text-green-600 border-green-200' : 
                           performanceStatus.color === 'yellow' ? 'text-yellow-600 border-yellow-200' : 
                           'text-red-600 border-red-200'}`}
              >
                {performanceStatus.status}
              </Badge>
    </div>
          </CardContent>
    </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Last Update</p>
                <p className="text-lg font-semibold">{formatLastUpdate()}</p>
    </div>
              <Clock className="w-6 h-6 text-blue-500" />
    </div>
          </CardContent>
    </Card>
      </div>

      {/* Settings Sections */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Monitoring Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Monitor className="w-5 h-5 text-blue-500" />
              Monitoring Settings
            </CardTitle>
    </CardHeader>
          <CardContent className="space-y-6">
            {/* Enable Monitoring */}
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="monitoring-enabled" className="text-sm font-medium">
                  Enable Monitoring
                </Label>
                <p className="text-xs text-gray-500">
                  Turn on/off performance monitoring
                </p>
    </div>
              <Switch
                id="monitoring-enabled"
                checked={settings.monitoringEnabled} onCheckedChange={(checked) => updateSettings({ monitoringEnabled: checked })}
              />
    </div>
            {/* Auto Refresh */}
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="auto-refresh" className="text-sm font-medium">
                  Auto Refresh
                </Label>
                <p className="text-xs text-gray-500">
                  Automatically update metrics
                </p>
    </div>
              <Switch
                id="auto-refresh"
                checked={settings.autoRefresh} onCheckedChange={(checked) => updateSettings({ autoRefresh: checked })}, disabled={!settings.monitoringEnabled}
              />
    </div>
            {/* Refresh Interval */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <Label className="text-sm font-medium">
                  Refresh Interval
                </Label>
                <Badge variant="outline">
                  {settings.refreshInterval / 1000}s
                </Badge>
    </div>
              <Slider
                value={[settings.refreshInterval]} onValueChange={([value]) => updateSettings({ refreshInterval: value })}, min={1000} max={30000}, step={1000} disabled={!settings.autoRefresh || !settings.monitoringEnabled}, className="w-full"
              />
              <div className="flex justify-between text-xs text-gray-500">
                <span>1s</span>
                <span>30s</span>
    </div>
            </div>
    </CardContent>
        </Card>

        {/* Notification & Export Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bell className="w-5 h-5 text-yellow-500" />
              Notifications & Export
            </CardTitle>
    </CardHeader>
          <CardContent className="space-y-6">
            {/* Enable Notifications */}
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="notifications" className="text-sm font-medium">
                  Enable Notifications
                </Label>
                <p className="text-xs text-gray-500">
                  Get alerts for performance issues
                </p>
    </div>
              <Switch
                id="notifications"
                checked={settings.enableNotifications} onCheckedChange={(checked) => updateSettings({ enableNotifications: checked })}
              />
    </div>
            {/* Export Format */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Export Format</Label>
              <Select
                value={settings.exportFormat} onValueChange={(value: 'json' | 'csv') => updateSettings({ exportFormat: value })}
              >
                <SelectTrigger>
                  <SelectValue />
    </SelectTrigger>
                <SelectContent>
                  <SelectItem value="json">JSON</SelectItem>
                  <SelectItem value="csv">CSV</SelectItem>
    </SelectContent>
              </Select>
              <p className="text-xs text-gray-500">
                Default format for data exports
              </p>
    </div>
          </CardContent>
    </Card>
      </div>

      {/* Advanced Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="w-5 h-5 text-purple-500" />
            Advanced Settings
          </CardTitle>
    </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Performance Thresholds */}
            <div className="space-y-4">
              <h4 className="font-medium">Performance Thresholds</h4>
              <div className="space-y-3 text-sm">
                <div className="flex justify-between">
                  <span>Excellent Score:</span>
                  <Badge variant="outline" className="text-green-600 border-green-200">≥90</Badge>
    </div>
                <div className="flex justify-between">
                  <span>Good Score:</span>
                  <Badge variant="outline" className="text-yellow-600 border-yellow-200">70-89</Badge>
    </div>
                <div className="flex justify-between">
                  <span>Poor Score:</span>
                  <Badge variant="outline" className="text-red-600 border-red-200">&lt;70</Badge>
    </div>
              </div>
    </div>
            {/* Memory Thresholds */}
            <div className="space-y-4">
              <h4 className="font-medium">Memory Thresholds</h4>
              <div className="space-y-3 text-sm">
                <div className="flex justify-between">
                  <span>Good Usage:</span>
                  <Badge variant="outline" className="text-green-600 border-green-200">&lt;60%</Badge>
    </div>
                <div className="flex justify-between">
                  <span>Warning:</span>
                  <Badge variant="outline" className="text-yellow-600 border-yellow-200">60-80%</Badge>
    </div>
                <div className="flex justify-between">
                  <span>Critical:</span>
                  <Badge variant="outline" className="text-red-600 border-red-200">&gt;80%</Badge>
    </div>
              </div>
    </div>
          </div>
    </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
    </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-3">
            <Button variant="outline" size="sm" className="flex items-center gap-2">
              <RefreshCw className="w-4 h-4" />
              Force Refresh
            </Button>
            <Button variant="outline" size="sm" className="flex items-center gap-2">
              <Download className="w-4 h-4" />
              Export Current Data
            </Button>
            <Button variant="outline" size="sm" className="flex items-center gap-2">
              <Monitor className="w-4 h-4" />
              Reset Monitoring
            </Button>
    </div>
        </CardContent>
    </Card>
    </div>
  );
});

OptimizationSettings.displayName = 'OptimizationSettings';

export default OptimizationSettings;