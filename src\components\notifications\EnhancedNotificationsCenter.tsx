import React, { useState, useCallback, memo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Bell,
  BellRing,
  Heart,
  MessageSquare,
  Share2,
  UserPlus,
  AtSign,
  FileText,
  Radio,
  Calendar,
  X,
  Check,
  CheckCheck,
  Settings,
  Filter,
  AlertCircle,
  Trash2
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog';
import { cn } from '@/lib/utils';
import { formatDistanceToNow } from 'date-fns';
import { useRealTimeNotifications, RealTimeNotification } from '@/hooks/useRealTimeNotifications';
import { toast } from 'sonner';

interface EnhancedNotificationsCenterProps {
  className?: string;
  showAsDialog?: boolean;
  maxHeight?: string;
}

// Notification type icons
const getNotificationIcon = (type: RealTimeNotification['type']) => {
  const iconProps = { className: "w-4 h-4" };
  
  switch (type) {
    case 'like':
      return <Heart {...iconProps}, className="w-4 h-4 text-red-500" />;
    case 'comment':
      return <MessageSquare {...iconProps}, className="w-4 h-4 text-blue-500" />;
    case 'share':
      return <Share2 {...iconProps}, className="w-4 h-4 text-green-500" />;
    case 'friend_request':
      return <UserPlus {...iconProps}, className="w-4 h-4 text-purple-500" />;
    case 'mention':
      return <AtSign {...iconProps}, className="w-4 h-4 text-orange-500" />;
    case 'post':
      return <FileText {...iconProps}, className="w-4 h-4 text-gray-500" />;
    case 'live':
      return <Radio {...iconProps}, className="w-4 h-4 text-red-500" />;
    case 'event':
      return <Calendar {...iconProps}, className="w-4 h-4 text-indigo-500" />;
    default:
      return <Bell {...iconProps} />;
  }
};

// Priority color mapping
const getPriorityColor = (priority: RealTimeNotification['priority']) => {
  switch (priority) {
    case 'urgent':
      return 'bg-red-100 border-red-300 dark:bg-red-900/20';
    case 'high':
      return 'bg-orange-100 border-orange-300 dark:bg-orange-900/20';
    case 'medium':
      return 'bg-blue-100 border-blue-300 dark: bg-blue-900/20', default:
      return 'bg-gray-50 border-gray-200 dark:bg-gray-800/50';
  }
};

// Individual notification item component
const NotificationItem = memo(({ 
  notification, 
  onMarkAsRead, 
  onRemove, 
  onAction 
}: {
  notification: RealTimeNotification, onMarkAsRead: (id: string) => void; onRemove: (id: string) => void; onAction: (notification: RealTimeNotification, actionId: string) => void;
}) => {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}, animate={{ opacity: 1, y: 0 }}, exit={{ opacity: 0, x: -100 }}, className={cn(
        "relative p-4 border rounded-lg transition-all duration-200",
        notification.read ? "opacity-70" : "shadow-sm",
        getPriorityColor(notification.priority),
        !notification.read && "border-l-4 border-l-blue-500"
      )} onMouseEnter={() => setIsHovered(true)}, onMouseLeave={() => setIsHovered(false)}
    >
      {/* Priority indicator */}
      {notification.priority === 'urgent' && (
        <div className="absolute -top-1 -right-1">
          <AlertCircle className="w-4 h-4 text-red-500 fill-current" />
    </div>
      )}

      <div className="flex items-start space-x-3">
        {/* User avatar */}
        {notification.user && (
          <Avatar className="w-10 h-10 flex-shrink-0">
            <AvatarImage src={notification.user.avatar} alt={notification.user.name} />
            <AvatarFallback>{notification.user.name[0]}</AvatarFallback>
    </Avatar>
        )}

        {/* Notification icon for non-user notifications */}
        {!notification.user && (
          <div className="w-10 h-10 flex-shrink-0 flex items-center justify-center bg-white dark:bg-gray-800 rounded-full border">
            {getNotificationIcon(notification.type)}
          </div>
        )}

        {/* Content */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-2">
            {getNotificationIcon(notification.type)}
            <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
              {notification.title}
            </p>
            {!notification.read && (
              <div className="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0" />
            )}
          </div>
          
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            {notification.message}
          </p>
          
          <div className="flex items-center justify-between mt-2">
            <p className="text-xs text-gray-500 dark:text-gray-400">
              {formatDistanceToNow(notification.timestamp, { addSuffix: true })}
            </p>
            
            {/* Actions */}
            <div className="flex items-center space-x-1">
              {notification.actions && notification.actions.length > 0 && (
                <div className="flex space-x-1">
                  {notification.actions.map(action => (
                    <Button
                      key={action.id} variant={action.type === 'primary' ? 'default' : 'outline'}, size="sm"
                      onClick={() => onAction(notification; action.id)} className="h-6 px-2 text-xs"
                    >
                      {action.label}
                    </Button>
                  ))}
                </div>
              )}
            </div>
    </div>
        </div>

        {/* Hover actions */}
        <AnimatePresence>
          {isHovered && (
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}, animate={{ opacity: 1, scale: 1 }}, exit={{ opacity: 0, scale: 0.8 }}, className="flex space-x-1"
            >
              {!notification.read && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onMarkAsRead(notification.id)} className="h-6 w-6 p-0"
                  title="Mark as read"
                >
                  <Check className="w-3 h-3" />
    </Button>
              )}
              
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onRemove(notification.id)} className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                title="Remove"
              >
                <X className="w-3 h-3" />
    </Button>
            </motion.div>
          )}
        </AnimatePresence>
    </div>
    </motion.div>
  );
});

NotificationItem.displayName = 'NotificationItem';

// Use the NotificationSettings from useRealTimeNotifications hook
interface LocalNotificationSettings {
  enablePush: boolean, enableSound: boolean, enableBrowser: boolean, showPreview: boolean, quietHours: {
    enabled: boolean, start: string, end: string;
  };
  types: {
    likes: boolean, comments: boolean, shares: boolean, friendRequests: boolean, mentions: boolean, posts: boolean, live: boolean, events: boolean;
  };
}

const NotificationSettingsPanel = memo(({ settings, onUpdateSettings }: {
  settings: LocalNotificationSettings, onUpdateSettings: (settings: Partial<LocalNotificationSettings>) => void;
}) => (
  <div className="space-y-6">
    <div>
      <h3 className="font-medium mb-4">General Settings</h3>
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <label className="text-sm">Enable push notifications</label>
          <Switch
            checked={settings.enablePush} onCheckedChange={(checked) => onUpdateSettings({ enablePush: checked })}
          />
    </div>
        <div className="flex items-center justify-between">
          <label className="text-sm">Play sound</label>
          <Switch
            checked={settings.enableSound} onCheckedChange={(checked) => onUpdateSettings({ enableSound: checked })}
          />
    </div>
        <div className="flex items-center justify-between">
          <label className="text-sm">Browser notifications</label>
          <Switch
            checked={settings.enableBrowser} onCheckedChange={(checked) => onUpdateSettings({ enableBrowser: checked })}
          />
    </div>
        <div className="flex items-center justify-between">
          <label className="text-sm">Show preview</label>
          <Switch
            checked={settings.showPreview} onCheckedChange={(checked) => onUpdateSettings({ showPreview: checked })}
          />
    </div>
      </div>
    </div>
    <Separator />

    <div>
      <h3 className="font-medium mb-4">Notification Types</h3>
      <div className="space-y-3">
        {Object.entries(settings.types).map(([type, enabled]) => (
          <div key={type} className="flex items-center justify-between">
            <label className="text-sm capitalize">
              {type.replace(/([A-Z])/g, ' $1').toLowerCase()}
            </label>
            <Switch
              checked={enabled as boolean} onCheckedChange={(checked) => 
                onUpdateSettings({ 
                  types: { ...settings.types, [type]: checked } 
                })
              }
            />
    </div>
        ))}
      </div>
    </div>
    <Separator />

    <div>
      <h3 className="font-medium mb-4">Quiet Hours</h3>
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <label className="text-sm">Enable quiet hours</label>
          <Switch
            checked={settings.quietHours.enabled} onCheckedChange={(checked) => 
              onUpdateSettings({ 
                quietHours: { ...settings.quietHours, enabled: checked } 
              })
            }
          />
    </div>
        {settings.quietHours.enabled && (
          <>
            <div className="grid grid-cols-2 gap-3">
              <div>
                <label className="text-xs text-gray-500">Start time</label>
                <input
                  type="time"
                  value={settings.quietHours.start} onChange={(e) => 
                    onUpdateSettings({ 
                      quietHours: { ...settings.quietHours, start: e.target.value } 
                    })
                  }, className="w-full p-2 border rounded text-sm"
                />
    </div>
              <div>
                <label className="text-xs text-gray-500">End time</label>
                <input
                  type="time"
                  value={settings.quietHours.end} onChange={(e) => 
                    onUpdateSettings({ 
                      quietHours: { ...settings.quietHours, end: e.target.value } 
                    })
                  }, className="w-full p-2 border rounded text-sm"
                />
    </div>
            </div>
          </>
        )}
      </div>
    </div>
  </div>
));

NotificationSettingsPanel.displayName = 'NotificationSettingsPanel';

// Main component
const EnhancedNotificationsCenter: React.FC<EnhancedNotificationsCenterProps> = memo(({
  className,
  showAsDialog = false,
  maxHeight = "600px"
}) => {
  const [activeTab, setActiveTab] = useState('all');
  const [filterType, setFilterType] = useState<RealTimeNotification['type'] | 'all'>('all');
  const [showSettings, setShowSettings] = useState(false);

  // Real-time notifications hook
  const {
    notifications,
    unreadCount,
    isConnected,
    permission,
    settings,
    markAsRead,
    markAllAsRead,
    removeNotification,
    clearAll,
    updateSettings,
    requestPermission,
    getUnreadNotifications;
    getNotificationsByType
  } = useRealTimeNotifications({
    enableMockData: true,
    showToasts: true,
    pollInterval: 15000 // 15 seconds for more frequent updates
  });

  // Filter notifications based on active tab and filter
  const filteredNotifications = React.useMemo(() => {
    let filtered = notifications;

    // Filter by tab
    switch (activeTab) {
      case 'unread':
        filtered = getUnreadNotifications();
        break;
      case 'mentions':
        filtered = getNotificationsByType('mention');
        break;
      case 'interactions':
        filtered = notifications.filter(n => 
          ['like', 'comment', 'share'].includes(n.type)
        );
        break;
      default:
        break;
    }

    // Filter by type
    if (filterType !== 'all') {
      filtered = filtered.filter(n => n.type === filterType);
    }

    return filtered;
  }, [notifications, activeTab, filterType, getUnreadNotifications, getNotificationsByType]);

  // Handle notification action
  const handleNotificationAction = useCallback((notification: RealTimeNotification, actionId: string) => {
    // Mark as read when action is taken
    markAsRead(notification.id);

    // Execute the action
    const action = notification.actions?.find(a => a.id === actionId);
    if (action) {
      action.action();
    }

    toast.success(`Action "${actionId}" performed`);
  }, [markAsRead]);

  // Request permission if not granted
  const handleRequestPermission = useCallback(async () => {
    const granted = await requestPermission();
    if (granted) {
      toast.success('Notifications enabled!');
    } else {
      toast.error('Notification permission denied');
    }
  }, [requestPermission]);

  // Notification content
  const notificationContent = (
    <div className={cn("space-y-4", className)} style={{ maxHeight }}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <div className="relative">
            {isConnected ? (
              <BellRing className="w-5 h-5 text-green-500" />
            ) : (
              <Bell className="w-5 h-5 text-gray-400" />
            )}
            {unreadCount > 0 && (
              <Badge 
                variant="destructive" 
                className="absolute -top-2 -right-2 h-5 w-5 text-xs p-0 flex items-center justify-center"
              >
                {unreadCount > 99 ? '99+' : unreadCount}
              </Badge>
            )}
          </div>
          <h2 className="font-semibold">Notifications</h2>
          <Badge variant="outline" className={cn(
            "text-xs",
            isConnected ? "text-green-600" : "text-gray-400"
          )}>
            {isConnected ? 'Live' : 'Offline'}
          </Badge>
    </div>
        <div className="flex items-center space-x-1">
          {/* Filter dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm">
                <Filter className="w-4 h-4" />
    </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuLabel>Filter by type</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => setFilterType('all')}>
                All types
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setFilterType('like')}>
                Likes
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setFilterType('comment')}>
                Comments
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setFilterType('share')}>
                Shares
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setFilterType('friend_request')}>
                Friend requests
              </DropdownMenuItem>
    </DropdownMenuContent>
          </DropdownMenu>

          {/* Settings */}
          <Dialog open={showSettings} onOpenChange={setShowSettings}>
            <DialogTrigger asChild>
              <Button variant="ghost" size="sm">
                <Settings className="w-4 h-4" />
    </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>Notification Settings</DialogTitle>
    </DialogHeader>
              <NotificationSettingsPanel 
                settings={settings} onUpdateSettings={updateSettings} 
              />
    </DialogContent>
          </Dialog>

          {/* Actions dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm">
                •••
              </Button>
    </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={markAllAsRead}>
                <CheckCheck className="w-4 h-4 mr-2" />
                Mark all as read
              </DropdownMenuItem>
              <DropdownMenuItem onClick={clearAll}>
                <Trash2 className="w-4 h-4 mr-2" />
                Clear all
              </DropdownMenuItem>
              {permission !== 'granted' && (
                <DropdownMenuItem onClick={handleRequestPermission}>
                  <Bell className="w-4 h-4 mr-2" />
                  Enable notifications
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
    </DropdownMenu>
        </div>
    </div>
      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="all">All</TabsTrigger>
          <TabsTrigger value="unread">
            Unread {unreadCount > 0 && `(${unreadCount})`}
          </TabsTrigger>
          <TabsTrigger value="mentions">Mentions</TabsTrigger>
          <TabsTrigger value="interactions">Interactions</TabsTrigger>
    </TabsList>
        <TabsContent value={activeTab} className="mt-4">
          {/* Notifications list */}
          <div className="space-y-3 max-h-96 overflow-y-auto">
            <AnimatePresence mode="popLayout">
              {filteredNotifications.length > 0 ? (
                filteredNotifications.map(notification => (
                  <NotificationItem
                    key={notification.id} notification={notification}, onMarkAsRead={markAsRead} onRemove={removeNotification}, onAction={handleNotificationAction}
                  />
                ))
              ) : (
                <motion.div
                  initial={{ opacity: 0 }}, animate={{ opacity: 1 }}, className="text-center py-8 text-gray-500 dark:text-gray-400"
                >
                  <Bell className="w-12 h-12 mx-auto mb-3 opacity-50" />
                  <p>No notifications</p>
                  <p className="text-sm mt-1">
                    {activeTab === 'unread' ? "You're all caught up!" : "Check back later for updates"}
                  </p>
                </motion.div>
              )}
            </AnimatePresence>
    </div>
        </TabsContent>
    </Tabs>
    </div>
  );

  if (showAsDialog) {
    return (
      <Dialog>
        <DialogTrigger asChild>
          <Button variant="ghost" size="sm" className="relative">
            <Bell className="w-5 h-5" />
            {unreadCount > 0 && (
              <Badge 
                variant="destructive" 
                className="absolute -top-1 -right-1 h-4 w-4 text-xs p-0 flex items-center justify-center"
              >
                {unreadCount > 99 ? '99+' : unreadCount}
              </Badge>
            )}
          </Button>
    </DialogTrigger>
        <DialogContent className="max-w-md">
          {notificationContent}
        </DialogContent>
    </Dialog>
    );
  }

  return (
    <Card className={className}>
      <CardContent className="p-4">
        {notificationContent}
      </CardContent>
    </Card>
  );
});

EnhancedNotificationsCenter.displayName = 'EnhancedNotificationsCenter';

export default EnhancedNotificationsCenter;