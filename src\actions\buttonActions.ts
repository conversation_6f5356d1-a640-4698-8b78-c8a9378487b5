import { apiClient } from '../lib/api-client';
import { uniqueToast } from '../utils/toastManager';

// Types
interface ActionResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
}

interface FriendRequestData {
  id: string, userId: string, status: 'pending' | 'accepted' | 'declined', createdAt: string;
}

interface GroupData {
  id: string, name: string, memberCount: number, isJoined: boolean;
}

interface PageData {
  id: string, name: string, followerCount: number, isFollowing: boolean, isLiked: boolean;
}

interface PostInteractionData {
  postId: string, likes: number, comments: number, shares: number, isLiked: boolean, isSaved: boolean;
}

// Error handling helper
interface ErrorWithResponse {
  response?: {
    data?: {
      message?: string;
    };
  };
}

const handleError = (error: unknown, defaultMessage: string): string => {
  console.error('Action error:', error);
  const errorWithResponse = error as ErrorWithResponse;
  if (errorWithResponse.response?.data?.message) {
    return errorWithResponse.response.data.message;
  }
  return defaultMessage;
};

// Friend request actions
export const sendFriendRequest = async (
  userId: string,
  options?: { showToast?: boolean }
): Promise<ActionResponse<FriendRequestData>> => {
  try {
    const response = await apiClient.post(`/api/friends/request/${userId}`);
    const data = await response.json();
    
    if (options?.showToast !== false) {
      uniqueToast.success('Friend request sent');
    }
    
    return { success: true, data };
  } catch (error) {
    const errorMessage = handleError(error, 'Failed to send friend request');
    if (options?.showToast !== false) {
      uniqueToast.error(errorMessage);
    }
    return { success: false, error: errorMessage };
  }
};

export const acceptFriendRequest = async (
  requestId: string,
  options?: { showToast?: boolean }
): Promise<ActionResponse<FriendRequestData>> => {
  try {
    const response = await apiClient.post(`/api/friends/accept/${requestId}`);
    const data = await response.json();
    
    if (options?.showToast !== false) {
      uniqueToast.success('Friend request accepted');
    }
    
    return { success: true, data };
  } catch (error) {
    const errorMessage = handleError(error, 'Failed to accept friend request');
    if (options?.showToast !== false) {
      uniqueToast.error(errorMessage);
    }
    return { success: false, error: errorMessage };
  }
};

export const declineFriendRequest = async (
  requestId: string,
  options?: { showToast?: boolean }
): Promise<ActionResponse<FriendRequestData>> => {
  try {
    const response = await apiClient.post(`/api/friends/decline/${requestId}`);
    const data = await response.json();
    
    if (options?.showToast !== false) {
      uniqueToast.info('Friend request declined');
    }
    
    return { success: true, data };
  } catch (error) {
    const errorMessage = handleError(error, 'Failed to decline friend request');
    if (options?.showToast !== false) {
      uniqueToast.error(errorMessage);
    }
    return { success: false, error: errorMessage };
  }
};

// Group actions
export const joinGroup = async (
  groupId: string,
  options?: { showToast?: boolean }
): Promise<ActionResponse<GroupData>> => {
  try {
    const response = await apiClient.post(`/api/groups/join/${groupId}`);
    const data = await response.json();
    
    if (options?.showToast !== false) {
      uniqueToast.success('Joined group successfully');
    }
    
    return { success: true, data };
  } catch (error) {
    const errorMessage = handleError(error, 'Failed to join group');
    if (options?.showToast !== false) {
      uniqueToast.error(errorMessage);
    }
    return { success: false, error: errorMessage };
  }
};

export const leaveGroup = async (
  groupId: string,
  options?: { showToast?: boolean }
): Promise<ActionResponse<GroupData>> => {
  try {
    const response = await apiClient.post(`/api/groups/leave/${groupId}`);
    const data = await response.json();
    
    if (options?.showToast !== false) {
      uniqueToast.success('Left group successfully');
    }
    
    return { success: true, data };
  } catch (error) {
    const errorMessage = handleError(error, 'Failed to leave group');
    if (options?.showToast !== false) {
      uniqueToast.error(errorMessage);
    }
    return { success: false, error: errorMessage };
  }
};

export const inviteToGroup = async (
  groupId: string,
  userId: string,
  options?: { showToast?: boolean }
): Promise<ActionResponse> => {
  try {
    const response = await apiClient.post(`/api/groups/invite`, { groupId, userId });
    const data = await response.json();
    
    if (options?.showToast !== false) {
      uniqueToast.success('Invitation sent successfully');
    }
    
    return { success: true, data };
  } catch (error) {
    const errorMessage = handleError(error, 'Failed to send invitation');
    if (options?.showToast !== false) {
      uniqueToast.error(errorMessage);
    }
    return { success: false, error: errorMessage };
  }
};

// Page actions
export const followPage = async (
  pageId: string,
  options?: { showToast?: boolean }
): Promise<ActionResponse<PageData>> => {
  try {
    const response = await apiClient.post(`/api/pages/follow/${pageId}`);
    const data = await response.json();
    
    if (options?.showToast !== false) {
      uniqueToast.success('Page followed');
    }
    
    return { success: true, data };
  } catch (error) {
    const errorMessage = handleError(error, 'Failed to follow page');
    if (options?.showToast !== false) {
      uniqueToast.error(errorMessage);
    }
    return { success: false, error: errorMessage };
  }
};

export const unfollowPage = async (
  pageId: string,
  options?: { showToast?: boolean }
): Promise<ActionResponse<PageData>> => {
  try {
    const response = await apiClient.post(`/api/pages/unfollow/${pageId}`);
    const data = await response.json();
    
    if (options?.showToast !== false) {
      uniqueToast.success('Page unfollowed');
    }
    
    return { success: true, data };
  } catch (error) {
    const errorMessage = handleError(error, 'Failed to unfollow page');
    if (options?.showToast !== false) {
      uniqueToast.error(errorMessage);
    }
    return { success: false, error: errorMessage };
  }
};

export const likePage = async (
  pageId: string,
  options?: { showToast?: boolean }
): Promise<ActionResponse<PageData>> => {
  try {
    const response = await apiClient.post(`/api/pages/like/${pageId}`);
    const data = await response.json();
    
    if (options?.showToast !== false) {
      uniqueToast.success('Page liked');
    }
    
    return { success: true, data };
  } catch (error) {
    const errorMessage = handleError(error, 'Failed to like page');
    if (options?.showToast !== false) {
      uniqueToast.error(errorMessage);
    }
    return { success: false, error: errorMessage };
  }
};

export const unlikePage = async (
  pageId: string,
  options?: { showToast?: boolean }
): Promise<ActionResponse<PageData>> => {
  try {
    const response = await apiClient.post(`/api/pages/unlike/${pageId}`);
    const data = await response.json();
    
    if (options?.showToast !== false) {
      uniqueToast.success('Page unliked');
    }
    
    return { success: true, data };
  } catch (error) {
    const errorMessage = handleError(error, 'Failed to unlike page');
    if (options?.showToast !== false) {
      uniqueToast.error(errorMessage);
    }
    return { success: false, error: errorMessage };
  }
};

// Post interactions
export const likePost = async (
  postId: string,
  options?: { showToast?: boolean; optimistic?: boolean }
): Promise<ActionResponse<PostInteractionData>> => {
  try {
    const response = await apiClient.post(`/api/posts/like/${postId}`);
    const data = await response.json();
    
    if (options?.showToast !== false) {
      uniqueToast.success('Post liked');
    }
    
    return { success: true, data };
  } catch (error) {
    const errorMessage = handleError(error, 'Failed to like post');
    if (options?.showToast !== false) {
      uniqueToast.error(errorMessage);
    }
    return { success: false, error: errorMessage };
  }
};

export const unlikePost = async (
  postId: string,
  options?: { showToast?: boolean; optimistic?: boolean }
): Promise<ActionResponse<PostInteractionData>> => {
  try {
    const response = await apiClient.post(`/api/posts/unlike/${postId}`);
    const data = await response.json();
    
    if (options?.showToast !== false) {
      uniqueToast.success('Post unliked');
    }
    
    return { success: true, data };
  } catch (error) {
    const errorMessage = handleError(error, 'Failed to unlike post');
    if (options?.showToast !== false) {
      uniqueToast.error(errorMessage);
    }
    return { success: false, error: errorMessage };
  }
};

export const commentOnPost = async (
  postId: string,
  comment: string,
  options?: { showToast?: boolean }
): Promise<ActionResponse<{ id: string, content: string, createdAt: string }>> => {
  try {
    const response = await apiClient.post(`/api/posts/comment/${postId}`, { content: comment });
    const data = await response.json();
    
    if (options?.showToast !== false) {
      uniqueToast.success('Comment added');
    }
    
    return { success: true, data };
  } catch (error) {
    const errorMessage = handleError(error, 'Failed to add comment');
    if (options?.showToast !== false) {
      uniqueToast.error(errorMessage);
    }
    return { success: false, error: errorMessage };
  }
};

export const sharePost = async (
  postId: string,
  options?: { showToast?: boolean; shareText?: string }
): Promise<ActionResponse<{ shareId: string }>> => {
  try {
    const response = await apiClient.post(`/api/posts/share/${postId}`, {
      text: options?.shareText
    });
    const data = await response.json();
    
    if (options?.showToast !== false) {
      uniqueToast.success('Post shared');
    }
    
    return { success: true, data };
  } catch (error) {
    const errorMessage = handleError(error, 'Failed to share post');
    if (options?.showToast !== false) {
      uniqueToast.error(errorMessage);
    }
    return { success: false, error: errorMessage };
  }
};

export const savePost = async (
  postId: string,
  options?: { showToast?: boolean; collectionId?: string }
): Promise<ActionResponse<{ savedId: string }>> => {
  try {
    const response = await apiClient.post(`/api/posts/save/${postId}`, {
      collectionId: options?.collectionId
    });
    const data = await response.json();
    
    if (options?.showToast !== false) {
      uniqueToast.success('Post saved');
    }
    
    return { success: true, data };
  } catch (error) {
    const errorMessage = handleError(error, 'Failed to save post');
    if (options?.showToast !== false) {
      uniqueToast.error(errorMessage);
    }
    return { success: false, error: errorMessage };
  }
};

export const unsavePost = async (
  postId: string,
  options?: { showToast?: boolean }
): Promise<ActionResponse> => {
  try {
    const response = await apiClient.delete(`/api/posts/save/${postId}`);
    const data = await response.json();
    
    if (options?.showToast !== false) {
      uniqueToast.success('Post removed from saved');
    }
    
    return { success: true, data };
  } catch (error) {
    const errorMessage = handleError(error, 'Failed to unsave post');
    if (options?.showToast !== false) {
      uniqueToast.error(errorMessage);
    }
    return { success: false, error: errorMessage };
  }
};
