import React, { useCallback, useMemo } from 'react';
import { Search, Edit, Filter } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import ConversationItem from './ConversationItem';

import { Conversation } from '@/types/messaging';

interface MessageListProps {
  conversations: Conversation[], selectedConversationId: string | null, searchQuery: string, onSearchChange: (query: string) => void; onSelectConversation: (id: string) => void; isMobile: boolean, showConversation: boolean;
}

const MessageList: React.FC<MessageListProps> = React.memo(({
  conversations,
  selectedConversationId,
  searchQuery,
  onSearchChange,
  onSelectConversation,
  isMobile,
  showConversation
}) => {
  // Memoize filtered conversations to avoid recalculation on every render
  const filteredConversations = useMemo(() => {
    if (!searchQuery) return conversations;
    
    const query = searchQuery.toLowerCase();
    return conversations.filter(conversation => {
      // Handle both direct conversations (user) and group conversations (name)
      const searchName = conversation.user?.name || conversation.name || '';
      return searchName.toLowerCase().includes(query);
    });
  }, [conversations, searchQuery]);

  // Memoize handlers to prevent unnecessary re-renders of conversation items
  const handleSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    onSearchChange(e.target.value);
  }, [onSearchChange]);

  const handleConversationClick = useCallback((conversationId: string) => {
    onSelectConversation(conversationId);
  }, [onSelectConversation]);

  // Only show if not mobile or not viewing a conversation
  if (isMobile && showConversation) return null;

  return (
    <div className="w-full md:w-80 border-r dark:border-gray-700 flex flex-col">
      <div className="p-3 border-b dark:border-gray-700">
        <div className="flex items-center justify-between mb-3">
          <h2 className="text-xl font-bold dark:text-white">Messages</h2>
          <div className="flex space-x-1">
            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
              <Edit className="w-5 h-5 text-gray-500 dark:text-gray-400" />
    </Button>
            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
              <Filter className="w-5 h-5 text-gray-500 dark:text-gray-400" />
    </Button>
          </div>
    </div>
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="Search messages"
            value={searchQuery} onChange={handleSearchChange}, className="pl-10 dark:bg-gray-700 dark:border-gray-600 dark:text-white h-9"
          />
    </div>
      </div>
      <div className="flex-1 overflow-y-auto">
        {filteredConversations.map((conversation) => (
          <ConversationItem
            key={conversation.id} conversation={conversation}, isSelected={selectedConversationId === conversation.id} onClick={handleConversationClick}
          />
        ))}
      </div>
    </div>
  );
});

MessageList.displayName = 'MessageList';

export default MessageList;
