CRUSH quick reference for this repo

Commands
- Dev: npm run dev (or npm run dev:stable for polling) • Preview: npm run preview
- Build: npm run build • Strict build: npm run build-with-types • Analyze: npm run build:analyze
- Lint: npm run lint • Types: npm run type-check • Clean: npm run clean
- Tests (Vitest): npx vitest run • Watch: npx vitest
- Run a single test file: npx vitest run path/to/file.test.tsx
- Run a single test by name: npx vitest -t "test name"

Testing setup
- Vitest + jsdom; setup file: src/test/setup.ts; path aliases: @ -> src
- Test files live under src/**/__tests__ and tests/unit/** with *.test|*.spec.(ts|tsx)

Code style
- Language: TypeScript strict, React 18 with react-jsx; module type: ESM
- Imports: use path alias @/ for src; order: node builtin/external then internal; prefer type-only imports (import type { X })
- Formatting: Prettier-compatible; 2 spaces; single quotes in config, double quotes OK in TSX; no semicolons policy not enforced
- Linting: ESLint flat config with @typescript-eslint, react, react-hooks, react-refresh; fix warnings before commit
- React: no need for React in scope; prefer functional components; hooks follow rules; memoize expensive components
- Types: avoid any; allow explicit any only with justification; enable noUnusedLocals/Parameters; return types inferred unless public APIs
- Naming: PascalCase for components/types; camelCase for vars/functions; UPPER_SNAKE_CASE for const enums and env flags
- Errors: never swallow; use error boundaries for UI; sanitize user content (see src/utils/contentSanitization.ts); use errorLogger where available
- Async: prefer async/await; wrap in try/catch with typed errors; never throw bare strings
- State: colocate; use Zustand/React Query where present; avoid prop drilling; memoize selectors

Project notes
- Build tool: Vite; aliases in vitest.config.ts; tsconfig paths { "@/*": "src/*" }
- Server mocks and local assets live under public/ and mocks/
- CI runs lint and type-check (see .github/workflows/ci.yml)

Contributors
- Before PR: npm run lint && npm run type-check && npx vitest run
- Commit messages: concise and purposeful; avoid generic "update"

Cursor/Copilot rules
- No Cursor or Copilot rule files found in repo at generation time
