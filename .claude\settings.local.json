{"permissions": {"allow": ["Bash(npm run dev:*)", "Bash(npm start)", "Bash(npm run lint)", "Bash(npm run:*)", "Bash(timeout 10s npm run dev)", "<PERSON><PERSON>(timeout 15s npm run dev)", "Bash(rm:*)", "Bash(node.exe:*)", "Bash(grep:*)", "<PERSON><PERSON>(curl:*)", "Bash(ls:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(timeout:*)", "Bash(ss:*)", "Bash(find:*)", "Bash(rg:*)", "Bash(npx tsc:*)", "Bash(npx eslint:*)", "<PERSON><PERSON>(wget:*)", "Bash(npm install:*)", "Bash(node:*)", "<PERSON><PERSON>(mv:*)", "Bash(npx vite build:*)", "<PERSON><PERSON>(tsc --noEmit --skipL<PERSON><PERSON><PERSON><PERSON>)", "Bash(npm ci:*)", "<PERSON><PERSON>(true)", "Bash(./node_modules/.bin/vite)", "<PERSON><PERSON>(cat:*)", "Bash(npm:*)", "<PERSON><PERSON>(touch:*)"], "deny": []}}