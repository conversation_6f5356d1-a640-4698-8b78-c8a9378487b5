/**
 * Messaging Constants
 * Consolidated constants and configurations for messaging components
 */

import type { AnimationProps } from 'framer-motion';

// Size variants used across messaging components
export const SIZE_VARIANTS = ['sm', 'md', 'lg'] as const;
export type SizeVariant = typeof SIZE_VARIANTS[number];

// Position variants for popover and floating elements
export const POSITION_VARIANTS = [
  'bottom-right', 
  'bottom-left', 
  'top-right', 
  'top-left',
  'center'
] as const;
export type PositionVariant = typeof POSITION_VARIANTS[number];

// Standard animation springs
export const ANIMATION_SPRINGS = {
  default: { stiffness: 400, damping: 25 },
  gentle: { stiffness: 300, damping: 30 },
  snappy: { stiffness: 500, damping: 30 },
  smooth: { stiffness: 200, damping: 20 }
} as const;

// Common animation variants
export const COMMON_VARIANTS = {
  fadeIn: {
    initial: { opacity: 0, y: 10 },
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: -10 }
  },
  scaleIn: {
    initial: { scale: 0, opacity: 0 },
    animate: { scale: 1, opacity: 1 },
    exit: { scale: 0, opacity: 0 }
  },
  slideIn: {
    initial: { x: -20, opacity: 0 },
    animate: { x: 0, opacity: 1 },
    exit: { x: 20, opacity: 0 }
  },
  pulse: {
    animate: {
      scale: [1, 1.1, 1],
      opacity: [1, 0.8, 1],
      transition: {
        duration: 2,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }
  }
} as const;

// Size configuration mappings
export const SIZE_CONFIG = {
  sm: {
    avatar: 'w-6 h-6',
    dot: 'w-1 h-1',
    text: 'text-xs',
    spacing: 'space-x-1',
    padding: 'px-2 py-1',
    iconSize: 'w-3 h-3'
  },
  md: {
    avatar: 'w-8 h-8',
    dot: 'w-1.5 h-1.5',
    text: 'text-sm',
    spacing: 'space-x-2',
    padding: 'px-3 py-2',
    iconSize: 'w-4 h-4'
  },
  lg: {
    avatar: 'w-10 h-10',
    dot: 'w-2 h-2',
    text: 'text-base',
    spacing: 'space-x-3',
    padding: 'px-4 py-3',
    iconSize: 'w-5 h-5'
  }
} as const;

// Status colors
export const STATUS_COLORS = {
  online: 'text-green-500',
  offline: 'text-gray-400',
  away: 'text-yellow-500',
  busy: 'text-red-500',
  connected: 'text-green-500',
  connecting: 'text-yellow-500',
  disconnected: 'text-red-500',
  sending: 'text-gray-400',
  sent: 'text-gray-400',
  delivered: 'text-gray-400',
  read: 'text-blue-500',
  error: 'text-red-500'
} as const;

// Common timeouts and limits
export const TIMING_CONSTANTS = {
  TYPING_TIMEOUT: 5000, // 5 seconds
  MESSAGE_RETRY_DELAY: 3000, // 3 seconds
  CONNECTION_TIMEOUT: 10000, // 10 seconds
  DEBOUNCE_DELAY: 300, // 300ms
  ANIMATION_DURATION: 200, // 200ms
  TOAST_DURATION: 4000, // 4 seconds
  MAX_RECONNECT_ATTEMPTS: 5,
  MAX_MESSAGE_LENGTH: 4000,
  MAX_FILE_SIZE: 50 * 1024 * 1024, // 50MB
  MAX_VISIBLE_REACTIONS: 6,
  MAX_TYPING_USERS: 3,
  CACHE_TTL: 5 * 60 * 1000 // 5 minutes
} as const;

// Common class patterns
export const COMMON_CLASSES = {
  button: {
    base: 'inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none',
    sizes: {
      sm: 'h-8 px-3 text-xs',
      md: 'h-9 px-4 text-sm',
      lg: 'h-10 px-6 text-base'
    }
  },
  input: {
    base: 'flex w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
    error: 'border-red-500 focus-visible:ring-red-500'
  },
  badge: {
    base: 'inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
    variants: {
      default: 'border-transparent bg-primary text-primary-foreground hover:bg-primary/80',
      secondary: 'border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80',
      destructive: 'border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80',
      outline: 'text-foreground'
    }
  }
} as const;

// Keyboard shortcuts
export const KEYBOARD_SHORTCUTS = {
  SEND_MESSAGE: 'Enter',
  NEW_LINE: 'Shift+Enter',
  ESCAPE: 'Escape',
  ARROW_UP: 'ArrowUp',
  ARROW_DOWN: 'ArrowDown',
  TAB: 'Tab',
  FOCUS_NEXT: 'Tab',
  FOCUS_PREVIOUS: 'Shift+Tab'
} as const;

// Default functions (reusable across components)
export const DEFAULT_FUNCTIONS = {
  getUserName: (id: string): string => `User ${id.slice(-4)}`,
  getUserAvatar: (): string => '/default-avatar.png'; formatFileSize: (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  },
  formatTimeAgo: (timestamp: number): string => {
    const now = Date.now();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    if (days < 7) return `${days}d ago`;
    
    return new Date(timestamp).toLocaleDateString();
  }
} as const;