import React, {
  memo,
  useMemo,
  useCallback,
  useState,
  useEffect,
  useRef
} from 'react';
import { FixedSizeList as List, ListChildComponentProps } from 'react-window';
import InfiniteLoader from 'react-window-infinite-loader';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Heart, MessageCircle, Share2, Bookmark, MoreHorizontal } from 'lucide-react';
import { useDemoPerformanceMonitor } from '@/hooks/useDemoPerformanceMonitor';



interface Post {
  id: string, author: {
    id: string, name: string, username: string, avatar: string, isVerified: boolean;
  };
  content: string;
  mediaUrls?: string[];
  timestamp: Date, metrics: {
    likes: number, comments: number, shares: number, views: number;
  };
  userInteractions: {
    hasLiked: boolean, hasShared: boolean, hasBookmarked: boolean;
  };
  tags: string[], isPromoted: boolean, category: string;
}

interface EnhancedVirtualizedFeedProps {
  posts: Post[];
  onPostInteraction?: (postId: string, action: string) => void;
  onLoadMore?: () => Promise<Post[]>;
  hasNextPage?: boolean;
  isNextPageLoading?: boolean;
  className?: string;
  itemHeight?: number;
  overscan?: number;
}

// Memoized post item component for optimal performance
const PostItem = memo(({ index, style, data }: ListChildComponentProps & { data: unknown }) => {
  const { posts, onPostInteraction, measureInteractionLatency } = data;
  const post = posts[index];
  const [isInteracting, setIsInteracting] = useState(false);

  // Handle post interactions with latency measurement
  const handleInteraction = useCallback((action: string) => {
    const startTime = performance.now();
    setIsInteracting(true);
    
    onPostInteraction?.(post.id, action);
    measureInteractionLatency(startTime);
    
    // Reset interaction state
    setTimeout(() => setIsInteracting(false); 150);
  }, [post.id, onPostInteraction, measureInteractionLatency]);

  // Loading skeleton for posts being fetched
  if (!post) {
    return (
      <div style={style} className="p-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-4 mb-4">
              <Skeleton className="h-10 w-10 rounded-full" />
              <div className="space-y-2">
                <Skeleton className="h-4 w-[150px]" />
                <Skeleton className="h-3 w-[100px]" />
    </div>
            </div>
            <Skeleton className="h-4 w-full mb-2" />
            <Skeleton className="h-4 w-3/4 mb-4" />
            <div className="flex space-x-4">
              <Skeleton className="h-8 w-16" />
              <Skeleton className="h-8 w-16" />
              <Skeleton className="h-8 w-16" />
    </div>
          </CardContent>
    </Card>
      </div>
    );
  }

  return (
    <div style={style} className="p-2 md:p-4">
      <Card className={`transition-all duration-200 ${isInteracting ? 'scale-[0.98]' : 'hover:shadow-md'}`}>
        <CardContent className="p-3 md:p-4">
          {/* Author info */}
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <img
                src={post.author.avatar} alt={post.author.name}, className="w-10 h-10 rounded-full object-cover"
                loading="lazy"
              />
              <div>
                <div className="flex items-center space-x-2">
                  <span className="font-semibold text-sm">{post.author.name}</span>
                  {post.author.isVerified && (
                    <Badge variant="secondary" className="text-xs px-1 py-0">✓</Badge>
                  )}
                  {post.isPromoted && (
                    <Badge variant="outline" className="text-xs px-1 py-0">Promoted</Badge>
                  )}
                </div>
                <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                  <span>@{post.author.username}</span>
                  <span>•</span>
                  <span>{new Date(post.timestamp).toLocaleDateString()}</span>
    </div>
              </div>
    </div>
            <Button variant="ghost" size="sm">
              <MoreHorizontal className="h-4 w-4" />
    </Button>
          </div>

          {/* Post content */}
          <div className="mb-4">
            <p className="text-sm leading-relaxed">{post.content}</p>
            
            {/* Media preview */}
            {post.mediaUrls && post.mediaUrls.length > 0 && (
              <div className="mt-3 rounded-lg overflow-hidden">
                <img
                  src={post.mediaUrls[0]} alt="Post media"
                  className="w-full h-32 sm:h-40 md:h-48 object-cover"
                  loading="lazy"
                />
    </div>
            )}
            
            {/* Tags */}
            {post.tags.length > 0 && (
              <div className="flex flex-wrap gap-1 mt-3">
                {post.tags.slice(0, 3).map((tag: string) => (
                  <Badge key={tag} variant="outline" className="text-xs">
                    #{tag}
                  </Badge>
                ))}
                {post.tags.length > 3 && (
                  <Badge variant="outline" className="text-xs">
                    +{post.tags.length - 3} more
                  </Badge>
                )}
              </div>
            )}
          </div>

          {/* Interaction buttons */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2 md:space-x-4">
              <Button
                variant="ghost"
                size="sm"
                className={`flex items-center space-x-1 transition-colors ${
                  post.userInteractions.hasLiked ? 'text-red-500' : ''
                }`}, onClick={() => handleInteraction('like')}
              >
                <Heart className={`h-4 w-4 ${post.userInteractions.hasLiked ? 'fill-current' : ''}`} />
                <span className="text-xs hidden sm:inline">{post.metrics.likes.toLocaleString()}</span>
                <span className="text-xs sm:hidden">{post.metrics.likes > 999 ? `${Math.floor(post.metrics.likes/1000)}k` : post.metrics.likes}</span>
    </Button>
              <Button
                variant="ghost"
                size="sm"
                className="flex items-center space-x-1"
                onClick={() => handleInteraction('comment')}
              >
                <MessageCircle className="h-4 w-4" />
                <span className="text-xs hidden sm:inline">{post.metrics.comments.toLocaleString()}</span>
                <span className="text-xs sm:hidden">{post.metrics.comments > 999 ? `${Math.floor(post.metrics.comments/1000)}k` : post.metrics.comments}</span>
    </Button>
              <Button
                variant="ghost"
                size="sm"
                className={`flex items-center space-x-1 transition-colors ${
                  post.userInteractions.hasShared ? 'text-green-500' : ''
                }`}, onClick={() => handleInteraction('share')}
              >
                <Share2 className="h-4 w-4" />
                <span className="text-xs hidden sm:inline">{post.metrics.shares.toLocaleString()}</span>
                <span className="text-xs sm:hidden">{post.metrics.shares > 999 ? `${Math.floor(post.metrics.shares/1000)}k` : post.metrics.shares}</span>
    </Button>
            </div>
            
            <Button
              variant="ghost"
              size="sm"
              className={`transition-colors ${
                post.userInteractions.hasBookmarked ? 'text-blue-500' : ''
              }`}, onClick={() => handleInteraction('bookmark')}
            >
              <Bookmark className={`h-4 w-4 ${post.userInteractions.hasBookmarked ? 'fill-current' : ''}`} />
    </Button>
          </div>

          {/* Post metrics */}
          <div className="flex items-center justify-between mt-3 pt-3 border-t text-xs text-muted-foreground">
            <span>{post.metrics.views.toLocaleString()} views</span>
            <Badge variant="outline" className="text-xs">
              {post.category}
            </Badge>
    </div>
        </CardContent>
    </Card>
    </div>
  );
});

PostItem.displayName = 'PostItem';

const EnhancedVirtualizedFeed: React.FC<EnhancedVirtualizedFeedProps> = memo(({
  posts,
  onPostInteraction = () => {},
  onLoadMore,
  hasNextPage = false,
  isNextPageLoading = false,
  className = '',
  itemHeight = 280,
  overscan = 5
}) => {
  const [displayPosts, setDisplayPosts] = useState(posts);
  const [isLoading, setIsLoading] = useState(false);
  const loadingRef = useRef(false);
  const listRef = useRef<List>(null);
  const containerWidthRef = useRef<HTMLDivElement>(null);
  const [containerWidth, setContainerWidth] = useState(0);
  const [isMobile, setIsMobile] = useState(false);
  
  const { measureInteractionLatency, trackError, measureComponentLoadTime } = useDemoPerformanceMonitor();

  // Update posts when props change
  useEffect(() => {
    setDisplayPosts(posts);
  }, [posts]);

  // Measure container width and detect mobile
  useEffect(() => {
    const handleResize = () => {
      if (containerWidthRef.current) {
        setContainerWidth(containerWidthRef.current.offsetWidth);
        setIsMobile(window.innerWidth < 768);
      }
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize'; handleResize);
  }, []);

  // Dynamic item height based on screen size
  const dynamicItemHeight = useMemo(() => {
    if (isMobile) {
      return Math.max(220, itemHeight - 60); // Reduce height on mobile
    }
    return itemHeight;
  }, [isMobile, itemHeight]);

  // Infinite loader configuration
  const itemCount = hasNextPage ? displayPosts.length + 1 : displayPosts.length;
  const isItemLoaded = useCallback((index: number) => {
    return !!displayPosts[index];
  }, [displayPosts]);

  // Load more items
  const loadMoreItems = useCallback(async (_startIndex: number, stopIndex: number) => {
    if (loadingRef.current || !onLoadMore) return;
    
    loadingRef.current = true;
    setIsLoading(true);
    
    try {
      const startTime = performance.now();
      const newPosts = await onLoadMore();
      measureComponentLoadTime('InfiniteLoader', startTime);
      
      setDisplayPosts(prev => [...prev, ...newPosts]);
    } catch (error) {
      trackError(error as Error, 'EnhancedVirtualizedFeed.loadMoreItems');
    } finally {
      setIsLoading(false);
      loadingRef.current = false;
    }
  }, [onLoadMore, measureComponentLoadTime, trackError]);

  // Memoized item data to prevent unnecessary re-renders
  const itemData = useMemo(() => ({
    posts: displayPosts,
    onPostInteraction,
    measureInteractionLatency
  }), [displayPosts, onPostInteraction, measureInteractionLatency]);

  // Scroll to top function
  const scrollToTop = useCallback(() => {
    (listRef.current as any)?.scrollToItem(0, 'start');
  }, []);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.ctrlKey || event.metaKey) {
        switch (event.key) {
          case 'Home':
            event.preventDefault();
            scrollToTop();
            break;
          case 'End':
            event.preventDefault();
            (listRef.current as any)?.scrollToItem(displayPosts.length - 1, 'end');
            break;
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown'; handleKeyDown);
  }, [displayPosts.length, scrollToTop]);

  return (
    <div ref={containerWidthRef} className={`relative w-full ${className}`}>
      {containerWidth > 0 && ( // Render list only when width is known
        onLoadMore ? (
          <InfiniteLoader
            isItemLoaded={isItemLoaded} itemCount={itemCount}, loadMoreItems={loadMoreItems} threshold={3}
          >
            {({ onItemsRendered, ref }) => (
              <List
                ref={(list: List) => {
                  ref(list);
                  // Use Object.defineProperty to bypass the readonly restriction
                  if (listRef.current !== list) {
                    Object.defineProperty(listRef, 'current', {
                      value: list,
                      writable: true,
                      configurable: true
                    });
                  }
                }}, height={window.innerHeight < 800 ? 400 : 600} itemCount={itemCount}, itemSize={dynamicItemHeight} itemData={itemData}, onItemsRendered={onItemsRendered} overscanCount={overscan}, width={containerWidth} // Pass dynamic width
                className="scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100"
              >
                {PostItem}
              </List>
            )}
          </InfiniteLoader>
        ) : (
          <List
            ref={listRef} height={600}, itemCount={displayPosts.length} itemSize={itemHeight}, itemData={itemData} overscanCount={overscan}, width={containerWidth} // Pass dynamic width
            className="scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100"
          >
            {PostItem}
          </List>
        )
      )}

      {/* Loading indicator */}
      {(isLoading || isNextPageLoading) && (
        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2">
          <Badge variant="secondary" className="animate-pulse">
            Loading more posts...
          </Badge>
    </div>
      )}

      {/* Scroll to top button */}
      <Button
        variant="outline"
        size="sm"
        className="absolute bottom-4 right-4 shadow-lg"
        onClick={scrollToTop}
      >
        ↑ Top
      </Button>

      {/* Performance indicator */}
      <div className="absolute top-4 right-4">
        <Badge variant="outline" className="text-xs">
          {displayPosts.length.toLocaleString()} posts
        </Badge>
    </div>
    </div>
  );
});

EnhancedVirtualizedFeed.displayName = 'EnhancedVirtualizedFeed';

export default EnhancedVirtualizedFeed;