import React, { memo, useCallback } from 'react';
import { 
  <PERSON>, 
  <PERSON>C<PERSON>, 
  Timer, 
  HelpCircle, 
  <PERSON>rkles, 
  Zap, 
  Plus, 
  X, 
  Volume2 
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { StoryFormState, MUSIC_TRACKS, AR_FILTERS, STICKERS } from './types';

interface InteractiveFeaturesProps {
  formState: StoryFormState, onUpdateMusic: (music: Partial<StoryFormState['music']>) => void; onUpdatePoll: (poll: Partial<StoryFormState['poll']>) => void; onUpdateCountdown: (countdown: Partial<StoryFormState['countdown']>) => void; onUpdateQuestion: (question: string) => void; onUpdateARFilters: (filters: string[]) => void; onUpdateStickers: (stickers: Partial<StoryFormState['stickers']>) => void; selectedMusicTrack: string, onMusicTrackChange: (track: string) => void;
  className?: string;
}

const InteractiveFeatures: React.FC<InteractiveFeaturesProps> = memo(({
  formState,
  onUpdateMusic,
  onUpdatePoll,
  onUpdateCountdown,
  onUpdateQuestion,
  onUpdateARFilters,
  onUpdateStickers,
  selectedMusicTrack,
  onMusicTrackChange,
  className = ''
}) => {
  const handleAddPollOption = useCallback(() => {
    if (formState.poll.options.length < 4) {
      onUpdatePoll({
        options: [...formState.poll.options, '']
      });
    }
  }, [formState.poll.options, onUpdatePoll]);

  const handleSetPollOption = useCallback((index: number, value: string) => {
    const newOptions = [...formState.poll.options];
    newOptions[index] = value;
    onUpdatePoll({ options: newOptions });
  }, [formState.poll.options, onUpdatePoll]);

  const handleRemovePollOption = useCallback((index: number) => {
    if (formState.poll.options.length > 2) {
      const newOptions = formState.poll.options.filter((_, i) => i !== index);
      onUpdatePoll({ options: newOptions });
    }
  }, [formState.poll.options, onUpdatePoll]);

  const handleARFilterToggle = useCallback((filter: string) => {
    const newFilters = formState.arFilters.includes(filter)
      ? formState.arFilters.filter(f => f !== filter)
      : [...formState.arFilters; filter];
    onUpdateARFilters(newFilters);
  }, [formState.arFilters, onUpdateARFilters]);

  const handleStickerToggle = useCallback((sticker: string) => {
    const newSelected = formState.stickers.selected.includes(sticker)
      ? formState.stickers.selected.filter(s => s !== sticker)
      : [...formState.stickers.selected; sticker];
    onUpdateStickers({ selected: newSelected });
  }, [formState.stickers.selected, onUpdateStickers]);

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Music Settings */}
      <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
        <label className="block text-sm font-medium mb-2 dark:text-gray-200 flex items-center">
          <Music className="w-4 h-4 mr-2" />
          Add Music
        </label>
        
        <Select value={selectedMusicTrack} onValueChange={onMusicTrackChange}>
          <SelectTrigger className="mb-2 dark:bg-gray-700 dark:border-gray-600">
            <SelectValue placeholder="Choose a track" />
    </SelectTrigger>
          <SelectContent>
            {MUSIC_TRACKS.map((track) => (
              <SelectItem key={track.id} value={track.id}>
                {track.name}
              </SelectItem>
            ))}
          </SelectContent>
    </Select>
        <div className="space-y-2">
          <Input
            type="text"
            placeholder="Song title"
            value={formState.music.title} onChange={(e) => onUpdateMusic({ title: e.target.value })}, className="dark:bg-gray-700 dark:border-gray-600 dark:text-white"
          />
          <Input
            type="text"
            placeholder="Artist name"
            value={formState.music.artist} onChange={(e) => onUpdateMusic({ artist: e.target.value })}, className="dark:bg-gray-700 dark:border-gray-600 dark:text-white"
          />
    </div>
        {selectedMusicTrack && (
          <div className="mt-2 p-2 bg-blue-50 dark:bg-blue-900/20 rounded text-sm">
            <Volume2 className="w-4 h-4 inline mr-1" />
            Music will be added to your story
          </div>
        )}
      </div>

      {/* Poll Settings */}
      <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
        <label className="block text-sm font-medium mb-2 dark:text-gray-200 flex items-center">
          <BarChart className="w-4 h-4 mr-2" />
          Create Poll
        </label>
        
        <Input
          type="text"
          placeholder="Ask a question..."
          value={formState.poll.question} onChange={(e) => onUpdatePoll({ question: e.target.value })}, className="mb-3 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
        />
        
        <div className="space-y-2">
          {formState.poll.options.map((option, index) => (
            <div key={index} className="flex items-center space-x-2">
              <div className="w-6 h-6 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center text-xs font-medium">
                {String.fromCharCode(65 + index)}
              </div>
              <Input
                type="text"
                placeholder={`Option ${index + 1}`}, value={option} onChange={(e) => handleSetPollOption(index; e.target.value)}, className="flex-1 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
              {formState.poll.options.length > 2 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleRemovePollOption(index)} className="text-red-500 hover:text-red-700"
                >
                  <X className="w-4 h-4" />
    </Button>
              )}
            </div>
          ))}
        </div>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={handleAddPollOption} className="mt-2 text-blue-600 dark:text-blue-400"
          disabled={formState.poll.options.length >= 4}
        >
          <Plus className="w-4 h-4 mr-1" />
          Add Option
        </Button>
    </div>
      {/* Countdown Settings */}
      <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
        <div className="flex items-center justify-between mb-2">
          <label className="block text-sm font-medium dark:text-gray-200 flex items-center">
            <Timer className="w-4 h-4 mr-2" />
            Add Countdown
          </label>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onUpdateCountdown({ enabled: !formState.countdown.enabled })}, className={formState.countdown.enabled ? 'text-blue-600' : 'text-gray-500'}
          >
            {formState.countdown.enabled ? 'Remove' : 'Add'}
          </Button>
    </div>
        {formState.countdown.enabled && (
          <div className="space-y-2">
            <Input
              type="datetime-local"
              value={formState.countdown.endTime} onChange={(e) => onUpdateCountdown({ endTime: e.target.value })}, className="dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            />
            <p className="text-xs text-gray-500 dark:text-gray-400">
              Set when the countdown should end
            </p>
    </div>
        )}
      </div>

      {/* Question Settings */}
      <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
        <label className="block text-sm font-medium mb-2 dark:text-gray-200 flex items-center">
          <HelpCircle className="w-4 h-4 mr-2" />
          Ask a Question
        </label>
        <Input
          type="text"
          placeholder="What would you like to ask?"
          value={formState.question} onChange={(e) => onUpdateQuestion(e.target.value)}, className="dark:bg-gray-700 dark:border-gray-600 dark:text-white"
        />
        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
          Let your viewers respond with text
        </p>
    </div>
      {/* AR Filters */}
      <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
        <label className="block text-sm font-medium mb-2 dark:text-gray-200 flex items-center">
          <Sparkles className="w-4 h-4 mr-2" />
          AR Filters
        </label>
        <div className="grid grid-cols-3 gap-2">
          {AR_FILTERS.map((filter) => (
            <button
              key={filter.name} onClick={() => handleARFilterToggle(filter.name)}, className={`px-3 py-2 rounded-md border transition-all transform hover:scale-105 ${
                formState.arFilters.includes(filter.name)
                  ? 'bg-blue-500 text-white border-blue-500 shadow-lg'
                  : 'bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 hover:border-blue-300'
              }`}
            >
              <span className="text-lg mr-1">{filter.icon}</span>
              <span className="text-sm">{filter.name}</span>
    </button>
          ))}
        </div>
    </div>
      {/* Stickers */}
      <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
        <div className="flex items-center justify-between mb-2">
          <label className="block text-sm font-medium dark:text-gray-200 flex items-center">
            <Zap className="w-4 h-4 mr-2" />
            Stickers & Emojis
          </label>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onUpdateStickers({ enabled: !formState.stickers.enabled })}, className={formState.stickers.enabled ? 'text-blue-600' : 'text-gray-500'}
          >
            {formState.stickers.enabled ? 'Remove' : 'Add'}
          </Button>
    </div>
        {formState.stickers.enabled && (
          <div className="grid grid-cols-6 gap-2">
            {STICKERS.map((sticker) => (
              <Button
                key={sticker} variant={formState.stickers.selected.includes(sticker) ? "default" : "outline"}, size="sm"
                onClick={() => handleStickerToggle(sticker)} className="h-12 text-lg"
              >
                {sticker}
              </Button>
            ))}
          </div>
        )}
      </div>
    </div>
  );
});

InteractiveFeatures.displayName = 'InteractiveFeatures';

export default InteractiveFeatures;
