#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

function fixJSXCommas(content) {
  let lines = content.split('\n');
  let modified = false;
  
  for (let i = 0; i < lines.length; i++) {
    let line = lines[i];
    
    // Fix pattern: prop={value}, prop2={value2} (comma should not be there)
    if (line.includes('={') && line.includes('},')) {
      const fixed = line.replace(/\}\s*,\s*(\w+)=/g, '}\n          $1=');
      if (fixed !== line) {
        lines[i] = fixed;
        modified = true;
      }
    }
    
    // Fix pattern: prop={value} prop2={value2} (missing line break)
    const jsxPropPattern = /(\w+)=\{([^}]+)\}\s+(\w+)=/;
    if (jsxPropPattern.test(line)) {
      const fixed = line.replace(jsxPropPattern, '$1={$2}\n          $3=');
      if (fixed !== line) {
        lines[i] = fixed;
        modified = true;
      }
    }
    
    // Fix semicolon in setTimeout
    if (line.includes('setTimeout(') && line.includes(';')) {
      const fixed = line.replace(/setTimeout\(([^,]+);\s*(\d+)\)/g, 'setTimeout($1, $2)');
      if (fixed !== line) {
        lines[i] = fixed;
        modified = true;
      }
    }
    
    // Fix resolve; pattern
    if (line.includes('resolve;')) {
      const fixed = line.replace(/resolve\s*;\s*(\d+)/g, 'resolve, $1');
      if (fixed !== line) {
        lines[i] = fixed;
        modified = true;
      }
    }
  }
  
  return modified ? lines.join('\n') : content;
}

function fixFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const fixedContent = fixJSXCommas(content);
    
    if (fixedContent !== content) {
      fs.writeFileSync(filePath, fixedContent, 'utf8');
      console.log(`Fixed: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`Error fixing ${filePath}:`, error.message);
    return false;
  }
}

function walkDirectory(dir, extensions = ['.tsx', '.ts', '.jsx', '.js']) {
  const files = [];
  
  function walk(currentDir) {
    try {
      const items = fs.readdirSync(currentDir);
      
      for (const item of items) {
        const fullPath = path.join(currentDir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          walk(fullPath);
        } else if (stat.isFile() && extensions.some(ext => item.endsWith(ext))) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      console.error(`Error reading directory ${currentDir}:`, error.message);
    }
  }
  
  walk(dir);
  return files;
}

function main() {
  const srcDir = path.join(__dirname, '..', 'src');
  const files = walkDirectory(srcDir);
  
  console.log(`Found ${files.length} files to check...`);
  
  let fixedCount = 0;
  
  files.forEach(file => {
    if (fixFile(file)) {
      fixedCount++;
    }
  });
  
  console.log(`\nFixed ${fixedCount} files out of ${files.length} total files.`);
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}
