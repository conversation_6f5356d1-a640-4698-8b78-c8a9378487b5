/**
 * Advanced memoization strategies for React components
 * Provides intelligent caching and optimization utilities
 */

import React, { 
  memo, 
  useMemo, 
  useCallback, 
  useRef, 
  useState, 
  useEffect,
  ComponentType,
  ReactElement
} from 'react';
import { debounce } from './consolidatedUtils';

// Deep comparison for complex objects
function deepEqual(a: unknown, b: unknown): boolean {
  if (a === b) return true;
  
  if (a == null || b == null) return a === b;
  
  if (typeof a !== typeof b) return false;
  
  if (typeof a !== 'object') return a === b;
  
  if (Array.isArray(a) !== Array.isArray(b)) return false;
  
  if (Array.isArray(a)) {
    if (a.length !== b.length) return false;
    for (let i = 0; i < a.length; i++) {
      if (!deepEqual(a[i], b[i])) return false;
    }
    return true;
  }
  
  const keysA = Object.keys(a);
  const keysB = Object.keys(b);
  
  if (keysA.length !== keysB.length) return false;
  
  for (const key of keysA) {
    if (!keysB.includes(key)) return false;
    if (!deepEqual(a[key], b[key])) return false;
  }
  
  return true;
}

// Shallow comparison for objects (faster than deep equal)
function shallowEqual(a: unknown, b: unknown): boolean {
  if (a === b) return true;
  
  if (typeof a !== 'object' || typeof b !== 'object' || a == null || b == null) {
    return a === b;
  }
  
  const keysA = Object.keys(a);
  const keysB = Object.keys(b);
  
  if (keysA.length !== keysB.length) return false;
  
  for (const key of keysA) {
    if (a[key] !== b[key]) return false;
  }
  
  return true;
}

// Smart memo HOC with configurable comparison
export function smartMemo<P extends Record<string, any>>(
  Component: ComponentType<P>, options: {
    compareProps?: 'shallow' | 'deep' | ((prev: P, next: P) => boolean);
    displayName?: string;
    skipKeys?: (keyof P)[];
    includeKeys?: (keyof P)[];
  } = {}
): ComponentType<P> {
  const { 
    compareProps = 'shallow', 
    displayName, 
    skipKeys = [], 
    includeKeys 
  } = options;

  const areEqual = (prevProps: P, nextProps: P) => {
    // Filter props based on include/skip keys
    const filterProps = (props: P) => {
      if (includeKeys && includeKeys.length > 0) {
        const filtered = {} as P;
        includeKeys.forEach(key => {
          if (key in props) {
            filtered[key] = props[key];
          }
        });
        return filtered;
      }
      
      if (skipKeys.length > 0) {
        const filtered = { ...props };
        skipKeys.forEach(key => {
          delete filtered[key];
        });
        return filtered;
      }
      
      return props;
    };

    const prev = filterProps(prevProps);
    const next = filterProps(nextProps);

    if (typeof compareProps === 'function') {
      return compareProps(prev, next);
    }
    
    return compareProps === 'deep' ? deepEqual(prev, next) : shallowEqual(prev, next);
  };

  const MemoizedComponent = memo(Component, areEqual);
  
  if (displayName) {
    MemoizedComponent.displayName = displayName;
  } else {
    MemoizedComponent.displayName = `SmartMemo(${Component.displayName || Component.name})`;
  }
  
  return MemoizedComponent;
}

// Cached selector hook for expensive computations
export function useCachedSelector<T, R>(
  data: T, selector: (data: T) => R; dependencies: unknown[] = [], options: {
    cacheSize?: number;
    ttl?: number; // Time to live in milliseconds
  } = {}
): R {
  const { cacheSize = 10, ttl = 60000 } = options;
  const cacheRef = useRef<Map<string, { value: R, timestamp: number }>>(new Map());
  const selectorRef = useRef(selector);
  
  // Update selector ref if it changes
  selectorRef.current = selector;
  
  return useMemo(() => {
    const cache = cacheRef.current;
    const cacheKey = JSON.stringify({ data, dependencies });
    const now = Date.now();
    
    // Check for cached result
    const cached = cache.get(cacheKey);
    if (cached && (now - cached.timestamp) < ttl) {
      return cached.value;
    }
    
    // Compute new result
    const result = selectorRef.current(data);
    
    // Store in cache
    cache.set(cacheKey, { value: result, timestamp: now });
    
    // Cleanup old cache entries
    if (cache.size > cacheSize) {
      const entries = Array.from(cache.entries());
      entries.sort(_(a, b) => a[1].timestamp - b[1].timestamp);
      
      // Remove oldest entries
      const toRemove = entries.slice(0, cache.size - cacheSize);
      toRemove.forEach(([key]) => cache.delete(key));
    }
    
    return result;
  }, [data, JSON.stringify(dependencies), ttl, cacheSize]);
}

// Stable callback hook that persists between renders
export function useStableCallback<T extends (...args: unknown[]) => any>(
  callback: T; dependencies: unknown[] = []
): T {
  const callbackRef = useRef<T>(callback);
  const depsRef = useRef(dependencies);
  
  // Update callback if dependencies change
  if (!shallowEqual(depsRef.current, dependencies)) {
    callbackRef.current = callback;
    depsRef.current = dependencies;
  }
  
  return useCallback((...args: unknown[]) => {
    return callbackRef.current(...args);
  }, []) as T;
}

// Memoized value hook with deep comparison
export function useDeepMemo<T>(factory: () => T; deps: unknown[]): T {
  const ref = useRef<{ deps: unknown[], value: T }>();
  
  if (!ref.current || !deepEqual(ref.current.deps, deps)) {
    ref.current = {
      deps,
      value: factory()
    };
  }
  
  return ref.current.value;
}

// Debounced state hook for expensive state updates
export function useDebouncedState<T>(
  initialValue: T, delay: number = 300
): [T, T, (value: T) => void] {
  const [immediateValue, setImmediateValue] = useState(initialValue);
  const [debouncedValue, setDebouncedValue] = useState(initialValue);
  
  const debouncedSetValue = useStableCallback(
    debounce((value: T) => {
      setDebouncedValue(value);
    }, delay),
    [delay]
  );
  
  const setValue = useCallback((value: T) => {
    setImmediateValue(value);
    debouncedSetValue(value);
  }, [debouncedSetValue]);
  
  return [immediateValue, debouncedValue, setValue];
}

// Memoized component factory for dynamic components
export function createMemoizedComponent<P extends Record<string, any>>(
  componentFactory: (props: P) => ReactElement; options: {
    cacheSize?: number;
    compareProps?: 'shallow' | 'deep' | ((prev: P, next: P) => boolean);
  } = {}
): ComponentType<P> {
  const { cacheSize = 50, compareProps = 'shallow' } = options;
  const cache = new Map<string, ReactElement>();
  
  return memo((props: P) => {
    const cacheKey = JSON.stringify(props);
    
    // Check cache first
    if (cache.has(cacheKey)) {
      return cache.get(cacheKey)!;
    }
    
    // Create new component
    const component = componentFactory(props);
    
    // Store in cache
    cache.set(cacheKey, component);
    
    // Cleanup cache if too large
    if (cache.size > cacheSize) {
      const firstKey = cache.keys().next().value;
      cache.delete(firstKey);
    }
    
    return component;
  }, compareProps === 'deep' ? deepEqual : shallowEqual);
}

// Performance measurement hook
export function usePerformanceMeasure(name: string, enabled = true) {
  const startTimeRef = useRef<number>();
  
  useEffect(() => {
    if (!enabled) return;
    
    startTimeRef.current = performance.now();
    
    return () => {
      if (startTimeRef.current) {
        const duration = performance.now() - startTimeRef.current;
        if (import.meta.env.DEV) {
          console.log(`🔍 ${name}: ${duration.toFixed(2)}ms`);
        }
      }
    };
  });
}

// Render optimization hook that skips renders when data hasn't changed
export function useRenderOptimization<T>(
  data: T;
  shouldUpdate?: (prev: T, next: T) => boolean
): T {
  const dataRef = useRef(data);
  const shouldUpdateFn = shouldUpdate || shallowEqual;
  
  if (!shouldUpdateFn(dataRef.current, data)) {
    dataRef.current = data;
  }
  
  return dataRef.current;
}

// Batch state updates hook
export function useBatchedState<T>(
  initialState: T, batchDelay: number = 16
): [T, (updater: (prev: T) => T) => void, () => void] {
  const [state, setState] = useState(initialState);
  const pendingUpdatesRef = useRef<((prev: T) => T)[]>([]);
  const timeoutRef = useRef<NodeJS.Timeout>();
  
  const flushUpdates = useCallback(() => {
    if (pendingUpdatesRef.current.length === 0) return;
    
    setState(prevState => {
      let newState = prevState;
      for (const updater of pendingUpdatesRef.current) {
        newState = updater(newState);
      }
      return newState;
    });
    
    pendingUpdatesRef.current = [];
    timeoutRef.current = undefined;
  }, []);
  
  const batchUpdate = useCallback((updater: (prev: T) => T) => {
    pendingUpdatesRef.current.push(updater);
    
    if (!timeoutRef.current) {
      timeoutRef.current = setTimeout(flushUpdates, batchDelay);
    }
  }, [flushUpdates, batchDelay]);
  
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);
  
  return [state, batchUpdate, flushUpdates];
}

// Component render count tracker (development only)
export function useRenderTracker(componentName: string) {
  const renderCountRef = useRef(0);
  const lastRenderRef = useRef(Date.now());
  
  renderCountRef.current += 1;
  const now = Date.now();
  const timeSinceLastRender = now - lastRenderRef.current;
  lastRenderRef.current = now;
  
  if (import.meta.env.DEV) {
    console.log(
      `🔄 ${componentName} render #${renderCountRef.current} (${timeSinceLastRender}ms since last)`
    );
    
    // Warn about rapid re-renders
    if (timeSinceLastRender < 16 && renderCountRef.current > 1) {
      console.warn(`⚠️ ${componentName} is re-rendering rapidly!`);
    }
  }
}

// Export memoization utilities
export const memoizationUtils={smartMemo,
  useCachedSelector,
  useStableCallback,
  useDeepMemo,
  useDebouncedState,
  createMemoizedComponent,
  usePerformanceMeasure,
  useRenderOptimization,
  useBatchedState,
  useRenderTracker,
  deepEqual}, shallowEqual
};

export default memoizationUtils;