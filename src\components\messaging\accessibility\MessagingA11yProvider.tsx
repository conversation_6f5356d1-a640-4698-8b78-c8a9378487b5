/**
 * Messaging Accessibility Provider
 * Comprehensive accessibility enhancements for messaging components
 */

import React, { createContext, useContext, useState, useCallback, useRef, useEffect } from 'react';
import { messagingUtils } from '../utils';
import { DEFAULT_FUNCTIONS, KEYBOARD_SHORTCUTS } from '../constants';
import type { AdvancedMessage, Conversation } from '@/types/messaging';

interface A11ySettings {
  screenReaderEnabled: boolean, highContrastMode: boolean, reducedMotion: boolean, keyboardNavigationEnabled: boolean, announceMessages: boolean, announceTyping: boolean, announcePresence: boolean, messageGrouping: boolean, skipRepeatedMessages: boolean, verboseDescriptions: boolean;
}

interface MessageA11yInfo {
  messageId: string, ariaLabel: string, ariaDescription: string, roleDescription: string, position: string, hasReactions: boolean, hasThread: boolean, isOwn: boolean, isEdited: boolean, isDeleted: boolean;
}

interface MessagingA11yContextType {
  settings: A11ySettings, updateSettings: (settings: Partial<A11ySettings>) => void; announceMessage: (message: string, priority?: 'polite' | 'assertive') => void;
  announceTyping: (users: string[], getUserName?: (id: string) => string) => void; announcePresence: (userId: string, status: string, getUserName?: (id: string) => string) => void; getMessageA11yInfo: (message: AdvancedMessage, index: number, total: number) => MessageA11yInfo; getFocusableElements: () => HTMLElement[]; focusMessage: (messageId: string) => void; focusNextMessage: () => void; focusPreviousMessage: () => void; focusMessageInput: () => void; getKeyboardShortcuts: () => Array<{ key: string, description: string, action: string }>;
}

const MessagingA11yContext = createContext<MessagingA11yContextType | null>(null);

interface MessagingA11yProviderProps {
  children: React.ReactNode;
  initialSettings?: Partial<A11ySettings>;
}

export const MessagingA11yProvider: React.FC<MessagingA11yProviderProps> = ({
  children,
  initialSettings = {}
}) => {
  // Default accessibility settings
  const [settings, setSettings] = useState<A11ySettings>({
    screenReaderEnabled: false,
    highContrastMode: false,
    reducedMotion: false,
    keyboardNavigationEnabled: true,
    announceMessages: true,
    announceTyping: true,
    announcePresence: true,
    messageGrouping: true,
    skipRepeatedMessages: false,
    verboseDescriptions: false,
    ...initialSettings
  });

  const liveRegionRef = useRef<HTMLDivElement | null>(null);
  const assertiveRegionRef = useRef<HTMLDivElement | null>(null);
  const lastAnnouncementRef = useRef<string>('');
  const lastAnnouncementTimeRef = useRef<number>(0);
  const focusedMessageRef = useRef<string | null>(null);

  // Detect accessibility preferences
  useEffect(() => {
    // Detect screen reader
    const detectScreenReader = () => {
      // Check for common screen reader indicators
      const hasScreenReader = 
        'speechSynthesis' in window ||
        navigator.userAgent.includes('NVDA') ||
        navigator.userAgent.includes('JAWS') ||
        navigator.userAgent.includes('VoiceOver') ||
        window.navigator.userAgent.includes('ChromeVox');
      
      if (hasScreenReader !== settings.screenReaderEnabled) {
        setSettings(prev => ({ ...prev, screenReaderEnabled: hasScreenReader }));
      }
    };

    // Detect reduced motion preference
    const detectReducedMotion = () => {
      const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
      if (prefersReducedMotion !== settings.reducedMotion) {
        setSettings(prev => ({ ...prev, reducedMotion: prefersReducedMotion }));
      }
    };

    // Detect high contrast preference
    const detectHighContrast = () => {
      const prefersHighContrast = window.matchMedia('(prefers-contrast: high)').matches;
      if (prefersHighContrast !== settings.highContrastMode) {
        setSettings(prev => ({ ...prev, highContrastMode: prefersHighContrast }));
      }
    };

    detectScreenReader();
    detectReducedMotion();
    detectHighContrast();

    // Listen for preference changes
    const reducedMotionQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    const highContrastQuery = window.matchMedia('(prefers-contrast: high)');

    const handleReducedMotionChange = (e: MediaQueryListEvent) => {
      setSettings(prev => ({ ...prev, reducedMotion: e.matches }));
    };

    const handleHighContrastChange = (e: MediaQueryListEvent) => {
      setSettings(prev => ({ ...prev, highContrastMode: e.matches }));
    };

    reducedMotionQuery.addEventListener('change', handleReducedMotionChange);
    highContrastQuery.addEventListener('change', handleHighContrastChange);

    return () => {
      reducedMotionQuery.removeEventListener('change', handleReducedMotionChange);
      highContrastQuery.removeEventListener('change', handleHighContrastChange);
    };
  }, []);

  // Create live regions for announcements
  useEffect(() => {
    if (!liveRegionRef.current) {
      const liveRegion = document.createElement('div');
      liveRegion.setAttribute('aria-live', 'polite');
      liveRegion.setAttribute('aria-atomic', 'true');
      liveRegion.className = 'sr-only';
      liveRegion.id = 'messaging-live-region';
      document.body.appendChild(liveRegion);
      liveRegionRef.current = liveRegion;
    }

    if (!assertiveRegionRef.current) {
      const assertiveRegion = document.createElement('div');
      assertiveRegion.setAttribute('aria-live', 'assertive');
      assertiveRegion.setAttribute('aria-atomic', 'true');
      assertiveRegion.className = 'sr-only';
      assertiveRegion.id = 'messaging-assertive-region';
      document.body.appendChild(assertiveRegion);
      assertiveRegionRef.current = assertiveRegion;
    }

    return () => {
      if (liveRegionRef.current) {
        document.body.removeChild(liveRegionRef.current);
        liveRegionRef.current = null;
      }
      if (assertiveRegionRef.current) {
        document.body.removeChild(assertiveRegionRef.current);
        assertiveRegionRef.current = null;
      }
    };
  }, []);

  // Update settings
  const updateSettings = useCallback((newSettings: Partial<A11ySettings>) => {
    setSettings(prev => ({ ...prev, ...newSettings }));
  }, []);

  // Announce message to screen readers
  const announceMessage = useCallback((
    message: string, 
    priority: 'polite' | 'assertive' = 'polite'
  ) => {
    if (!settings.announceMessages || !message) return;

    // Prevent repeated announcements
    const now = Date.now();
    if (message === lastAnnouncementRef.current && now - lastAnnouncementTimeRef.current < 1000) {
      return;
    }

    lastAnnouncementRef.current = message;
    lastAnnouncementTimeRef.current = now;

    const targetRegion = priority === 'assertive' ? assertiveRegionRef.current : liveRegionRef.current;
    if (targetRegion) {
      // Clear and then set to ensure announcement
      targetRegion.textContent = '';
      setTimeout(() => {
        if (targetRegion) {
          targetRegion.textContent = message;
        }
      }, 100);
    }
  }, [settings.announceMessages]);

  // Announce typing indicators
  const announceTyping = useCallback((
    users: string[], 
    getUserName = DEFAULT_FUNCTIONS.getUserName
  ) => {
    if (!settings.announceTyping || users.length === 0) return;

    let message: string;
    if (users.length === 1) {
      message = `${getUserName(users[0])} is typing`;
    } else if (users.length === 2) {
      message = `${getUserName(users[0])} and ${getUserName(users[1])} are typing`;
    } else {
      message = `${users.length} people are typing`;
    }

    announceMessage(message, 'polite');
  }, [settings.announceTyping, announceMessage]);

  // Announce presence changes
  const announcePresence = useCallback((
    userId: string,
    status: string,
    getUserName = DEFAULT_FUNCTIONS.getUserName
  ) => {
    if (!settings.announcePresence) return;

    const userName = getUserName(userId);
    const message = `${userName} is now ${status}`;
    announceMessage(message, 'polite');
  }, [settings.announcePresence, announceMessage]);

  // Get accessibility information for a message
  const getMessageA11yInfo = useCallback((
    message: AdvancedMessage,
    index: number,
    total: number
  ): MessageA11yInfo => {
    const isOwn = message.senderId === 'current-user'; // This would come from context
    const hasReactions = message.reactions.length > 0;
    const hasThread = !!message.threadId;
    const isEdited = !!message.editedAt;
    const isDeleted = !!message.deletedAt;

    // Generate position description
    const position = `Message ${index + 1} of ${total}`;

    // Generate accessible label
    let ariaLabel = messagingUtils.getMessageAriaLabel(message, DEFAULT_FUNCTIONS.getUserName);
    
    if (settings.verboseDescriptions) {
      if (hasReactions) {
        const totalReactions = messagingUtils.getTotalReactions(message.reactions);
        ariaLabel += `, ${totalReactions} reactions`;
      }
      if (hasThread) {
        ariaLabel += ', has thread';
      }
      if (isEdited) {
        ariaLabel += ', edited';
      }
    }

    // Generate detailed description
    let ariaDescription = '';
    if (settings.verboseDescriptions) {
      const parts: string[] = [];
      
      if (message.type !== 'text') {
        parts.push(`${message.type} message`);
      }
      
      if (message.mentions.length > 0) {
        parts.push(`mentions ${message.mentions.length} users`);
      }
      
      if (message.attachments.length > 0) {
        parts.push(`has ${message.attachments.length} attachments`);
      }, ariaDescription = parts.join(', ');
    }

    // Role description based on message type and state
    let roleDescription = 'message';
    if (isOwn) {
      roleDescription = 'sent message';
    } else {
      roleDescription = 'received message';
    }
    
    if (isDeleted) {
      roleDescription = 'deleted ' + roleDescription;
    }

    return {
      messageId: message.id,
      ariaLabel,
      ariaDescription,
      roleDescription,
      position,
      hasReactions,
      hasThread,
      isOwn,
      isEdited,
      isDeleted
    };
  }, [settings.verboseDescriptions]);

  // Focus management
  const getFocusableElements = useCallback((): HTMLElement[] => {
    const focusableSelectors = [
      'button:not([disabled])',
      '[href]',
      'input:not([disabled])',
      'select:not([disabled])',
      'textarea:not([disabled])',
      '[tabindex]:not([tabindex="-1"])',
      '[role="button"]:not([disabled])',
      '[role="link"]:not([disabled])',
      '.message-bubble[tabindex="0"]'
    ].join(', ');

    return Array.from(document.querySelectorAll(focusableSelectors));
  }, []);

  const focusMessage = useCallback((messageId: string) => {
    const messageElement = document.querySelector(`[data-message-id="${messageId}"]`) as HTMLElement;
    if (messageElement) {
      messageElement.focus();
      focusedMessageRef.current = messageId;
      
      // Announce focus change
      const messageInfo = messageElement.getAttribute('aria-label');
      if (messageInfo) {
        announceMessage(`Focused on ${messageInfo}`, 'polite');
      }
    }
  }, [announceMessage]);

  const focusNextMessage = useCallback(() => {
    const focusableElements = getFocusableElements();
    const messageElements = focusableElements.filter(el => 
      el.classList.contains('message-bubble') || el.hasAttribute('data-message-id')
    );
    
    const currentIndex = messageElements.findIndex(el => el === document.activeElement);
    const nextIndex = currentIndex < messageElements.length - 1 ? currentIndex + 1 : 0;
    
    if (messageElements[nextIndex]) {
      (messageElements[nextIndex] as HTMLElement).focus();
    }
  }, [getFocusableElements]);

  const focusPreviousMessage = useCallback(() => {
    const focusableElements = getFocusableElements();
    const messageElements = focusableElements.filter(el => 
      el.classList.contains('message-bubble') || el.hasAttribute('data-message-id')
    );
    
    const currentIndex = messageElements.findIndex(el => el === document.activeElement);
    const previousIndex = currentIndex > 0 ? currentIndex - 1 : messageElements.length - 1;
    
    if (messageElements[previousIndex]) {
      (messageElements[previousIndex] as HTMLElement).focus();
    }
  }, [getFocusableElements]);

  const focusMessageInput = useCallback(() => {
    const messageInput = document.querySelector('[data-message-input="true"], .message-input input, textarea[placeholder*="message"]') as HTMLElement;
    if (messageInput) {
      messageInput.focus();
      announceMessage('Focused on message input', 'polite');
    }
  }, [announceMessage]);

  // Get available keyboard shortcuts
  const getKeyboardShortcuts = useCallback(() => {
    return [
      {
        key: KEYBOARD_SHORTCUTS.SEND_MESSAGE,
        description: 'Send message',
        action: 'send-message'
      },
      {
        key: KEYBOARD_SHORTCUTS.NEW_LINE,
        description: 'New line',
        action: 'new-line'
      },
      {
        key: KEYBOARD_SHORTCUTS.ESCAPE,
        description: 'Close modal or clear selection',
        action: 'escape'
      },
      {
        key: KEYBOARD_SHORTCUTS.ARROW_UP,
        description: 'Navigate to previous message',
        action: 'previous-message'
      },
      {
        key: KEYBOARD_SHORTCUTS.ARROW_DOWN,
        description: 'Navigate to next message',
        action: 'next-message'
      },
      {
        key: 'Ctrl+/',
        description: 'Show keyboard shortcuts',
        action: 'show-shortcuts'
      },
      {
        key: 'Ctrl+F',
        description: 'Search messages',
        action: 'search'
      },
      {
        key: 'Ctrl+E',
        description: 'Toggle emoji picker',
        action: 'toggle-emoji'
      }
    ];
  }, []);

  // Apply accessibility styles to document
  useEffect(() => {
    const root = document.documentElement;
    
    if (settings.highContrastMode) {
      root.classList.add('high-contrast');
    } else {
      root.classList.remove('high-contrast');
    }
    
    if (settings.reducedMotion) {
      root.classList.add('reduce-motion');
    } else {
      root.classList.remove('reduce-motion');
    }
  }, [settings.highContrastMode, settings.reducedMotion]);

  const contextValue: MessagingA11yContextType = {
    settings,
    updateSettings,
    announceMessage,
    announceTyping,
    announcePresence,
    getMessageA11yInfo,
    getFocusableElements,
    focusMessage,
    focusNextMessage,
    focusPreviousMessage,
    focusMessageInput,
    getKeyboardShortcuts
  };

  return (
    <MessagingA11yContext.Provider value={contextValue}>
      {children}
    </MessagingA11yContext.Provider>
  );
};

// Hook to use messaging accessibility context
export const useMessagingA11y = (): MessagingA11yContextType => {
  const context = useContext(MessagingA11yContext);
  if (!context) {
    throw new Error('useMessagingA11y must be used within MessagingA11yProvider');
  }
  return context;
};

// HOC for adding accessibility to messaging components
export function withMessagingA11y<P extends object>(
  Component: React.ComponentType<P>
) {
  const WrappedComponent = React.forwardRef<any, P>((props, ref) => {
    const a11y = useMessagingA11y();
    
    return (
      <Component
        {...props}, ref={ref}
        // Inject accessibility props
        {...{
          'data-a11y-enabled': true,
          'data-reduced-motion': a11y.settings.reducedMotion,
          'data-high-contrast': a11y.settings.highContrastMode,
          'aria-live': a11y.settings.announceMessages ? 'polite' : undefined
        }}
      />
    );
  });
  
  WrappedComponent.displayName = `withMessagingA11y(${Component.displayName || Component.name})`;
  return WrappedComponent;
}

export default MessagingA11yProvider;