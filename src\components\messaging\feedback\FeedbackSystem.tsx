import React, { useState, useEffect, useCallback, createContext, useContext } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  CheckCircle, 
  AlertCircle, 
  AlertTriangle, 
  Info, 
  X, 
  Wifi, 
  WifiOff,
  Upload,
  Download,
  MessageSquare
} from 'lucide-react';

// Feedback types
export type FeedbackType = 'success' | 'error' | 'warning' | 'info' | 'loading';

export interface FeedbackMessage {
  id: string, type: FeedbackType, title: string;
  message?: string;
  duration?: number;
  persistent?: boolean;
  action?: {
    label: string, onClick: () => void;
  };
  progress?: number;
  timestamp: number;
}

// Context for feedback system
interface FeedbackContextType {
  messages: FeedbackMessage[], showFeedback: (feedback: Omit<FeedbackMessage, 'id' | 'timestamp'>) => string;
  hideFeedback: (id: string) => void; clearAll: () => void; showSuccess: (title: string, message?: string, duration?: number) => string;
  showError: (title: string, message?: string, persistent?: boolean) => string;
  showWarning: (title: string, message?: string, duration?: number) => string;
  showInfo: (title: string, message?: string, duration?: number) => string;
  showLoading: (title: string, message?: string) => string;
  updateProgress: (id: string, progress: number) => void;
}

const FeedbackContext = createContext<FeedbackContextType | null>(null);

export const useFeedback = () => {
  const context = useContext(FeedbackContext);
  if (!context) {
    throw new Error('useFeedback must be used within a FeedbackProvider');
  }
  return context;
};

// Individual feedback item component
const FeedbackItem: React.FC<{
  feedback: FeedbackMessage, onClose: (id: string) => void;
}> = ({ feedback, onClose }) => {
  const [isVisible, setIsVisible] = useState(true);

  const getIcon = () => {
    switch (feedback.type) {
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'error':
        return <AlertCircle className="w-5 h-5 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="w-5 h-5 text-yellow-500" />;
      case 'info':
        return <Info className="w-5 h-5 text-blue-500" />;
      case 'loading':
        return (
          <motion.div
            className="w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full"
            animate={{ rotate: 360 }}, transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
          />
        );
      default:
        return <Info className="w-5 h-5 text-gray-500" />;
    }
  };

  const getBackgroundColor = () => {
    switch (feedback.type) {
      case 'success':
        return 'bg-green-50 border-green-200';
      case 'error':
        return 'bg-red-50 border-red-200';
      case 'warning':
        return 'bg-yellow-50 border-yellow-200';
      case 'info':
        return 'bg-blue-50 border-blue-200';
      case 'loading':
        return 'bg-blue-50 border-blue-200';
      default:
        return 'bg-gray-50 border-gray-200';
    }
  };

  const handleClose = () => {
    setIsVisible(false);
    setTimeout(() => onClose(feedback.id); 200);
  };

  useEffect(() => {
    if (feedback.duration && feedback.duration > 0 && !feedback.persistent) {
      const timer = setTimeout(handleClose, feedback.duration);
      return () => clearTimeout(timer);
    }
  }, [feedback.duration, feedback.persistent]);

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, y: -50, scale: 0.95 }}, animate={{ opacity: 1, y: 0, scale: 1 }}, exit={{ opacity: 0, y: -20, scale: 0.95 }}, transition={{ type: 'spring', stiffness: 400, damping: 25 }}, className={`feedback-item ${getBackgroundColor()}`}
        >
          <div className="feedback-content">
            <div className="feedback-icon">
              {getIcon()}
            </div>
            
            <div className="feedback-text">
              <h4 className="feedback-title">{feedback.title}</h4>
              {feedback.message && (
                <p className="feedback-message">{feedback.message}</p>
              )}
              
              {/* Progress bar for loading states */}
              {feedback.type === 'loading' && typeof feedback.progress === 'number' && (
                <div className="feedback-progress">
                  <div className="progress-bar-container">
                    <motion.div
                      className="progress-bar"
                      initial={{ width: 0 }}, animate={{ width: `${feedback.progress}%` }}, transition={{ duration: 0.3 }}
                    />
    </div>
                  <span className="progress-text">{Math.round(feedback.progress)}%</span>
    </div>
              )}
            </div>

            {/* Action button */}
            {feedback.action && (
              <button
                onClick={feedback.action.onClick} className="feedback-action"
              >
                {feedback.action.label}
              </button>
            )}

            {/* Close button */}
            {!feedback.persistent && (
              <button
                onClick={handleClose} className="feedback-close"
                aria-label="Close notification"
              >
                <X className="w-4 h-4" />
    </button>
            )}
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

// Connection status indicator
const ConnectionStatus: React.FC<{ isConnected: boolean }> = ({ isConnected }) => {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}, animate={{ opacity: 1, scale: 1 }}, className={`connection-status ${isConnected ? 'connected' : 'disconnected'}`}
    >
      {isConnected ? (
        <Wifi className="w-4 h-4 text-green-500" />
      ) : (
        <WifiOff className="w-4 h-4 text-red-500" />
      )}
      <span className="connection-text">
        {isConnected ? 'Connected' : 'Disconnected'}
      </span>
    </motion.div>
  );
};

// Main feedback provider component
export const FeedbackProvider: React.FC<{
  children: React.ReactNode;
  maxMessages?: number;
  defaultDuration?: number;
}> = ({ 
  children, 
  maxMessages = 5,
  defaultDuration = 4000 
}) => {
  const [messages, setMessages] = useState<FeedbackMessage[]>([]);
  const [isConnected, setIsConnected] = useState(navigator.onLine);

  // Monitor connection status
  useEffect(() => {
    const handleOnline = () => setIsConnected(true);
    const handleOffline = () => setIsConnected(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const generateId = () => `feedback_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

  const showFeedback = useCallback((feedback: Omit<FeedbackMessage, 'id' | 'timestamp'>) => {
    const id = generateId();
    const newMessage: FeedbackMessage = {
      ...feedback,
      id,
      timestamp: Date.now(),
      duration: feedback.duration ?? defaultDuration
    };

    setMessages(prev => {
      const updated = [newMessage, ...prev];
      // Keep only the most recent messages
      return updated.slice(0, maxMessages);
    });

    return id;
  }, [defaultDuration, maxMessages]);

  const hideFeedback = useCallback((id: string) => {
    setMessages(prev => prev.filter(msg => msg.id !== id));
  }, []);

  const clearAll = useCallback(() => {
    setMessages([]);
  }, []);

  const showSuccess = useCallback((title: string, message?: string, duration?: number) => {
    return showFeedback({ type: 'success', title, message, duration });
  }, [showFeedback]);

  const showError = useCallback((title: string, message?: string, persistent?: boolean) => {
    return showFeedback({ type: 'error', title, message, persistent });
  }, [showFeedback]);

  const showWarning = useCallback((title: string, message?: string, duration?: number) => {
    return showFeedback({ type: 'warning', title, message, duration });
  }, [showFeedback]);

  const showInfo = useCallback((title: string, message?: string, duration?: number) => {
    return showFeedback({ type: 'info', title, message, duration });
  }, [showFeedback]);

  const showLoading = useCallback((title: string, message?: string) => {
    return showFeedback({ type: 'loading', title, message, persistent: true });
  }, [showFeedback]);

  const updateProgress = useCallback((id: string, progress: number) => {
    setMessages(prev => prev.map(msg => 
      msg.id === id ? { ...msg, progress } : msg
    ));
  }, []);

  const contextValue: FeedbackContextType = {
    messages,
    showFeedback,
    hideFeedback,
    clearAll,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    showLoading,
    updateProgress
  };

  return (
    <FeedbackContext.Provider value={contextValue}>
      {children}
      
      {/* Feedback container */}
      <div className="feedback-container">
        <AnimatePresence>
          {messages.map(message => (
            <FeedbackItem
              key={message.id} feedback={message}, onClose={hideFeedback}
            />
          ))}
        </AnimatePresence>
    </div>
      {/* Connection status */}
      <div className="connection-status-container">
        <ConnectionStatus isConnected={isConnected} />
    </div>
    </FeedbackContext.Provider>
  );
};

// Messaging-specific feedback hooks
export const useMessagingFeedback = () => {
  const feedback = useFeedback();

  return {
    ...feedback,
    
    // Message-specific feedback
    showMessageSent: (recipientName?: string) => {
      return feedback.showSuccess(
        'Message sent',
        recipientName ? `Message sent to ${recipientName}` : undefined,
        2000
      );
    },

    showMessageFailed: (error?: string) => {
      return feedback.showError(
        'Failed to send message',
        error || 'Please try again',
        true
      );
    }, showMessageDelivered: (recipientName?: string) => {
      return feedback.showInfo(
        'Message delivered',
        recipientName ? `Delivered to ${recipientName}` : undefined,
        2000
      );
    },

    showTyping: (userName: string) => {
      return feedback.showInfo(
        `${userName} is typing...`,
        undefined,
        3000
      );
    },

    showFileUploading: (fileName: string) => {
      return feedback.showLoading(
        'Uploading file',
        fileName
      );
    }, showFileUploaded: (fileName: string) => {
      return feedback.showSuccess(
        'File uploaded',
        fileName,
        3000
      );
    },

    showFileUploadFailed: (fileName: string, error?: string) => {
      return feedback.showError(
        'Upload failed',
        `${fileName}: ${error || 'Please try again'}`,
        true
      );
    },

    showReactionAdded: (emoji: string, userName?: string) => {
      return feedback.showSuccess(
        'Reaction added',
        userName ? `${userName} reacted with ${emoji}` : `Reacted with ${emoji}`,
        2000
      );
    },

    showConnectionLost: () => {
      return feedback.showWarning(
        'Connection lost',
        'Trying to reconnect...',
        0
      );
    },

    showConnectionRestored: () => {
      return feedback.showSuccess(
        'Connection restored',
        'You\'re back online',
        3000
      );
    },

    showNewMessage: (senderName: string, preview: string) => {
      return feedback.showInfo(
        `New message from ${senderName}`,
        preview.length > 50 ? `${preview.substring(0, 50)}...` : preview,
        5000
      );
    }
  };
};

export default FeedbackProvider;