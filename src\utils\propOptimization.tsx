import React, { createContext, useContext, useMemo, memo } from 'react';

// Prop injection system to reduce prop drilling
export function createPropProvider<T>() {
  const Context = createContext<T | null>(null);
  
  const Provider = memo(_({ value, children }: { value: T, children: React.ReactNode }) => {
    const memoizedValue = useMemo(() => value, [value]);
    return <Context.Provider value={memoizedValue}>{children}</Context.Provider>;
  });
  
  const useProp = () => {
    const context = useContext(Context);
    if (context === null) {
      throw new Error('useProp must be used within a Provider');
    }
    return context;
  };
  
  return { Provider, useProp };
}

// HOC to inject common props automatically
export function withCommonProps<P extends object>(
  Component: React.ComponentType<P>
) {
  const WrappedComponent = memo((props: P) => {
    // Auto-inject commonly used props from context if available
    const enhancedProps = useMemo(() => {
      // Add any common prop injection logic here
      return props;
    }, [props]);
    
    return <Component {...enhancedProps} />;
  });
  
  WrappedComponent.displayName = `withCommonProps(${Component.displayName || Component.name})`;
  return WrappedComponent;
}

// Render props optimization
export function createRenderPropsComponent<T>(
  name: string, useData: () => T
) {
  const Component = memo(({ 
    children 
  }: { 
    children: (data: T) => React.ReactNode;
  }) => {
    const data = useData();
    const memoizedChildren = useMemo(() => children(data), [children, data]);
    return <>{memoizedChildren}</>;
  });
  
  Component.displayName = name;
  return Component;
}

export const _propOptimization={createPropProvider,
  withCommonProps,
  createRenderPropsComponent}
};