import React, { useState, useEffect } from 'react';
import { 
  PhoneOff, Video, VideoOff, Mic, MicOff, Volume2,
  Minimize2, Maximize2, MessageSquare, User, Users
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback } from '@/components/ui';
import { Badge } from '@/components/ui/badge';
import { motion } from 'framer-motion';
import { toast } from 'sonner';
import { CallData } from '@/types/enhanced-messaging';

interface CallInterfaceProps {
  call: CallData, onEndCall: () => Promise<void>;
}

const CallInterface: React.FC<CallInterfaceProps> = ({
  call,
  onEndCall
}) => {
  // Local state for call controls
  const [isVideoEnabled, setIsVideoEnabled] = useState(call.type === 'video');
  const [isMicEnabled, setIsMicEnabled] = useState(true);
  const [isSpeakerEnabled, setIsSpeakerEnabled] = useState(true);
  const [isRecording, setIsRecording] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [callDuration, setCallDuration] = useState(0);

  // Simulate call duration timer
  useEffect(() => {
    if (call.status === 'active') {
      const interval = setInterval(() => {
        setCallDuration(prev => prev + 1);
      }, 1000);
      
      return () => clearInterval(interval);
    }
  }, [call.status]);

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleEndCall = async () => {
    try {
      await onEndCall();
      toast.success('Call ended');
    } catch {
      toast.error('Failed to end call');
    }
  };

  const toggleVideo = () => {
    setIsVideoEnabled(!isVideoEnabled);
    toast.info(isVideoEnabled ? 'Video disabled' : 'Video enabled');
  };

  const toggleMic = () => {
    setIsMicEnabled(!isMicEnabled);
    toast.info(isMicEnabled ? 'Microphone muted' : 'Microphone unmuted');
  };

  const toggleSpeaker = () => {
    setIsSpeakerEnabled(!isSpeakerEnabled);
    toast.info(isSpeakerEnabled ? 'Speaker disabled' : 'Speaker enabled');
  };

  const toggleRecording = () => {
    if (isRecording) {
      setIsRecording(false);
      toast.success('Recording stopped');
    } else {
      setIsRecording(true);
      toast.success('Recording started');
    }
  };

  if (!call) return null;

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}, animate={{ opacity: 1, scale: 1 }}, exit={{ opacity: 0, scale: 0.9 }}, className={`fixed inset-0 bg-black z-50 flex flex-col ${
        isMinimized ? 'bottom-4 right-4 w-80 h-60 inset-auto rounded-lg' : ''
      }`}
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 bg-gray-900/50 backdrop-blur-sm">
        <div className="flex items-center gap-3">
          <Avatar className="w-10 h-10">
            <AvatarFallback>U</AvatarFallback>
    </Avatar>
          <div>
            <h2 className="font-semibold text-white">Call</h2>
            <p className="text-sm text-gray-300">
              {call.status === 'ringing' ? 'Calling...' : formatDuration(callDuration)}
            </p>
    </div>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsMinimized(!isMinimized)} className="text-white hover:bg-white/10"
          >
            {isMinimized ? <Maximize2 className="w-4 h-4" /> : <Minimize2 className="w-4 h-4" />}
          </Button>
    </div>
      </div>

      {/* Video Area */}
      {call.type === 'video' && isVideoEnabled && !isMinimized ? (
        <div className="flex-1 relative bg-gray-900">
          {/* Remote video */}
          <div className="w-full h-full bg-gray-800 flex items-center justify-center">
            <div className="text-white text-center">
              <Avatar className="w-32 h-32 mx-auto mb-4">
                <AvatarFallback className="text-4xl">U</AvatarFallback>
    </Avatar>
              <p className="text-lg">Waiting for video...</p>
    </div>
          </div>

          {/* Local video (picture-in-picture) */}
          <div className="absolute top-4 right-4 w-32 h-24 bg-gray-700 rounded-lg overflow-hidden">
            <div className="w-full h-full bg-gray-600 flex items-center justify-center">
              <User className="w-8 h-8 text-gray-400" />
    </div>
          </div>
    </div>
      ) : (
        <div className="flex-1 bg-gradient-to-br from-blue-900 to-purple-900 flex items-center justify-center">
          <div className="text-center text-white">
            <Avatar className="w-32 h-32 mx-auto mb-4">
              <AvatarFallback className="text-4xl">U</AvatarFallback>
    </Avatar>
            <h2 className="text-2xl font-semibold mb-2">Voice Call</h2>
            <p className="text-lg opacity-80">
              {call.status === 'ringing' ? 'Calling...' : formatDuration(callDuration)}
            </p>
    </div>
        </div>
      )}

      {/* Call Controls */}
      {!isMinimized && (
        <div className="p-6 bg-gray-900/50 backdrop-blur-sm">
          <div className="flex items-center justify-center gap-4">
            {/* Video toggle */}
            {call.type === 'video' && (
              <Button
                variant={isVideoEnabled ? "secondary" : "destructive"} size="lg"
                onClick={toggleVideo} className="rounded-full w-14 h-14"
              >
                {isVideoEnabled ? <Video className="w-6 h-6" /> : <VideoOff className="w-6 h-6" />}
              </Button>
            )}

            {/* Mic toggle */}
            <Button
              variant={isMicEnabled ? "secondary" : "destructive"} size="lg"
              onClick={toggleMic} className="rounded-full w-14 h-14"
            >
              {isMicEnabled ? <Mic className="w-6 h-6" /> : <MicOff className="w-6 h-6" />}
            </Button>

            {/* Speaker toggle */}
            <Button
              variant={isSpeakerEnabled ? "secondary" : "outline"} size="lg"
              onClick={toggleSpeaker} className="rounded-full w-14 h-14"
            >
              {isSpeakerEnabled ? <Volume2 className="w-6 h-6" /> : <Volume2 className="w-6 h-6 opacity-50" />}
            </Button>

            {/* Add participant */}
            <Button
              variant="outline"
              size="lg"
              className="rounded-full w-14 h-14"
            >
              <Users className="w-6 h-6" />
    </Button>
            {/* End call */}
            <Button
              variant="destructive"
              size="lg"
              onClick={handleEndCall} className="rounded-full w-14 h-14 bg-red-600 hover:bg-red-700"
            >
              <PhoneOff className="w-6 h-6" />
    </Button>
          </div>

          {/* Secondary controls */}
          <div className="flex items-center justify-center gap-2 mt-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleRecording} className="text-white hover:bg-white/10"
            >
              {isRecording ? '● Stop Recording' : '○ Start Recording'}
            </Button>

            <Button
              variant="ghost"
              size="sm"
              className="text-white hover:bg-white/10"
            >
              <MessageSquare className="w-4 h-4 mr-2" />
              Chat
            </Button>
    </div>
        </div>
      )}

      {/* Recording indicator */}
      {isRecording && (
        <div className="absolute top-4 left-4">
          <Badge className="bg-red-600 text-white animate-pulse">
            ● Recording
          </Badge>
    </div>
      )}
    </motion.div>
  );
};

export default CallInterface;
