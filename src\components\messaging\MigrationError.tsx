import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>f<PERSON>C<PERSON>, SkipForward, ChevronDown, ChevronUp, Bug, HelpCircle } from 'lucide-react';

interface MigrationErrorProps {
  error: string, onRetry: () => void; onSkip: () => void; canRetry: boolean, canSkip: boolean;
}

const MigrationError: React.FC<MigrationErrorProps> = ({
  error,
  onRetry,
  onSkip,
  canRetry,
  canSkip
}) => {
  const [showDetails, setShowDetails] = useState(false);
  const [isRetrying, setIsRetrying] = useState(false);

  const handleRetry = async () => {
    setIsRetrying(true);
    try {
      await onRetry();
    } finally {
      setIsRetrying(false);
    }
  };

  // Common error messages and their user-friendly explanations
  const getErrorExplanation = (errorMessage: string) => {
    const lowerError = errorMessage.toLowerCase();
    
    if (lowerError.includes('network') || lowerError.includes('connection')) {
      return {
        title: 'Connection Issue',
        description: 'There was a problem connecting to our servers. Please check your internet connection and try again.',
        suggestion: 'Try refreshing the page or checking your network connection.'
      };
    }
    
    if (lowerError.includes('storage') || lowerError.includes('quota')) {
      return {
        title: 'Storage Issue',
        description: 'Your browser storage is full or unavailable. This can happen when storage quota is exceeded.',
        suggestion: 'Try clearing some browser data or freeing up storage space.'
      };
    }
    
    if (lowerError.includes('permission') || lowerError.includes('access')) {
      return {
        title: 'Permission Issue',
        description: 'The migration process needs certain permissions that are currently unavailable.',
        suggestion: 'Try refreshing the page or checking your browser permissions.'
      };
    }
    
    if (lowerError.includes('data') || lowerError.includes('format')) {
      return {
        title: 'Data Format Issue',
        description: 'Some of your existing message data is in an unexpected format.',
        suggestion: 'This is usually temporary. Try the migration again, or skip for now and try later.'
      };
    }
    
    return {
      title: 'Migration Error',
      description: 'An unexpected error occurred during the migration process.',
      suggestion: 'Try the migration again, or skip for now and contact support if the problem persists.'
    };
  };

  const errorInfo = getErrorExplanation(error);

  return (
    <div className="flex h-[calc(100vh-4rem)] bg-gradient-to-br from-red-50 to-orange-100 dark:from-gray-900 dark:to-gray-800">
      <div className="flex items-center justify-center w-full p-4">
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl max-w-2xl w-full overflow-hidden">
          {/* Header */}
          <div className="bg-gradient-to-r from-red-500 to-orange-500 p-8 text-white text-center">
            <div className="mb-4">
              <div className="bg-white/20 p-4 rounded-full inline-flex">
                <AlertTriangle className="w-8 h-8" />
    </div>
            </div>
            <h1 className="text-2xl font-bold mb-2">Migration Encountered an Issue</h1>
            <p className="text-red-100">
              Don't worry - your data is safe and we can try again
            </p>
    </div>
          {/* Error Content */}
          <div className="p-8">
            {/* Error Summary */}
            <div className="mb-6">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                {errorInfo.title}
              </h2>
              <p className="text-gray-600 dark:text-gray-300 mb-4">
                {errorInfo.description}
              </p>
              
              {/* Suggestion */}
              <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                <div className="flex items-start gap-3">
                  <HelpCircle className="w-5 h-5 text-blue-500 flex-shrink-0 mt-0.5" />
                  <div>
                    <h3 className="font-medium text-blue-900 dark:text-blue-100 mb-1">
                      What you can do:
                    </h3>
                    <p className="text-sm text-blue-800 dark:text-blue-200">
                      {errorInfo.suggestion}
                    </p>
    </div>
                </div>
    </div>
            </div>

            {/* Technical Details (Collapsible) */}
            <div className="mb-6">
              <button
                onClick={() => setShowDetails(!showDetails)} className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
              >
                <Bug className="w-4 h-4" />
                Technical Details
                {showDetails ? (
                  <ChevronUp className="w-4 h-4" />
                ) : (
                  <ChevronDown className="w-4 h-4" />
                )}
              </button>
              
              {showDetails && (
                <div className="mt-3 p-4 bg-gray-100 dark:bg-gray-700 rounded-lg">
                  <pre className="text-xs text-gray-700 dark:text-gray-300 whitespace-pre-wrap break-words">
                    {error}
                  </pre>
    </div>
              )}
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4">
              {canRetry && (
                <button
                  onClick={handleRetry} disabled={isRetrying}, className="flex-1 bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-6 py-3 rounded-lg font-semibold hover:from-blue-700 hover:to-indigo-700 transition-all duration-200 shadow-lg hover:shadow-xl flex items-center justify-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isRetrying ? (
                    <>
                      <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      Retrying...
                    </>
                  ) : (
                    <>
                      <RefreshCw className="w-5 h-5" />
                      Try Again
                    </>
                  )}
                </button>
              )}
              
              {canSkip && (
                <button
                  onClick={onSkip} className="flex-1 sm:flex-none bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 px-6 py-3 rounded-lg font-medium hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors flex items-center justify-center gap-2"
                >
                  <SkipForward className="w-5 h-5" />
                  Skip for Now
                </button>
              )}
            </div>

            {/* Safety Assurance */}
            <div className="mt-6 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
              <div className="flex items-start gap-3">
                <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <div className="w-2 h-2 bg-white rounded-full" />
    </div>
                <div>
                  <h3 className="font-medium text-green-800 dark:text-green-200 mb-1">
                    Your Data is Safe
                  </h3>
                  <p className="text-sm text-green-700 dark:text-green-300">
                    We created a backup before starting the migration. Your original conversations 
                    and messages are completely safe and unchanged. You can continue using the 
                    classic messaging system while we work on resolving this issue.
                  </p>
    </div>
              </div>
    </div>
            {/* Help Text */}
            <div className="mt-4 text-center">
              <p className="text-xs text-gray-500 dark:text-gray-400">
                If this problem persists, you can continue using the classic messaging system. 
                We'll notify you when the upgrade is available again.
              </p>
    </div>
          </div>
    </div>
      </div>
    </div>
  );
};

export default MigrationError;