#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

function fixJSXSyntax(content) {
  let modified = false;
  let result = content;
  
  // Fix 1: prop={value}, prop2={value2} -> prop={value} prop2={value2}
  const commaInPropsPattern = /(\w+)=\{([^}]+)\},\s*(\w+)=/g;
  const newResult1 = result.replace(commaInPropsPattern, '$1={$2}\n                      $3=');
  if (newResult1 !== result) {
    result = newResult1;
    modified = true;
  }
  
  // Fix 2: setTimeout(resolve; 2000) -> setTimeout(resolve, 2000)
  const semicolonInFunctionPattern = /setTimeout\(([^,]+);\s*(\d+)\)/g;
  const newResult2 = result.replace(semicolonInFunctionPattern, 'setTimeout($1, $2)');
  if (newResult2 !== result) {
    result = newResult2;
    modified = true;
  }
  
  // Fix 3: resolve; 2000 -> resolve, 2000
  const resolveSemicolonPattern = /resolve\s*;\s*(\d+)/g;
  const newResult3 = result.replace(resolveSemicolonPattern, 'resolve, $1');
  if (newResult3 !== result) {
    result = newResult3;
    modified = true;
  }
  
  // Fix 4: Multiple props on same line without proper spacing
  const multiplePropsPattern = /(\w+)=\{([^}]+)\}\s+(\w+)=\{([^}]+)\}\s+(\w+)=/g;
  const newResult4 = result.replace(multiplePropsPattern, '$1={$2}\n                      $3={$4}\n                      $5=');
  if (newResult4 !== result) {
    result = newResult4;
    modified = true;
  }
  
  // Fix 5: Two props on same line
  const twoPropsPattern = /(\w+)=\{([^}]+)\}\s+(\w+)=/g;
  const newResult5 = result.replace(twoPropsPattern, '$1={$2}\n                      $3=');
  if (newResult5 !== result) {
    result = newResult5;
    modified = true;
  }
  
  return { content: result, modified };
}

function fixFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const { content: fixedContent, modified } = fixJSXSyntax(content);
    
    if (modified) {
      fs.writeFileSync(filePath, fixedContent, 'utf8');
      console.log(`Fixed: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`Error fixing ${filePath}:`, error.message);
    return false;
  }
}

function walkDirectory(dir, extensions = ['.tsx', '.ts', '.jsx', '.js']) {
  const files = [];
  
  function walk(currentDir) {
    try {
      const items = fs.readdirSync(currentDir);
      
      for (const item of items) {
        const fullPath = path.join(currentDir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          walk(fullPath);
        } else if (stat.isFile() && extensions.some(ext => item.endsWith(ext))) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      console.error(`Error reading directory ${currentDir}:`, error.message);
    }
  }
  
  walk(dir);
  return files;
}

function main() {
  const srcDir = path.join(__dirname, '..', 'src');
  const files = walkDirectory(srcDir);
  
  console.log(`Found ${files.length} files to check...`);
  
  let fixedCount = 0;
  
  files.forEach(file => {
    if (fixFile(file)) {
      fixedCount++;
    }
  });
  
  console.log(`\nFixed ${fixedCount} files out of ${files.length} total files.`);
  
  if (fixedCount > 0) {
    console.log('\nRunning TypeScript check to verify fixes...');
  }
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}
