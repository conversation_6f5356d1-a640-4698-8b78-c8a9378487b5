import React, { memo } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Filter, Brain, BarChart3 } from 'lucide-react';

// Enhanced feed filters interface
export interface FeedFilters {
  creator: string, dateRange: string, location: string, liked: boolean, saved: boolean, contentType: 'all' | 'text' | 'image' | 'video', sortBy: 'recent' | 'relevant' | 'top' | 'trending', minLikes: number, minComments: number, onlyVerified: boolean, showTrending: boolean;
}

interface FeedFiltersProps {
  filter: 'all' | 'friends' | 'pages' | 'groups', sortBy: 'recent' | 'relevant' | 'top', onFilterChange: (filter: 'all' | 'friends' | 'pages' | 'groups') => void; onSortChange: (sort: 'recent' | 'relevant' | 'top') => void; showFilters: boolean;
  className?: string;
}

// Default filters to avoid recreating on every render
export const DEFAULT_FILTERS: FeedFilters = {
  creator: '',
  dateRange: 'all',
  location: '',
  liked: false,
  saved: false,
  contentType: 'all',
  sortBy: 'recent',
  minLikes: 0,
  minComments: 0,
  onlyVerified: false,
  showTrending: false
};

const FeedFiltersComponent: React.FC<FeedFiltersProps> = memo(({
  filter,
  sortBy,
  onFilterChange,
  onSortChange,
  showFilters,
  className = ''
}) => {
  if (!showFilters) return null;

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 mb-6 ${className}`}>
      <div className="flex flex-wrap items-center gap-3">
        <div className="flex items-center gap-2">
          <Filter className="w-4 h-4 text-gray-500" />
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Filter:</span>
    </div>
        <div className="flex gap-2">
          {(['all', 'friends', 'pages', 'groups'] as const).map((filterOption) => (
            <Button
              key={filterOption} variant={filter === filterOption ? 'default' : 'outline'}, size="sm"
              onClick={() => onFilterChange(filterOption)} className="capitalize"
            >
              {filterOption}
            </Button>
          ))}
        </div>

        <div className="flex items-center gap-2 ml-4">
          <BarChart3 className="w-4 h-4 text-gray-500" />
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Sort:</span>
    </div>
        <div className="flex gap-2">
          {(['recent', 'relevant', 'top'] as const).map((sortOption) => (
            <Button
              key={sortOption} variant={sortBy === sortOption ? 'default' : 'outline'}, size="sm"
              onClick={() => onSortChange(sortOption)} className="capitalize"
            >
              {sortOption}
            </Button>
          ))}
        </div>

        <Badge variant="secondary" className="ml-auto">
          <Brain className="w-3 h-3 mr-1" />
          AI Enhanced
        </Badge>
    </div>
    </div>
  );
});

FeedFiltersComponent.displayName = 'FeedFilters';

export default FeedFiltersComponent;
