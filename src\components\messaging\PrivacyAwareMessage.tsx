import React, { useState, useEffect } from 'react';
import { <PERSON>, EyeOff, Copy, Forward, Download, MoreHorizontal, Trash2 } from 'lucide-react';
import PrivacyService from '../../services/messaging/PrivacyService';
import { AdvancedMessage } from '../../types/messaging';

interface PrivacyAwareMessageProps {
  message: AdvancedMessage, currentUserId: string;
  onDelete?: (messageId: string) => void;
  onCopy?: (content: string) => void;
  onForward?: (message: AdvancedMessage) => void;
  className?: string;
}

export const PrivacyAwareMessage: React.FC<PrivacyAwareMessageProps> = ({
  message,
  currentUserId,
  onDelete,
  onCopy,
  onForward,
  className = ''
}) => {
  const [showActions, setShowActions] = useState(false);
  const [canSeeReadReceipts, setCanSeeReadReceipts] = useState(false);
  const [privacySettings, setPrivacySettings] = useState<any>(null);
  const [isBlurred, setIsBlurred] = useState(false);

  const privacyService = PrivacyService.getInstance();

  useEffect(() => {
    // Check privacy permissions
    const checkPrivacyPermissions = () => {
      // Check if current user can see read receipts for this message
      const canSeeReceipts = privacyService.canSeeReadReceipts(currentUserId, message.senderId);
      setCanSeeReadReceipts(canSeeReceipts);

      // Get privacy settings for the message sender
      const senderSettings = privacyService.getPrivacySettings(message.senderId);
      setPrivacySettings(senderSettings);

      // Check if message should be blurred (for sensitive content)
      const shouldBlur = message.senderId !== currentUserId && 
                        !privacyService.canSeeOnlineStatus(currentUserId, message.senderId);
      setIsBlurred(shouldBlur);
    };

    checkPrivacyPermissions();
  }, [message, currentUserId, privacyService]);

  const handleCopy = () => {
    if (privacySettings?.messageHistory?.allowCopying !== false) {
      onCopy?.(message.content);
    }
  };

  const handleForward = () => {
    if (privacySettings?.messageHistory?.allowForwarding !== false) {
      onForward?.(message);
    }
  };

  const handleDelete = () => {
    if (message.senderId === currentUserId) {
      const canDelete = privacyService.deleteMessage(currentUserId, message.id, message.conversationId);
      if (canDelete) {
        onDelete?.(message.id);
      }
    }
  };

  const formatTimestamp = (timestamp: number) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else {
      return date.toLocaleDateString();
    }
  };

  const getReadReceiptStatus = () => {
    if (!canSeeReadReceipts || !message.status) {
      return null;
    }

    switch (message.status) {
      case 'sending':
        return { icon: '⏳', text: 'Sending', color: 'text-gray-400' };
      case 'sent':
        return { icon: '✓', text: 'Sent', color: 'text-gray-400' };
      case 'delivered':
        return { icon: '✓✓', text: 'Delivered', color: 'text-gray-400' };
      case 'read':
        return { icon: '✓✓', text: 'Read', color: 'text-blue-500' };
      default:
        return null;
    }
  };

  const readReceiptStatus = getReadReceiptStatus();
  const isOwnMessage = message.senderId === currentUserId;
  const canCopy = privacySettings?.messageHistory?.allowCopying !== false;
  const canForward = privacySettings?.messageHistory?.allowForwarding !== false;

  return (
    <div className={`privacy-aware-message ${className}`}>
      <div
        className={`flex ${isOwnMessage ? 'justify-end' : 'justify-start'}, mb-2`}, onMouseEnter={() => setShowActions(true)} onMouseLeave={() => setShowActions(false)}
      >
        <div className={`max-w-xs lg:max-w-md ${isOwnMessage ? 'order-2' : 'order-1'}`}>
          {/* Message Bubble */}
          <div
            className={`relative px-4 py-2 rounded-lg ${
              isOwnMessage
                ? 'bg-blue-600 text-white'
                : 'bg-gray-200 text-gray-900'
            } ${isBlurred ? 'filter blur-sm' : ''}`}
          >
            {/* Privacy Indicator */}
            {!isOwnMessage && privacySettings && (
              <div className="absolute -top-2 -right-2">
                {privacySettings.messageHistory?.allowScreenshots === false && (
                  <div className="w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
                    <EyeOff className="w-2 h-2 text-white" />
    </div>
                )}
              </div>
            )}

            {/* Message Content */}
            <div className="break-words">
              {message.content}
            </div>

            {/* Message Metadata */}
            <div className={`flex items-center justify-between mt-1 text-xs ${
              isOwnMessage ? 'text-blue-100' : 'text-gray-500'
            }`}>
              <span>{formatTimestamp(message.timestamp)}</span>
              
              {/* Read Receipt Status */}
              {isOwnMessage && readReceiptStatus && (
                <div className={`flex items-center gap-1 ${readReceiptStatus.color}`}>
                  <span>{readReceiptStatus.icon}</span>
                  <span className="hidden sm:inline">{readReceiptStatus.text}</span>
    </div>
              )}
            </div>

            {/* Privacy Warning */}
            {!isOwnMessage && privacySettings?.messageHistory?.allowScreenshots === false && (
              <div className="mt-2 text-xs opacity-75 flex items-center gap-1">
                <EyeOff className="w-3 h-3" />
                <span>Screenshot protected</span>
    </div>
            )}
          </div>

          {/* Message Actions */}
          {showActions && (
            <div className={`flex items-center gap-1 mt-1 ${
              isOwnMessage ? 'justify-end' : 'justify-start'
            }`}>
              {/* Copy Action */}
              {canCopy && (
                <button
                  onClick={handleCopy} className="p-1 rounded hover:bg-gray-100 transition-colors"
                  title="Copy message"
                >
                  <Copy className="w-3 h-3 text-gray-500" />
    </button>
              )}

              {/* Forward Action */}
              {canForward && (
                <button
                  onClick={handleForward} className="p-1 rounded hover:bg-gray-100 transition-colors"
                  title="Forward message"
                >
                  <Forward className="w-3 h-3 text-gray-500" />
    </button>
              )}

              {/* Delete Action (own messages only) */}
              {isOwnMessage && (
                <button
                  onClick={handleDelete} className="p-1 rounded hover:bg-gray-100 transition-colors"
                  title="Delete message"
                >
                  <Trash2 className="w-3 h-3 text-gray-500" />
    </button>
              )}

              {/* More Actions */}
              <button
                className="p-1 rounded hover:bg-gray-100 transition-colors"
                title="More actions"
              >
                <MoreHorizontal className="w-3 h-3 text-gray-500" />
    </button>
            </div>
          )}
        </div>
    </div>
      {/* Privacy Notice */}
      {!isOwnMessage && (
        <div className="text-xs text-gray-400 mb-2">
          {!canCopy && !canForward && (
            <div className="flex items-center gap-1">
              <EyeOff className="w-3 h-3" />
              <span>Message actions restricted by sender</span>
    </div>
          )}
          {!canSeeReadReceipts && (
            <div className="flex items-center gap-1">
              <Eye className="w-3 h-3" />
              <span>Read receipts hidden by sender</span>
    </div>
          )}
        </div>
      )}
    </div>
  );
};

export default PrivacyAwareMessage;