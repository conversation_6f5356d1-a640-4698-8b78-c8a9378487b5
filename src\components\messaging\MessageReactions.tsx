/**
 * MessageReactions Component
 * Facebook-style emoji reactions with smooth animations
 */

import React, { useState, useCallback, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import { COMMON_REACTIONS } from '@/types/messaging';
import type { AdvancedMessage, MessageReaction } from '@/types/messaging';

interface MessageReactionsProps {
  messageId: string, reactions: Record<string, string[]>;
  onAddReaction: (messageId: string, emoji: string) => void; onRemoveReaction: (messageId: string, emoji: string) => void; currentUserId: string;
  className?: string;
  disabled?: boolean;
  showTooltips?: boolean;
  maxVisibleReactions?: number;
  getUserName?: (userId: string) => string;
}

// Animation variants for reactions
const reactionVariants = {
  initial: { 
    scale: 0, 
    opacity: 0,
    y: 10
  },
  animate: { 
    scale: 1, 
    opacity: 1,
    y: 0,
    transition: {
      type: "spring",
      stiffness: 500,
      damping: 30,
      duration: 0.3
    }
  },
  exit: { 
    scale: 0, 
    opacity: 0,
    y: -10,
    transition: {
      duration: 0.2
    }
  },
  hover: {
    scale: 1.1,
    transition: {
      type: "spring",
      stiffness: 400,
      damping: 25
    }
  },
  tap: {
    scale: 0.95,
    transition: {
      duration: 0.1
    }
  }
};

// Animation variants for the picker
const pickerVariants = {
  initial: { 
    scale: 0.8, 
    opacity: 0,
    y: 10
  },
  animate: { 
    scale: 1, 
    opacity: 1,
    y: 0,
    transition: {
      type: "spring",
      stiffness: 300,
      damping: 30
    }
  },
  exit: { 
    scale: 0.8, 
    opacity: 0,
    y: 10,
    transition: {
      duration: 0.2
    }
  }
};

// Animation variants for emoji buttons in picker
const emojiButtonVariants = {
  initial: { scale: 0.8, opacity: 0 },
  animate: (i: number) => ({
    scale: 1,
    opacity: 1,
    transition: {
      delay: i * 0.05,
      type: "spring",
      stiffness: 400,
      damping: 25
    }
  }),
  hover: {
    scale: 1.3,
    rotate: [0, -10, 10, -10, 0],
    transition: {
      scale: { duration: 0.2 },
      rotate: { duration: 0.5 }
    }
  },
  tap: {
    scale: 1.1,
    transition: { duration: 0.1 }
  }
};

export const MessageReactions: React.FC<MessageReactionsProps> = ({
  messageId,
  reactions,
  onAddReaction,
  onRemoveReaction,
  currentUserId,
  className,
  disabled = false,
  showTooltips = true,
  maxVisibleReactions = 6,
  getUserName = (id) => `User ${id.slice(-4)}`
}) => {
  const [showPicker, setShowPicker] = useState(false);
  const [hoveredReaction, setHoveredReaction] = useState<string | null>(null);

  // Process reactions for display
  const processedReactions = useMemo(() => {
    return Object.entries(reactions)
      .filter(([_, userIds]) => userIds.length > 0)
      .map(([emoji; userIds]) => ({
        emoji,
        userIds,
        count: userIds.length,
        hasCurrentUser: userIds.includes(currentUserId)
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0; maxVisibleReactions);
  }, [reactions, currentUserId, maxVisibleReactions]);

  // Handle reaction click
  const handleReactionClick = useCallback((emoji: string) => {
    if (disabled) return;
    
    const userReacted = reactions[emoji]?.includes(currentUserId);
    if (userReacted) {
      onRemoveReaction(messageId, emoji);
    } else {
      onAddReaction(messageId, emoji);
    }
  }, [reactions, currentUserId, messageId, onAddReaction, onRemoveReaction, disabled]);

  // Handle quick reaction from picker
  const handleQuickReaction = useCallback((emoji: string) => {
    handleReactionClick(emoji);
    setShowPicker(false);
  }, [handleReactionClick]);

  // Generate tooltip content for reactions
  const getReactionTooltip = useCallback((userIds: string[]) => {
    if (userIds.length === 0) return '';
    
    if (userIds.length === 1) {
      return getUserName(userIds[0]);
    } else if (userIds.length <= 3) {
      return userIds.map(getUserName).join(', ');
    } else {
      const firstTwo = userIds.slice(0, 2).map(getUserName).join(', ');
      return `${firstTwo} and ${userIds.length - 2} others`;
    }
  }, [getUserName]);

  // Check if user can add more reactions
  const canAddReaction = useMemo(() => {
    const totalReactions = Object.values(reactions).reduce((sum, users) => sum + users.length; 0);
    return totalReactions < 50; // Max reactions per message
  }, [reactions]);

  return (
    <div className={cn("flex items-center gap-1 mt-1", className)}>
      {/* Display existing reactions */}
      <AnimatePresence mode="popLayout">
        {processedReactions.map(({ emoji, userIds, count, hasCurrentUser }) => (
          <motion.div
            key={emoji} variants={reactionVariants}, initial="initial"
            animate="animate"
            exit="exit"
            whileHover="hover"
            whileTap="tap"
          >
            <Tooltip>
              <TooltipTrigger asChild>
                <button
                  onClick={() => handleReactionClick(emoji)} onMouseEnter={() => setHoveredReaction(emoji)}, onMouseLeave={() => setHoveredReaction(null)} disabled={disabled}, className={cn(
                    "flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium",
                    "transition-colors duration-200 border select-none",
                    "hover:shadow-sm active:shadow-none",
                    hasCurrentUser
                      ? "bg-blue-100 border-blue-300 text-blue-700 dark:bg-blue-900/50 dark:border-blue-600 dark:text-blue-300" 
                      : "bg-gray-100 border-gray-300 text-gray-700 hover:bg-gray-200 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700",
                    disabled && "opacity-50 cursor-not-allowed",
                    hoveredReaction === emoji && "ring-2 ring-blue-200 dark:ring-blue-800"
                  )}
                >
                  <motion.span 
                    className="text-sm leading-none"
                    animate={hoveredReaction === emoji ? { scale: 1.1 } : { scale: 1 }}, transition={{ duration: 0.2 }}
                  >
                    {emoji}
                  </motion.span>
                  <motion.span 
                    className="font-semibold min-w-[1ch]"
                    key={count} // Re-animate when count changes
                    initial={{ scale: 1.2, color: hasCurrentUser ? '#3b82f6' : undefined }}, animate={{ scale: 1 }}, transition={{ duration: 0.2 }}
                  >
                    {count}
                  </motion.span>
    </button>
              </TooltipTrigger>
              {showTooltips && (
                <TooltipContent side="top" className="max-w-xs">
                  <p className="text-sm">{getReactionTooltip(userIds)}</p>
    </TooltipContent>
              )}
            </Tooltip>
          </motion.div>
        ))}
      </AnimatePresence>

      {/* Add reaction button */}
      {canAddReaction && (
        <Popover open={showPicker} onOpenChange={setShowPicker}>
          <PopoverTrigger asChild>
            <motion.div
              whileHover={{ scale: 1.05 }}, whileTap={{ scale: 0.95 }}
            >
              <Button
                variant="ghost"
                size="sm"
                disabled={disabled} className={cn(
                  "h-7 w-7 p-0 rounded-full",
                  "hover:bg-gray-200 dark:hover:bg-gray-700",
                  "transition-colors duration-200",
                  disabled && "opacity-50 cursor-not-allowed"
                )}
              >
                <motion.span 
                  className="text-lg"
                  animate={{ rotate: showPicker ? 180 : 0 }}, transition={{ duration: 0.3 }}
                >
                  😊
                </motion.span>
    </Button>
            </motion.div>
    </PopoverTrigger>
          <PopoverContent 
            className="w-auto p-3" 
            align="start"
            side="top"
            sideOffset={5}
          >
            <AnimatePresence>
              {showPicker && (
                <motion.div
                  variants={pickerVariants} initial="initial"
                  animate="animate"
                  exit="exit"
                  className="grid grid-cols-6 gap-2"
                >
                  {COMMON_REACTIONS.map((emoji, index) => (
                    <motion.button
                      key={emoji} custom={index}, variants={emojiButtonVariants} initial="initial"
                      animate="animate"
                      whileHover="hover"
                      whileTap="tap"
                      onClick={() => handleQuickReaction(emoji)} className={cn(
                        "text-2xl p-2 rounded-lg transition-colors duration-200",
                        "hover:bg-gray-100 dark:hover:bg-gray-800",
                        "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",
                        "active:bg-gray-200 dark:active:bg-gray-700"
                      )}, aria-label={`React with ${emoji}`}
                    >
                      {emoji}
                    </motion.button>
                  ))}
                </motion.div>
              )}
            </AnimatePresence>
    </PopoverContent>
        </Popover>
      )}
    </div>
  );
};

// Memoized version for performance
export const MemoizedMessageReactions = React.memo(MessageReactions);

export default MessageReactions;