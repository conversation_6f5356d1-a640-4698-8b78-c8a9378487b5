import React, { useState, useCallback, memo, useEffect } from 'react';
import { Home, MessageCircle, Video, Store, Plus, Menu, Users, Film, Gamepad, Radio, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui';
import { useNavigate, useLocation } from 'react-router-dom';
import LiveStreamButton from './LiveStreamButton';
import { useIsMobile } from '@/hooks/use-device';
import { ROUTES, getSafeImage } from '@/lib/constants';
import ThemeToggle from './ThemeToggle';
import ReelsButton from './ReelsButton';
import NotificationButton from './NotificationButton';
import GlobalSearchHeader from './GlobalSearchHeader';
import SubtleOnlineStatus from './SubtleOnlineStatus';
import { NavItem } from '@/types/interfaces';

// Network status hook
const useNetworkStatus = () => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);
    
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);
  
  return isOnline;
};

// Memoize navigation items to prevent recreation on every render
const NAV_ITEMS: NavItem[] = [{ id: 'home', icon: Home, path: ROUTES.HOME, label: 'Home' },
  { id: 'watch', icon: Video, path: ROUTES.WATCH, label: 'Watch' },
  { id: 'marketplace', icon: Store, path: ROUTES.MARKETPLACE, label: 'Marketplace' },
  { id: 'messages', icon: MessageCircle, path: ROUTES.MESSAGES, label: 'Messages' }];

interface HeaderProps {
  onMobileSidebarToggle?: () => void;
  isMobileSidebarOpen?: boolean;
}

// Optimize Header with memoization to prevent unnecessary re-renders  
const Header = memo<HeaderProps>(({ 
  onMobileSidebarToggle,
  isMobileSidebarOpen = false 
}) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const isMobile = useIsMobile();
  const isOnline = useNetworkStatus();

  const handleNavigation = useCallback((path: string) => {
    navigate(path);
    setIsMenuOpen(false);
    // Navigation feedback removed for cleaner UX
  }, [navigate]);

  const handleProfileClick = useCallback(() => {
    navigate(ROUTES.PROFILE);
    // Profile navigation feedback removed for cleaner UX
  }, [navigate]);


  const handleCreatePost = useCallback(() => {
    navigate(ROUTES.HOME);
    // Post creation feedback removed for cleaner UX
  }, [navigate]);

  return (
    <>
      <header className="sticky top-0 z-50 bg-white shadow-sm border-b border-gray-200 safe-area-top dark:bg-gray-900 dark:border-gray-800 w-full" style={{ position: 'relative' }}>
        <div className="container-responsive mx-auto">
          <div className="flex items-center justify-between h-14 px-2 md:px-4">
            {/* Left section - Mobile Menu + Logo */}
            <div className="flex items-center space-x-2 min-w-0 flex-shrink-0">
              {/* Mobile Menu Button */}
              {isMobile && onMobileSidebarToggle && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="p-2 md:hidden"
                  onClick={onMobileSidebarToggle}, aria-label="Toggle sidebar"
                >
                  {isMobileSidebarOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
                </Button>
              )}
              
              <div 
                className="flex items-center space-x-2 cursor-pointer hover:opacity-80 transition-opacity"
                onClick={() => handleNavigation(ROUTES.HOME)}
              >
                <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                  <span className="text-white font-bold text-lg">f</span>
    </div>
                <span className="hide-mobile text-lg font-bold text-blue-600 dark:text-blue-400">facebook</span>
    </div>
            </div>

            {/* Center section - Search */}
            <div className="flex-1 max-w-xs sm:max-w-lg mx-2 md:mx-4" style={{ position: 'relative', zIndex: 100 }}>
              <GlobalSearchHeader
                placeholder={isMobile ? "Search" : "Search Facebook"} showQuickFilters={false}
              />
    </div>
            {/* Right section - Navigation + User actions */}
            <div className="flex items-center space-x-0.5 sm:space-x-1 flex-shrink-0">
              {/* Navigation Icons - Show on all screen sizes */}
              <div className="flex items-center space-x-0.5 sm:space-x-1">
                {NAV_ITEMS.map((item) => {
                  const isActive = location.pathname === item.path;
                  return (
                    <Button 
                      key={item.id} variant="ghost" 
                      size="sm" 
                      className={`relative p-2 rounded-md transition-colors h-10 w-10 ${
                        isActive ? 'text-blue-600 dark:text-blue-400' : 'text-gray-600 dark:text-gray-300'
                      }`}, onClick={() => handleNavigation(item.path)}, aria-label={item.label}
                    >
                      <item.icon className="w-5 h-5" />
                      {isActive && (
                        <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-6 h-1 bg-blue-600 dark:bg-blue-400 rounded-t-full"></div>
                      )}
                    </Button>
                  );
                })}
              </div>

              {/* Theme toggle */}
              <ThemeToggle />

              {/* Online Status */}
              <SubtleOnlineStatus isOnline={isOnline} />

              {/* Reels Button */}
              <ReelsButton />

              {/* Action Buttons */}
              <Button 
                variant="ghost" 
                size="sm" 
                className="p-2 rounded-full hover:bg-gray-100 transition-colors h-10 w-10 dark:hover:bg-gray-800"
                onClick={handleCreatePost}, aria-label="Create post"
              >
                <Plus className="w-5 h-5 text-gray-600 dark:text-gray-300" />
    </Button>
              <LiveStreamButton />
              
              <NotificationButton />
              
              {isMobile && (
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="p-2 rounded-full hover:bg-gray-100 transition-colors h-10 w-10 dark:hover:bg-gray-800"
                  onClick={() => setIsMenuOpen(!isMenuOpen)}, aria-label="Menu"
                >
                  <Menu className="w-5 h-5 text-gray-600 dark:text-gray-300" />
    </Button>
              )}
              
              <Avatar 
                className="w-8 h-8 cursor-pointer hover:opacity-80 transition-opacity ml-2" 
                onClick={handleProfileClick}
              >
                <AvatarImage src={getSafeImage('AVATARS', 7)} />
                <AvatarFallback className="text-sm">JD</AvatarFallback>
    </Avatar>
            </div>
    </div>
          {/* Mobile menu - Additional options */}
          {isMenuOpen && isMobile && (
            <div className="bg-white border-t border-gray-200 py-2 dark:bg-gray-900 dark:border-gray-800">
              <div className="grid grid-cols-2 gap-2 px-4">
                <Button
                  variant="ghost"
                  className="flex items-center space-x-2 p-3 rounded-lg text-left justify-start h-12 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800"
                  onClick={() => {
                    handleNavigation(ROUTES.FRIENDS);
                    setIsMenuOpen(false);
                  }}
                >
                  <Users className="w-5 h-5" />
                  <span>Friends</span>
    </Button>
                <Button
                  variant="ghost"
                  className="flex items-center space-x-2 p-3 rounded-lg text-left justify-start h-12 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800"
                  onClick={() => {
                    handleNavigation(ROUTES.GROUPS);
                    setIsMenuOpen(false);
                  }}
                >
                  <Users className="w-5 h-5" />
                  <span>Groups</span>
    </Button>
                <Button
                  variant="ghost"
                  className="flex items-center space-x-2 p-3 rounded-lg text-left justify-start h-12 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800"
                  onClick={() => {
                    handleNavigation(ROUTES.REELS);
                    setIsMenuOpen(false);
                  }}
                >
                  <Film className="w-5 h-5" />
                  <span>Reels</span>
    </Button>
                <Button
                  variant="ghost"
                  className="flex items-center space-x-2 p-3 rounded-lg text-left justify-start h-12 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800"
                  onClick={() => {
                    handleNavigation(ROUTES.GAMING);
                    setIsMenuOpen(false);
                  }}
                >
                  <Gamepad className="w-5 h-5" />
                  <span>Gaming</span>
    </Button>
                <Button
                  variant="ghost"
                  className="flex items-center space-x-2 p-3 rounded-lg text-left justify-start h-12 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800"
                  onClick={() => {
                    handleNavigation(ROUTES.LIVE);
                    setIsMenuOpen(false);
                  }}
                >
                  <Radio className="w-5 h-5 text-red-600" />
                  <span>Go Live</span>
    </Button>
              </div>
    </div>
          )}
        </div>
    </header>
    </>
  );
});

Header.displayName = 'Header';

export default Header;
