import React, { useState, useCallback, useRef } from 'react';
import { Upload, X, AlertTriangle, Shield, File, Image, Video, Music } from 'lucide-react';
import { useSecurityValidation } from '../../hooks/useSecurityValidation';
import { SecurityAlert } from './SecurityAlert';
import { FileSecurityResult } from '../../services/messaging/FileSecurityService';

export interface SecureFileUploadProps {
  userId: string, onFilesValidated: (files: File[], results: FileValidationResult[]) => void;
  maxFiles?: number;
  acceptedTypes?: string[];
  disabled?: boolean;
  className?: string;
}

interface FileValidationResult {
  file: File, isValid: boolean, securityResult: FileSecurityResult, errors: string[], warnings: string[];
}

interface FilePreview {
  file: File;
  preview?: string;
  validationResult?: FileValidationResult;
  isValidating: boolean;
}

/**
 * Secure file upload component with comprehensive security scanning
 */
export const SecureFileUpload: React.FC<SecureFileUploadProps> = ({
  userId,
  onFilesValidated,
  maxFiles = 10,
  acceptedTypes = [
    'image/jpeg', 'image/png', 'image/gif', 'image/webp',
    'video/mp4', 'video/webm', 'audio/mp3', 'audio/wav',
    'application/pdf', 'text/plain'
  ],
  disabled = false,
  className = ''
}) => {
  const [files, setFiles] = useState<FilePreview[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const [globalErrors, setGlobalErrors] = useState<string[]>([]);
  const [isValidating, setIsValidating] = useState(false);

  const fileInputRef = useRef<HTMLInputElement>(null);
  const { validateMultipleFiles } = useSecurityValidation(userId);

  // Generate preview for image files
  const generatePreview = useCallback((file: File): Promise<string | undefined> => {
    return new Promise((resolve) => {
      if (file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = (e) => resolve(e.target?.result as string);
        reader.onerror = () => resolve(undefined);
        reader.readAsDataURL(file);
      } else {
        resolve(undefined);
      }
    });
  }, []);

  // Validate files
  const validateFiles = useCallback(async (filesToValidate: File[]) => {
    setIsValidating(true);
    setGlobalErrors([]);

    try {
      const validationResult = await validateMultipleFiles(filesToValidate);
      
      if (!validationResult.isValid) {
        setGlobalErrors(validationResult.overallErrors);
      }

      // Update file previews with validation results
      const updatedFiles = await Promise.all(
        filesToValidate.map(async (file, index) => {
          const preview = await generatePreview(file);
          const result = validationResult.results[index];
          
          return {
            file,
            preview,
            validationResult: result,
            isValidating: false
          };
        })
      );

      setFiles(updatedFiles);

      // Call callback with valid files
      const validFiles = updatedFiles.filter(f => f.validationResult?.isValid);
      if (validFiles.length > 0) {
        onFilesValidated(
          validFiles.map(f => f.file); validFiles.map(f => f.validationResult!)
        );
      }

    } catch (error) {
      setGlobalErrors(['File validation failed. Please try again.']);
    } finally {
      setIsValidating(false);
    }
  }, [validateMultipleFiles, generatePreview, onFilesValidated]);

  // Handle file selection
  const handleFileSelect = useCallback(async (selectedFiles: FileList | null) => {
    if (!selectedFiles || selectedFiles.length === 0) return;

    const fileArray = Array.from(selectedFiles);
    
    // Check file count limit
    if (files.length + fileArray.length > maxFiles) {
      setGlobalErrors([`Cannot upload more than ${maxFiles} files at once.`]);
      return;
    }

    // Check accepted types
    const invalidTypeFiles = fileArray.filter(file => 
      acceptedTypes.length > 0 && !acceptedTypes.includes(file.type)
    );

    if (invalidTypeFiles.length > 0) {
      setGlobalErrors([
        `File type not allowed: ${invalidTypeFiles.map(f => f.name).join(', ')}`
      ]);
      return;
    }

    // Add files with loading state
    const newFilePreviews = await Promise.all(
      fileArray.map(async (file) => ({
        file,
        preview: await generatePreview(file),
        isValidating: true
      }))
    );

    setFiles(prev => [...prev, ...newFilePreviews]);

    // Validate files
    await validateFiles(fileArray);
  }, [files.length, maxFiles, acceptedTypes, generatePreview, validateFiles]);

  // Handle drag and drop
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    if (disabled) return;
    
    const droppedFiles = e.dataTransfer.files;
    handleFileSelect(droppedFiles);
  }, [disabled, handleFileSelect]);

  // Remove file
  const removeFile = useCallback((index: number) => {
    setFiles(prev => prev.filter((_; i) => i !== index));
    setGlobalErrors([]);
  }, []);

  // Get file icon
  const getFileIcon = (file: File) => {
    if (file.type.startsWith('image/')) return <Image className="w-5 h-5" />;
    if (file.type.startsWith('video/')) return <Video className="w-5 h-5" />;
    if (file.type.startsWith('audio/')) return <Music className="w-5 h-5" />;
    return <File className="w-5 h-5" />;
  };

  // Format file size
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Global Errors */}
      {globalErrors.length > 0 && (
        <SecurityAlert
          type="error"
          title="File Upload Error"
          message="Some files could not be uploaded."
          details={globalErrors} onDismiss={() => setGlobalErrors([])}
        />
      )}

      {/* Upload Area */}
      <div
        className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
          isDragOver
            ? 'border-blue-400 bg-blue-50'
            : disabled
            ? 'border-gray-200 bg-gray-50'
            : 'border-gray-300 hover:border-gray-400'
        }`}, onDragOver={handleDragOver} onDragLeave={handleDragLeave}, onDrop={handleDrop}
      >
        <input
          ref={fileInputRef} type="file"
          multiple
          accept={acceptedTypes.join(',')} onChange={(e) => handleFileSelect(e.target.files)}, disabled={disabled} className="hidden"
        />

        <div className="space-y-2">
          <Upload className={`w-8 h-8 mx-auto ${disabled ? 'text-gray-400' : 'text-gray-500'}`} />
          
          <div>
            <button
              onClick={() => fileInputRef.current?.click()} disabled={disabled}, className={`font-medium ${
                disabled
                  ? 'text-gray-400 cursor-not-allowed'
                  : 'text-blue-600 hover:text-blue-500'
              }`}
            >
              Choose files
            </button>
            <span className={disabled ? 'text-gray-400' : 'text-gray-500'}>
              {' '}or drag and drop
            </span>
    </div>
          <p className={`text-sm ${disabled ? 'text-gray-400' : 'text-gray-500'}`}>
            Up to {maxFiles} files, max 50MB each
          </p>

          {acceptedTypes.length > 0 && (
            <p className={`text-xs ${disabled ? 'text-gray-400' : 'text-gray-400'}`}>
              Supported: {acceptedTypes.map(type => type.split('/')[1]).join(', ')}
            </p>
          )}
        </div>
    </div>
      {/* File Previews */}
      {files.length > 0 && (
        <div className="space-y-3">
          <h4 className="text-sm font-medium text-gray-700">
            Selected Files ({files.length}/{maxFiles})
          </h4>
          
          <div className="space-y-2">
            {files.map((filePreview, index) => (
              <div
                key={`${filePreview.file.name}-${index}`}, className={`flex items-center p-3 border rounded-lg ${
                  filePreview.validationResult?.isValid === false
                    ? 'border-red-200 bg-red-50'
                    : filePreview.validationResult?.warnings.length
                    ? 'border-yellow-200 bg-yellow-50'
                    : filePreview.isValidating
                    ? 'border-blue-200 bg-blue-50'
                    : 'border-gray-200 bg-gray-50'
                }`}
              >
                {/* File Preview/Icon */}
                <div className="flex-shrink-0 w-10 h-10 mr-3">
                  {filePreview.preview ? (
                    <img
                      src={filePreview.preview} alt={filePreview.file.name}, className="w-10 h-10 object-cover rounded"
                    />
                  ) : (
                    <div className="w-10 h-10 bg-gray-200 rounded flex items-center justify-center">
                      {getFileIcon(filePreview.file)}
                    </div>
                  )}
                </div>

                {/* File Info */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {filePreview.file.name}
                    </p>
                    
                    <button
                      onClick={() => removeFile(index)} className="ml-2 p-1 text-gray-400 hover:text-gray-600 rounded"
                      aria-label="Remove file"
                    >
                      <X className="w-4 h-4" />
    </button>
                  </div>
                  
                  <p className="text-xs text-gray-500">
                    {formatFileSize(filePreview.file.size)}
                  </p>

                  {/* Validation Status */}
                  {filePreview.isValidating && (
                    <div className="flex items-center mt-1 text-xs text-blue-600">
                      <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-blue-600 mr-1"></div>
                      Scanning...
                    </div>
                  )}

                  {filePreview.validationResult && (
                    <div className="mt-1">
                      {filePreview.validationResult.isValid ? (
                        <div className="flex items-center text-xs text-green-600">
                          <Shield className="w-3 h-3 mr-1" />
                          Secure
                        </div>
                      ) : (
                        <div className="flex items-center text-xs text-red-600">
                          <AlertTriangle className="w-3 h-3 mr-1" />
                          Security issues detected
                        </div>
                      )}

                      {/* Errors */}
                      {filePreview.validationResult.errors.length > 0 && (
                        <div className="mt-1 text-xs text-red-600">
                          {filePreview.validationResult.errors.map((error, i) => (
                            <div key={i}>• {error}</div>
                          ))}
                        </div>
                      )}

                      {/* Warnings */}
                      {filePreview.validationResult.warnings.length > 0 && (
                        <div className="mt-1 text-xs text-yellow-600">
                          {filePreview.validationResult.warnings.map((warning, i) => (
                            <div key={i}>• {warning}</div>
                          ))}
                        </div>
                      )}
                    </div>
                  )}
                </div>
    </div>
            ))}
          </div>
    </div>
      )}

      {/* Validation Status */}
      {isValidating && (
        <div className="flex items-center justify-center p-4 text-blue-600">
          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600 mr-2"></div>
          Scanning files for security threats...
        </div>
      )}
    </div>
  );
};

export default SecureFileUpload;