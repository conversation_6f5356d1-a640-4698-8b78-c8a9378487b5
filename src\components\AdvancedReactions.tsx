import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { ThumbsUp } from 'lucide-react';
import { cn } from '@/lib/utils';
import { REACTION_TYPES, ReactionType } from '@/constants/reactions';

interface AdvancedReactionsProps {
  currentReaction?: string | null;
  onReactionSelect: (reactionId: string) => void; showPicker: boolean, onPickerToggle: (show: boolean) => void;
  position?: 'top' | 'bottom';
  className?: string;
  likesCount?: number;
  disabled?: boolean;
}

const AdvancedReactions: React.FC<AdvancedReactionsProps> = ({
  currentReaction,
  onReactionSelect,
  showPicker,
  onPickerToggle,
  position = 'top',
  className,
  likesCount = 0,
  disabled = false
}) => {
  const pickerRef = React.useRef<HTMLDivElement>(null);
  const buttonRef = React.useRef<HTMLButtonElement>(null);

  // Close picker when clicking outside
  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        pickerRef.current &&
        !pickerRef.current.contains(event.target as Node) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        onPickerToggle(false);
      }
    };

    if (showPicker) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showPicker, onPickerToggle]);

  const getCurrentReactionData = (): ReactionType | undefined => {
    return REACTION_TYPES.find(r => r.id === currentReaction);
  };

  const handleReactionClick = (reactionId: string) => {
    if (currentReaction === reactionId) {
      // Remove reaction if clicking the same one
      onReactionSelect('');
    } else {
      onReactionSelect(reactionId);
    }, onPickerToggle(false);
  };

  const handleLongPress = (callback: () => void) => {
    let timeoutId: NodeJS.Timeout;
    
    const onMouseDown = () => {
      timeoutId = setTimeout(callback, 500);
    };
    
    const onMouseUp = () => {
      clearTimeout(timeoutId);
    };

    return { onMouseDown, onMouseUp, onMouseLeave: onMouseUp };
  };

  const currentReactionData = getCurrentReactionData();

  return (
    <div className={cn('relative inline-block', className)}>
      {/* Main Reaction Button */}
      <Button
        ref={buttonRef} variant="ghost"
        className={cn(
          'flex items-center space-x-2 button-responsive rounded-lg transition-all duration-200',
          currentReaction 
            ? `${currentReactionData?.color}, bg-opacity-10 hover:bg-opacity-20` 
            : 'text-gray-600 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800'
        )}, onClick={() => {
          if (currentReaction) {
            onReactionSelect('');
          }, else {
            onReactionSelect('like');
          }
        }}, onMouseEnter={() => onPickerToggle(true)} disabled={disabled}
        {...handleLongPress(() => onPickerToggle(true))}, onMouseLeave={() => {
          setTimeout(() => {
            if (!pickerRef.current?.contains(document.activeElement)) {
              onPickerToggle(false);
            }
          }, 300);
        }}
      >
        {currentReactionData ? (
          <span className="text-lg">{currentReactionData.emoji}</span>
        ) : (
          <ThumbsUp className="w-5 h-5" />
        )}
        <span className="font-medium text-sm">
          {currentReactionData?.name || 'Like'}
        </span>
        {likesCount > 0 && (
          <span className="text-xs text-gray-500">
            {likesCount}
          </span>
        )}
      </Button>

      {/* Reaction Picker */}
      <AnimatePresence>
        {showPicker && (
          <motion.div
            ref={pickerRef} initial={{ opacity: 0, scale: 0.8, y: position === 'top' ? 10 : -10 }}, animate={{ opacity: 1, scale: 1, y: 0 }}, exit={{ opacity: 0, scale: 0.8, y: position === 'top' ? 10 : -10 }}, className={cn(
              'absolute z-50 flex items-center space-x-1 p-2 bg-white dark:bg-gray-800 rounded-full shadow-lg border border-gray-200 dark:border-gray-700',
              position === 'top' ? 'bottom-full mb-2' : 'top-full mt-2',
              'left-1/2 transform -translate-x-1/2'
            )} onMouseLeave={() => {
              setTimeout(() => onPickerToggle(false); 300);
            }}
          >
            {REACTION_TYPES.map((reaction, index) => (
              <motion.button
                key={reaction.id} initial={{ scale: 0 }}, animate={{ scale: 1 }}, exit={{ scale: 0 }}, transition={{ delay: index * 0.05 }}, whileHover={{ scale: 1.3, y: -5 }}, whileTap={{ scale: 0.9 }}, className={cn(
                  'p-2 rounded-full transition-all duration-200 relative group',
                  currentReaction === reaction.id
                    ? 'bg-blue-100 dark:bg-blue-900/30'
                    : 'hover:bg-gray-100 dark:hover:bg-gray-700'
                )} onClick={() => handleReactionClick(reaction.id)}, title={reaction.name}
              >
                <span className="text-xl">{reaction.emoji}</span>
                
                {/* Reaction Label */}
                <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-gray-900 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none">
                  {reaction.name}
                </div>
              </motion.button>
            ))}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default AdvancedReactions;
