import React, { useState, useCallback, useMemo, lazy, Suspense, useEffect, memo } from 'react';
import { ErrorBoundary } from 'react-error-boundary';
import { motion, AnimatePresence } from 'framer-motion';
import { Activity, BarChart3, MessageCircle, Bell, Settings, Home, ArrowLeft, Sliders, Zap, Database, TrendingUp, Users, Search, Menu, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';

// Import sidebar directly to avoid build warnings
import OptimizedSidebar from './components/OptimizedSidebar';
import OptimizedDemoTab from './components/OptimizedDemoTab';

// Optimized lazy loading with preloading
const OptimizedNotificationCenter = lazy(() => 
  import('./components/OptimizedNotificationCenter')
);
const OptimizedMessagingInterface = lazy(() => 
  import('./components/OptimizedMessagingInterface')
);
const RealTimeCollaborationDemo = lazy(() => 
  import('./components/advanced/RealTimeCollaborationDemo')
);
const FuzzySearchDemo = lazy(() => 
  import('./components/advanced/FuzzySearchDemo')
);
const RealTimePerformanceDashboard = lazy(() => 
  import('./components/enhanced/RealTimePerformanceDashboard')
);

// Import our enhanced hooks
import { useRealTimePerformance } from '@/hooks/useRealTimePerformance';
import { useIntelligentCache } from '@/hooks/useIntelligentCache';
import { useDemoStateManager } from '@/hooks/useDemoStateManager';
import { useDemoKeyboardNavigation } from '@/hooks/useDemoKeyboardNavigation';

// Import enhanced components
import DemoAnalyticsDashboard from './components/DemoAnalyticsDashboard';
import EnhancedVirtualizedFeed from './components/EnhancedVirtualizedFeed';

interface DemoConfig {
  mode: 'basic' | 'enhanced' | 'safe' | 'performance', features: {
    virtualizedFeed: boolean, realTimeUpdates: boolean, advancedAnimations: boolean, performanceMonitoring: boolean, offlineSupport: boolean, experimentalFeatures: boolean;
  };
  theme: 'light' | 'dark' | 'auto', performance: {
    lazyLoading: boolean, imageOptimization: boolean, bundleSplitting: boolean, memoryOptimization: boolean;
  };
}

const UnifiedOptimizedDemo: React.FC = () => {
  // Enhanced state management with persistence
  const {
    state: config,
    updateMode,
    updateActiveTab,
    updateFeature,
    updatePerformanceSetting
  } = useDemoStateManager();
  
  const [currentView, setCurrentView] = useState<'demo' | 'settings' | 'analytics'>('demo');
  const [isNotificationOpen, setIsNotificationOpen] = useState(false);
  const [isMessagingOpen, setIsMessagingOpen] = useState(false);
  const [configLoading, setConfigLoading] = useState(false);
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);
  
  // Use state manager for active tab
  const activeTab = config.activeTab;

  // Performance monitoring
  const {
    metrics: performanceMetrics,
    isMonitoring,
    startMonitoring,
    stopMonitoring,
    getPerformanceScore,
    alerts
  } = useRealTimePerformance();

  // Intelligent caching - stable reference with better optimization
  const cacheOptions = useMemo(() => ({
    maxSize: config.mode === 'performance' ? 50 : 25,
    maxEntries: config.mode === 'performance' ? 1000 : 500,
    enablePredictive: config.features.experimentalFeatures,
    enableCompression: config.performance.memoryOptimization
  }), [config.mode, config.features.experimentalFeatures, config.performance.memoryOptimization]);

  const cache = useIntelligentCache(cacheOptions);

  // Wrapper functions with loading states
  const handleConfigUpdate = useCallback(async (updateFn: () => void) => {
    setConfigLoading(true);
    try {
      updateFn();
      // Simulate brief loading delay for UX
      await new Promise(resolve => setTimeout(resolve, 300));
    } finally {
      setConfigLoading(false);
    }
  }, []);

  const handleModeUpdate = useCallback((mode: DemoConfig['mode']) => {
    handleConfigUpdate(() => updateMode(mode));
  }, [handleConfigUpdate, updateMode]);

  const handleFeatureUpdate = useCallback((key: keyof DemoConfig['features'], value: boolean) => {
    handleConfigUpdate(() => updateFeature(key, value));
  }, [handleConfigUpdate, updateFeature]);

  // Optimized demo data generation with better memoization
  const demoData = useMemo(() => {
    const basePostCount = config.mode === 'performance' ? 1000 : config.mode === 'basic' ? 20 : 100;
    const cacheKey = `demoData_${config.mode}_${config.features.experimentalFeatures}`;
    
    // Check if data exists in cache first
    const cachedData = cache.get(cacheKey);
    if (cachedData && Array.isArray(cachedData)) {
      return cachedData;
    }
    
    // Generate data with deterministic randomness for consistency
    const data = Array.from({ length: basePostCount }, (_, i) => {
      const seed = i + (config.mode.charCodeAt(0) * 1000);
      return {
        id: `unified-post-${i}`,
        author: {
          id: `author-${i}`,
          name: `Demo User ${i}`,
          username: `user${i}`,
          avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${seed}`,
          isVerified: (seed % 7) === 0
        },
        content: `🚀 Unified Demo Post #${i} - Mode: ${config.mode}! ${config.features.experimentalFeatures ? '⚡ Experimental features enabled!' : ''} #UnifiedDemo #${config.mode}`,
        mediaUrls: (seed % 3) === 0 ? [`https://picsum.photos/400/300?random=${seed}`] : [],
        timestamp: new Date(Date.now() - (seed % 864000000)),
        metrics: {
          likes: seed % 1000,
          comments: seed % 100,
          shares: seed % 50,
          views: seed % 5000
        },
        userInteractions: {
          hasLiked: (seed % 5) === 0,
          hasShared: (seed % 11) === 0,
          hasBookmarked: (seed % 8) === 0
        },
        tags: [`${config.mode}${seed % 10}`, `feature${seed % 5}`],
        isPromoted: (seed % 17) === 0,
        category: ['trending', 'recent', 'following', 'recommended'][seed % 4]
      };
    });
    
    // Cache the generated data
    cache.set(cacheKey, data, { 
      priority: 'high', 
      ttl: 300000 // 5 minutes
    });
    
    return data;
  }, [config.mode, config.features.experimentalFeatures]); // Removed cache from dependencies to prevent infinite loops

  // Preload components based on current tab for better UX
  useEffect(() => {
    const preloadComponents = async () => {
      try {
        if (activeTab === 'feed' && config.features.virtualizedFeed) {
          // Preload feed components if needed
          console.log('Preloading feed components');
        } else if (activeTab === 'workers') {
          await import('./components/advanced/WebWorkerDashboard');
        } else if (activeTab === 'performance' && config.features.performanceMonitoring) {
          await Promise.all([
            import('./components/PerformanceDashboard'),
            import('./components/enhanced/RealTimePerformanceDashboard'),
          ]);
        }
      } catch (error) {
        console.warn('Failed to preload components:', error);
      }
    };
    
    // Debounced preloading to avoid excessive network requests
    const timer = setTimeout(preloadComponents, 100);
    return () => clearTimeout(timer);
  }, [activeTab, config.features.virtualizedFeed, config.features.performanceMonitoring]);

  // Keyboard navigation setup
  const {
    containerRef,
    announce,
    KeyboardShortcutsModal
  } = useDemoKeyboardNavigation({
    enabled: true,
    enableTabNavigation: true,
    enableShortcuts: true,
    enableVoiceAnnouncements: config.features.experimentalFeatures,
    onNavigate: (direction) => {
      console.log('Keyboard navigation:', direction);
    }, onTabSwitch: (tabIndex) => {
      const tabs = ['feed', 'virtualized', 'workers', 'collab', 'search', 'performance', 'realtime', 'activity', 'cache'];
      if (tabs[tabIndex]) {
        updateActiveTab(tabs[tabIndex]);
      }
    },
    onAction: (action) => {
      switch (action) {
        case 'save':
          announce('Demo state saved');
          break;
        case 'refresh':
          window.location.reload();
          break;
        case 'help':
          setCurrentView('analytics');
          break;
      }
    }
  });

  // Auto-start performance monitoring when feature is enabled
  useEffect(() => {
    if (config.features.performanceMonitoring && !isMonitoring) {
      startMonitoring();
    } else if (!config.features.performanceMonitoring && isMonitoring) {
      stopMonitoring();
    }
  }, [config.features.performanceMonitoring, isMonitoring]); // Removed function dependencies to prevent loops

  // Update active tab through state manager
  const handleTabChange = useCallback((newTab: string) => {
    updateActiveTab(newTab);
    announce(`Switched to ${newTab} tab`);
  }, [updateActiveTab, announce]);

  // Cache prefetching based on usage patterns
  useEffect(() => {
    if (config.features.experimentalFeatures) {
      cache.prefetch(async (key) => {
        // Simulate fetching additional demo data
        await new Promise(resolve => setTimeout(resolve, 100));
        return `prefetched_${key}`;
      });
    }
  }, [config.features.experimentalFeatures]); // Removed cache.prefetch from dependencies

  const getModeDescription = (mode: DemoConfig['mode']) => {
    switch (mode) {
      case 'basic': return 'Lightweight, essential features only';
      case 'enhanced': return 'Full feature set with optimizations';
      case 'safe': return 'Conservative settings, maximum stability';
      case 'performance': return 'High-performance mode, extensive data';
      default: return '';
    }
  };

  // Memoized fallback components to prevent re-renders
  const ErrorFallback = memo(({ error, resetErrorBoundary }: { error: Error, resetErrorBoundary: () => void }) => (
    <Card className="m-4">
      <CardHeader>
        <CardTitle className="text-red-600">Something went wrong</CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-sm text-muted-foreground mb-4">{error.message}</p>
        <Button onClick={resetErrorBoundary} variant="outline">
          Try again
        </Button>
    </CardContent>
    </Card>
  ));

  const LoadingFallback = memo(({ name }: { name: string }) => (
    <Card className="m-4">
      <CardContent className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="animate-spin h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-sm text-muted-foreground">Loading {name}...</p>
        </div>
      </CardContent>
    </Card>
  ));

  if (currentView === 'analytics') {
    return (
      <div className="min-h-screen bg-background" ref={containerRef as React.RefObject<HTMLDivElement>}>
        <div className="max-w-6xl mx-auto p-6">
          <div className="flex items-center gap-4 mb-6">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setCurrentView('demo')}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Demo
            </Button>
            <h1 className="text-2xl font-bold">Analytics Dashboard</h1>
    </div>
          <DemoAnalyticsDashboard
            demoMode={config.mode}
            activeTab={activeTab}
            dataSize={demoData.length}
            onOptimizationSuggestion={(suggestion) => {
              announce(`Optimization suggestion: ${suggestion}`);
            }}
          />
    </div>
        <KeyboardShortcutsModal />
    </div>
    );
  }

  if (currentView === 'settings') {
    return (
      <div className="min-h-screen bg-background">
        <div className="max-w-4xl mx-auto p-6">
          <div className="flex items-center gap-4 mb-6">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setCurrentView('demo')}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Demo
            </Button>
            <h1 className="text-2xl font-bold">Demo Configuration</h1>
    </div>
          <div className="grid gap-6">
            {/* Mode Selection */}
            <Card>
              <CardHeader>
                <CardTitle>Demo Mode</CardTitle>
    </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4">
                  {(['basic', 'enhanced', 'safe', 'performance'] as const).map((mode) => (
                    <Button
                      key={mode}
                      variant={config.mode === mode ? 'default' : 'outline'}
                      className="h-auto p-4 flex-col items-start"
                      onClick={() => handleModeUpdate(mode)}
                    >
                      <span className="font-semibold capitalize">{mode}</span>
                      <span className="text-xs text-muted-foreground text-left">
                        {getModeDescription(mode)}
                      </span>
    </Button>
                  ))}
                </div>
    </CardContent>
            </Card>

            {/* Features */}
            <Card>
              <CardHeader>
                <CardTitle>Features</CardTitle>
    </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {Object.entries(config.features).map(([key, value]) => (
                    <div key={key} className="flex items-center justify-between">
                      <Label htmlFor={key} className="capitalize">
                        {key.replace(/([A-Z])/g, ' $1').toLowerCase()}
                      </Label>
                      <Switch
                        id={key} checked={value} onCheckedChange={(checked) => handleFeatureUpdate(key as keyof DemoConfig['features'], checked)}
                      />
    </div>
                  ))}
                </div>
    </CardContent>
            </Card>

            {/* Performance Settings */}
            <Card>
              <CardHeader>
                <CardTitle>Performance Optimizations</CardTitle>
    </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {Object.entries(config.performance).map(([key, value]) => (
                    <div key={key} className="flex items-center justify-between">
                      <Label htmlFor={key} className="capitalize">
                        {key.replace(/([A-Z])/g, ' $1').toLowerCase()}
                      </Label>
                      <Switch
                        id={key} checked={value} onCheckedChange={(checked) => updatePerformanceSetting(key as keyof DemoConfig['performance'], checked)}
                      />
    </div>
                  ))}
                </div>
    </CardContent>
            </Card>
    </div>
        </div>
    </div>
    );
  }

  return (
    <div className="min-h-screen bg-background" ref={containerRef as React.RefObject<HTMLDivElement>}>
      {/* Header */}
      <header className="border-b bg-card">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="sm"
              className="md:hidden"
              onClick={() => setIsMobileSidebarOpen(!isMobileSidebarOpen)}
            >
              {isMobileSidebarOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
            </Button>
            <h1 className="text-lg md:text-xl font-bold">Unified Optimized Demo</h1>
            <Badge variant="secondary">{config.mode} mode</Badge>
            {configLoading && <Badge variant="outline">Saving...</Badge>}
            {isMonitoring && (
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                <span className="text-sm text-muted-foreground">
                  {getPerformanceScore()}% Performance
                </span>
    </div>
            )}
            {alerts.filter(a => !a.resolved).length > 0 && (
              <Badge variant="destructive">
                {alerts.filter(a => !a.resolved).length} Alert{alerts.filter(a => !a.resolved).length !== 1 ? 's' : ''}
              </Badge>
            )}
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setCurrentView('analytics')} title="View Analytics Dashboard (Ctrl+H)"
            >
              <BarChart3 className="h-4 w-4 mr-2" />
              Analytics
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setCurrentView('settings')} title="Configure Demo Settings"
            >
              <Sliders className="h-4 w-4 mr-2" />
              Configure
            </Button>
            <div className="flex items-center gap-1 text-sm">
              <Database className="h-4 w-4" />
              <span>{cache.stats.hitRate.toFixed(0)}%</span>
    </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsNotificationOpen(!isNotificationOpen)} className="relative"
            >
              <Bell className="h-4 w-4" />
              {alerts.filter(a => !a.resolved).length > 0 && (
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full text-xs flex items-center justify-center text-white">
                  {alerts.filter(a => !a.resolved).length}
                </div>
              )}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsMessagingOpen(!isMessagingOpen)}
            >
              <MessageCircle className="h-4 w-4" />
    </Button>
          </div>
    </div>
      </header>

      <div className="flex min-h-0">
        {/* Sidebar */}
        <ErrorBoundary FallbackComponent={ErrorFallback}>
          <OptimizedSidebar 
            isMobileOpen={isMobileSidebarOpen} onMobileClose={() => setIsMobileSidebarOpen(false)}
          />
    </ErrorBoundary>
        {/* Main Content */}
        <main className="flex-1 w-full md:ml-80 md:max-w-4xl md:mx-auto px-2 md:px-4">
          <Tabs value={activeTab} onValueChange={handleTabChange}, className="p-2 md:p-4">
            <TabsList className="grid w-full grid-cols-4 md:grid-cols-9 overflow-x-auto">
              <TabsTrigger value="feed" className="flex-shrink-0">
                <Home className="h-4 w-4 mr-0 md:mr-1" />
                <span className="hidden sm:inline">Feed</span>
    </TabsTrigger>
              <TabsTrigger value="virtualized" className="flex-shrink-0">
                <TrendingUp className="h-4 w-4 mr-0 md:mr-1" />
                <span className="hidden sm:inline">Virtual</span>
    </TabsTrigger>
              <TabsTrigger value="workers" className="flex-shrink-0 hidden md:flex">
                <Zap className="h-4 w-4 mr-1" />
                Workers
              </TabsTrigger>
              <TabsTrigger value="collab" className="flex-shrink-0 hidden md:flex">
                <Users className="h-4 w-4 mr-1" />
                Collab
              </TabsTrigger>
              <TabsTrigger value="search" className="flex-shrink-0">
                <Search className="h-4 w-4 mr-0 md:mr-1" />
                <span className="hidden sm:inline">Search</span>
    </TabsTrigger>
              <TabsTrigger value="performance" className="flex-shrink-0 hidden md:flex">
                <BarChart3 className="h-4 w-4 mr-1" />
                Perf
              </TabsTrigger>
              <TabsTrigger value="realtime" className="flex-shrink-0 hidden md:flex">
                <Activity className="h-4 w-4 mr-1" />
                Live
              </TabsTrigger>
              <TabsTrigger value="activity" className="flex-shrink-0 hidden md:flex">
                <Settings className="h-4 w-4 mr-1" />
                Activity
              </TabsTrigger>
              <TabsTrigger value="cache" className="flex-shrink-0 hidden md:flex">
                <Database className="h-4 w-4 mr-1" />
                Cache
              </TabsTrigger>
              
              {/* Mobile More Menu */}
              <div className="md:hidden">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm" className="flex-shrink-0">
                      <Settings className="h-4 w-4" />
                      <span className="ml-1">More</span>
    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => handleTabChange('workers')}>
                      <Zap className="h-4 w-4 mr-2" />
                      Workers
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleTabChange('collab')}>
                      <Users className="h-4 w-4 mr-2" />
                      Collaboration
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleTabChange('performance')}>
                      <BarChart3 className="h-4 w-4 mr-2" />
                      Performance
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleTabChange('realtime')}>
                      <Activity className="h-4 w-4 mr-2" />
                      Real-time
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleTabChange('activity')}>
                      <Settings className="h-4 w-4 mr-2" />
                      Activity
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleTabChange('cache')}>
                      <Database className="h-4 w-4 mr-2" />
                      Cache
                    </DropdownMenuItem>
    </DropdownMenuContent>
                </DropdownMenu>
    </div>
            </TabsList>

            <TabsContent value="feed">
              <div className="space-y-4">
                <EnhancedVirtualizedFeed
                  posts={demoData} onPostInteraction={(postId, action) => {
                    console.log('Post interaction:', postId, action);
                    announce(`${action}, on post ${postId}`);
                  }}, hasNextPage={config.mode === 'performance'} onLoadMore={async () => {
                    // Simulate loading more posts
                    await new Promise(resolve => setTimeout(resolve; 1000));
                    return demoData.slice(0, 20).map((post, i) => ({
                      ...post,
                      id: `${post.id}-extra-${i}`,
                      content: `${post.content} (Loaded via infinite scroll)`
                    }));
                  }}, className="h-[400px] md:h-[600px]"
                />
    </div>
            </TabsContent>

            <TabsContent value="virtualized">
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <TrendingUp className="h-5 w-5" />
                      Advanced Virtualized Feed
                    </CardTitle>
    </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground mb-4">
                      Experience high-performance virtualization with dynamic item sizing, 
                      smooth scrolling, and optimized rendering for large datasets.
                    </p>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                      <div className="text-center">
                        <div className="text-2xl font-bold">{demoData.length}</div>
                        <div className="text-sm text-muted-foreground">Total Posts</div>
    </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold">~5</div>
                        <div className="text-sm text-muted-foreground">Rendered Items</div>
    </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold">60</div>
                        <div className="text-sm text-muted-foreground">FPS</div>
    </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold">Dynamic</div>
                        <div className="text-sm text-muted-foreground">Item Height</div>
    </div>
                    </div>
    </CardContent>
                </Card>

                <OptimizedDemoTab
                  tabType="virtualized"
                  data={demoData} config={config}, onPostInteraction={(postId, action) => console.log('Advanced Feed - Post interaction:'; postId, action)}
                />
    </div>
            </TabsContent>

            <TabsContent value="workers">
              <OptimizedDemoTab
                tabType="workers"
                data={demoData} config={config}
              />
    </TabsContent>
            <TabsContent value="collab" className="mt-6">
              <ErrorBoundary FallbackComponent={ErrorFallback}>
                <Suspense fallback={<LoadingFallback name="Real-Time Collaboration" />}>
                  <RealTimeCollaborationDemo />
    </Suspense>
              </ErrorBoundary>
    </TabsContent>
            <TabsContent value="search" className="mt-6">
              <ErrorBoundary FallbackComponent={ErrorFallback}>
                <Suspense fallback={<LoadingFallback name="Fuzzy Search Demo" />}>
                  <FuzzySearchDemo posts={demoData} />
    </Suspense>
              </ErrorBoundary>
    </TabsContent>
            <TabsContent value="performance">
              <OptimizedDemoTab
                tabType="performance"
                data={demoData} config={config}
              />
    </TabsContent>
            <TabsContent value="realtime" className="mt-6">
              <ErrorBoundary FallbackComponent={ErrorFallback}>
                <Suspense fallback={<LoadingFallback name="Real-Time Dashboard" />}>
                  <RealTimePerformanceDashboard />
    </Suspense>
              </ErrorBoundary>
    </TabsContent>
            <TabsContent value="activity" className="mt-6">
              <div className="grid gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Activity Summary</CardTitle>
    </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex justify-between items-center">
                        <span>Posts loaded</span>
                        <Badge>{demoData.length}</Badge>
    </div>
                      <div className="flex justify-between items-center">
                        <span>Current mode</span>
                        <Badge variant="secondary">{config.mode}</Badge>
    </div>
                      <div className="flex justify-between items-center">
                        <span>Features enabled</span>
                        <Badge>{Object.values(config.features).filter(Boolean).length}</Badge>
    </div>
                      <div className="flex justify-between items-center">
                        <span>Config changes</span>
                        <Badge variant="outline">{history.length}</Badge>
    </div>
                    </div>
    </CardContent>
                </Card>

                {config.features.performanceMonitoring && (
                  <Card>
                    <CardHeader>
                      <CardTitle>Performance Summary</CardTitle>
    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="text-center">
                          <div className="text-2xl font-bold">{getPerformanceScore()}</div>
                          <div className="text-sm text-muted-foreground">Performance Score</div>
    </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold">{performanceMetrics.fps}</div>
                          <div className="text-sm text-muted-foreground">FPS</div>
    </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold">{performanceMetrics.memoryUsage.toFixed(1)}</div>
                          <div className="text-sm text-muted-foreground">Memory (MB)</div>
    </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold">{performanceMetrics.renderTime.toFixed(1)}</div>
                          <div className="text-sm text-muted-foreground">Render (ms)</div>
    </div>
                      </div>
    </CardContent>
                  </Card>
                )}
              </div>
    </TabsContent>
            <TabsContent value="cache" className="mt-6">
              <div className="grid gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Database className="h-5 w-5" />
                      Intelligent Cache Analytics
                    </CardTitle>
    </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                      <div className="text-center">
                        <div className="text-2xl font-bold">{cache.stats.totalEntries}</div>
                        <div className="text-sm text-muted-foreground">Cached Items</div>
    </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold">{cache.stats.totalSize.toFixed(1)}</div>
                        <div className="text-sm text-muted-foreground">Size (MB)</div>
    </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold">{cache.stats.hitRate.toFixed(1)}</div>
                        <div className="text-sm text-muted-foreground">Hit Rate (%)</div>
    </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold">{cache.stats.evictions}</div>
                        <div className="text-sm text-muted-foreground">Evictions</div>
    </div>
                    </div>
                    
                    <div className="space-y-4">
                      <div>
                        <div className="flex items-center justify-between text-sm mb-2">
                          <span>Cache Efficiency</span>
                          <Badge variant={cache.stats.hitRate > 80 ? 'default' : 'destructive'}>
                            {cache.stats.hitRate > 80 ? 'Excellent' : 'Needs Optimization'}
                          </Badge>
    </div>
                        <Progress 
                          value={cache.stats.hitRate} className="h-2"
                        />
    </div>
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => cache.clear()}
                        >
                          Clear Cache
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => cache.cleanup()}
                        >
                          Run Cleanup
                        </Button>
    </div>
                    </div>
    </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Cache Items by Priority</CardTitle>
    </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {(['critical', 'high', 'medium', 'low'] as const).map(priority => {
                        const items = cache.getByPriority(priority);
                        return (
                          <div key={priority} className="flex justify-between items-center">
                            <span className="capitalize">{priority} Priority</span>
                            <Badge variant="outline">{items.length} items</Badge>
    </div>
                        );
                      })}
                    </div>
    </CardContent>
                </Card>
    </div>
            </TabsContent>
    </Tabs>
        </main>
    </div>
      {/* Modals */}
      <AnimatePresence>
        {isNotificationOpen && (
          <motion.div
            initial={{ opacity: 0, x: 300 }}, animate={{ opacity: 1, x: 0 }}, exit={{ opacity: 0, x: 300 }}, className="fixed right-0 top-0 h-full w-80 bg-card border-l shadow-lg z-50"
          >
            <ErrorBoundary FallbackComponent={ErrorFallback}>
              <Suspense fallback={<LoadingFallback name="Notifications" />}>
                <OptimizedNotificationCenter
                  isOpen={isNotificationOpen} onClose={() => setIsNotificationOpen(false)}
                />
    </Suspense>
            </ErrorBoundary>
          </motion.div>
        )}

        {isMessagingOpen && (
          <motion.div
            initial={{ opacity: 0, y: 50 }}, animate={{ opacity: 1, y: 0 }}, exit={{ opacity: 0, y: 50 }}, className="fixed bottom-4 right-4 w-96 h-96 z-50"
          >
            <ErrorBoundary FallbackComponent={ErrorFallback}>
              <Suspense fallback={<LoadingFallback name="Messaging" />}>
                <OptimizedMessagingInterface
                  conversations={[]} messages={[]}, currentUserId="demo-user"
                  onSendMessage={() => {}}, onSelectConversation={() => {}}, onMarkAsRead={() => {}}, onClose={() => setIsMessagingOpen(false)}
                />
    </Suspense>
            </ErrorBoundary>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Keyboard shortcuts modal */}
      <KeyboardShortcutsModal />
    </div>
  );
};

export default UnifiedOptimizedDemo;
