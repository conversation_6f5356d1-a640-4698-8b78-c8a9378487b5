import React from 'react';
import { Button, ButtonProps } from '@/components/ui/button';

interface AccessibleButtonProps extends ButtonProps {
  ariaLabel?: string;
  ariaDescribedBy?: string;
  ariaExpanded?: boolean;
  ariaControls?: string;
  ariaPressed?: boolean;
}

const AccessibleButton: React.FC<AccessibleButtonProps> = ({
  children,
  ariaLabel,
  ariaDescribedBy,
  ariaExpanded,
  ariaControls,
  ariaPressed,
  ...props
}) => {
  return (
    <Button
      aria-label={ariaLabel} aria-describedby={ariaDescribedBy} aria-expanded={ariaExpanded} aria-controls={ariaControls} aria-pressed={ariaPressed}
      {...props}
    >
      {children}
    </Button>
  );
};

export default AccessibleButton;