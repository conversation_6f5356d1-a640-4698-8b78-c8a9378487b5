import React, { memo, useMemo } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Rocket, Zap, BarChart3, Users, MessageCircle, Bell } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

// Memoized feature card component to prevent unnecessary re-renders
const FeatureCard = memo(({ feature }: { feature: any }) => (
  <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow duration-300">
    <CardHeader className="pb-3">
      <div className="flex items-center space-x-3">
        {feature.icon}
        <CardTitle className="text-lg">{feature.title}</CardTitle>
    </div>
    </CardHeader>
    <CardContent>
      <CardDescription>{feature.description}</CardDescription>
    </CardContent>
  </Card>
));

const OptimizedDemoLauncher: React.FC = () => {
  // Memoize features array to prevent recreation
  const features = useMemo(() => [
    {
      icon: <Zap className="w-6 h-6 text-yellow-500" />
        title: "60-80% Faster Renders",
      description: "Advanced memoization and optimization patterns"
    },
    {
      icon: <BarChart3 className="w-6 h-6 text-blue-500" />
        title: "Real-time Monitoring",
      description: "Live performance tracking and bottleneck detection"
    },
    {
      icon: <Users className="w-6 h-6 text-green-500" />
        title: "Virtualized Feeds",
      description: "Handle 10,000+ posts without performance loss"
    },
    {
      icon: <MessageCircle className="w-6 h-6 text-purple-500" />
        title: "Optimized Messaging",
      description: "Sub-millisecond message rendering and interactions"
    },
    {
      icon: <Bell className="w-6 h-6 text-red-500" />
        title: "Smart Notifications",
      description: "Intelligent notification processing and filtering"
    }
  ], []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
      <div className="container mx-auto px-4 py-12">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center space-x-2 mb-4">
            <Rocket className="w-10 h-10 text-blue-600" />
            <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Optimized React Demo
            </h1>
    </div>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Experience the power of advanced React optimization patterns with real-time performance monitoring
          </p>
          <Badge variant="secondary" className="mt-4 bg-green-100 text-green-800 px-4 py-1">
            🚀 Production Ready
          </Badge>
    </div>
        {/* Feature Grid - Optimized with memoized components */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          {features.map((feature, index) => (
            <FeatureCard key={feature.title} feature={feature} />
          ))}
        </div>

        {/* Demo Sections */}
        <div className="grid lg:grid-cols-2 gap-8 mb-12">
          {/* Optimized Demo */}
          <Card className="border-2 border-blue-200 shadow-xl">
            <CardHeader className="bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-t-lg">
              <div className="flex items-center space-x-3">
                <Zap className="w-8 h-8" />
                <div>
                  <CardTitle className="text-2xl">Optimized Demo</CardTitle>
                  <CardDescription className="text-blue-100">
                    Experience lightning-fast performance with advanced optimization
                  </CardDescription>
    </div>
              </div>
    </CardHeader>
            <CardContent className="p-6">
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    <span>Sub-5ms interactions</span>
    </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    <span>Virtualized scrolling</span>
    </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    <span>Real-time monitoring</span>
    </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    <span>Smart memoization</span>
    </div>
                </div>
                
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-semibold text-gray-800 mb-2">Includes:</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• 6 fully optimized components</li>
                    <li>• Performance monitoring dashboard</li>
                    <li>• Real-time render tracking</li>
                    <li>• Advanced search and filtering</li>
                    <li>• Infinite scroll optimization</li>
    </ul>
                </div>

                <Link to="/demo" className="block">
                  <Button className="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-semibold py-3 transition-all duration-200">
                    <Rocket className="w-5 h-5 mr-2" />
                    Launch Unified Demo
                  </Button>
    </Link>
              </div>
    </CardContent>
          </Card>

          {/* Original App */}
          <Card className="border-2 border-gray-200 shadow-lg">
            <CardHeader className="bg-gradient-to-r from-gray-500 to-gray-700 text-white rounded-t-lg">
              <div className="flex items-center space-x-3">
                <Users className="w-8 h-8" />
                <div>
                  <CardTitle className="text-2xl">Original App</CardTitle>
                  <CardDescription className="text-gray-100">
                    Access the full social media application
                  </CardDescription>
    </div>
              </div>
    </CardHeader>
            <CardContent className="p-6">
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                    <span>Complete features</span>
    </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                    <span>Full authentication</span>
    </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                    <span>All pages included</span>
    </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                    <span>Standard performance</span>
    </div>
                </div>

                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-semibold text-gray-800 mb-2">Features:</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• Complete social media platform</li>
                    <li>• User authentication system</li>
                    <li>• All original functionality</li>
                    <li>• Traditional React patterns</li>
                    <li>• Standard performance metrics</li>
    </ul>
                </div>

                <Link to="/home" className="block">
                  <Button variant="outline" className="w-full border-2 border-gray-300 hover:bg-gray-50 font-semibold py-3 transition-all duration-200">
                    <Users className="w-5 h-5 mr-2" />
                    Access Original App
                  </Button>
    </Link>
              </div>
    </CardContent>
          </Card>
    </div>
        {/* Performance Comparison */}
        <Card className="bg-white border-0 shadow-xl">
          <CardHeader className="text-center">
            <CardTitle className="text-2xl mb-2">Performance Comparison</CardTitle>
            <CardDescription>See the difference optimization makes</CardDescription>
    </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-3 gap-8 text-center">
              <div className="space-y-2">
                <div className="text-3xl font-bold text-green-600">60-80%</div>
                <div className="text-sm text-gray-600">Faster Rendering</div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-green-500 h-2 rounded-full" style={{ width: '80%' }}></div>
    </div>
              </div>
              <div className="space-y-2">
                <div className="text-3xl font-bold text-blue-600">40%</div>
                <div className="text-sm text-gray-600">Memory Reduction</div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-blue-500 h-2 rounded-full" style={{ width: '40%' }}></div>
    </div>
              </div>
              <div className="space-y-2">
                <div className="text-3xl font-bold text-purple-600">10x</div>
                <div className="text-sm text-gray-600">Data Handling</div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-purple-500 h-2 rounded-full" style={{ width: '100%' }}></div>
    </div>
              </div>
    </div>
          </CardContent>
    </Card>
        {/* Footer */}
        <div className="text-center mt-12 text-gray-600">
          <p className="text-sm">
            Built with React 18.3.1 • TypeScript • Advanced Optimization Patterns
          </p>
          <p className="text-xs mt-2">
            Performance monitoring active in development mode
          </p>
    </div>
      </div>
    </div>
  );
};

export default OptimizedDemoLauncher;
