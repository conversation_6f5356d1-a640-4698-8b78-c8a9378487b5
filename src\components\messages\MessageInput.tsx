import React, { useRef, forwardRef, useImperativeHandle, memo, useCallback } from 'react';
import { Smile, Paperclip, Mic, Send, X, Reply } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Message } from '@/types/enhanced-messaging';

interface MessageInputProps {
  value: string, onChange: (value: string) => void; onSend: () => void; onAttachFile: () => void;
  onSelectEmoji?: () => void;
  onStartRecording: () => void; onStopRecording: () => void; onCancelRecording: () => void; onDiscardRecording: () => void; onSendRecording: () => void; onPlayRecording: () => void; onPausePlayback: () => void; isRecording: boolean, hasRecording: boolean, isPlaying: boolean, recordingTime: number, playbackTime: number, isMobile: boolean;
  isKeyboardOpen?: boolean;
  isUploading?: boolean;
  replyingTo?: Message | null;
  onCancelReply?: () => void;
}

export interface MessageInputRef {
  focus: () => void;
}

const MessageInput = forwardRef<MessageInputRef, MessageInputProps>(({
  value,
  onChange,
  onSend,
  onAttachFile,
  onSelectEmoji,
  onStartRecording,
  onStopRecording,
  onCancelRecording,
  onDiscardRecording,
  onSendRecording,
  onPlayRecording,
  onPausePlayback,
  isRecording,
  hasRecording,
  isPlaying,
  recordingTime,
  playbackTime,
  isMobile,
  isKeyboardOpen = false,
  isUploading = false,
  replyingTo,
  onCancelReply
}, ref) => {
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  useImperativeHandle(ref, () => ({
    focus: () => {
      textareaRef.current?.focus();
    }
  }));

  const handleKeyPress = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      onSend();
    }
  }, [onSend]);

  const formatRecordingTime = useCallback((seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  }, []);

  const handleTextareaChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    onChange(e.target.value);
  }, [onChange]);

  return (
    <>
      {/* Reply indicator */}
      {replyingTo && (
        <div className="px-4 py-2 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Reply className="w-4 h-4 text-blue-500" />
              <span className="text-sm text-gray-600 dark:text-gray-400">
                Replying to: {replyingTo.content?.slice(0, 50)}...
              </span>
    </div>
            {onCancelReply && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onCancelReply}
              >
                <X className="w-3 h-3" />
    </Button>
            )}
          </div>
    </div>
      )}

      {/* Message Input Area */}
      <div className={`border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 ${
        isMobile ? 'p-4 pb-6' : 'p-4'
      } ${isKeyboardOpen && isMobile ? 'pb-2' : ''}`}>
        
        <div className={`flex gap-2 items-end ${isMobile ? 'gap-3' : ''}`}>
          {!hasRecording && (
            <div className={`flex gap-1 ${isMobile ? 'gap-2' : ''}`}>
              {onSelectEmoji && (
                <Button 
                  variant="ghost" 
                  size={isMobile ? "default" : "sm"} className={isMobile ? "h-10 w-10 p-0" : ""}, onClick={onSelectEmoji}
                >
                  <Smile className={`${isMobile ? 'w-5 h-5' : 'w-4 h-4'}`} />
    </Button>
              )}
              
              {/* File Attachment Button */}
              <Button 
                variant="ghost" 
                size={isMobile ? "default" : "sm"} className={isMobile ? "h-10 w-10 p-0" : ""}, onClick={onAttachFile} disabled={isUploading}, title="Attach file"
              >
                <Paperclip className={`${isMobile ? 'w-5 h-5' : 'w-4 h-4'}`} />
    </Button>
              {/* Voice Message Button */}
              <Button 
                variant="ghost" 
                size={isMobile ? "default" : "sm"} className={`${isMobile ? "h-10 w-10 p-0" : ""} ${
                  isRecording ? 'bg-red-100 text-red-600' : ''
                }`}, onClick={isRecording ? onStopRecording : onStartRecording} title={isRecording ? 'Stop recording' : 'Record voice message'}
              >
                <Mic className={`${isMobile ? 'w-5 h-5' : 'w-4 h-4'} ${
                  isRecording ? 'animate-pulse' : ''
                }`} />
    </Button>
            </div>
          )}
          
          <div className="flex-1 relative">
            {isRecording ? (
              // Voice Recording Indicator
              <div className={`flex items-center justify-between p-3 bg-red-50 border border-red-200 rounded-2xl`}>
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
                  <span className="text-red-600 font-medium">
                    Recording... {formatRecordingTime(recordingTime)}
                  </span>
    </div>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={onCancelRecording} className="text-red-600 hover:bg-red-100"
                  >
                    Cancel
                  </Button>
                  <Button
                    size="sm"
                    onClick={onStopRecording} className="bg-blue-500 hover:bg-blue-600 text-white"
                  >
                    Stop
                  </Button>
    </div>
              </div>
            ) : hasRecording ? (
              // Voice Recording Preview
              <div className={`flex items-center justify-between p-3 bg-blue-50 border border-blue-200 rounded-2xl`}>
                <div className="flex items-center space-x-3">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={isPlaying ? onPausePlayback : onPlayRecording} className="text-blue-600 hover:bg-blue-100 p-1"
                  >
                    {isPlaying ? (
                      <div className="w-3 h-3 bg-blue-600 rounded-sm"></div>
                    ) : (
                      <div className="w-0 h-0 border-l-[6px] border-l-blue-600 border-t-[3px] border-t-transparent border-b-[3px] border-b-transparent ml-1"></div>
                    )}
                  </Button>
                  <span className="text-blue-600 font-medium">
                    Voice message {formatRecordingTime(playbackTime)}/{formatRecordingTime(recordingTime)}
                  </span>
    </div>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={onDiscardRecording} className="text-red-600 hover:bg-red-100"
                  >
                    Discard
                  </Button>
                  <Button
                    size="sm"
                    onClick={onSendRecording} className="bg-green-500 hover:bg-green-600 text-white"
                  >
                    Send
                  </Button>
    </div>
              </div>
            ) : (
              // Normal Message Input
              <>
                <Textarea
                  ref={textareaRef} value={value}, onChange={handleTextareaChange} onKeyPress={handleKeyPress}, placeholder="Type a message..."
                  className={`resize-none ${
                    isMobile 
                      ? 'min-h-12 max-h-32 text-base px-4 py-3 pr-14 rounded-2xl' 
                      : 'min-h-10 max-h-32 pr-12 rounded-lg'
                  }`}, rows={1} style={{
                    fontSize: isMobile ? '16px' : '14px', // Prevents zoom on iOS
                  }}
                />
                <button
                  type="button"
                  onClick={onSend} disabled={!value.trim()}, className={`absolute right-2 top-1/2 transform -translate-y-1/2 z-10 ${
                    isMobile 
                      ? 'h-10 w-10' 
                      : 'h-8 w-8'
                  }, rounded-full bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 text-white disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center justify-center shadow-lg border-none outline-none focus:ring-2 focus:ring-blue-300 focus:ring-offset-2`}, style={{
                    background: !value.trim() ? '#9CA3AF' : '#3B82F6'
                  }}
                >
                  <Send className={`${isMobile ? 'w-5 h-5' : 'w-4 h-4'}, text-white`} />
    </button>
              </>
            )}
          </div>
    </div>
      </div>
    </>
  );
});

MessageInput.displayName = 'MessageInput';

export default memo(MessageInput);
