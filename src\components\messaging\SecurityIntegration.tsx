import React, { useEffect, useState } from 'react';
import { Shield, AlertTriangle, Ban, Clock, CheckCircle } from 'lucide-react';
import { useMessageSecurity } from '../../hooks/useMessageSecurity';
import { useMessagingStore } from '../../store/messagingStore';

interface SecurityIntegrationProps {
  userId: string, children: React.ReactNode;
  showSecurityStatus?: boolean;
  className?: string;
}

/**
 * Security integration wrapper that provides security context to messaging components
 */
export const SecurityIntegration: React.FC<SecurityIntegrationProps> = ({
  userId,
  children,
  showSecurityStatus = true,
  className = ''
}) => {
  const [securityInitialized, setSecurityInitialized] = useState(false);
  const [lastSecurityCheck, setLastSecurityCheck] = useState<Date | null>(null);

  const {
    securityStatus,
    getRateLimitStatus,
    isUserBlocked;
    updateSecurityStatus
  } = useMessageSecurity(userId);

  const { setError, clearErrors } = useMessagingStore();

  // Initialize security monitoring
  useEffect(() => {
    const initializeSecurity = async () => {
      try {
        // Update security status
        updateSecurityStatus();
        setSecurityInitialized(true);
        setLastSecurityCheck(new Date());
      } catch (error) {
        console.error('Failed to initialize security:', error);
        setError('security', 'Failed to initialize security monitoring');
      }
    };

    initializeSecurity();
  }, [userId, updateSecurityStatus, setError]);

  // Periodic security checks
  useEffect(() => {
    if (!securityInitialized) return;

    const interval = setInterval(() => {
      updateSecurityStatus();
      setLastSecurityCheck(new Date());

      // Check if user is blocked and update store accordingly
      const blocked = isUserBlocked();
      if (blocked.blocked) {
        setError('security', `Account restricted: ${blocked.actions.join(', ')}`);
      } else {
        clearErrors();
      }
    }, 30000); // Check every 30 seconds

    return () => clearInterval(interval);
  }, [securityInitialized, updateSecurityStatus, isUserBlocked, setError, clearErrors]);

  // Monitor rate limits and show warnings
  useEffect(() => {
    const rateLimits = getRateLimitStatus();
    const warnings: string[] = [];

    Object.entries(rateLimits).forEach(([action, status]) => {
      if (!status.allowed) {
        warnings.push(`${action} rate limit exceeded`);
      } else if (status.remaining < 5) {
        warnings.push(`${action} rate limit low (${status.remaining} remaining)`);
      }
    });

    if (warnings.length > 0) {
      setError('rateLimits', warnings.join(', '));
    }
  }, [getRateLimitStatus, setError]);

  const getSecurityStatusIcon = () => {
    if (!securityInitialized) {
      return <Clock className="w-4 h-4 text-gray-400 animate-spin" />;
    }

    if (!securityStatus.isSecure) {
      return <Ban className="w-4 h-4 text-red-500" />;
    }

    if (securityStatus.warnings.length > 0) {
      return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
    }

    return <Shield className="w-4 h-4 text-green-500" />;
  };

  const getSecurityStatusText = () => {
    if (!securityInitialized) {
      return 'Initializing security...';
    }

    if (!securityStatus.isSecure) {
      return 'Security issues detected';
    }

    if (securityStatus.warnings.length > 0) {
      return 'Security warnings';
    }

    return 'Secure';
  };

  const getSecurityStatusColor = () => {
    if (!securityInitialized) {
      return 'text-gray-500 bg-gray-50 border-gray-200';
    }

    if (!securityStatus.isSecure) {
      return 'text-red-600 bg-red-50 border-red-200';
    }

    if (securityStatus.warnings.length > 0) {
      return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    }

    return 'text-green-600 bg-green-50 border-green-200';
  };

  return (
    <div className={`security-integration ${className}`}>
      {/* Security Status Bar */}
      {showSecurityStatus && (
        <div className={`flex items-center gap-2 px-3 py-2 mb-3 rounded-lg border text-sm ${getSecurityStatusColor()}`}>
          {getSecurityStatusIcon()}
          <span className="font-medium">{getSecurityStatusText()}</span>
          
          {lastSecurityCheck && (
            <span className="ml-auto text-xs opacity-75">
              Last check: {lastSecurityCheck.toLocaleTimeString()}
            </span>
          )}
        </div>
      )}

      {/* Security Errors */}
      {securityStatus.errors.length > 0 && (
        <div className="mb-3 space-y-1">
          {securityStatus.errors.map((error, index) => (
            <div key={index} className="flex items-center gap-2 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700">
              <AlertTriangle className="w-4 h-4 flex-shrink-0" />
              <span>{error}</span>
    </div>
          ))}
        </div>
      )}

      {/* Security Warnings */}
      {securityStatus.warnings.length > 0 && (
        <div className="mb-3 space-y-1">
          {securityStatus.warnings.map((warning, index) => (
            <div key={index} className="flex items-center gap-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-sm text-yellow-700">
              <AlertTriangle className="w-4 h-4 flex-shrink-0" />
              <span>{warning}</span>
    </div>
          ))}
        </div>
      )}

      {/* Main Content */}
      <div className={`messaging-content ${!securityInitialized ? 'opacity-50 pointer-events-none' : ''}`}>
        {children}
      </div>

      {/* Security Overlay for Blocked Users */}
      {securityInitialized && !securityStatus.isSecure && (
        <div className="absolute inset-0 bg-red-50 bg-opacity-90 flex items-center justify-center z-50 rounded-lg">
          <div className="text-center p-6">
            <Ban className="w-12 h-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-red-700 mb-2">
              Account Temporarily Restricted
            </h3>
            <p className="text-red-600 mb-4">
              Your account has been temporarily restricted due to security concerns.
            </p>
            <div className="text-sm text-red-500">
              {securityStatus.errors.map((error, index) => (
                <div key={index}>{error}</div>
              ))}
            </div>
    </div>
        </div>
      )}
    </div>
  );
};

/**
 * Hook to provide security context to child components
 */
export const useSecurityContext = (userId: string) => {
  const security = useMessageSecurity(userId);
  const { setError, clearErrors } = useMessagingStore();

  const validateAndSanitizeMessage = async (content: string) => {
    const result = await security.validateMessage(content);
    
    if (!result.allowed) {
      setError('messageValidation', result.errors.join(', '));
      return null;
    }

    if (result.warnings.length > 0) {
      // Show warnings but allow message
      console.warn('Message warnings:', result.warnings);
    }

    return result.sanitizedContent || content;
  };

  const validateReaction = (emoji: string) => {
    const result = security.validateReaction(emoji);
    
    if (!result.allowed) {
      setError('reactionValidation', result.errors.join(', '));
      return null;
    }

    return result.sanitizedContent || emoji;
  };

  const validateFileUpload = async (files: File[]) => {
    const result = await security.validateFileUploads(files);
    
    if (!result.allowed) {
      setError('fileValidation', result.errors.join(', '));
      return null;
    }

    if (result.warnings.length > 0) {
      console.warn('File upload warnings:', result.warnings);
    }

    return files;
  };

  return {
    ...security,
    validateAndSanitizeMessage,
    validateReaction,
    validateFileUpload,
    clearSecurityErrors: () => clearErrors()
  };
};

export default SecurityIntegration;