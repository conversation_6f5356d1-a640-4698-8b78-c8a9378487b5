import React, { memo } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { RefreshCw, ArrowUp } from 'lucide-react';

interface FeedHeaderProps {
  isLoading: boolean, onRefresh: () => void; showScrollToTop: boolean, onScrollToTop: () => void;
  newPostsCount?: number;
  className?: string;
}

const FeedHeader: React.FC<FeedHeaderProps> = memo(({
  isLoading,
  onRefresh,
  showScrollToTop,
  onScrollToTop,
  newPostsCount = 0,
  className = ''
}) => {
  return (
    <div className={`flex items-center justify-between mb-6 ${className}`}>
      <div className="flex items-center gap-4">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          News Feed
        </h1>
        
        {newPostsCount > 0 && (
          <motion.div
            initial={{ scale: 0 }}, animate={{ scale: 1 }}, className="flex items-center gap-2"
          >
            <Badge variant="secondary" className="bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300">
              {newPostsCount} new post{newPostsCount !== 1 ? 's' : ''}
            </Badge>
            <Button
              size="sm"
              variant="outline"
              onClick={onRefresh} className="text-blue-600 border-blue-200 hover:bg-blue-50 dark:text-blue-400 dark:border-blue-800 dark:hover:bg-blue-900/20"
            >
              <RefreshCw className="w-4 h-4 mr-1" />
              Load New
            </Button>
          </motion.div>
        )}
      </div>

      <div className="flex items-center gap-2">
        {showScrollToTop && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}, animate={{ opacity: 1, scale: 1 }}, exit={{ opacity: 0, scale: 0.8 }}
          >
            <Button
              size="sm"
              variant="outline"
              onClick={onScrollToTop} className="shadow-lg"
            >
              <ArrowUp className="w-4 h-4" />
    </Button>
          </motion.div>
        )}
        
        <Button
          size="sm"
          variant="outline"
          onClick={onRefresh} disabled={isLoading}, className="shadow-sm"
        >
          <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
    </div>
    </div>
  );
});

FeedHeader.displayName = 'FeedHeader';

export default FeedHeader;
