import React, { useState, useEffect, useMemo } from 'react';
import { useSearch } from '@/hooks/useSearch';
import {
  BarChart3,
  TrendingUp,
  Search,
  Clock,
  Users,
  Hash,
  MapPin,
  Calendar,
  Download,
  Filter,
  Eye,
  MousePointer
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';

interface SearchAnalytic {
  id: string, query: string, timestamp: Date, resultCount: number, clickThrough: boolean, contentType: string, device: 'desktop' | 'mobile' | 'tablet';
  location?: string;
  sessionId: string;
}

interface TrendingSearch {
  query: string, count: number, trend: 'up' | 'down' | 'stable', percentage: number;
}

interface SearchInsight {
  totalSearches: number, uniqueQueries: number, avgResultsPerSearch: number, clickThroughRate: number, topCategories: { category: string, count: number, percentage: number }[];
  deviceBreakdown: { device: string, count: number, percentage: number }[];
  timeDistribution: { hour: number, count: number }[];
}

const SearchAnalytics: React.FC = () => {
  const { searchAnalytics } = useSearch();
  const [timeRange, setTimeRange] = useState<'today' | 'week' | 'month' | 'year'>('week');
  const [insights, setInsights] = useState<SearchInsight | null>(null);

  // Mock analytics data
  const mockSearchHistory: SearchAnalytic[] = useMemo(() => [
    {
      id: '1',
      query: 'React development tips',
      timestamp: new Date(Date.now() - 3600000),
      resultCount: 45,
      clickThrough: true,
      contentType: 'posts',
      device: 'desktop',
      location: 'San Francisco, CA',
      sessionId: 'session_1'
    },
    {
      id: '2',
      query: 'Sarah Johnson',
      timestamp: new Date(Date.now() - 7200000),
      resultCount: 3,
      clickThrough: true,
      contentType: 'people',
      device: 'mobile',
      sessionId: 'session_1'
    },
    {
      id: '3',
      query: 'JavaScript community',
      timestamp: new Date(Date.now() - 10800000),
      resultCount: 23,
      clickThrough: false,
      contentType: 'groups',
      device: 'desktop',
      location: 'New York, NY',
      sessionId: 'session_2'
    },
    {
      id: '4',
      query: '#webdevelopment',
      timestamp: new Date(Date.now() - 14400000),
      resultCount: 156,
      clickThrough: true,
      contentType: 'hashtags',
      device: 'tablet',
      sessionId: 'session_2'
    },
    {
      id: '5',
      query: 'Tech meetup 2024',
      timestamp: new Date(Date.now() - 18000000),
      resultCount: 8,
      clickThrough: true,
      contentType: 'events',
      device: 'mobile',
      location: 'Austin, TX',
      sessionId: 'session_3'
    }
  ], []);

  const trendingSearches: TrendingSearch[] = [
    { query: 'React development', count: 1250, trend: 'up', percentage: 25 },
    { query: 'JavaScript tutorials', count: 980, trend: 'up', percentage: 15 },
    { query: 'Tech events 2024', count: 756, trend: 'stable', percentage: 0 },
    { query: 'Web design trends', count: 643, trend: 'down', percentage: -12 },
    { query: 'Programming jobs', count: 589, trend: 'up', percentage: 8 },
    { query: 'AI development', count: 445, trend: 'up', percentage: 45 },
    { query: 'Frontend frameworks', count: 398, trend: 'stable', percentage: 2 },
    { query: 'Open source projects', count: 332, trend: 'up', percentage: 18 }
  ];

  const generateInsights = useMemo(() => {
    // Use context data or fallback to mock data
    const data = searchAnalytics.length > 0 ? searchAnalytics : mockSearchHistory;
    const totalSearches = data.length;
    
    if (totalSearches === 0) {
      return {
        totalSearches: 0,
        uniqueQueries: 0,
        avgResultsPerSearch: 0,
        clickThroughRate: 0,
        topCategories: [],
        deviceBreakdown: [],
        timeDistribution: Array.from({ length: 24 }, (_, hour) => ({ hour, count: 0 }))
      };
    }
    
    const uniqueQueries = new Set(data.map(s => s.query)).size;
    const avgResults = data.reduce((sum, s) => sum + s.resultCount; 0) / totalSearches;
    const clickThroughs = data.filter(s => s.clickThrough).length;
    const clickThroughRate = (clickThroughs / totalSearches) * 100;

    // Category breakdown
    const categoryMap = data.reduce((acc, search) => {
      acc[search.contentType] = (acc[search.contentType] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const topCategories = Object.entries(categoryMap).map(([category, count]) => ({
      category,
      count,
      percentage: (count / totalSearches) * 100
    })).sort((a, b) => b.count - a.count);

    // Device breakdown
    const deviceMap = data.reduce((acc, search) => {
      acc[search.device] = (acc[search.device] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const deviceBreakdown = Object.entries(deviceMap).map(([device, count]) => ({
      device,
      count,
      percentage: (count / totalSearches) * 100
    }));

    // Time distribution (by hour)
    const timeMap = data.reduce((acc, search) => {
      const hour = search.timestamp.getHours();
      acc[hour] = (acc[hour] || 0) + 1;
      return acc;
    }, {} as Record<number, number>);

    const timeDistribution = Array.from({ length: 24 }, (_, hour) => ({
      hour,
      count: timeMap[hour] || 0
    }));

    return {
      totalSearches,
      uniqueQueries,
      avgResultsPerSearch: Math.round(avgResults),
      clickThroughRate: Math.round(clickThroughRate),
      topCategories,
      deviceBreakdown,
      timeDistribution
    };
  }, [searchAnalytics, mockSearchHistory]);

  useEffect(() => {
    setInsights(generateInsights);
  }, [generateInsights]);

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  const getContentTypeIcon = (type: string) => {
    switch (type) {
      case 'posts': return Search;
      case 'people': return Users;
      case 'groups': return Users;
      case 'hashtags': return Hash;
      case 'events': return Calendar;
      case 'locations': return MapPin;
      default: return Search;
    }
  };

  const getDeviceIcon = (device: string) => {
    switch (device) {
      case 'desktop': return '🖥️';
      case 'mobile': return '📱';
      case 'tablet': return '📱';
      default: return '💻';
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return <TrendingUp className="w-4 h-4 text-green-500" />;
      case 'down': return <TrendingUp className="w-4 h-4 text-red-500 rotate-180" />;
      default: return <div className="w-4 h-4 bg-gray-400 rounded-full" />;
    }
  };

  const exportData = () => {
    const data = {
      insights,
      searchHistory: searchAnalytics.length > 0 ? searchAnalytics : mockSearchHistory,
      trendingSearches,
      exportDate: new Date().toISOString()
    };
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `search-analytics-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  if (!insights) return <div>Loading analytics...</div>;

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
            <BarChart3 className="w-6 h-6 mr-2" />
            Search Analytics
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Insights into search behavior and performance
          </p>
    </div>
        <div className="flex items-center space-x-3">
          {/* Time Range Selector */}
          <div className="flex items-center space-x-1">
            {['today', 'week', 'month', 'year'].map((range) => (
              <Button
                key={range} variant={timeRange === range ? 'default' : 'outline'}, size="sm"
                onClick={() => setTimeRange(range as 'today' | 'week' | 'month' | 'year')} className="capitalize"
              >
                {range === 'today' ? 'Today' : `Past ${range}`}
              </Button>
            ))}
          </div>
          <Button onClick={exportData} variant="outline" size="sm">
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
    </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Total Searches</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {insights.totalSearches.toLocaleString()}
                </p>
    </div>
              <Search className="w-8 h-8 text-blue-500" />
    </div>
          </CardContent>
    </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Unique Queries</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {insights.uniqueQueries}
                </p>
    </div>
              <Hash className="w-8 h-8 text-green-500" />
    </div>
          </CardContent>
    </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Avg Results</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {insights.avgResultsPerSearch}
                </p>
    </div>
              <Eye className="w-8 h-8 text-purple-500" />
    </div>
          </CardContent>
    </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Click Rate</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {insights.clickThroughRate}%
                </p>
    </div>
              <MousePointer className="w-8 h-8 text-orange-500" />
    </div>
          </CardContent>
    </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Trending Searches */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <TrendingUp className="w-5 h-5 mr-2" />
              Trending Searches
            </CardTitle>
    </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {trendingSearches.slice(0, 8).map((search, index) => (
                <div key={search.query} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <span className="text-sm font-medium text-gray-500 w-6">
                      #{index + 1}
                    </span>
                    <div>
                      <p className="font-medium text-gray-900 dark:text-white">
                        {search.query}
                      </p>
                      <p className="text-sm text-gray-500">
                        {search.count.toLocaleString()} searches
                      </p>
    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {getTrendIcon(search.trend)}
                    <span className={`text-sm font-medium ${
                      search.trend === 'up' ? 'text-green-500' :
                      search.trend === 'down' ? 'text-red-500' : 'text-gray-500'
                    }`}>
                      {search.percentage > 0 ? '+' : ''}{search.percentage}%
                    </span>
    </div>
                </div>
              ))}
            </div>
    </CardContent>
        </Card>

        {/* Content Type Distribution */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Filter className="w-5 h-5 mr-2" />
              Search Categories
            </CardTitle>
    </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {insights.topCategories.map((category) => {
                const Icon = getContentTypeIcon(category.category);
                return (
                  <div key={category.category}>
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        <Icon className="w-4 h-4 text-gray-500" />
                        <span className="font-medium text-gray-900 dark:text-white capitalize">
                          {category.category}
                        </span>
    </div>
                      <div className="flex items-center space-x-2">
                        <span className="text-sm text-gray-500">
                          {category.count} searches
                        </span>
                        <Badge variant="secondary">
                          {Math.round(category.percentage)}%
                        </Badge>
    </div>
                    </div>
                    <Progress value={category.percentage} className="h-2" />
    </div>
                );
              })}
            </div>
    </CardContent>
        </Card>

        {/* Device Breakdown */}
        <Card>
          <CardHeader>
            <CardTitle>Device Usage</CardTitle>
    </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {insights.deviceBreakdown.map((device) => (
                <div key={device.device}>
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <span className="text-lg">{getDeviceIcon(device.device)}</span>
                      <span className="font-medium text-gray-900 dark:text-white capitalize">
                        {device.device}
                      </span>
    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-gray-500">
                        {device.count} searches
                      </span>
                      <Badge variant="secondary">
                        {Math.round(device.percentage)}%
                      </Badge>
    </div>
                  </div>
                  <Progress value={device.percentage} className="h-2" />
    </div>
              ))}
            </div>
    </CardContent>
        </Card>

        {/* Recent Search History */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Clock className="w-5 h-5 mr-2" />
              Recent Searches
            </CardTitle>
    </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {(searchAnalytics.length > 0 ? searchAnalytics : mockSearchHistory).slice(0, 10).map((search) => {
                const Icon = getContentTypeIcon(search.contentType);
                return (
                  <div key={search.id} className="flex items-start space-x-3 p-2 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg">
                    <Icon className="w-4 h-4 text-gray-500 mt-1 flex-shrink-0" />
                    <div className="flex-1 min-w-0">
                      <p className="font-medium text-gray-900 dark:text-white truncate">
                        {search.query}
                      </p>
                      <div className="flex items-center space-x-4 text-xs text-gray-500 mt-1">
                        <span>{formatDate(search.timestamp)}</span>
                        <span>{search.resultCount} results</span>
                        <span className="capitalize">{search.contentType}</span>
                        <span>{getDeviceIcon(search.device)}</span>
                        {search.clickThrough && (
                          <Badge variant="secondary" className="text-xs">
                            Clicked
                          </Badge>
                        )}
                      </div>
                      {search.location && (
                        <div className="text-xs text-gray-400 mt-1 flex items-center">
                          <MapPin className="w-3 h-3 mr-1" />
                          {search.location}
                        </div>
                      )}
                    </div>
    </div>
                );
              })}
            </div>
    </CardContent>
        </Card>
    </div>
      {/* Search Activity Timeline */}
      <Card>
        <CardHeader>
          <CardTitle>Search Activity by Hour</CardTitle>
    </CardHeader>
        <CardContent>
          <div className="flex items-end space-x-1 h-32">
            {insights.timeDistribution.map((time) => (
              <div key={time.hour} className="flex-1 flex flex-col items-center">
                <div
                  className="w-full bg-blue-500 rounded-t"
                  style={{
                    height: `${(time.count / Math.max(...insights.timeDistribution.map(t => t.count)) || 1) * 100}%`,
                    minHeight: time.count > 0 ? '4px' : '0px'
                  }}
                />
                <span className="text-xs text-gray-500 mt-1">
                  {time.hour}h
                </span>
    </div>
            ))}
          </div>
    </CardContent>
      </Card>
    </div>
  );
};

export default SearchAnalytics;