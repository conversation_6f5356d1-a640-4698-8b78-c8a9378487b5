import React, { useEffect, useState } from 'react';
import { Notification } from '../../types/notification';
import MessagingNotificationService from '../../services/messaging/MessagingNotificationService';

interface MessagingNotificationIntegrationProps {
  children: React.ReactNode;
}

const MessagingNotificationIntegration: React.FC<MessagingNotificationIntegrationProps> = ({
  children
}) => {
  const [notificationService] = useState(() => MessagingNotificationService.getInstance());
  const [notifications, setNotifications] = useState<Notification[]>([]);

  // Set up notification listeners
  useEffect(() => {
    const handleMessagingNotification = (event: CustomEvent<Notification>) => {
      const notification = event.detail;
      setNotifications(prev => [notification, ...prev.slice(0, 49)]); // Keep last 50
      
      // Forward to global notification system
      const globalEvent = new CustomEvent('global-notification', {
        detail: notification
      });
      window.dispatchEvent(globalEvent);
    };

    const handleGlobalNotification = (event: CustomEvent<Notification>) => {
      const notification = event.detail;
      
      // Only handle messaging-related notifications
      if (notification.type === 'mention' || 
          notification.metadata?.conversationId ||
          notification.metadata?.messageId) {
        
        // Add to messaging notifications if not already present
        setNotifications(prev => {
          const exists = prev.some(n => n.id === notification.id);
          if (!exists) {
            return [notification, ...prev.slice(0, 49)];
          }
          return prev;
        });
      }
    };

    // Listen for messaging notifications
    window.addEventListener('messaging-notification', handleMessagingNotification as EventListener);
    window.addEventListener('global-notification', handleGlobalNotification as EventListener);

    return () => {
      window.removeEventListener('messaging-notification', handleMessagingNotification as EventListener);
      window.removeEventListener('global-notification', handleGlobalNotification as EventListener);
    };
  }, []);

  // Set up notification permission handling
  useEffect(() => {
    const checkPermission = async () => {
      const status = notificationService.getNotificationPermissionStatus();
      
      if (status === 'default') {
        // Show permission request after a delay
        setTimeout(async () => {
          const granted = await notificationService.requestPermission();
          if (granted) {
            if (window.toast) {
              window.toast.success('Desktop notifications enabled for messages');
            }
          }
        }, 3000);
      }
    };

    checkPermission();
  }, [notificationService]);

  // Handle notification clicks
  useEffect(() => {
    const handleNotificationClick = (event: CustomEvent<{ notificationId: string }>) => {
      const { notificationId } = event.detail;
      const notification = notifications.find(n => n.id === notificationId);
      
      if (notification && notification.link) {
        // Navigate to the message/conversation
        window.location.href = notification.link;
        
        // Mark as read
        setNotifications(prev => 
          prev.map(n => 
            n.id === notificationId ? { ...n, isRead: true } : n
          )
        );
      }
    };

    window.addEventListener('notification-click', handleNotificationClick as EventListener);
    
    return () => {
      window.removeEventListener('notification-click', handleNotificationClick as EventListener);
    };
  }, [notifications]);

  // Provide notification context to children
  useEffect(() => {
    // Make notifications available globally for components
    (window as any).messagingNotifications = {
      notifications,
      markAsRead: (notificationId: string) => {
        setNotifications(prev => 
          prev.map(n => 
            n.id === notificationId ? { ...n, isRead: true } : n
          )
        );
      },
      markAllAsRead: () => {
        setNotifications(prev => prev.map(n => ({ ...n, isRead: true })));
      },
      clear: (notificationId: string) => {
        setNotifications(prev => prev.filter(n => n.id !== notificationId));
      }, clearAll: () => {
        setNotifications([]);
      },
      getUnreadCount: () => {
        return notifications.filter(n => !n.isRead).length;
      }
    };

    return () => {
      delete (window as any).messagingNotifications;
    };
  }, [notifications]);

  // Handle app visibility changes
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        // Mark recent notifications as read when user returns to app
        const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;
        setNotifications(prev => 
          prev.map(n => {
            const notificationTime = new Date(n.timestamp).getTime();
            if (notificationTime > fiveMinutesAgo && !n.isRead) {
              return { ...n, isRead: true };
            }
            return n;
          })
        );
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  // Handle notification settings changes
  useEffect(() => {
    const handleSettingsChange = (event: CustomEvent<{ settings: any }>) => {
      const { settings } = event.detail;
      notificationService.updateSettings(settings);
      
      if (window.toast) {
        window.toast.success('Notification settings updated');
      }
    };

    window.addEventListener('messaging-settings-change', handleSettingsChange as EventListener);
    
    return () => {
      window.removeEventListener('messaging-settings-change', handleSettingsChange as EventListener);
    };
  }, [notificationService]);

  // Cleanup old notifications
  useEffect(() => {
    const cleanup = setInterval(() => {
      const oneDayAgo = Date.now() - 24 * 60 * 60 * 1000;
      setNotifications(prev => 
        prev.filter(n => new Date(n.timestamp).getTime() > oneDayAgo)
      );
    }, 60 * 60 * 1000); // Run every hour

    return () => clearInterval(cleanup);
  }, []);

  // Provide notification badge count
  useEffect(() => {
    const unreadCount = notifications.filter(n => !n.isRead).length;
    
    // Update document title with unread count
    const baseTitle = document.title.replace(/^\(\d+\) /, '');
    document.title = unreadCount > 0 ? `(${unreadCount}) ${baseTitle}` : baseTitle;
    
    // Update favicon badge (if supported)
    if ('setAppBadge' in navigator) {
      (navigator as any).setAppBadge(unreadCount);
    }
    
    // Emit event for other components
    const event = new CustomEvent('messaging-unread-count', {
      detail: { count: unreadCount }
    });
    window.dispatchEvent(event);
  }, [notifications]);

  return (
    <>
      {children}
      
      {/* Debug panel for development */}
      {process.env.NODE_ENV === 'development' && (
        <div className="fixed bottom-4 right-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg p-4 max-w-sm max-h-64 overflow-y-auto z-50">
          <div className="text-sm font-medium text-gray-900 dark:text-white mb-2">
            Messaging Notifications ({notifications.length})
          </div>
          <div className="space-y-2">
            {notifications.slice(0, 5).map(notification => (
              <div
                key={notification.id} className={`text-xs p-2 rounded ${
                  notification.isRead 
                    ? 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400' 
                    : 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200'
                }`}
              >
                <div className="font-medium">{notification.title}</div>
                <div className="truncate">{notification.message}</div>
                <div className="text-xs opacity-75">
                  {new Date(notification.timestamp).toLocaleTimeString()}
                </div>
    </div>
            ))}
            {notifications.length === 0 && (
              <div className="text-xs text-gray-500 dark:text-gray-400">
                No notifications
              </div>
            )}
          </div>
          
          {notifications.length > 0 && (
            <div className="mt-2 pt-2 border-t border-gray-200 dark:border-gray-600">
              <button
                onClick={() => setNotifications([])} className="text-xs text-red-500 hover:text-red-700 underline"
              >
                Clear All
              </button>
    </div>
          )}
        </div>
      )}
    </>
  );
};

export default MessagingNotificationIntegration;