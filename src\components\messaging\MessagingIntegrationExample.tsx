import React, { useState } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { MessageCircle, Send, Users } from 'lucide-react';

interface MessagingIntegrationExampleProps {
  className?: string;
}

export const MessagingIntegrationExample: React.FC<MessagingIntegrationExampleProps> = ({
  className
}) => {
  const [message, setMessage] = useState('');
  const [messages, setMessages] = useState([
    { id: '1', text: 'Welcome to the messaging integration example!', sender: 'System', timestamp: new Date() },
    { id: '2', text: 'This demonstrates how messaging components work together.', sender: 'System', timestamp: new Date() }
  ]);

  const handleSendMessage = () => {
    if (message.trim()) {
      setMessages(prev => [...prev, {
        id: Date.now().toString(),
        text: message,
        sender: 'You',
        timestamp: new Date()
      }]);
      setMessage('');
    }
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MessageCircle className="w-5 h-5" />
          Messaging Integration Example
        </CardTitle>
    </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center gap-2">
          <Badge variant="secondary">
            <Users className="w-3 h-3 mr-1" />
            3 Online
          </Badge>
          <Badge variant="outline">Demo Mode</Badge>
    </div>
        <div className="h-64 border rounded-lg p-4 overflow-y-auto space-y-2">
          {messages.map((msg) => (
            <div key={msg.id} className="flex flex-col space-y-1">
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium">{msg.sender}</span>
                <span className="text-xs text-muted-foreground">
                  {msg.timestamp.toLocaleTimeString()}
                </span>
    </div>
              <div className="text-sm bg-muted p-2 rounded-lg max-w-xs">
                {msg.text}
              </div>
    </div>
          ))}
        </div>

        <div className="flex gap-2">
          <Input
            value={message} onChange={(e) => setMessage(e.target.value)}, placeholder="Type a message..."
            onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
          />
          <Button onClick={handleSendMessage} size="sm">
            <Send className="w-4 h-4" />
    </Button>
        </div>
    </CardContent>
    </Card>
  );
};

export default MessagingIntegrationExample;