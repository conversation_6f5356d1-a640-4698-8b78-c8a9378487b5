/**
 * VirtualizedMessageList - High-performance virtualized list for large message histories
 * Uses react-window for efficient rendering of thousands of messages
 */

import React, { 
  useCallback, 
  useMemo, 
  useRef, 
  useEffect, 
  useState,
  forwardRef,
  useImperativeHandle
} from 'react';
import { FixedSizeList as List, VariableSizeList, ListChildComponentProps } from 'react-window';
import { cn } from '@/lib/utils';
import { Loader2, ArrowDown } from 'lucide-react';

import { Message } from '@/types/enhanced-messaging';
import OptimizedMessageBubble from './OptimizedMessageBubble';
import { Button } from '@/components/ui/button';
import {
  useMessagingMemo,
  useStableCallback,
  useRenderPerformance
} from '@/utils/messagingMemoization';
import { performanceMonitoringService } from '@/services/messaging/PerformanceMonitoringService';

interface VirtualizedMessageListProps {
  messages: Message[], currentUserId: string;
  className?: string;
  height?: number;
  width?: number;
  itemHeight?: number;
  variableHeight?: boolean;
  overscan?: number;
  loading?: boolean;
  hasMore?: boolean;
  onLoadMore?: () => Promise<void>;
  onScrollToBottom?: () => void;
  onMessageAction?: {
    onReply?: (message: Message) => void;
    onEdit?: (message: Message) => void;
    onDelete?: (messageId: string) => void;
    onReaction?: (messageId: string, emoji: string) => void;
    onCopy?: (content: string) => void;
  };
  autoScrollToBottom?: boolean;
  scrollBehavior?: 'smooth' | 'auto';
}

interface VirtualizedMessageListRef {
  scrollToBottom: (behavior?: 'smooth' | 'auto') => void; scrollToMessage: (messageId: string, behavior?: 'smooth' | 'auto') => void;
  scrollToIndex: (index: number, behavior?: 'smooth' | 'auto') => void;
}

// Item data structure for react-window
interface ItemData {
  messages: Message[], currentUserId: string;
  onMessageAction?: VirtualizedMessageListProps['onMessageAction'];
}

// Memoized message item component for virtualization
const MessageItem = React.memo<ListChildComponentProps<ItemData>>(({ 
  index, 
  style, 
  data 
}) => {
  const { messages, currentUserId, onMessageAction } = data;
  const message = messages[index];
  
  if (!message) {
    return (
      <div style={style} className="flex items-center justify-center">
        <div className="text-sm text-gray-500">Message not found</div>
    </div>
    );
  }

  const isOwn = message.senderId === currentUserId;

  return (
    <div style={style} className="px-4">
      <OptimizedMessageBubble
        message={message} currentUserId={currentUserId}, isOwn={isOwn} onReply={onMessageAction?.onReply}, onEdit={onMessageAction?.onEdit} onDelete={onMessageAction?.onDelete}, onReaction={onMessageAction?.onReaction} onCopy={onMessageAction?.onCopy}
      />
    </div>
  );
}, (prevProps, nextProps) => {
  const prevMessage = prevProps.data.messages[prevProps.index];
  const nextMessage = nextProps.data.messages[nextProps.index];
  
  return (
    prevMessage?.id === nextMessage?.id &&
    prevMessage?.content === nextMessage?.content &&
    prevMessage?.status === nextMessage?.status &&
    prevMessage?.reactions?.length === nextMessage?.reactions?.length &&
    prevProps.data.currentUserId === nextProps.data.currentUserId
  );
});

MessageItem.displayName = 'MessageItem';

// Variable height item component for dynamic sizing
const VariableMessageItem = React.memo<ListChildComponentProps<ItemData>>(({ 
  index, 
  style, 
  data 
}) => {
  const { messages, currentUserId, onMessageAction } = data;
  const message = messages[index];
  
  if (!message) {
    return (
      <div style={style} className="flex items-center justify-center">
        <div className="text-sm text-gray-500">Message not found</div>
    </div>
    );
  }

  const isOwn = message.senderId === currentUserId;

  return (
    <div style={style}>
      <div className="px-4">
        <OptimizedMessageBubble
          message={message} currentUserId={currentUserId}, isOwn={isOwn} onReply={onMessageAction?.onReply}, onEdit={onMessageAction?.onEdit} onDelete={onMessageAction?.onDelete}, onReaction={onMessageAction?.onReaction} onCopy={onMessageAction?.onCopy}
        />
    </div>
    </div>
  );
});

VariableMessageItem.displayName = 'VariableMessageItem';

// Main virtualized message list component
export const VirtualizedMessageList = forwardRef<
  VirtualizedMessageListRef,
  VirtualizedMessageListProps
>(({
  messages,
  currentUserId,
  className,
  height = 600,
  width,
  itemHeight = 80,
  variableHeight = false,
  overscan = 5,
  loading = false,
  hasMore = false,
  onLoadMore,
  onScrollToBottom,
  onMessageAction,
  autoScrollToBottom = true,
  scrollBehavior = 'smooth'
}, ref) => {
  // Performance monitoring
  useRenderPerformance('VirtualizedMessageList', process.env.NODE_ENV === 'development');

  // Refs for list components
  const listRef = useRef<List | VariableSizeList>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const lastMessageCountRef = useRef(messages.length);
  const itemHeightsRef = useRef<Map<number, number>>(new Map());

  // State for scroll management
  const [isNearBottom, setIsNearBottom] = useState(true);
  const [showScrollToBottom, setShowScrollToBottom] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);

  // Memoized item data to prevent unnecessary re-renders
  const itemData = useMessagingMemo((): ItemData => ({
    messages,
    currentUserId,
    onMessageAction
  }), [messages, currentUserId, onMessageAction]);

  // Calculate item height for variable sizing
  const getItemHeight = useCallback((index: number): number => {
    if (!variableHeight) return itemHeight;
    
    // Return cached height if available
    const cachedHeight = itemHeightsRef.current.get(index);
    if (cachedHeight) return cachedHeight;
    
    // Estimate height based on message content
    const message = messages[index];
    if (!message) return itemHeight;
    
    let estimatedHeight = 60; // Base height
    
    // Add height for content
    if (message.content) {
      const lines = Math.ceil(message.content.length / 50); // Rough estimation
      estimatedHeight += lines * 20;
    }
    
    // Add height for attachments
    if (message.attachments && message.attachments.length > 0) {
      estimatedHeight += message.attachments.length * 40;
    }
    
    // Add height for reactions
    if (message.reactions && message.reactions.length > 0) {
      estimatedHeight += 30;
    }
    
    // Cache the height
    itemHeightsRef.current.set(index, estimatedHeight);
    
    return estimatedHeight;
  }, [messages, itemHeight, variableHeight]);

  // Handle scroll events
  const handleScroll = useStableCallback(({ scrollOffset, scrollUpdateWasRequested }: {
    scrollOffset: number, scrollUpdateWasRequested: boolean;
  }) => {
    if (!containerRef.current) return;
    
    const { scrollHeight, clientHeight } = containerRef.current;
    const distanceFromBottom = scrollHeight - scrollOffset - clientHeight;
    
    const nearBottom = distanceFromBottom < 100;
    setIsNearBottom(nearBottom);
    setShowScrollToBottom(!nearBottom && messages.length > 10);
    
    // Load more messages when scrolling to top
    if (scrollOffset < 100 && hasMore && !isLoadingMore && !scrollUpdateWasRequested) {
      handleLoadMore();
    }
  }, [hasMore, isLoadingMore, messages.length]);

  // Handle loading more messages
  const handleLoadMore = useStableCallback(async () => {
    if (!onLoadMore || isLoadingMore) return;
    
    setIsLoadingMore(true);
    try {
      await onLoadMore();
    } catch (error) {
      console.error('Failed to load more messages:', error);
    } finally {
      setIsLoadingMore(false);
    }
  }, [onLoadMore, isLoadingMore]);

  // Scroll to bottom function
  const scrollToBottom = useCallback((behavior: 'smooth' | 'auto' = scrollBehavior) => {
    if (listRef.current && messages.length > 0) {
      const lastIndex = messages.length - 1;
      
      if ('scrollToItem' in listRef.current) {
        listRef.current.scrollToItem(lastIndex, 'end');
      } else if ('scrollTo' in listRef.current) {
        const totalHeight = messages.length * itemHeight;
        listRef.current.scrollTo(totalHeight);
      }, onScrollToBottom?.();
    }
  }, [messages.length, itemHeight, scrollBehavior, onScrollToBottom]);

  // Scroll to specific message
  const scrollToMessage = useCallback((messageId: string, behavior: 'smooth' | 'auto' = scrollBehavior) => {
    const messageIndex = messages.findIndex(msg => msg.id === messageId);
    if (messageIndex >= 0 && listRef.current && 'scrollToItem' in listRef.current) {
      listRef.current.scrollToItem(messageIndex, 'center');
    }
  }, [messages, scrollBehavior]);

  // Scroll to specific index
  const scrollToIndex = useCallback((index: number, behavior: 'smooth' | 'auto' = scrollBehavior) => {
    if (listRef.current && 'scrollToItem' in listRef.current && index >= 0 && index < messages.length) {
      listRef.current.scrollToItem(index, 'center');
    }
  }, [messages.length, scrollBehavior]);

  // Expose methods via ref
  useImperativeHandle(ref, () => ({
    scrollToBottom,
    scrollToMessage,
    scrollToIndex
  }), [scrollToBottom, scrollToMessage, scrollToIndex]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    const newMessageCount = messages.length;
    const hadNewMessages = newMessageCount > lastMessageCountRef.current;
    
    if (hadNewMessages && autoScrollToBottom && isNearBottom) {
      // Small delay to allow for DOM updates
      const timer = setTimeout(() => {
        scrollToBottom('auto');
      }, 50);
      
      return () => clearTimeout(timer);
    }
    
    lastMessageCountRef.current = newMessageCount;
  }, [messages.length, autoScrollToBottom, isNearBottom, scrollToBottom]);

  // Performance metrics
  useEffect(() => {
    if (messages.length > 0) {
      performanceMonitoringService.recordMetric({
        name: 'virtualized-message-list-size',
        value: messages.length,
        timestamp: new Date(),
        category: 'render',
        metadata: {
          variableHeight,
          hasVirtualization: true
        }
      });
    }
  }, [messages.length, variableHeight]);

  // Render loading indicator
  const LoadingIndicator = () => (
    <div className="flex items-center justify-center py-4">
      <Loader2 className="w-4 h-4 animate-spin mr-2" />
      <span className="text-sm text-gray-500">Loading messages...</span>
    </div>
  );

  return (
    <div className={cn("relative flex flex-col", className)}>
      {/* Loading more indicator at top */}
      {isLoadingMore && (
        <div className="absolute top-0 left-0 right-0 z-10 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm">
          <LoadingIndicator />
    </div>
      )}

      {/* Message list container */}
      <div 
        ref={containerRef} className="flex-1 overflow-hidden"
        style={{ height }}
      >
        {messages.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center text-gray-500">
              <div className="text-lg mb-2">No messages yet</div>
              <div className="text-sm">Start the conversation!</div>
    </div>
          </div>
        ) : variableHeight ? (
          <VariableSizeList
            ref={listRef as React.Ref<VariableSizeList>} height={height}, width={width || '100%'} itemCount={messages.length}, itemSize={getItemHeight} itemData={itemData}, overscanCount={overscan} onScroll={handleScroll}
          >
            {VariableMessageItem}
          </VariableSizeList>
        ) : (
          <List
            ref={listRef as React.Ref<List>} height={height}, width={width || '100%'} itemCount={messages.length}, itemSize={itemHeight} itemData={itemData}, overscanCount={overscan} onScroll={handleScroll}
          >
            {MessageItem}
          </List>
        )}
      </div>

      {/* Scroll to bottom button */}
      {showScrollToBottom && (
        <Button
          variant="outline"
          size="sm"
          onClick={() => scrollToBottom()} className="absolute bottom-4 right-4 rounded-full shadow-lg bg-white dark:bg-gray-800 border-2"
        >
          <ArrowDown className="w-4 h-4" />
    </Button>
      )}

      {/* Loading indicator */}
      {loading && (
        <div className="absolute inset-0 flex items-center justify-center bg-white/50 dark:bg-gray-900/50 backdrop-blur-sm">
          <LoadingIndicator />
    </div>
      )}
    </div>
  );
});

VirtualizedMessageList.displayName = 'VirtualizedMessageList';

export type { VirtualizedMessageListRef, VirtualizedMessageListProps };
export default VirtualizedMessageList;