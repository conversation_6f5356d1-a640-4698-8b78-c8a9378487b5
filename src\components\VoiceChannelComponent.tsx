import React, { useState, useCallback } from 'react';
import { motion } from 'framer-motion';
import {
  Volume2,
  VolumeX,
  Mic,
  Mic<PERSON>ff,
  <PERSON>tings,
  PhoneOff,
  UserPlus,
  Crown,
  MoreVertical
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import { toast } from 'sonner';
import { useGroupManagement } from '@/hooks/useGroupManagement';
import { VoiceChannel } from '@/types/enhanced-messaging';
import { formatDistanceToNow } from 'date-fns';

interface VoiceChannelComponentProps {
  channel: VoiceChannel, currentUserId: string, isConnected: boolean, onJoin: () => void; onLeave: () => void;
  className?: string;
}

interface VoiceParticipant {
  id: string, name: string, avatar: string, isMuted: boolean, isSpeaking: boolean, volume: number, joinedAt: Date;
  isHost?: boolean;
}

const VoiceChannelComponent: React.FC<VoiceChannelComponentProps> = ({
  channel,
  currentUserId,
  isConnected,
  onJoin,
  onLeave,
  className = ''
}) => {
  const { leaveVoiceChannel } = useGroupManagement('voice-user-id'); // Would use actual currentUserId prop

  // Mock voice participants data (would come from real voice service)
  const [participants] = useState<VoiceParticipant[]>([
    {
      id: '1',
      name: 'John Doe',
      avatar: '',
      isMuted: false,
      isSpeaking: true,
      volume: 85,
      joinedAt: new Date(Date.now() - 1000 * 60 * 15), // 15 minutes ago
      isHost: true
    },
    {
      id: '2',
      name: 'Jane Smith',
      avatar: '',
      isMuted: true,
      isSpeaking: false,
      volume: 0,
      joinedAt: new Date(Date.now() - 1000 * 60 * 8), // 8 minutes ago
    },
    {
      id: currentUserId,
      name: 'You',
      avatar: '',
      isMuted: false,
      isSpeaking: false,
      volume: 70,
      joinedAt: new Date(Date.now() - 1000 * 60 * 5), // 5 minutes ago
    }
  ]);

  // Voice controls state
  const [isMuted, setIsMuted] = useState(false);
  const [isDeafened, setIsDeafened] = useState(false);
  const [outputVolume, setOutputVolume] = useState([75]);
  const [inputVolume, setInputVolume] = useState([80]);
  const [showSettings, setShowSettings] = useState(false);
  const [showLeaveDialog, setShowLeaveDialog] = useState(false);

  const currentParticipant = participants.find(p => p.id === currentUserId);
  const otherParticipants = participants.filter(p => p.id !== currentUserId);

  const handleToggleMute = useCallback(() => {
    setIsMuted(!isMuted);
    toast.success(`Microphone ${!isMuted ? 'muted' : 'unmuted'}`);
  }, [isMuted]);

  const handleToggleDeafen = useCallback(() => {
    setIsDeafened(!isDeafened);
    if (!isDeafened) {
      setIsMuted(true); // Auto-mute when deafening
    }
    toast.success(`Audio ${!isDeafened ? 'deafened' : 'undeafened'}`);
  }, [isDeafened]);

  const handleLeaveChannel = useCallback(async () => {
    try {
      await leaveVoiceChannel(channel.id);
      onLeave();
      setShowLeaveDialog(false);
      toast.success('Left voice channel');
    } catch (error) {
      console.error('Failed to leave voice channel:', error);
      toast.error('Failed to leave voice channel');
    }
  }, [channel.id, leaveVoiceChannel, onLeave]);

  const handleJoinChannel = useCallback(() => {
    if (channel.maxParticipants && participants.length >= channel.maxParticipants) {
      toast.error('Voice channel is full');
      return;
    }, onJoin();
    toast.success(`Joined ${channel.name}`);
  }, [channel, participants.length, onJoin]);

  if (!isConnected) {
    return (
      <Card className={`w-full max-w-sm ${className}`}>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-base">
            <Volume2 className="h-4 w-4" />
            {channel.name}
          </CardTitle>
          <div className="flex items-center justify-between text-sm text-muted-foreground">
            <span>{channel.participants.length} connected</span>
            {channel.maxParticipants && (
              <span>Max: {channel.maxParticipants}</span>
            )}
          </div>
    </CardHeader>
        <CardContent className="pt-0">
          <div className="space-y-3">
            {/* Preview participants */}
            {channel.participants.length > 0 && (
              <div className="flex -space-x-2">
                {channel.participants.slice(0, 3).map((participantId, index) => (
                  <Avatar key={participantId} className="h-6 w-6 border-2 border-background">
                    <AvatarFallback className="text-xs">
                      {index + 1}
                    </AvatarFallback>
    </Avatar>
                ))}
                {channel.participants.length > 3 && (
                  <div className="h-6 w-6 rounded-full bg-muted border-2 border-background flex items-center justify-center">
                    <span className="text-xs text-muted-foreground">
                      +{channel.participants.length - 3}
                    </span>
    </div>
                )}
              </div>
            )}

            <Button 
              onClick={handleJoinChannel} className="w-full"
              disabled={channel.maxParticipants ? participants.length >= channel.maxParticipants : false}
            >
              <Volume2 className="h-4 w-4 mr-2" />
              Join Voice Channel
            </Button>
    </div>
        </CardContent>
    </Card>
    );
  }

  return (
    <>
      <Card className={`w-full max-w-md ${className}`}>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2 text-base">
              <Volume2 className="h-4 w-4 text-green-500" />
              {channel.name}
              <Badge variant="outline" className="text-xs">
                Connected
              </Badge>
    </CardTitle>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm">
                  <MoreVertical className="h-4 w-4" />
    </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Channel Options</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => setShowSettings(true)}>
                  <Settings className="h-4 w-4 mr-2" />
                  Voice Settings
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <UserPlus className="h-4 w-4 mr-2" />
                  Invite Friends
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem 
                  onClick={() => setShowLeaveDialog(true)} className="text-destructive"
                >
                  <PhoneOff className="h-4 w-4 mr-2" />
                  Leave Channel
                </DropdownMenuItem>
    </DropdownMenuContent>
            </DropdownMenu>
    </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Current User Controls */}
          <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
            <div className="flex items-center gap-3">
              <Avatar className="h-8 w-8">
                <AvatarImage src={currentParticipant?.avatar} />
                <AvatarFallback>You</AvatarFallback>
    </Avatar>
              <div>
                <p className="font-medium text-sm">You</p>
                <p className="text-xs text-muted-foreground">
                  {currentParticipant?.joinedAt && 
                    `Joined ${formatDistanceToNow(currentParticipant.joinedAt, { addSuffix: true })}`
                  }
                </p>
    </div>
            </div>
            <div className="flex gap-2">
              <Button
                onClick={handleToggleMute} variant={isMuted ? 'destructive' : 'outline'}, size="sm"
              >
                {isMuted ? <MicOff className="h-4 w-4" /> : <Mic className="h-4 w-4" />}
              </Button>
              <Button
                onClick={handleToggleDeafen} variant={isDeafened ? 'destructive' : 'outline'}, size="sm"
              >
                {isDeafened ? <VolumeX className="h-4 w-4" /> : <Volume2 className="h-4 w-4" />}
              </Button>
    </div>
          </div>

          {/* Other Participants */}
          {otherParticipants.length > 0 && (
            <div className="space-y-2">
              <h4 className="text-sm font-medium text-muted-foreground">
                Other Participants ({otherParticipants.length})
              </h4>
              <ScrollArea className="max-h-40">
                <div className="space-y-2">
                  {otherParticipants.map((participant) => (
                    <motion.div
                      key={participant.id} initial={{ opacity: 0, y: 10 }}, animate={{ opacity: 1, y: 0 }}, className="flex items-center justify-between p-2 rounded bg-background"
                    >
                      <div className="flex items-center gap-3">
                        <div className="relative">
                          <Avatar className="h-6 w-6">
                            <AvatarImage src={participant.avatar} />
                            <AvatarFallback className="text-xs">
                              {participant.name[0]}
                            </AvatarFallback>
    </Avatar>
                          {participant.isSpeaking && (
                            <motion.div
                              animate={{ scale: [1, 1.2, 1] }}, transition={{ duration: 1, repeat: Infinity }}, className="absolute -bottom-1 -right-1 h-2 w-2 bg-green-500 rounded-full"
                            />
                          )}
                        </div>
                        <div>
                          <div className="flex items-center gap-1">
                            <p className="text-sm font-medium">{participant.name}</p>
                            {participant.isHost && (
                              <Crown className="h-3 w-3 text-yellow-500" />
                            )}
                          </div>
                          <p className="text-xs text-muted-foreground">
                            Joined {formatDistanceToNow(participant.joinedAt, { addSuffix: true })}
                          </p>
    </div>
                      </div>
                      <div className="flex items-center gap-1">
                        {participant.isMuted ? (
                          <MicOff className="h-3 w-3 text-destructive" />
                        ) : (
                          <Mic className="h-3 w-3 text-green-500" />
                        )}
                        {participant.isSpeaking && (
                          <div className="flex items-center gap-1">
                            <div className="flex space-x-1">
                              {[1, 2, 3].map((bar) => (
                                <motion.div
                                  key={bar} animate={{ height: [2, 8, 2] }}, transition={{
                                    duration: 0.5,
                                    repeat: Infinity,
                                    delay: bar * 0.1
                                  }}, className="w-1 bg-green-500 rounded-full"
                                />
                              ))}
                            </div>
                            <span className="text-xs text-muted-foreground ml-1">
                              {participant.volume}%
                            </span>
    </div>
                        )}
                      </div>
                    </motion.div>
                  ))}
                </div>
    </ScrollArea>
            </div>
          )}

          {/* Quick Volume Controls */}
          <div className="space-y-3 pt-2 border-t">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium">Output Volume</label>
                <span className="text-xs text-muted-foreground">{outputVolume[0]}%</span>
    </div>
              <Slider
                value={outputVolume} onValueChange={setOutputVolume}, max={100} step={1}, className="w-full"
                disabled={isDeafened}
              />
    </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium">Input Volume</label>
                <span className="text-xs text-muted-foreground">{inputVolume[0]}%</span>
    </div>
              <Slider
                value={inputVolume} onValueChange={setInputVolume}, max={100} step={1}, className="w-full"
                disabled={isMuted}
              />
    </div>
          </div>
    </CardContent>
      </Card>

      {/* Voice Settings Dialog */}
      <Dialog open={showSettings} onOpenChange={setShowSettings}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Voice Settings</DialogTitle>
            <DialogDescription>
              Adjust your voice and audio settings for this channel
            </DialogDescription>
    </DialogHeader>
          <div className="space-y-6">
            <div className="space-y-4">
              <h4 className="font-medium">Audio Settings</h4>
              <div className="space-y-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Output Volume: {outputVolume[0]}%</label>
                  <Slider
                    value={outputVolume} onValueChange={setOutputVolume}, max={100} step={1}
                  />
    </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Input Volume: {inputVolume[0]}%</label>
                  <Slider
                    value={inputVolume} onValueChange={setInputVolume}, max={100} step={1}
                  />
    </div>
              </div>
    </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowSettings(false)}>
              Close
            </Button>
    </DialogFooter>
        </DialogContent>
    </Dialog>
      {/* Leave Channel Dialog */}
      <Dialog open={showLeaveDialog} onOpenChange={setShowLeaveDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Leave Voice Channel</DialogTitle>
            <DialogDescription>
              Are you sure you want to leave {channel.name}? You can rejoin at any time.
            </DialogDescription>
    </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowLeaveDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleLeaveChannel}>
              Leave Channel
            </Button>
    </DialogFooter>
        </DialogContent>
    </Dialog>
    </>
  );
};

export default VoiceChannelComponent;
