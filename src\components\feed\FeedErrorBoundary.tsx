import React from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { AlertCircle, RefreshCw } from 'lucide-react';

interface FeedErrorFallbackProps {
  error: Error, resetErrorBoundary: () => void;
}

export const FeedErrorFallback: React.FC<FeedErrorFallbackProps> = ({ 
  error, 
  resetErrorBoundary 
}) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}, animate={{ opacity: 1, y: 0 }}, className="flex flex-col items-center justify-center py-12 px-4"
  >
    <Card className="w-full max-w-md border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20">
      <CardContent className="text-center py-8">
        <AlertCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
        <h2 className="text-xl font-semibold text-red-700 dark:text-red-400 mb-2">
          Feed Loading Error
        </h2>
        <p className="text-red-600 dark:text-red-300 mb-6 text-sm">
          {error?.message || 'Something went wrong while loading your feed.'}
        </p>
        <Button 
          onClick={resetErrorBoundary} variant="outline"
          className="border-red-300 text-red-700 hover:bg-red-100 dark:border-red-600 dark:text-red-400 dark:hover:bg-red-900/30"
        >
          <RefreshCw className="w-4 h-4 mr-2" />
          Try Again
        </Button>
    </CardContent>
    </Card>
  </motion.div>
);

export default FeedErrorFallback;
