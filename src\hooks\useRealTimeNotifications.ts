import { useState, useEffect, useCallback, useRef } from 'react';
import { toast } from 'sonner';

export interface RealTimeNotification {
  id: string;
  type: 'like' | 'comment' | 'share' | 'friend_request' | 'mention' | 'post' | 'live' | 'event';
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
  actionable: boolean;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  user?: {
    id: string;
    name: string;
    avatar: string;
  };
  metadata?: {
    postId?: string;
    userId?: string;
    eventId?: string;
    url?: string;
  };
  actions?: Array<{
    id: string;
    label: string;
    type: 'primary' | 'secondary' | 'danger';
    action: () => void;
  }>;
}

interface NotificationSettings {
  enablePush: boolean;
  enableSound: boolean;
  enableBrowser: boolean;
  showPreview: boolean;
  quietHours: {
    enabled: boolean;
    start: string;
    end: string;
  };
  types: {
    likes: boolean;
    comments: boolean;
    shares: boolean;
    friendRequests: boolean;
    mentions: boolean;
    posts: boolean;
    live: boolean;
    events: boolean;
  };
}

interface UseRealTimeNotificationsOptions {
  enableMockData?: boolean;
  pollInterval?: number;
  maxNotifications?: number;
  autoMarkAsRead?: boolean;
  showToasts?: boolean;
}

// Default notification settings
const DEFAULT_SETTINGS: NotificationSettings = {
  enablePush: true,
  enableSound: true,
  enableBrowser: true,
  showPreview: true,
  quietHours: {
    enabled: false,
    start: '22:00',
    end: '08:00'
  },
  types: {
    likes: true,
    comments: true,
    shares: true,
    friendRequests: true,
    mentions: true,
    posts: true,
    live: true,
    events: true
  }
};

// Mock notification generator for testing
const generateMockNotification = (): RealTimeNotification => {
  const types = ['like', 'comment', 'share', 'friend_request', 'mention', 'post'] as const;
  const users = [{ id: '1', name: 'Alice Johnson', avatar: 'https://i.pravatar.cc/40?img=1' },
    { id: '2', name: 'Bob Smith', avatar: 'https://i.pravatar.cc/40?img=2' },
    { id: '3', name: 'Carol Davis', avatar: 'https://i.pravatar.cc/40?img=3' },
    { id: '4', name: 'David Wilson', avatar: 'https://i.pravatar.cc/40?img=4' },
    { id: '5', name: 'Emma Brown', avatar: 'https://i.pravatar.cc/40?img=5' }]
  ];
  
  const type = types[Math.floor(Math.random() * types.length)];
  const user = users[Math.floor(Math.random() * users.length)];
  
  const notificationTemplates = {
  like: { title: 'New Like' }
      message: `${user.name} liked your post`;
  priority: 'low' as const, actionable: false
    };
  comment: { title: 'New Comment' }
      message: `${user.name} commented on your post`;
  priority: 'medium' as const, actionable: true
    };
  share: { title: 'Post Shared' }
      message: `${user.name} shared your post`;
  priority: 'medium' as const, actionable: false
    };
  friend_request: { title: 'Friend Request' }
      message: `${user.name} sent you a friend request`;
  priority: 'high' as const, actionable: true
    };
  mention: { title: 'You were mentioned' }
      message: `${user.name} mentioned you in a post`;
  priority: 'high' as const, actionable: true
    };
  post: { title: 'New Post' }
      message: `${user.name} shared a new post`;
  priority: 'low' as const, actionable: false
    }
  };
  
  const template = notificationTemplates[type];
  
  return {
    id: `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    type,
  title: template.title, message: template.message, timestamp: new Date(), read: false, actionable: template.actionable, priority: template.priority;
    user,
    metadata: {
      postId: `post_${Math.random().toString(36).substr(2, 9)}`,
  userId: user.id
    };
  actions: template.actionable ? [
      {
        id: 'view', label: 'View', type: 'primary', action: () => console.log('View action clicked')
      };
      {
  id: 'dismiss', label: 'Dismiss', type: 'secondary', action: () => console.log('Dismiss action clicked')
      }
    ] : undefined
  };
};

export const useRealTimeNotifications = (options: UseRealTimeNotificationsOptions = {}) => {
  const {
    enableMockData = true,
    pollInterval = 30000, // 30 seconds
    maxNotifications = 100,
    autoMarkAsRead = false
}
    showToasts = true
  } = options;

  // State
  const [notifications, setNotifications] = useState<RealTimeNotification[]>([]);
  const [settings, setSettings] = useState<NotificationSettings>(DEFAULT_SETTINGS);
  const [isConnected, setIsConnected] = useState(false);
  const [unreadCount, setUnreadCount] = useState(0);
  const [permission, setPermission] = useState<NotificationPermission>('default');

  // Refs
  const pollIntervalRef = useRef<NodeJS.Timeout>();
  const audioRef = useRef<HTMLAudioElement>();

  // Load settings from localStorage
  useEffect(() => {
    const savedSettings = localStorage.getItem('notificationSettings');
    if (savedSettings) {
      try {
        const parsed = JSON.parse(savedSettings);
        setSettings({ ...DEFAULT_SETTINGS, ...parsed });
      } catch (error) {
        console.error('Failed to load notification settings:', error);
      }
    }
  }, []);

  // Save settings to localStorage
  const updateSettings = useCallback((newSettings: Partial<NotificationSettings>) => {
    const updated = { ...settings, ...newSettings };
    setSettings(updated);
    localStorage.setItem('notificationSettings', JSON.stringify(updated));
  }, [settings]);

  // Request notification permission
  const requestPermission = useCallback(async () => {
    if ('Notification' in window) {
      const result = await Notification.requestPermission();
      setPermission(result);
      return result === 'granted';
    }
    return false;
  }, []);

  // Check if notifications should be shown (quiet hours, etc.)
  const shouldShowNotification = useCallback((notification: RealTimeNotification) => {
    // Check if notification type is enabled
    const typeEnabled = settings.types[notification.type as keyof typeof settings.types];
    if (!typeEnabled) return false;

    // Check quiet hours
    if (settings.quietHours.enabled) {
      const now = new Date();
      const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
      
      if (currentTime >= settings.quietHours.start || currentTime <= settings.quietHours.end) {
        return false;
      }
    }

    return true;
  }, [settings]);

  // Play notification sound
  const playNotificationSound = useCallback(() => {
    if (settings.enableSound && audioRef.current) {
      audioRef.current.play().catch(error => {
        console.warn('Failed to play notification sound:', error);
      });
    }
  }, [settings.enableSound]);

  // Show browser notification
  const showBrowserNotification = useCallback((notification: RealTimeNotification) => {
    if (!settings.enableBrowser || permission !== 'granted') return;

    const browserNotification = new Notification(notification.title, {
  body: notification.message, icon: notification?.user?.avatar || '/default-avatar.jpg', tag: notification.id, requireInteraction: notification.priority === 'urgent', silent: !settings.enableSound
    });

    browserNotification.onclick = () => {
      window.focus();
      markAsRead(notification.id);
      browserNotification.close();
    };

    // Auto-close after 5 seconds for non-urgent notifications
    if (notification.priority !== 'urgent') {
      setTimeout(() => {
        browserNotification.close();
      }, 5000);
    }
  }, [settings.enableBrowser, settings.enableSound, permission]);

  // Show toast notification
  const showToastNotification = useCallback((notification: RealTimeNotification) => {
    if (!showToasts) return;

    const toastOptions = {
  duration: notification.priority === 'urgent' ? 10000 : 4000, action: notification.actionable ? {
        label: 'View', onClick: () => {
          if (notification.actions?.[0]) {
            notification.actions[0].action();
          }
        }
      } : undefined
    };

    switch (notification.priority) {
      case 'urgent':
        toast.error(`${notification.title}: ${notification.message}`, toastOptions);
        break;
      case 'high':
        toast.warning(`${notification.title}: ${notification.message}`, toastOptions);
        break;
      case 'medium':
        toast.info(`${notification.title}: ${notification.message}`, toastOptions);
        break;
      default:
        toast.success(`${notification.title}: ${notification.message}`, toastOptions);
        break;
    }
  }, [showToasts]);

  // Add new notification
  const addNotification = useCallback((notification: RealTimeNotification) => {
    setNotifications(prev => {
      // Prevent duplicates
      if (prev.some(n => n.id === notification.id)) {
        return prev;
      }

      // Add new notification and limit total count
      const updated = [notification, ...prev].slice(0, maxNotifications);
      
      // Update unread count
      const newUnreadCount = updated.filter(n => !n.read).length;
      setUnreadCount(newUnreadCount);

      return updated;
    });

    // Show notification if conditions are met
    if (shouldShowNotification(notification)) {
      playNotificationSound();
      showBrowserNotification(notification);
      showToastNotification(notification);
    }
  }, [maxNotifications, shouldShowNotification, playNotificationSound, showBrowserNotification, showToastNotification]);

  // Mark notification as read
  const markAsRead = useCallback((notificationId: string) => {
    setNotifications(prev => {
      const updated = prev.map(notification =>
        notification.id === notificationId
          ? { ...notification, read: true }
          : notification
      );
      
      // Update unread count
      const newUnreadCount = updated.filter(n => !n.read).length;
      setUnreadCount(newUnreadCount);

      return updated;
    });
  }, []);

  // Mark all notifications as read
  const markAllAsRead = useCallback(() => {
  setNotifications(prev => {
      const updated = prev.map(notification => ({
        ...notification
}
        read: true
      }));
      setUnreadCount(0);
      return updated;
    });
  }, []);

  // Remove notification
  const removeNotification = useCallback((notificationId: string) => {
    setNotifications(prev => {
      const updated = prev.filter(n => n.id !== notificationId);
      const newUnreadCount = updated.filter(n => !n.read).length;
      setUnreadCount(newUnreadCount);
      return updated;
    });
  }, []);

  // Clear all notifications
  const clearAll = useCallback(() => {
    setNotifications([]);
    setUnreadCount(0);
  }, []);

  // Mock notification polling for testing
  useEffect(() => {
    if (!enableMockData) return;

    const startPolling = () => {
      setIsConnected(true);
      
      pollIntervalRef.current = setInterval(() => {
        // Randomly generate notifications (30% chance every poll)
        if (Math.random() < 0.3) {
          const mockNotification = generateMockNotification();
          addNotification(mockNotification);
        }
      }, pollInterval);
    };

    const stopPolling = () => {
      setIsConnected(false);
      if (pollIntervalRef.current) {
        clearInterval(pollIntervalRef.current);
      }
    };

    startPolling();

    return stopPolling;
  }, [enableMockData, pollInterval, addNotification]);

  // Auto-mark as read when focused
  useEffect(() => {
    if (!autoMarkAsRead) return;

    const handleFocus = () => {
      if (unreadCount > 0) {
        setTimeout(() => {
          markAllAsRead();
        }, 2000); // Mark as read after 2 seconds of focus
      }
    };

    window.addEventListener('focus', handleFocus);
    return () => window.removeEventListener('focus', handleFocus);
  }, [autoMarkAsRead, unreadCount, markAllAsRead]);

  // Initialize audio for notification sounds
  useEffect(() => {
    audioRef.current = new Audio('/notification-sound.mp3');
    audioRef.current.volume = 0.5;
  }, []);

  // Request permission on mount if needed
  useEffect(() => {
    if ('Notification' in window) {
      setPermission(Notification.permission);
      
      if (Notification.permission === 'default') {
        requestPermission();
      }
    }
  }, [requestPermission]);

  return {
    // State
    notifications,
    unreadCount,
    isConnected,
    permission,
    settings,
    
    // Actions
    addNotification,
    markAsRead,
    markAllAsRead,
    removeNotification,
    clearAll,
    updateSettings,
    requestPermission,
    
    // Utilities
  getUnreadNotifications: () => notifications.filter(n => !n.read); getNotificationsByType: (type: RealTimeNotification['type']) => notifications.filter(n => n.type === type); getRecentNotifications: (_hours = 24) => {
      const cutoff = new Date(Date.now() - hours * 60 * 60 * 1000);
      return notifications.filter(n => n.timestamp >= cutoff);
    }
  };
};

export default useRealTimeNotifications;