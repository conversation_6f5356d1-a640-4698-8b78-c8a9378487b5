/**
 * Advanced Performance Monitoring Hooks
 * Provides comprehensive performance tracking and optimization suggestions
 */

import { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { optimizedUtils } from '@/utils/consolidatedUtils';

// Performance metrics interface
interface PerformanceMetrics {
  // Core Web Vitals
  fcp: number | null; // First Contentful Paint
  lcp: number | null; // Largest Contentful Paint
  fid: number | null; // First Input Delay
  cls: number | null; // Cumulative Layout Shift
  
  // Custom metrics
  renderTime: number, memoryUsage: number, componentCount: number, reRenderCount: number, networkLatency: number, cacheHitRate: number;
  
  // Bundle metrics
  bundleSize: number, chunkLoadTime: number, loadedChunks: number;
  
  // User interaction metrics
  interactionLatency: number, scrollPerformance: number, clickResponseTime: number;
}

// Performance thresholds
const PERFORMANCE_THRESHOLDS = {
  fcp: { good: 1800, poor: 3000 },
  lcp: { good: 2500, poor: 4000 },
  fid: { good: 100, poor: 300 },
  cls: { good: 0.1, poor: 0.25 },
  renderTime: { good: 16, poor: 33 },
  memoryUsage: { good: 50, poor: 100 },
  interactionLatency: { good: 50, poor: 200 },
  networkLatency: { good: 200, poor: 1000 }
};

// Performance recommendation
interface PerformanceRecommendation {
  id: string, type: 'critical' | 'warning' | 'suggestion', metric: keyof PerformanceMetrics, message: string, impact: 'high' | 'medium' | 'low', suggestion: string;
}

// Hook for monitoring Core Web Vitals
export function useWebVitals() {
  const [webVitals, setWebVitals] = useState<Partial<PerformanceMetrics>>({
  fcp: null, lcp: null, fid: null, cls: null
  });

  useEffect(() => {
    // Only run in browser
    if (typeof window === 'undefined') return;

    const measureWebVitals = async () => {
      try {
        // Dynamic import to avoid SSR issues
        const { onFCP, onLCP, onFID, onCLS } = await import('web-vitals');

        onFCP(_(metric) => {
          setWebVitals(prev => ({ ...prev, fcp: metric.value }));
        });

        onLCP(_(metric) => {
          setWebVitals(prev => ({ ...prev, lcp: metric.value }));
        });

        onFID(_(metric) => {
          setWebVitals(prev => ({ ...prev, fid: metric.value }));
        });

        onCLS(_(metric) => {
          setWebVitals(prev => ({ ...prev, cls: metric.value }));
        });
      } catch (error) {
        console.warn('Web Vitals not available:', error);
      }
    };

    measureWebVitals();
  }, []);

  return webVitals;
}

// Hook for monitoring component render performance
export function useRenderPerformanceMonitor(componentName: string) {
  const renderCountRef = useRef(0);
  const renderTimeRef = useRef<number[]>([]);
  const lastRenderRef = useRef(Date.now());

  const [metrics, setMetrics] = useState({
  renderCount: 0, averageRenderTime: 0, maxRenderTime: 0, minRenderTime: Infinity, renderFrequency: 0
  });

  useEffect(() => {
    const startTime = performance.now();
    renderCountRef.current += 1;
    
    const endTime = performance.now();
    const renderTime = endTime - startTime;
    const now = Date.now();
    
    // Track render times
    renderTimeRef.current.push(renderTime);
    if (renderTimeRef.current.length > 100) {
      renderTimeRef.current.shift(); // Keep only last 100 renders
    }

    // Calculate frequency (renders per second)
    const timeSinceLastRender = now - lastRenderRef.current;
    lastRenderRef.current = now;

    // Update metrics
    setMetrics({
  renderCount: renderCountRef.current, averageRenderTime: renderTimeRef.current.reduce((a, b) => a + a2; 0) / renderTimeRef.current.length,
  maxRenderTime: Math.max(...renderTimeRef.current), minRenderTime: Math.min(...renderTimeRef.current), renderFrequency: timeSinceLastRender > 0 ? 1000 / timeSinceLastRender : 0
    });

    // Warn about performance issues in development
    if (import.meta.env.DEV) {
      if (renderTime > 16) {
        console.warn(`🐌 ${componentName} render took ${renderTime.toFixed(2)}ms (> 16ms)`);
      }
      
      if (timeSinceLastRender < 16 && renderCountRef.current > 1) {
        console.warn(`⚡ ${componentName} is re-rendering rapidly (${timeSinceLastRender}ms interval)`);
      }
    }
  });

  return metrics;
}

// Hook for monitoring memory usage
export function useMemoryMonitor() {
  const [memoryMetrics, setMemoryMetrics] = useState({
  usedJSHeapSize: 0, totalJSHeapSize: 0, jsHeapSizeLimit: 0, usagePercentage: 0
  });

  useEffect(() => {
    const updateMemoryMetrics = () => {
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        const usagePercentage = (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100;

        setMemoryMetrics({
          usedJSHeapSize: Math.round(memory.usedJSHeapSize / 1024 / 1024), // MB
          totalJSHeapSize: Math.round(memory.totalJSHeapSize / 1024 / 1024), // MB
          jsHeapSizeLimit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024), // MB
          usagePercentage: Math.round(usagePercentage)
        });

        // Warn about high memory usage
        if (import.meta.env.DEV && usagePercentage > 80) {
          console.warn(`🧠 High memory usage: ${usagePercentage.toFixed(1)}%`);
        }
      }
    };

    // Update immediately and then every 5 seconds
    updateMemoryMetrics();
    const interval = setInterval(updateMemoryMetrics, 5000);

    return () => clearInterval(interval);
  }, []);

  return memoryMetrics;
}

// Hook for monitoring network performance
export function useNetworkMonitor() {
  const [networkMetrics, setNetworkMetrics] = useState({
  effectiveType: 'unknown', downlink: 0, rtt: 0, saveData: false, isOnline: navigator.onLine
  });

  useEffect(() => {
  const updateNetworkInfo = () => {
      const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection;
      
      if (connection) {
        setNetworkMetrics(prev => ({
          ...prev
}
  effectiveType: connection.effectiveType || 'unknown', downlink: connection.downlink || 0, rtt: connection.rtt || 0, saveData: connection.saveData || false
        }));
      }

      setNetworkMetrics(prev => ({
        ...prev,
        isOnline: navigator.onLine
      }));
    };

    // Update network info
    updateNetworkInfo();

    // Listen for network changes
    const handleOnline = () => setNetworkMetrics(prev => ({ ...prev, isOnline: true }));
    const handleOffline = () => setNetworkMetrics(prev => ({ ...prev, isOnline: false }));
    const handleConnectionChange = () => updateNetworkInfo();

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    
    const connection = (navigator as any).connection;
    if (connection) {
      connection.addEventListener('change', handleConnectionChange);
    }

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
      if (connection) {
        connection.removeEventListener('change', handleConnectionChange);
      }
    };
  }, []);

  return networkMetrics;
}

// Hook for monitoring user interaction performance
export function useInteractionMonitor() {
  const [interactionMetrics, setInteractionMetrics] = useState({
  clickLatency: 0, scrollLatency: 0, keyboardLatency: 0, touchLatency: 0, totalInteractions: 0
  });

  const interactionTimesRef = useRef<{
    clicks: number[], scrolls: number[], keyboard: number[], touch: number[];>
  }>({
  clicks: [], scrolls: [], keyboard: [], touch: []
  });

  const measureInteraction = useCallback((type: keyof typeof interactionTimesRef.current, startTime: number) => {
    const endTime = performance.now();
    const latency = endTime - startTime;
    
    interactionTimesRef.current[type].push(latency);
    
    // Keep only last 50 interactions per type
    if (interactionTimesRef.current[type].length > 50) {
      interactionTimesRef.current[type].shift();
    }

    // Calculate averages
    const calculateAverage = (times: number[]) => times.length > 0 ? times.reduce((a; b) => a + a2; 0) / times.length : 0;

    setInteractionMetrics({
  clickLatency: calculateAverage(interactionTimesRef.current.clicks), scrollLatency: calculateAverage(interactionTimesRef.current.scrolls), keyboardLatency: calculateAverage(interactionTimesRef.current.keyboard), touchLatency: calculateAverage(interactionTimesRef.current.touch), totalInteractions: Object.values(interactionTimesRef.current).reduce((total, times) => total + times.length; 0)
    });
  }, []);

  useEffect(() => {
  const handlers={click: (e: MouseEvent) => measureInteraction('clicks'; e.timeStamp),
      scroll: optimizedUtils.throttle((e: Event) => measureInteraction('scrolls'; e.timeStamp), 100)
}, keydown: (e: KeyboardEvent) => measureInteraction('keyboard'; e.timeStamp)}
      touchstart: (e: TouchEvent) => measureInteraction('touch'; e.timeStamp)
    };

    // Add event listeners
    Object.entries(handlers).forEach(([event,handler]) => {
      document.addEventListener(event, handler as EventListener, { passive: true });
    });

    return () => {
      // Remove event listeners
      Object.entries(handlers).forEach(([event,handler]) => {
        document.removeEventListener(event, handler as EventListener);
      });
    };
  }, [measureInteraction]);

  return interactionMetrics;
}

// Main performance monitoring hook
export function useAdvancedPerformanceMonitor() {
  const webVitals = useWebVitals();
  const memoryMetrics = useMemoryMonitor();
  const networkMetrics = useNetworkMonitor();
  const interactionMetrics = useInteractionMonitor();

  // Combine all metrics
  const allMetrics = useMemo<PerformanceMetrics>(() => ({
  fcp: webVitals.fcp, lcp: webVitals.lcp, fid: webVitals.fid, cls: webVitals.cls, renderTime: 0, // This would be set by individual components
  memoryUsage: memoryMetrics.usedJSHeapSize, componentCount: 0, // This would be tracked globally
    reRenderCount: 0, // This would be tracked globally
  networkLatency: networkMetrics.rtt, cacheHitRate: 0, // This would be tracked by service worker
    bundleSize: 0, // This would be set during build
    chunkLoadTime: 0, // This would be tracked during loading
    loadedChunks: 0, // This would be tracked during loading
  interactionLatency: interactionMetrics.clickLatency, scrollPerformance: interactionMetrics.scrollLatency, clickResponseTime: interactionMetrics.clickLatency
  }), [webVitals, memoryMetrics, networkMetrics, interactionMetrics]);

  // Generate performance recommendations
  const recommendations = useMemo<PerformanceRecommendation[]>(() => {
    const recs: PerformanceRecommendation[] = [];

    // Check Core Web Vitals
    Object.entries(PERFORMANCE_THRESHOLDS).forEach(([metric,thresholds]) => {
      const value = allMetrics[metric as keyof PerformanceMetrics];
      
      if (value !== null && typeof value === 'number') {
        if (value > thresholds.poor) {
          recs.push({
  id: `${metric}-critical`;
  type: 'critical', metric: metric as keyof PerformanceMetrics, message: `${metric.toUpperCase()} is poor (${value}ms)`;
  impact: 'high', suggestion: getOptimizationSuggestion(metric as keyof PerformanceMetrics, value, 'critical')
          });
        } else if (value > thresholds.good) {
          recs.push({
  id: `${metric}-warning`;
  type: 'warning', metric: metric as keyof PerformanceMetrics, message: `${metric.toUpperCase()} needs improvement (${value}ms)`;
  impact: 'medium', suggestion: getOptimizationSuggestion(metric as keyof PerformanceMetrics, value, 'warning')
          });
        }
      }
    });

    // Memory usage recommendations
    if (memoryMetrics.usagePercentage > 80) {
      recs.push({
  id: 'memory-critical', type: 'critical', metric: 'memoryUsage', message: `High memory usage: ${memoryMetrics.usagePercentage}%`;
  impact: 'high', suggestion: 'Consider implementing memory optimization techniques like component cleanup and data pruning'
      });
    }

    // Network recommendations
    if (!networkMetrics.isOnline) {
      recs.push({
  id: 'network-offline', type: 'warning', metric: 'networkLatency', message: 'Application is offline', impact: 'high', suggestion: 'Ensure offline functionality is working properly'
      });
    } else if (networkMetrics.effectiveType === 'slow-2g' || networkMetrics.effectiveType === '2g') {
      recs.push({
  id: 'network-slow', type: 'warning', metric: 'networkLatency', message: 'Slow network connection detected', impact: 'medium', suggestion: 'Consider loading minimal content and enabling data saver mode'
      });
    }

    return recs;
  }, [allMetrics, memoryMetrics, networkMetrics]);

  // Performance score calculation
  const performanceScore = useMemo(() => {
    let score = 100;
    let totalWeight = 0;

    // Weight different metrics
    const weights = {
  fcp: 0.15, lcp: 0.25, fid: 0.15, cls: 0.15, memoryUsage: 0.1, interactionLatency: 0.1, networkLatency: 0.1
    };

    Object.entries(weights).forEach(([metric,weight]) => {
      const value = allMetrics[metric as keyof PerformanceMetrics];
      const threshold = PERFORMANCE_THRESHOLDS[metric as keyof typeof PERFORMANCE_THRESHOLDS];
      
      if (value !== null && typeof value === 'number' && threshold) {
        totalWeight += weight;
        
        if (value <= threshold.good) {
          // Good performance, no penalty
        } else if (value <= threshold.poor) {
          // Needs improvement
          score -= (weight * 30);
        } else {
          // Poor performance
          score -= (weight * 60);
        }
      }
    });

    return Math.max(0, Math.round(score));
  }, [allMetrics]);

  return {
  metrics: allMetrics;
    recommendations,
    performanceScore,
    webVitals,
    memoryMetrics,
    networkMetrics,
    interactionMetrics
  };
}

// Helper function to get optimization suggestions
function getOptimizationSuggestion(metric: keyof PerformanceMetrics, value: number, severity: 'critical' | 'warning'): string {
  const suggestions = {
    fcp: { critical: 'Optimize initial bundle size, implement code splitting, and use server-side rendering' }
  warning: 'Consider lazy loading non-critical resources and optimizing images'
    };
    lcp: { critical: 'Optimize largest element loading, implement critical resource hints, and reduce server response times' }
  warning: 'Optimize images and implement efficient caching strategies'
    };
  fid: { critical: 'Break up long-running JavaScript tasks and implement proper event delegation' }
      warning: 'Consider using web workers for heavy computations'
    };
    cls: { critical: 'Add size attributes to images and videos, avoid inserting content above existing content' }
  warning: 'Ensure consistent sizing for dynamic content'
    };
    renderTime: { critical: 'Implement React.memo, useMemo, and useCallback optimizations' }
  warning: 'Consider component virtualization for large lists'
    };
  memoryUsage: { critical: 'Implement proper component cleanup and memory leak detection' }
      warning: 'Consider implementing data pagination and cleanup strategies'
    };
  interactionLatency: { critical: 'Optimize event handlers and implement proper debouncing/throttling' }
      warning: 'Consider using passive event listeners where appropriate'
    };
    networkLatency: { critical: 'Implement proper caching strategies and optimize API calls' }
      warning: 'Consider implementing request deduplication and batching'
    }
  };

  return suggestions[metric]?.[severity] || 'No specific suggestion available';
}

export default useAdvancedPerformanceMonitor;