import React, { memo, useMemo, useCallback } from 'react';
import { motion } from 'framer-motion';
import { Check, <PERSON><PERSON>he<PERSON>, <PERSON>ly, Co<PERSON>, Trash2, X, Paperclip } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui';
import { Button } from '@/components/ui/button';
import VoiceMessagePlayer from './VoiceMessagePlayer';
import MessageOptions from './MessageOptions';
import ThreadIndicator from '../ThreadIndicator';
import MessageThreadComponent from '../MessageThread';
import { Message, Conversation, MessageAttachment } from '@/types/enhanced-messaging';
import { MESSAGING_CONSTANTS } from '@/constants/messaging';

interface MessageRendererProps {
  message: Message, currentUserId: string, activeConversation: Conversation | null, isMobile: boolean, isSelected: boolean, showActions: boolean;
  
  // Thread props
  hasThread: boolean, threadRepliesCount: number, thread: {
    id: string;
    participantCount?: number;
    [key: string]: unknown;
  } | null;
  activeThreadId: string | null, isThreadOpen: boolean;
  
  // Handlers
  onInteraction: (e: React.MouseEvent | React.TouchEvent) => void; onReply: (message: Message) => void; onCopy: (message: Message) => void; onDelete: (messageId: string, deleteForEveryone: boolean) => void; onEdit: (message: Message) => void; onForward: (message: Message) => void; onPin: (message: Message) => void; onStar: (message: Message) => void; onStartThread: (message: Message) => void; onToggleThread: (messageId: string) => void; onReplyToThread: (messageId: string, content: string) => Promise<void>; onCloseThread: () => void; onClearSelection: () => void; downloadFile: (attachment: { id: string, name: string, size: number;
    [key: string]: unknown; }) => void;
  
  // Original message for replies
  originalMessage?: Message | null;
}

// Optimized message status component
const MessageStatus = memo<{ status?: string }>(({ status }) => {
  switch (status) {
    case 'sent':
      return <Check className="w-3 h-3 text-gray-400" />;
    case 'delivered':
      return <CheckCheck className="w-3 h-3 text-gray-400" />;
    case 'read':
      return <CheckCheck className="w-3 h-3 text-blue-500" />;
    default:
      return null;
  }
});

MessageStatus.displayName = 'MessageStatus';

// Optimized file attachment component
const FileAttachment = memo<{
  attachment: MessageAttachment, onDownload: (attachment: MessageAttachment) => void;
}>(({ attachment, onDownload }) => {
  const sizeInKB = useMemo(() => Math.round(attachment.size / 1024), [attachment.size]);
  
  return (
    <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-3 bg-gray-50 dark:bg-gray-800">
      <div className="flex items-center space-x-3">
        <div className="flex-shrink-0">
          <Paperclip className="w-5 h-5 text-gray-500" />
    </div>
        <div className="flex-1 min-w-0">
          <p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
            {attachment.name}
          </p>
          <p className="text-xs text-gray-500 dark:text-gray-400">
            {sizeInKB} KB
          </p>
    </div>
        <div className="flex-shrink-0">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onDownload(attachment)} className="text-blue-600 hover:text-blue-800 dark:text-blue-400"
          >
            Download
          </Button>
    </div>
      </div>
    </div>
  );
});

FileAttachment.displayName = 'FileAttachment';

// Optimized message actions component
const QuickActions = memo<{
  message: Message, onReply: (message: Message) => void; onCopy: (message: Message) => void; onDelete: (messageId: string, deleteForEveryone: boolean) => void; onClearSelection: () => void;
}>(({ message, onReply, onCopy, onDelete, onClearSelection }) => {
  const handleReply = useCallback(() => {
    onReply(message);
    onClearSelection();
  }, [message, onReply, onClearSelection]);

  const handleCopy = useCallback(() => {
    onCopy(message);
    onClearSelection();
  }, [message, onCopy, onClearSelection]);

  const handleDelete = useCallback(() => {
    onDelete(message.id, false);
    onClearSelection();
  }, [message.id, onDelete, onClearSelection]);

  return (
    <div className="mt-3 flex items-center justify-center space-x-4 p-2 bg-gray-50 dark:bg-gray-800 rounded-lg border">
      <Button
        variant="ghost"
        size="sm"
        onClick={handleReply} className="flex items-center space-x-1"
      >
        <Reply className="w-4 h-4" />
        <span className="text-xs">Reply</span>
    </Button>
      {message.content && (
        <Button
          variant="ghost"
          size="sm"
          onClick={handleCopy} className="flex items-center space-x-1"
        >
          <Copy className="w-4 h-4" />
          <span className="text-xs">Copy</span>
    </Button>
      )}
      
      <Button
        variant="ghost"
        size="sm"
        onClick={handleDelete} className="flex items-center space-x-1 text-red-600"
      >
        <Trash2 className="w-4 h-4" />
        <span className="text-xs">Delete</span>
    </Button>
      <Button
        variant="ghost"
        size="sm"
        onClick={onClearSelection} className="flex items-center space-x-1"
      >
        <X className="w-4 h-4" />
        <span className="text-xs">Cancel</span>
    </Button>
    </div>
  );
});

QuickActions.displayName = 'QuickActions';

const MessageRenderer: React.FC<MessageRendererProps> = memo(({
  message,
  currentUserId,
  activeConversation,
  isMobile,
  isSelected,
  showActions,
  hasThread,
  threadRepliesCount,
  thread,
  activeThreadId,
  isThreadOpen,
  onInteraction,
  onReply,
  onCopy,
  onDelete,
  onEdit,
  onForward,
  onPin,
  onStar,
  onStartThread,
  onToggleThread,
  onReplyToThread,
  onCloseThread,
  onClearSelection,
  downloadFile,
  originalMessage
}) => {
  const isOwnMessage = useMemo(() => message.senderId === currentUserId, [message.senderId, currentUserId]);
  
  // Memoize formatted time to avoid recalculation
  const formattedTime = useMemo(() => {
    return new Intl.DateTimeFormat('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    }).format(new Date(message.timestamp));
  }, [message.timestamp]);
  
  // Memoize message styling classes
  const messageClasses = useMemo(() => {
    if (message.type === MESSAGING_CONSTANTS.MESSAGE_TYPES.AUDIO) return '';
    if (message.isDeleted) return 'bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700';
    return isOwnMessage 
      ? 'bg-blue-500 text-white' 
      : 'bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-white';
  }, [message.type, message.isDeleted, isOwnMessage]);
  
  // Memoize padding classes
  const paddingClasses = useMemo(() => {
    return message.type === MESSAGING_CONSTANTS.MESSAGE_TYPES.AUDIO ? '' : (isMobile ? 'p-4' : 'p-3');
  }, [message.type, isMobile]);

  return (
    <motion.div
      key={message.id} initial={MESSAGING_CONSTANTS.UI.ANIMATION.MESSAGE_INITIAL}, animate={MESSAGING_CONSTANTS.UI.ANIMATION.MESSAGE_ANIMATE} className={`group flex ${isOwnMessage ? 'justify-end' : 'justify-start'}, mb-3`}
    >
      {/* Non-own message avatar */}
      {!isOwnMessage && (
        <Avatar className="w-6 h-6 mr-2 flex-shrink-0">
          <AvatarImage src={activeConversation?.user?.avatar} />
          <AvatarFallback className="text-xs">
            {activeConversation?.user?.name?.[0]}
          </AvatarFallback>
    </Avatar>
      )}

      <div className={`flex items-start gap-2 ${isOwnMessage ? 'flex-row-reverse' : 'flex-row'} ${isMobile ? MESSAGING_CONSTANTS.UI.MESSAGE_MAX_WIDTH.MOBILE : MESSAGING_CONSTANTS.UI.MESSAGE_MAX_WIDTH.DESKTOP}`}>
        {/* Message Content Container */}
        <div 
          className={`flex-1 ${
            isSelected ? 'ring-2 ring-blue-500 ring-opacity-50 rounded-lg' : ''
          }`}, onClick={onInteraction} onDoubleClick={onInteraction}
        >
          {/* Reply indicator */}
          {originalMessage && (
            <div className="bg-gray-100 dark:bg-gray-800 p-2 rounded-t-lg text-xs border-l-2 border-blue-500">
              <span className="text-gray-600 dark:text-gray-400">
                Replying to: {originalMessage.content?.slice(0, 50)}...
              </span>
    </div>
          )}
          
          {/* Message content */}
          <div className={`rounded-lg relative ${messageClasses} ${paddingClasses}`}>
            
            {/* Render deleted message */}
            {message.isDeleted ? (
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 text-gray-400">
                  <svg viewBox="0 0 16 16" fill="currentColor">
                    <path d="M8 0a8 8 0 100 16A8 8 0 008 0zM4.5 7.5a.5.5 0 000 1h7a.5.5 0 000-1h-7z"/>
    </svg>
                </div>
                <span className={`italic text-gray-500 ${isMobile ? 'text-base' : 'text-sm'}`}>
                  This message was deleted
                </span>
    </div>
            ) : (
              <>
                {/* Render voice message player for audio messages */}
                {message.type === MESSAGING_CONSTANTS.MESSAGE_TYPES.AUDIO && message.attachments?.[0] ? (
                  <VoiceMessagePlayer
                    voiceMessage={{
                      id: message.attachments[0].id || `voice-${message.id}`,
                      url: message.attachments[0].url || '',
                      duration: message.attachments[0].duration || 0,
                      waveform: (message as Message & { voiceMessage?: { waveform?: number[] } }).voiceMessage?.waveform || [],
                      fileSize: message.attachments[0].size || 0,
                      mimeType: message.attachments[0].mimeType || 'audio/webm',
                      recordedAt: new Date(message.timestamp),
                      quality: 'medium' as const
                    }}, isCurrentUser={isOwnMessage}
                  />
                ) : (
                  <>
                    {/* Render file attachments for file messages */}
                    {message.type === MESSAGING_CONSTANTS.MESSAGE_TYPES.FILE && message.attachments && message.attachments.length > 0 && (
                      <div className="space-y-2">
                        {message.attachments.map((attachment) => (
                          <FileAttachment
                            key={attachment.id} attachment={attachment}, onDownload={downloadFile}
                          />
                        ))}
                        {message.content && message.content !== `📎 ${message.attachments[0]?.name}` && (
                          <p className={`break-words mt-2 ${isMobile ? 'text-base leading-relaxed' : 'text-sm'}`}>
                            {message.content}
                            {message.isEdited && (
                              <span className="text-xs opacity-70 ml-2">(edited)</span>
                            )}
                          </p>
                        )}
                      </div>
                    )}
                    
                    {/* Render regular text message */}
                    {(!message.type || message.type === MESSAGING_CONSTANTS.MESSAGE_TYPES.TEXT) && (
                      <p className={`break-words ${isMobile ? 'text-base leading-relaxed' : 'text-sm'}`}>
                        {message.content || ''}
                        {message.isEdited && (
                          <span className="text-xs opacity-70 ml-2">(edited)</span>
                        )}
                      </p>
                    )}
                  </>
                )}
              </>
            )}

            {/* Reactions */}
            {message.reactions && message.reactions.length > 0 && (
              <div className="flex gap-1 mt-2">
                {message.reactions.map((reaction, index) => (
                  <span key={index} className="text-xs bg-white bg-opacity-20 rounded-full px-2 py-1">
                    {reaction.emoji}
                  </span>
                ))}
              </div>
            )}
          </div>

          {/* Quick Action Bar for Mobile */}
          {isMobile && showActions && !message.isDeleted && (
            <QuickActions
              message={message} onReply={onReply}, onCopy={onCopy} onDelete={onDelete}, onClearSelection={onClearSelection}
            />
          )}

          {/* Message timestamp */}
          <div className={`flex items-center gap-1 mt-1 text-xs text-gray-500 ${
            isOwnMessage ? 'justify-end' : 'justify-start'
          }`}>
            <span>{formattedTime}</span>
            {isOwnMessage && <MessageStatus status={message.status} />}
          </div>
    </div>
        {/* Message Options - Always visible to the right/left of message */}
        {!message.isDeleted && (
          <MessageOptions
            message={message} isOwnMessage={isOwnMessage}, onReply={onReply} onForward={onForward}, onCopy={onCopy} onEdit={onEdit}, onDelete={onDelete} onPin={onPin}, onStar={onStar} onStartThread={onStartThread}, className="flex-shrink-0 mt-1"
          />
        )}
      </div>

      {/* Thread Indicator */}
      {(hasThread || message.threadReplies?.length) && (
        <div className={`mt-2 ${isOwnMessage ? 'mr-12' : 'ml-12'}`}>
          <ThreadIndicator
            replyCount={threadRepliesCount || message.threadReplies?.length || 0} participantCount={thread?.participantCount}, hasActiveThread={activeThreadId === message.id} onClick={() => onToggleThread(message.id)}, size="sm"
          />
    </div>
      )}

      {/* Thread Component */}
      {activeThreadId === message.id && isThreadOpen && thread && (
        <div className={`mt-3 ${isOwnMessage ? 'mr-4' : 'ml-4'}`}>
          <MessageThreadComponent
            thread={thread} parentMessage={message}, isOpen={isThreadOpen} onToggle={() => onToggleThread(message.id)}, onReply={async (content) => await onReplyToThread(message.id; content)} onClose={onCloseThread}, currentUserId={currentUserId} users={{
              [currentUserId]: {
                id: currentUserId,
                name: 'You',
                avatar: '',
                isOnline: true,
                lastActive: new Date().toISOString(),
                username: currentUserId
              }
            }}
          />
    </div>
      )}
    </motion.div>
  );
});

MessageRenderer.displayName = 'MessageRenderer';

export default MessageRenderer;
