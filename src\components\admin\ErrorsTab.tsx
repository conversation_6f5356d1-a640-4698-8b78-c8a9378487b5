import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { AlertTriangle, Trash2, RefreshCw, Search, Calendar, ChevronDown, ChevronUp, Bug } from 'lucide-react';
import { errorLoggingService } from '@/services/errorLoggingService';
import type { ErrorLog } from '@/services/errorLoggingService';
import { format } from 'date-fns';
import { toast } from 'sonner';
import { Badge } from '@/components/ui/badge';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';


const ErrorsTab: React.FC = () => {
  const [errors, setErrors] = useState<ErrorLog[]>([]);
  const [filteredErrors, setFilteredErrors] = useState<ErrorLog[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [severityFilter, setSeverityFilter] = useState<'all' | 'critical' | 'error' | 'warning'>('all');
  const [refreshInterval, setRefreshInterval] = useState<number>(30000); // 30 seconds default
  const [expandedErrors, setExpandedErrors] = useState<Set<string>>(new Set());
  const [isLoading, setIsLoading] = useState(false);

  const filterErrors = (errorList: ErrorLog[], query: string, severity: string) => {
    let filtered = errorList;

    // Filter by severity
    if (severity !== 'all') {
      filtered = filtered.filter(error => error.severity === severity);
    }

    // Filter by search query
    if (query) {
      const lowerQuery = query.toLowerCase();
      filtered = filtered.filter(error => 
        error.message.toLowerCase().includes(lowerQuery) ||
        error.url?.toLowerCase().includes(lowerQuery) ||
        error.stack?.toLowerCase().includes(lowerQuery)
      );
    }

    setFilteredErrors(filtered);
  };

  const loadErrors = useCallback(() => {
    setIsLoading(true);
    const allErrors = errorLoggingService.getErrors();
    setErrors(allErrors);
    filterErrors(allErrors, searchQuery, severityFilter);
    setIsLoading(false);
  }, [searchQuery, severityFilter]);

  // Load errors on mount and subscribe to updates
  useEffect(() => {
    loadErrors();
    const unsubscribe = errorLoggingService.subscribe((updatedErrors) => {
      setErrors(updatedErrors);
      filterErrors(updatedErrors, searchQuery, severityFilter);
    });

    return () => {
      unsubscribe();
    };
  }, [loadErrors, searchQuery, severityFilter]);

  // Set up auto-refresh
  useEffect(() => {
    if (refreshInterval > 0) {
      const interval = setInterval(() => {
        loadErrors();
      }, refreshInterval);

      return () => clearInterval(interval);
    }
  }, [refreshInterval, loadErrors]);

  // Filter errors when search or severity filter changes
  useEffect(() => {
    filterErrors(errors, searchQuery, severityFilter);
  }, [errors, searchQuery, severityFilter]);

  const handleDeleteError = (id: string) => {
    errorLoggingService.deleteError(id);
    toast.success('Error log deleted');
  };

  const handleDeleteAllErrors = () => {
    if (window.confirm('Are you sure you want to delete all error logs?')) {
      errorLoggingService.deleteAllErrors();
      toast.success('All error logs deleted');
    }
  };

  const handleDeleteBySeverity = () => {
    if (severityFilter !== 'all' && window.confirm(`Delete all ${severityFilter} errors?`)) {
      errorLoggingService.deleteErrorsBySeverity(severityFilter);
      toast.success(`All ${severityFilter} errors deleted`);
    }
  };

  const toggleErrorExpansion = (id: string, open: boolean) => {
    const newExpanded = new Set(expandedErrors);
    if (open) {
      newExpanded.add(id);
    } else {
      newExpanded.delete(id);
    }
    setExpandedErrors(newExpanded);
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'error':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
    }
  };

  return (
    <div className="space-y-4">
      {/* Test Error Generator for Development */}
      
      
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Bug className="h-5 w-5" />
                Error Logs
              </CardTitle>
              <CardDescription>
                Monitor and manage application errors. Showing {filteredErrors.length} of {errors.length} errors.
              </CardDescription>
    </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={loadErrors} disabled={isLoading}
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
              <Button
                variant="destructive"
                size="sm"
                onClick={handleDeleteAllErrors} disabled={errors.length === 0}
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Clear All
              </Button>
    </div>
          </div>
    </CardHeader>
        <CardContent>
          {/* Filters and Controls */}
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search errors..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
    </div>
            </div>
            
            <Select value={severityFilter} onValueChange={(value: 'all' | 'critical' | 'error' | 'warning') => setSeverityFilter(value)}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by severity" />
    </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Severities</SelectItem>
                <SelectItem value="critical">Critical Only</SelectItem>
                <SelectItem value="error">Errors Only</SelectItem>
                <SelectItem value="warning">Warnings Only</SelectItem>
    </SelectContent>
            </Select>

            <Select value={refreshInterval.toString()} onValueChange={(value) => setRefreshInterval(parseInt(value))}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Auto-refresh" />
    </SelectTrigger>
              <SelectContent>
                <SelectItem value="0">No auto-refresh</SelectItem>
                <SelectItem value="10000">Every 10 seconds</SelectItem>
                <SelectItem value="30000">Every 30 seconds</SelectItem>
                <SelectItem value="60000">Every minute</SelectItem>
                <SelectItem value="300000">Every 5 minutes</SelectItem>
    </SelectContent>
            </Select>

            {severityFilter !== 'all' && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleDeleteBySeverity} disabled={filteredErrors.length === 0}
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete {severityFilter}
              </Button>
            )}
          </div>

          {/* Error List */}
          <div className="space-y-3">
            {filteredErrors.length === 0 ? (
              <div className="text-center py-12">
                <AlertTriangle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">No errors found matching your criteria.</p>
    </div>
            ) : (
              filteredErrors.map((error) => (
                <Collapsible
                  key={error.id}
                  open={expandedErrors.has(error.id)}
                  onOpenChange={(open) => toggleErrorExpansion(error.id, open)}
                >
                  <Card className="border-l-4" style={{
                    borderLeftColor: error.severity === 'critical' ? '#ef4444' : 
                                   error.severity === 'error' ? '#f97316' : '#eab308'
                  }}>
                    <CardHeader className="pb-3">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <Badge className={getSeverityColor(error.severity)}>
                              {error.severity.toUpperCase()}
                            </Badge>
                            <span className="text-xs text-gray-500 dark:text-gray-400">
                              <Calendar className="inline h-3 w-3 mr-1" />
                              {format(new Date(error.timestamp), 'MMM dd, yyyy HH:mm:ss')}
                            </span>
                            {error.userId && (
                              <span className="text-xs text-gray-500 dark:text-gray-400">
                                User: {error.userId.substring(0, 8)}...
                              </span>
                            )}
                          </div>
                          <p className="font-medium text-sm break-words">{error.message}</p>
                          {error.url && (
                            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                              {error.url}
                            </p>
                          )}
                        </div>
                        <div className="flex items-center gap-2 ml-4">
                          <CollapsibleTrigger asChild>
                            <Button variant="ghost" size="sm">
                              {expandedErrors.has(error.id) ? (
                                <ChevronUp className="h-4 w-4" />
                              ) : (
                                <ChevronDown className="h-4 w-4" />
                              )}
                            </Button>
    </CollapsibleTrigger>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteError(error.id)}
                          >
                            <Trash2 className="h-4 w-4 text-red-500" />
    </Button>
                        </div>
    </div>
                    </CardHeader>
                    <CollapsibleContent>
                      <CardContent className="pt-0">
                        {error.stack && (
                          <div className="mt-3">
                            <p className="text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1">
                              Stack Trace:
                            </p>
                            <pre className="text-xs bg-gray-100 dark:bg-gray-800 p-3 rounded overflow-x-auto">
                              {error.stack}
                            </pre>
    </div>
                        )}
                        {error.context && Object.keys(error.context).length > 0 && (
                          <div className="mt-3">
                            <p className="text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1">
                              Additional Context:
                            </p>
                            <pre className="text-xs bg-gray-100 dark:bg-gray-800 p-3 rounded overflow-x-auto">
                              {JSON.stringify(error.context, null, 2)}
                            </pre>
    </div>
                        )}
                        {error.userAgent && (
                          <div className="mt-3">
                            <p className="text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1">
                              User Agent:
                            </p>
                            <p className="text-xs text-gray-600 dark:text-gray-400">
                              {error.userAgent}
                            </p>
    </div>
                        )}
                      </CardContent>
    </CollapsibleContent>
                  </Card>
    </Collapsible>
              ))
            )}
          </div>
    </CardContent>
      </Card>
    </div>
  );
};

export default ErrorsTab;
