import { render } from '@testing-library/react';
import { screen, fireEvent, waitFor } from '@testing-library/dom';
import { describe, it, expect, vi } from 'vitest';
import UnifiedNewsFeedFilters from '../UnifiedNewsFeedFilters';

// Mock props
const mockProps = {
  activeTab: 'foryou',
  onTabChange: vi.fn(),
  creator: 'all',
  setCreator: vi.fn(),
  dateRange: 'all',
  setDateRange: vi.fn(),
  location: '',
  setLocation: vi.fn(),
  liked: false,
  setLiked: vi.fn(),
  saved: false,
  setSaved: vi.fn(),
  onApply: vi.fn(),
  onClear: vi.fn()
};

describe('UnifiedNewsFeedFilters', () => {
  it('renders all tab triggers', () => {
    render(<UnifiedNewsFeedFilters {...mockProps} />);
    
    expect(screen.getByText('For you')).toBeInTheDocument();
    expect(screen.getByText('Photos')).toBeInTheDocument();
    expect(screen.getByText('Videos')).toBeInTheDocument();
    expect(screen.getByText('Popular')).toBeInTheDocument();
  });

  it('renders quick filter buttons', () => {
    render(<UnifiedNewsFeedFilters {...mockProps} />);
    
    expect(screen.getByText('Trending')).toBeInTheDocument();
    expect(screen.getByText('Recent')).toBeInTheDocument();
    expect(screen.getByText('Friends')).toBeInTheDocument();
    expect(screen.getByText('Verified')).toBeInTheDocument();
  });

  it('renders search input', () => {
    render(<UnifiedNewsFeedFilters {...mockProps} />);
    
    expect(screen.getByPlaceholderText('Search posts...')).toBeInTheDocument();
  });

  it('calls onTabChange when tab is clicked', () => {
    render(<UnifiedNewsFeedFilters {...mockProps} />);
    
    fireEvent.click(screen.getByText('Photos'));
    expect(mockProps.onTabChange).toHaveBeenCalledWith('photos');
  });

  it('shows advanced filters when filter button is clicked', async () => {
    render(<UnifiedNewsFeedFilters {...mockProps} />);
    
    const filterButton = screen.getByRole('button', { name: /filters/i });
    fireEvent.click(filterButton);
    
    await waitFor(() => {
      expect(screen.getByText('Creator')).toBeInTheDocument();
      expect(screen.getByText('Date Range')).toBeInTheDocument();
    });
  });

  it('calls onApply when Apply Filters button is clicked', async () => {
    render(<UnifiedNewsFeedFilters {...mockProps} />);
    
    // Open advanced filters
    const filterButton = screen.getByRole('button', { name: /filters/i });
    fireEvent.click(filterButton);
    
    await waitFor(() => {
      const applyButton = screen.getByText('Apply Filters');
      fireEvent.click(applyButton);
      expect(mockProps.onApply).toHaveBeenCalled();
    });
  });

  it('calls onClear when Clear All button is clicked', async () => {
    render(<UnifiedNewsFeedFilters {...mockProps} />);
    
    // Open advanced filters
    const filterButton = screen.getByRole('button', { name: /filters/i });
    fireEvent.click(filterButton);
    
    await waitFor(() => {
      const clearButton = screen.getByText('Clear All');
      fireEvent.click(clearButton);
      expect(mockProps.onClear).toHaveBeenCalled();
    });
  });

  it('shows active filter count when filters are applied', () => {
    const propsWithFilters = {
      ...mockProps,
      creator: 'friends',
      liked: true
    };
    
    render(<UnifiedNewsFeedFilters {...propsWithFilters} />);
    
    expect(screen.getByText('2')).toBeInTheDocument(); // Active filter count badge
  });

  it('shows liked and saved badges when active', () => {
    const propsWithActive = {
      ...mockProps,
      liked: true,
      saved: true
    };
    
    render(<UnifiedNewsFeedFilters {...propsWithActive} />);
    
    expect(screen.getByText('Liked')).toBeInTheDocument();
    expect(screen.getByText('Saved')).toBeInTheDocument();
  });
});