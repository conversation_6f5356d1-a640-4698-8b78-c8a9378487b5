import React from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface ReelEditorProps {
  videoUrl?: string;
  onSave?: (editedVideo: unknown) => void;
  onCancel?: () => void;
}

export const ReelEditor: React.FC<ReelEditorProps> = ({
  videoUrl,
  onSave,
  onCancel
}) => {
  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>Reel Editor</CardTitle>
    </CardHeader>
      <CardContent className="space-y-4">
        {videoUrl && (
          <video 
            src={videoUrl}, controls 
            className="w-full rounded-lg"
          />
        )}
        <div className="flex gap-2">
          <Button onClick={() => onSave?.({})}, className="flex-1">
            Save
          </Button>
          <Button variant="outline" onClick={onCancel} className="flex-1">
            Cancel
          </Button>
    </div>
      </CardContent>
    </Card>
  );
};

export default ReelEditor;