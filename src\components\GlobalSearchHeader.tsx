import React, { useState, useCallback } from 'react';
import { Search, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { useNavigate } from 'react-router-dom';
import { toast } from 'sonner';

interface GlobalSearchHeaderProps {
  onSearchFocus?: () => void;
  onSearchResult?: (query: string) => void;
  className?: string;
  placeholder?: string;
  showQuickFilters?: boolean;
}

const GlobalSearchHeader: React.FC<GlobalSearchHeaderProps> = ({
  onSearchFocus,
  onSearchResult,
  className,
  placeholder = "Search Facebook",
  showQuickFilters = true
}) => {
  const [query, setQuery] = useState('');
  const navigate = useNavigate();

  // Handle input changes
  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    e.stopPropagation();
    const value = e.target.value;
    setQuery(value);
  }, []);

  // Handle form submission
  const handleSubmit = useCallback((e: React.FormEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (!query.trim()) {
      return;
    }
    
    // Navigate to search results page with query parameter
    navigate(`/search?q=${encodeURIComponent(query.trim())}`);
    
    // Show success toast
    toast.success(`🔍 Searching for "${query.trim()}"`);
    
    // Clear the input after successful navigation
    setQuery('');
    
    // Call the callback if provided
    onSearchResult?.(query);
  }, [query, navigate, onSearchResult]);

  // Handle input focus
  const handleFocus = useCallback(() => {
    onSearchFocus?.();
  }, [onSearchFocus]);

  // Handle input click
  const handleClick = useCallback((e: React.MouseEvent<HTMLInputElement>) => {
    e.stopPropagation();
    e.currentTarget.focus();
  }, []);

  // Clear input
  const clearInput = useCallback(() => {
    setQuery('');
  }, []);

  return (
    <div className={cn("relative", className)}>
      <form onSubmit={handleSubmit} className="relative">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 z-10" />
          <input
            type="text"
            value={query} onChange={handleInputChange}, onFocus={handleFocus} onClick={handleClick}, placeholder={placeholder} className="w-full pl-10 pr-12 h-10 bg-gray-100 dark:bg-gray-700 border-0 rounded-full text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:bg-white dark:focus:bg-gray-800 relative z-20"
            style={{ pointerEvents: 'auto', cursor: 'text' }}
            data-testid="search-input"
          />
          {query && (
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={clearInput} className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-full z-30"
            >
              <X className="w-3 h-3" />
    </Button>
          )}
        </div>
    </form>
    </div>
  );
};

export default GlobalSearchHeader;