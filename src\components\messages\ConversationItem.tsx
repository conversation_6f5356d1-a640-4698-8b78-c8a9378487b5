import React, { memo } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui';
import { Badge } from '@/components/ui/badge';
import OnlineStatus from '../OnlineStatus';
import { Conversation } from '@/types/messaging';

interface ConversationItemProps {
  conversation: Conversation, isSelected: boolean, onClick: (id: string) => void;
}

const ConversationItem: React.FC<ConversationItemProps> = memo(({ 
  conversation, 
  isSelected, 
  onClick 
}) => {
  // Get display data for both direct and group conversations
  const displayName = conversation.user?.name || conversation.name || 'Unknown';
  const displayAvatar = conversation.user?.avatar || '';
  const isOnline = conversation.user?.isOnline || false;
  
  const handleClick = () => {
    onClick(conversation.id);
  };
  
  return (
    <div
      className={`p-3 hover:bg-gray-50 cursor-pointer flex items-center space-x-3 dark:hover:bg-gray-700 ${
        isSelected ? 'bg-blue-50 dark:bg-blue-900/20' : ''
      } ${conversation.unreadCount > 0 ? 'bg-blue-50 dark:bg-blue-900/20' : ''}`}, onClick={handleClick} role="button"
      tabIndex={0} onKeyPress={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          handleClick();
        }
      }}
      aria-label={`Conversation with ${displayName}`}
      aria-selected={isSelected}
    >
      <div className="relative">
        <Avatar className="h-12 w-12">
          <AvatarImage src={displayAvatar} alt={displayName} />
          <AvatarFallback aria-label={`${displayName}, avatar`}>
            {displayName.charAt(0).toUpperCase()}
          </AvatarFallback>
    </Avatar>
        <OnlineStatus isOnline={isOnline} />
    </div>
      <div className="flex-1 min-w-0">
        <div className="flex items-center justify-between">
          <p className="font-medium text-sm text-gray-900 truncate dark:text-white">
            {displayName}
          </p>
          <p className="text-xs text-gray-500 dark:text-gray-400">
            {conversation.lastMessage?.timestamp}
          </p>
    </div>
        <div className="flex items-center justify-between">
          <p className={`text-sm truncate ${
            conversation.unreadCount ? 'text-gray-900 font-medium dark:text-white' : 'text-gray-500 dark:text-gray-400'
          }`}>
            {conversation.lastMessage?.content}
          </p>
          {conversation.unreadCount ? (
            <Badge className="ml-2 bg-blue-600" aria-label={`${conversation.unreadCount}, unread messages`}>
              {conversation.unreadCount}
            </Badge>
          ) : null}
        </div>
    </div>
    </div>
  );
});

ConversationItem.displayName = 'ConversationItem';

export default ConversationItem;
