//!/usr/bin/env node

/**
 * Precise repair script to undo underscore-induced syntax corruption
 * Scope: Only .ts/.tsx files under ./src
 * Features:
 *  - Dry-run mode (default): shows planned changes per file with line-level diff summary
 *  - Apply mode (--apply): writes changes
 *  - Logs counts per rule and per file
 *
 * Patterns fixed:
 *  1) Arrow params: _() =>  -> () =>
 *  2) Stray leading "_" before JSX tags and some delimiters: _<Tag ... -> <Tag ... ; _'text' -> 'text'
 *  3) Generics like Omit<...,_'id'> -> Omit<...,'id'> (removes stray '_' before string literal)
 *  4) Common underscore placeholders replacing separators near function/type signatures:
 *       - ",_" -> ", " in type parameter lists or argument lists
 *       - "_>" immediately before '>' in generics becomes ''
 *       - "_(" immediately before '(' after keywords like map, forEach becomes '('
 *       - "_{" after "=>" becomes "{"
 *  5) Avoid touching legitimate identifiers that start with "_"
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const SRC_DIR = path.join(__dirname, '..', 'src');

const args = process.argv.slice(2);
const APPLY = args.includes('--apply');

function listTsFiles(dir) {
  const out = [];
  const stack = [dir];
  while (stack.length) {
    const d = stack.pop();
    if (!d) continue;
    let entries = [];
    try {
      entries = fs.readdirSync(d, { withFileTypes: true });
    } catch {
      continue;
    }
    for (const e of entries) {
      if (e.name === 'node_modules' || e.name === 'dist' || e.name === '.git' || e.name === 'coverage') continue;
      const full = path.join(d, e.name);
      if (e.isDirectory()) {
        stack.push(full);
      } else if (e.isFile() && (e.name.endsWith('.ts') || e.name.endsWith('.tsx'))) {
        out.push(full);
      }
    }
  }
  return out;
}

function applyRepairs(source, filePath) {
  let text = source;

  const counters = {
    arrowParams: 0,
    jsxPrefix: 0,
    stringPrefix: 0,
    genericStringUnderscore: 0,
    commaUnderscore: 0,
    underscoreBeforeGt: 0,
    underscoreBeforeParen: 0,
    underscoreBeforeBrace: 0,
    jsxAttrCommaFix: 0,
    jsxClassNameMergeFix: 0,
  };

  // 1) Arrow params: _() => -> () =>
  // Case: useMemo(_() => ...), useEffect(_() => ...), setTimeout(_() => ...)
  text = text.replace(
    /(\b(?:useMemo|useEffect|useCallback|setTimeout|setInterval|waitFor|forEach|map|filter|reduce|some|every)\s*\()\s*_\(\)\s*=>/g,
    (m, p1) => {
      counters.arrowParams++;
      return `${p1}() =>`;
    }
  );

  // Case: standalone start of arrow function: _() => ...
  text = text.replace(/(^|[^\w$])_\(\)\s*=>/g, (m, p1) => {
    counters.arrowParams++;
    return `${p1}() =>`;
  });

  // Case: arrow returning arrow: => _() => ...
  text = text.replace(/=>\s*_\(\)\s*=>/g, () => {
    counters.arrowParams++;
    return '=> () =>';
  });

  // 2) Remove stray "_" before JSX tags like _<div ... or _</div>
  text = text.replace(/_\s*(<\s*\/?\s*[A-Za-z][^>]*>)/g, (m, tag) => {
    counters.jsxPrefix++;
    return tag;
  });

  // 2b) Remove stray "_" before string delimiters in obvious non-identifier contexts: _("text"), _'text'
  text = text.replace(/(^|[^A-Za-z0-9_$])_(['"])/g, (m, p1, q) => {
    counters.stringPrefix++;
    return `${p1}${q}`;
  });

  // 3) Fix generics with underscore before string literal: <...,_'id' | ...> or <...,_"id"...>
  text = text.replace(/(<[^>]*?)_\s*'([^']+)'/g, (m, before, key) => {
    counters.genericStringUnderscore++;
    return `${before}'${key}'`;
  });
  text = text.replace(/(<[^>]*?)_\s*"([^"]+)"/g, (m, before, key) => {
    counters.genericStringUnderscore++;
    return `${before}"${key}"`;
  });

  // 4a) ",_" -> ", " in argument/type lists conservatively
  text = text.replace(/,(\s*)_(?=\s*[A-Za-z0-9_'"}\])>])/g, (m, sp) => {
    counters.commaUnderscore++;
    return `,${sp}`;
  });

  // 4b) Remove stray "_" immediately before ">" in generics
  text = text.replace(/_(?=\s*>)/g, () => {
    counters.underscoreBeforeGt++;
    return '';
  });

  // 4c) Remove stray "_" before "(" in common call-sites: map(_(x)=>...), forEach(_(...)->...)
  text = text.replace(/(\b(?:map|forEach|filter|reduce|some|every|setTimeout|setInterval|useMemo|useEffect|useCallback|waitFor)\s*\()\s*_/g, (m, p1) => {
    counters.underscoreBeforeParen++;
    return p1;
  });

  // 4d) Remove stray "_" before "{" when directly following "=>"
  text = text.replace(/=>\s*_({)/g, (m, brace) => {
    counters.underscoreBeforeBrace++;
    return `=> ${brace}`;
  });

  // 4e) JSX attribute commas replaced by underscores in some cases:
  // Example observed: className={cn(...)} isDeleted && ... got merged; fix only safe pattern: "}\s+is" preceded by string/paren without comma
  // We will not insert commas aggressively; instead, we fix cases like: "..., \"text\"} is" -> "..., \"text\"}, is"
  text = text.replace(/(\}\s*)(?=is[A-Z])/g, (m, brace) => {
    counters.jsxAttrCommaFix++;
    return `${brace}, `;
  });

  // 4f) Fix pattern where "className={cn(...)} isDeleted" lost comma between props; ensure there's a space-comma-space:
  text = text.replace(/(\)\s*\})\s+(is[A-Za-z0-9_]+)/g, (m, group1, prop) => {
    counters.jsxClassNameMergeFix++;
    return `${group1} ${prop.startsWith(',') ? '' : ', '}${prop.replace(/^,?\s*/, '')}`;
  });

  return { text, counters };
}

function summarizeDiff(original, updated) {
  const origLines = original.split('\n');
  const updLines = updated.split('\n');
  let changed = 0;
  const len = Math.max(origLines.length, updLines.length);
  for (let i = 0; i < len; i++) {
    if (origLines[i] !== updLines[i]) changed++;
  }
  return { changedLines: changed, originalLines: origLines.length, updatedLines: updLines.length };
}

function main() {
  if (!fs.existsSync(SRC_DIR)) {
    console.error('src directory not found at', SRC_DIR);
    process.exit(1);
  }

  const files = listTsFiles(SRC_DIR);
  let totalFilesTouched = 0;
  const totals = {
    arrowParams: 0,
    jsxPrefix: 0,
    stringPrefix: 0,
    genericStringUnderscore: 0,
    commaUnderscore: 0,
    underscoreBeforeGt: 0,
    underscoreBeforeParen: 0,
    underscoreBeforeBrace: 0,
    jsxAttrCommaFix: 0,
    jsxClassNameMergeFix: 0,
    changedLines: 0
  };

  console.log(`Scanning ${files.length} TS/TSX files under src`);
  console.log(APPLY ? 'Mode: APPLY (writing changes)\n' : 'Mode: DRY-RUN (no changes written)\n');

  for (const file of files) {
    let original = '';
    try {
      original = fs.readFileSync(file, 'utf8');
    } catch {
      continue;
    }
    const { text: updated, counters } = applyRepairs(original, file);
    if (updated !== original) {
      const summary = summarizeDiff(original, updated);
      totalFilesTouched++;
      totals.changedLines += summary.changedLines;
      Object.keys(counters).forEach(k => {
        totals[k] += counters[k];
      });

      console.log(`- ${path.relative(process.cwd(), file)}: ${summary.changedLines} lines changed`);
      console.log(`  fixes: ${JSON.stringify(counters)}`);

      if (APPLY) {
        try {
          fs.writeFileSync(file, updated, 'utf8');
        } catch (e) {
          console.error(`  ! Failed to write ${file}: ${e?.message || e}`);
        }
      }
    }
  }

  console.log('\nSummary:');
  console.log(`  Files with changes: ${totalFilesTouched}`);
  console.log(`  Total changed lines: ${totals.changedLines}`);
  console.log('  Rule counters:', totals);
  console.log('\nNext steps:');
  console.log('  1) Review the dry-run output above. If it looks correct, run:');
  console.log('     node scripts/repair-underscores.js --apply');
  console.log('  2) Then re-run type-check to measure reduction in errors.');
  console.log('  3) If any file still shows underscore artifacts, we can add targeted rules safely.');
}

main();