import React, { useState, useEffect, useCallback, memo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Package, 
  FileText, 
  Eye,
  Download,
  Search,
  Filter,
  BarChart3,
  PieChart,
  TreePine,
  AlertCircle
} from 'lucide-react';
import { toast } from 'sonner';
import { BundleAnalyzer } from '@/utils/bundleOptimization';

interface ChunkInfo {
  name: string, size: number, gzippedSize: number, modules: ModuleInfo[], dependencies: string[], loadTime: number, cacheHit: boolean;
}

interface ModuleInfo {
  name: string, size: number, gzippedSize: number, optimized: boolean, source: string, imports: string[], exports: string[], treeshakeable: boolean;
}

interface DependencyInfo {
  name: string, version: string, size: number, gzippedSize: number, essential: boolean, alternatives: string[], usageCount: number, lastUsed: Date;
}

interface BundleData {
  totalSize: number, gzippedSize: number, chunks: ChunkInfo[], modules: ModuleInfo[], dependencies: DependencyInfo[];
}

const BundleAnalyzerComponent: React.FC = memo(() => {
  const [bundleData, setBundleData] = useState<BundleData | null>(null);
  const [chunks, setChunks] = useState<ChunkInfo[]>([]);
  const [modules, setModules] = useState<ModuleInfo[]>([]);
  const [dependencies, setDependencies] = useState<DependencyInfo[]>([]);
  const [selectedChunk, setSelectedChunk] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<'all' | 'large' | 'unused' | 'optimizable'>('all');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analyzer] = useState(() => BundleAnalyzer.getInstance());

  // Format file size
  const formatSize = useCallback((bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  }, []);

  // Get size color based on file size
  const getSizeColor = useCallback((size: number) => {
    if (size > 1024 * 1024) return 'text-red-600'; // > 1MB
    if (size > 500 * 1024) return 'text-yellow-600'; // > 500KB
    return 'text-green-600';
  }, []);

  // Calculate compression ratio
  const getCompressionRatio = useCallback((original: number, compressed: number) => {
    if (original === 0) return 0;
    return Math.round((1 - compressed / original) * 100);
  }, []);

  // Filter modules based on search and filter type
  const filteredModules = useCallback(() => {
    let filtered = modules;

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(module => 
        module.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        module.source.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply type filter
    switch (filterType) {
      case 'large':
        filtered = filtered.filter(module => module.size > 100 * 1024); // > 100KB
        break;
      case 'unused':
        filtered = filtered.filter(module => module.exports.length === 0);
        break;
      case 'optimizable':
        filtered = filtered.filter(module => !module.optimized || !module.treeshakeable);
        break;
    }

    return filtered.sort((a, b) => b.size - a.size);
  }, [modules, searchTerm, filterType]);

  // Filter dependencies
  const filteredDependencies = useCallback(() => {
    let filtered = dependencies;

    if (searchTerm) {
      filtered = filtered.filter(dep => 
        dep.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    switch (filterType) {
      case 'large':
        filtered = filtered.filter(dep => dep.size > 500 * 1024); // > 500KB
        break;
      case 'unused':
        filtered = filtered.filter(dep => dep.usageCount === 0);
        break;
      case 'optimizable':
        filtered = filtered.filter(dep => dep.alternatives.length > 0);
        break;
    }

    return filtered.sort((a, b) => b.size - a.size);
  }, [dependencies, searchTerm, filterType]);

  // Analyze bundle
  const analyzeBundleData = useCallback(async () => {
    setIsAnalyzing(true);
    
    try {
      // Generate comprehensive bundle report
      const report = analyzer.generateReport();
      setBundleData({
        totalSize: report.totalSize,
        gzippedSize: report.gzippedSize,
        chunks: [],
        modules: [],
        dependencies: []
      });

      // Simulate chunk analysis (would come from actual webpack stats)
      const mockChunks: ChunkInfo[] = [
        {
          name: 'main',
          size: 850 * 1024,
          gzippedSize: 280 * 1024,
          modules: [],
          dependencies: ['react', 'react-dom', 'lucide-react'],
          loadTime: 1200,
          cacheHit: true
        },
        {
          name: 'vendor',
          size: 1200 * 1024,
          gzippedSize: 380 * 1024,
          modules: [],
          dependencies: ['react', 'react-dom', '@radix-ui/react-dialog'],
          loadTime: 1800,
          cacheHit: false
        },
        {
          name: 'components',
          size: 450 * 1024,
          gzippedSize: 150 * 1024,
          modules: [],
          dependencies: ['@/components/ui'],
          loadTime: 800,
          cacheHit: true
        }
      ];
      setChunks(mockChunks);

      // Simulate module analysis
      const mockModules: ModuleInfo[] = [
        {
          name: 'react',
          size: 120 * 1024,
          gzippedSize: 45 * 1024,
          optimized: true,
          source: 'node_modules/react',
          imports: [],
          exports: ['createElement', 'useState', 'useEffect'],
          treeshakeable: false
        },
        {
          name: 'lodash',
          size: 540 * 1024,
          gzippedSize: 180 * 1024,
          optimized: false,
          source: 'node_modules/lodash',
          imports: [],
          exports: ['map', 'filter', 'reduce'],
          treeshakeable: true
        },
        {
          name: '@/components/posts/PostCard',
          size: 35 * 1024,
          gzippedSize: 12 * 1024,
          optimized: true,
          source: 'src/components/posts',
          imports: ['react', 'lucide-react'],
          exports: ['PostCard'],
          treeshakeable: true
        }
      ];
      setModules(mockModules);

      // Simulate dependency analysis
      const mockDependencies: DependencyInfo[] = [
        {
          name: 'react',
          version: '18.2.0',
          size: 120 * 1024,
          gzippedSize: 45 * 1024,
          essential: true,
          alternatives: [],
          usageCount: 25,
          lastUsed: new Date()
        },
        {
          name: 'lodash',
          version: '4.17.21',
          size: 540 * 1024,
          gzippedSize: 180 * 1024,
          essential: false,
          alternatives: ['ramda', 'native methods'],
          usageCount: 3,
          lastUsed: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
        },
        {
          name: 'moment',
          version: '2.29.4',
          size: 280 * 1024,
          gzippedSize: 95 * 1024,
          essential: false,
          alternatives: ['date-fns', 'dayjs'],
          usageCount: 0,
          lastUsed: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
        }
      ];
      setDependencies(mockDependencies);

      toast.success('Bundle analysis completed');
    } catch (error) {
      console.error('Bundle analysis failed:', error);
      toast.error('Bundle analysis failed');
    } finally {
      setIsAnalyzing(false);
    }
  }, [analyzer]);

  // Export analysis report
  const exportReport = useCallback(() => {
    const report = {
      timestamp: new Date().toISOString(),
      bundleData,
      chunks,
      modules: filteredModules(),
      dependencies: filteredDependencies(),
      summary: {
        totalSize: chunks.reduce((sum, chunk) => sum + chunk.size; 0),
        totalGzippedSize: chunks.reduce((sum, chunk) => sum + chunk.gzippedSize; 0),
        moduleCount: modules.length,
        dependencyCount: dependencies.length,
        optimizableModules: modules.filter(m => !m.optimized).length; unusedDependencies: dependencies.filter(d => d.usageCount === 0).length
      }
    };

    const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `bundle-analysis-${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);

    toast.success('Bundle analysis exported');
  }, [bundleData, chunks, filteredModules, filteredDependencies, modules, dependencies]);

  // Initialize analysis on mount
  useEffect(() => {
    analyzeBundleData();
  }, [analyzeBundleData]);

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Bundle Analyzer</h1>
          <p className="text-gray-600 dark:text-gray-400">Analyze and optimize your application bundle</p>
    </div>
        <div className="flex items-center space-x-3">
          <Button
            variant="outline"
            onClick={exportReport} className="flex items-center space-x-2"
          >
            <Download className="w-4 h-4" />
            <span>Export Report</span>
    </Button>
          <Button
            onClick={analyzeBundleData} disabled={isAnalyzing}, className="flex items-center space-x-2"
          >
            <BarChart3 className="w-4 h-4" />
            <span>{isAnalyzing ? 'Analyzing...' : 'Re-analyze'}</span>
    </Button>
        </div>
    </div>
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Bundle Size</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
    </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatSize(chunks.reduce((sum, chunk) => sum + chunk.size; 0))}
            </div>
            <p className="text-xs text-muted-foreground">
              {formatSize(chunks.reduce((sum, chunk) => sum + chunk.gzippedSize; 0))} gzipped
            </p>
    </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Chunks</CardTitle>
            <PieChart className="h-4 w-4 text-muted-foreground" />
    </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{chunks.length}</div>
            <p className="text-xs text-muted-foreground">
              {chunks.filter(c => c.cacheHit).length} cached
            </p>
    </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Modules</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
    </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{modules.length}</div>
            <p className="text-xs text-muted-foreground">
              {modules.filter(m => !m.optimized).length} need optimization
            </p>
    </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Dependencies</CardTitle>
            <TreePine className="h-4 w-4 text-muted-foreground" />
    </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dependencies.length}</div>
            <p className="text-xs text-muted-foreground">
              {dependencies.filter(d => d.usageCount === 0).length} unused
            </p>
    </CardContent>
        </Card>
    </div>
      {/* Search and Filter Controls */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search modules, dependencies..."
                  value={searchTerm} onChange={(e) => setSearchTerm(e.target.value)}, className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
    </div>
            </div>
            <div className="flex items-center space-x-2">
              <Filter className="w-4 h-4 text-gray-400" />
              <select
                value={filterType} onChange={(e) => setFilterType(e.target.value as 'all' | 'large' | 'unused' | 'optimizable')}, className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="all">All Items</option>
                <option value="large">Large Files</option>
                <option value="unused">Unused</option>
                <option value="optimizable">Optimizable</option>
    </select>
            </div>
    </div>
        </CardContent>
    </Card>
      {/* Main Content Tabs */}
      <Tabs defaultValue="chunks" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="chunks">Chunks</TabsTrigger>
          <TabsTrigger value="modules">Modules</TabsTrigger>
          <TabsTrigger value="dependencies">Dependencies</TabsTrigger>
    </TabsList>
        {/* Chunks Tab */}
        <TabsContent value="chunks">
          <Card>
            <CardHeader>
              <CardTitle>Bundle Chunks</CardTitle>
    </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {chunks.map((chunk) => (
                  <div
                    key={chunk.name} className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                      selectedChunk === chunk.name 
                        ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' 
                        : 'hover:border-gray-300'
                    }`}, onClick={() => setSelectedChunk(selectedChunk === chunk.name ? null : chunk.name)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <h3 className="font-medium text-gray-900 dark:text-white">
                          {chunk.name}
                        </h3>
                        <div className="flex items-center space-x-4 mt-2 text-sm text-gray-600 dark:text-gray-400">
                          <span className={getSizeColor(chunk.size)}>
                            {formatSize(chunk.size)}
                          </span>
                          <span>
                            {formatSize(chunk.gzippedSize)} gzipped 
                            ({getCompressionRatio(chunk.size, chunk.gzippedSize)}% compression)
                          </span>
                          <span>
                            {chunk.loadTime}ms load time
                          </span>
                          {chunk.cacheHit && (
                            <Badge variant="outline" className="text-green-600">
                              Cached
                            </Badge>
                          )}
                        </div>
    </div>
                      <div className="flex items-center space-x-2">
                        <Progress 
                          value={getCompressionRatio(chunk.size, chunk.gzippedSize)} className="w-20"
                        />
                        <Eye className="w-4 h-4 text-gray-400" />
    </div>
                    </div>
                    
                    {selectedChunk === chunk.name && (
                      <div className="mt-4 pt-4 border-t">
                        <h4 className="font-medium mb-2">Dependencies:</h4>
                        <div className="flex flex-wrap gap-2">
                          {chunk.dependencies.map((dep) => (
                            <Badge key={dep} variant="outline">
                              {dep}
                            </Badge>
                          ))}
                        </div>
    </div>
                    )}
                  </div>
                ))}
              </div>
    </CardContent>
          </Card>
    </TabsContent>
        {/* Modules Tab */}
        <TabsContent value="modules">
          <Card>
            <CardHeader>
              <CardTitle>Module Analysis</CardTitle>
    </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {filteredModules().map((module) => (
                  <div key={module.name} className="border rounded-lg p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h3 className="font-medium text-gray-900 dark:text-white">
                          {module.name}
                        </h3>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                          {module.source}
                        </p>
                        <div className="flex items-center space-x-4 mt-2 text-sm">
                          <span className={getSizeColor(module.size)}>
                            {formatSize(module.size)}
                          </span>
                          <span className="text-gray-600 dark:text-gray-400">
                            {formatSize(module.gzippedSize)} gzipped
                          </span>
                          {module.treeshakeable && (
                            <Badge variant="outline" className="text-green-600">
                              Tree-shakeable
                            </Badge>
                          )}
                          {module.optimized ? (
                            <Badge variant="outline" className="text-green-600">
                              Optimized
                            </Badge>
                          ) : (
                            <Badge variant="outline" className="text-yellow-600">
                              <AlertCircle className="w-3 h-3 mr-1" />
                              Needs Optimization
                            </Badge>
                          )}
                        </div>
    </div>
                      <div className="text-right">
                        <div className="text-sm text-gray-600 dark:text-gray-400">
                          {module.exports.length} exports
                        </div>
                        <div className="text-sm text-gray-600 dark:text-gray-400">
                          {module.imports.length} imports
                        </div>
    </div>
                    </div>
    </div>
                ))}
              </div>
    </CardContent>
          </Card>
    </TabsContent>
        {/* Dependencies Tab */}
        <TabsContent value="dependencies">
          <Card>
            <CardHeader>
              <CardTitle>Dependency Analysis</CardTitle>
    </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {filteredDependencies().map((dep) => (
                  <div key={dep.name} className="border rounded-lg p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h3 className="font-medium text-gray-900 dark:text-white">
                          {dep.name}
                        </h3>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                          Version: {dep.version}
                        </p>
                        <div className="flex items-center space-x-4 mt-2 text-sm">
                          <span className={getSizeColor(dep.size)}>
                            {formatSize(dep.size)}
                          </span>
                          <span className="text-gray-600 dark:text-gray-400">
                            Used {dep.usageCount} times
                          </span>
                          <span className="text-gray-600 dark:text-gray-400">
                            Last used: {dep.lastUsed.toLocaleDateString()}
                          </span>
                          {dep.essential ? (
                            <Badge variant="outline" className="text-blue-600">
                              Essential
                            </Badge>
                          ) : (
                            <Badge variant="outline" className="text-gray-600">
                              Optional
                            </Badge>
                          )}
                          {dep.usageCount === 0 && (
                            <Badge variant="outline" className="text-red-600">
                              <AlertCircle className="w-3 h-3 mr-1" />
                              Unused
                            </Badge>
                          )}
                        </div>
                        {dep.alternatives.length > 0 && (
                          <div className="mt-3">
                            <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                              Alternatives:
                            </p>
                            <div className="flex flex-wrap gap-2">
                              {dep.alternatives.map((alt) => (
                                <Badge key={alt} variant="outline" className="text-green-600">
                                  {alt}
                                </Badge>
                              ))}
                            </div>
    </div>
                        )}
                      </div>
    </div>
                  </div>
                ))}
              </div>
    </CardContent>
          </Card>
    </TabsContent>
      </Tabs>
    </div>
  );
});

BundleAnalyzerComponent.displayName = 'BundleAnalyzerComponent';

export default BundleAnalyzerComponent;
