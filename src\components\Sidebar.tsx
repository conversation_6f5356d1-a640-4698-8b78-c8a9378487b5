import React, { useState, useCallback, useMemo, memo } from 'react';
import { Home, Users, Bookmark, Clock, Calendar, Store, Video, MessageCircle, Flag, ChevronDown, UsersRound, TrendingUp, Gamepad2, Film, CloudSun, Heart, Briefcase, Building2, Settings, Sun, Moon, Plus, X, GripVertical, Radio, BarChart3, PlaySquare } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui';
import { useNavigate, useLocation } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { ROUTES, MOCK_IMAGES, getSafeImage } from '@/lib/constants';
import { useTheme } from '@/hooks/useTheme';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { useIsMobile } from '@/hooks/use-device';
import { WeatherContent } from './shared/widgets';
import WidgetFactory from './shared/WidgetFactory';
import { WidgetType } from '@/utils/widgetUtilities';
import { toast } from 'sonner';
import { MenuItem, Shortcut } from '@/types/interfaces';

// Create static widgets outside component to prevent recreation
const STATIC_WIDGETS = {
  weather: <WeatherContent title="Weather" icon={CloudSun} />,
  stocks: WidgetFactory.createWidget(WidgetType.STOCKS, { key: 'sidebar-stocks' }),
  reminders: WidgetFactory.createWidget(WidgetType.REMINDERS, { key: 'sidebar-reminders' }),
  tasks: WidgetFactory.createWidget(WidgetType.TASKS, { key: 'sidebar-tasks' }),
  marketplace: WidgetFactory.createWidget(WidgetType.MARKETPLACE, { key: 'sidebar-marketplace' })
};

// Memoized individual menu item component to prevent unnecessary re-renders
const MenuItemComponent = memo(({ item, isActive, onNavigate }: { 
  item: MenuItem, isActive: boolean, onNavigate: (path: string) => void;
}) => {
  const handleClick = useCallback(() => {
    onNavigate(item.path);
  }, [onNavigate, item.path]);

  return (
    <Button
      variant="ghost"
      className={`w-full flex items-center justify-between p-3 rounded-lg text-left font-normal transition-colors ${
        isActive 
          ? 'bg-blue-50 text-blue-600 hover:bg-blue-100 dark:bg-blue-900/20 dark:text-blue-400 dark:hover:bg-blue-900/30' 
          : 'text-gray-700 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-800'
      }`} onClick={handleClick} aria-label={item.label}
    >
      <div className="flex items-center space-x-3 min-w-0">
        <item.icon className="w-6 h-6 flex-shrink-0" />
        <span className="truncate">{item.label}</span>
        {item.isNew && (
          <Badge className="text-xs font-medium text-blue-600 dark:text-blue-400 bg-blue-100 dark:bg-blue-900/30 px-2 py-0.5 rounded">New</Badge>
        )}
      </div>
      {item.count && (
        <Badge className="bg-red-500 text-white text-xs px-2 py-1 rounded-full flex-shrink-0">
          {item.count}
        </Badge>
      )}
    </Button>
  );
});

MenuItemComponent.displayName = 'MenuItemComponent';

// Optimize sidebar with memoization to prevent unnecessary rerenders
interface SidebarProps {
  isMobileOpen?: boolean;
  onMobileClose?: () => void;
}

const Sidebar = memo<SidebarProps>(({ 
  isMobileOpen = false, 
  onMobileClose = () => {} 
}) => {
  const isMobile = useIsMobile();
  const [showMore, setShowMore] = useState(true); // Show all tabs by default on all devices
  const [showWidgets, setShowWidgets] = useState(true);
  const [showShortcutDialog, setShowShortcutDialog] = useState(false);
  const [searchShortcut, setSearchShortcut] = useState('');
  const [touchStart, setTouchStart] = useState(0);
  const navigate = useNavigate();
  const location = useLocation();
  const { theme, setTheme } = useTheme();

  // Widgets are now static to prevent recreation

  // Memoize menu items to prevent recreation on every render
  const menuItems: MenuItem[] = useMemo(() => [{ id: 'home', icon: Home, path: ROUTES.HOME, label: 'Home' },
    { id: 'recent', icon: TrendingUp, path: ROUTES.RECENT, label: 'Most Recent' },
    { id: 'friends', icon: Users, path: ROUTES.FRIENDS, label: 'Friends', count: 3 },
    { id: 'messages', icon: MessageCircle, path: ROUTES.MESSAGES, label: 'Messenger', count: 2 },
    { id: 'watch', icon: Video, path: ROUTES.WATCH, label: 'Watch' },
    { id: 'youtube', icon: PlaySquare, path: ROUTES.YOUTUBE, label: 'YouTube', isNew: true },
    { id: 'reels', icon: Film, path: ROUTES.REELS, label: 'Reels', isNew: true },
    { id: 'marketplace', icon: Store, path: ROUTES.MARKETPLACE, label: 'Marketplace' },
    { id: 'groups', icon: UsersRound, path: ROUTES.GROUPS, label: 'Groups' },
    { id: 'gaming', icon: Gamepad2, path: ROUTES.GAMING, label: 'Gaming' },
    { id: 'live', icon: Radio, path: ROUTES.LIVE, label: 'Go Live', isNew: true },
    { id: 'saved', icon: Bookmark, path: ROUTES.SAVED, label: 'Saved' },
    { id: 'events', icon: Calendar, path: ROUTES.EVENTS, label: 'Events' },
    { id: 'memories', icon: Clock, path: ROUTES.MEMORIES, label: 'Memories' },
    { id: 'pages', icon: Flag, path: ROUTES.PAGES, label: 'Pages' },
    { id: 'weather', icon: CloudSun, path: ROUTES.WEATHER, label: 'Weather' },
    { id: 'dating', icon: Heart, path: ROUTES.DATING, label: 'Dating', isNew: true },
    { id: 'optimization', icon: BarChart3, path: ROUTES.OPTIMIZATION, label: 'Performance', isNew: true },
    { id: 'jobs', icon: Briefcase, path: ROUTES.JOBS, label: 'Jobs' },
    { id: 'business', icon: Building2, path: ROUTES.BUSINESS, label: 'Business Manager' },
    { id: 'settings', icon: Settings, path: ROUTES.SETTINGS, label: 'Settings' }], []);

  // Shortcuts state - made mutable for editing
  const [shortcuts, setShortcuts] = useState<Shortcut[]>([
    { 
      id: '1',
      name: 'React Developers', 
      image: MOCK_IMAGES.POSTS[0], 
      path: '/groups/react-developers',
      members: '12.5k'
    },
    { 
      id: '2',
      name: 'Web Design Community', 
      image: MOCK_IMAGES.POSTS[1], 
      path: '/groups/web-design',
      members: '8.2k'
    },
    { 
      id: '3',
      name: 'JavaScript Enthusiasts', 
      image: getSafeImage('POSTS', 2), 
      path: '/groups/javascript',
      members: '15.7k'
    },
  ]);

  const handleNavigation = useCallback((path: string) => {
    navigate(path);
    onMobileClose(); // Close mobile sidebar after navigation
    // Navigation feedback removed for cleaner UX
  }, [navigate, onMobileClose]);

  const handleProfileClick = useCallback(() => {
    navigate(ROUTES.PROFILE);
    onMobileClose(); // Close mobile sidebar after navigation
    // Profile navigation feedback removed for cleaner UX
  }, [navigate, onMobileClose]);

  const handleRemoveShortcut = useCallback((shortcutId: string) => {
    setShortcuts(prev => prev.filter(s => s.id !== shortcutId));
    toast.success('Shortcut removed');
  }, []);

  const toggleTheme = useCallback(() => {
    setTheme(theme === 'dark' ? 'light' : 'dark');
  }, [theme, setTheme]);

  // Memoize shortcut handlers for performance
  const handleShortcutNavigation = useCallback((path: string) => {
    handleNavigation(path);
  }, [handleNavigation]);

  // Memoize Quick Actions handlers
  const handlePostClick = useCallback(() => {
    navigate('/');
  }, [navigate]);

  const handleEventClick = useCallback(() => {
    navigate(ROUTES.EVENTS);
  }, [navigate]);

  // Memoize the shortcuts dialog handlers
  const handleShortcutDialogOpen = useCallback(() => {
    setShowShortcutDialog(true);
  }, []);

  const handleShortcutDialogClose = useCallback(() => {
    setShowShortcutDialog(false);
  }, []);

  const handleAddShortcut = useCallback(() => {
    navigate(ROUTES.GROUPS);
    setShowShortcutDialog(false);
    toast.success('Navigate to Groups to add shortcuts');
  }, [navigate]);

  // Memoize filtered shortcuts for dialog
  const filteredShortcuts = useMemo(() => {
    return shortcuts.filter((shortcut) =>
      shortcut.name.toLowerCase().includes(searchShortcut.toLowerCase())
    );
  }, [shortcuts, searchShortcut]);

  // Memoize show more toggle handler
  const toggleShowMore = useCallback(() => {
    setShowMore(!showMore);
  }, [showMore]);

  // Memoize widget toggle handler
  const toggleShowWidgets = useCallback(() => {
    setShowWidgets(!showWidgets);
  }, [showWidgets]);

  // Touch handlers for swipe-to-close
  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    setTouchStart(e.touches[0].clientX);
  }, []);

  const handleTouchEnd = useCallback((e: React.TouchEvent) => {
    if (!touchStart) return;
    
    const touchEnd = e.changedTouches[0].clientX;
    const swipeDistance = touchStart - touchEnd;
    
    // If swipe left more than 50px, close sidebar
    if (swipeDistance > 50) {
      onMobileClose();
    }
    
    setTouchStart(0);
  }, [touchStart, onMobileClose]);

  // Show all items when showMore is true, otherwise show first 15 items
  const visibleItems = useMemo(() => {
    return showMore ? menuItems : menuItems.slice(0, 15);
  }, [showMore, menuItems]);
  const renderedMenuItems = useMemo(() => {
    return visibleItems.map((item) => {
      const isActive = location.pathname === item.path;
      return (
        <MenuItemComponent
          key={item.id} item={item} isActive={isActive} onNavigate={handleNavigation}
        />
      );
    });
  }, [visibleItems, location.pathname, handleNavigation]);

  return (
    <>
      {/* Mobile overlay backdrop */}
      {isMobileOpen && (
        <div 
          className="fixed inset-0 bg-black/50 z-40 md:hidden"
          onClick={onMobileClose}
        />
      )}
      
      {/* Sidebar */}
      <div 
        className={`
          fixed left-0 top-16 h-[calc(100vh-4rem)] w-80 
          bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-800 
          overflow-hidden transition-transform duration-300 ease-in-out z-50
          ${isMobileOpen ? 'translate-x-0' : '-translate-x-full'} md:translate-x-0 md:relative md:top-0 md:h-[calc(100vh-3.5rem)] md:w-full md:border-r-0
          space-y-2 overflow-y-auto pb-4 p-2 sm:p-3 md:p-4 scrollbar-thin
        `} onTouchStart={handleTouchStart} onTouchEnd={handleTouchEnd}
      >
        {/* User Profile */}
        <div 
          className="flex items-center space-x-3 p-3 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg cursor-pointer transition-colors"
          onClick={handleProfileClick}
        >
          <Avatar className="w-8 h-8 md:w-10 md:h-10">
            <AvatarImage src={getSafeImage('AVATARS', 7)} />
            <AvatarFallback>JD</AvatarFallback>
    </Avatar>
          <span className="font-medium text-sm md:text-base text-gray-900 dark:text-gray-100">John Doe</span>
    </div>
        {/* Main Menu */}
        <nav className="space-y-0.5 md:space-y-1">
          {renderedMenuItems}
        </nav>

        {/* See More Button - Only show on mobile */}
        {isMobile && (
          <>
            <Button
              variant="ghost"
              className="w-full flex items-center space-x-3 p-3 rounded-lg text-gray-700 hover:bg-gray-100 transition-colors dark:text-gray-200 dark:hover:bg-gray-800"
              onClick={toggleShowMore} aria-expanded={showMore}
            >
              <div className="w-6 h-6 bg-gray-300 dark:bg-gray-700 rounded-full flex items-center justify-center flex-shrink-0">
                <ChevronDown className={`w-4 h-4 transition-transform ${showMore ? 'rotate-180' : ''}`} />
    </div>
              <span>{showMore ? 'See less' : 'See more'}</span>
    </Button>
              {/* Theme Toggle */}
              <Button
                variant="ghost"
                className="w-full flex items-center space-x-3 p-3 rounded-lg text-gray-700 hover:bg-gray-100 transition-colors dark:text-gray-200 dark:hover:bg-gray-800"
                onClick={toggleTheme}
              >
                <div className="w-6 h-6 bg-gray-300 dark:bg-gray-700 rounded-full flex items-center justify-center flex-shrink-0">
                  {theme === 'dark' ? (
                    <Sun className="w-4 h-4" />
                  ) : (
                    <Moon className="w-4 h-4" />
                  )}
                </div>
                <span>{theme === 'dark' ? 'Light Mode' : 'Dark Mode'}</span>
    </Button>
          </>
        )}

        {/* Divider */}
        <hr className="border-gray-200 dark:border-gray-700" />

        {/* Widgets */}
        <div className="space-y-4">
          <Button
            variant="ghost" 
            className="w-full flex items-center justify-start space-x-3 p-2 md:p-3 rounded-lg text-gray-700 hover:bg-gray-100 transition-colors dark:text-gray-200 dark:hover:bg-gray-800"
            onClick={toggleShowWidgets}
          >
            <div className="w-6 h-6 bg-gray-300 dark:bg-gray-700 rounded-full flex items-center justify-center flex-shrink-0">
              <ChevronDown className={`w-4 h-4 transition-transform ${!showWidgets ? 'rotate-180' : ''}`} />
    </div>
            <span>{showWidgets ? 'Hide widgets' : 'Show widgets'}</span>
    </Button>
          {showWidgets && (
            <div className="space-y-4">
              {STATIC_WIDGETS.weather}
              {STATIC_WIDGETS.marketplace}
              {STATIC_WIDGETS.stocks}
              {STATIC_WIDGETS.reminders}
              {STATIC_WIDGETS.tasks}
            </div>
          )}
        </div>

        {/* Shortcuts */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h3 className="text-gray-500 dark:text-gray-400 font-medium text-xs md:text-sm">Your shortcuts</h3>
            <Button
              variant="ghost"
              size="sm"
              className="text-blue-600 text-sm hover:bg-blue-50 transition-colors dark:text-blue-400 dark:hover:bg-blue-900/20"
              onClick={handleShortcutDialogOpen}
            >
              Edit
            </Button>
    </div>
          {shortcuts.map((shortcut) => (
            <Button
              key={shortcut.id} variant="ghost" 
              className="w-full flex items-center justify-start space-x-3 p-2 md:p-3 rounded-lg text-left hover:bg-gray-100 transition-colors dark:hover:bg-gray-800"
              onClick={() => handleShortcutNavigation(shortcut.path)}
            >
              <img
                src={shortcut.image} alt={shortcut.name} className="w-10 h-10 rounded-lg object-cover flex-shrink-0"
              />
              <div className="flex-1 min-w-0">
                <p className="text-gray-900 dark:text-gray-100 font-medium truncate">{shortcut.name}</p>
                <p className="text-xs text-gray-500 dark:text-gray-400">{shortcut.members} members</p>
    </div>
            </Button>
          ))}
        </div>

        {/* Quick Actions */}
        <div className="space-y-3">
          <h3 className="text-gray-500 dark:text-gray-400 font-medium text-sm">Quick Actions</h3>
          <div className="grid grid-cols-2 gap-2">
            <Button
              variant="outline"
              size="sm"
              className="flex flex-col items-center space-y-1 p-3 h-auto dark:border-gray-700 dark:text-gray-200"
              onClick={handlePostClick}
            >
              <MessageCircle className="w-5 h-5" />
              <span className="text-xs">Post</span>
    </Button>
            <Button
              variant="outline"
              size="sm"
              className="flex flex-col items-center space-y-1 p-3 h-auto dark:border-gray-700 dark:text-gray-200"
              onClick={handleEventClick}
            >
              <Calendar className="w-5 h-5" />
              <span className="text-xs">Event</span>
    </Button>
          </div>
    </div>
        {/* Shortcuts Editing Dialog */}
        <Dialog open={showShortcutDialog} onOpenChange={handleShortcutDialogClose}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Edit Shortcuts</DialogTitle>
    </DialogHeader>
            <Input
              placeholder="Search for shortcuts..."
              value={searchShortcut} onChange={(e) => setSearchShortcut(e.target.value)} className="mb-4"
            />
            <div className="space-y-2 max-h-96 overflow-y-auto">
              {filteredShortcuts.map((shortcut) => (
                  <div
                    key={shortcut.id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
                  >
                    <div className="flex items-center space-x-3">
                      <img
                        src={shortcut.image} alt={shortcut.name} className="w-10 h-10 rounded-lg object-cover"
                      />
                      <div className="min-w-0">
                        <p className="text-gray-900 dark:text-gray-100 font-medium truncate">
                          {shortcut.name}
                        </p>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          {shortcut.members} members
                        </p>
    </div>
                    </div>
                    <div className="flex space-x-1">
                      <Button
                        size="icon"
                        variant="ghost"
                        onClick={() => handleRemoveShortcut(shortcut.id)} className="hover:bg-red-100 dark:hover:bg-red-900/20"
                      >
                        <X className="w-4 h-4 text-red-600 dark:text-red-400" />
    </Button>
                      <Button 
                        size="icon" 
                        variant="ghost"
                        className="cursor-move"
                      >
                        <GripVertical className="w-4 h-4" />
    </Button>
                    </div>
    </div>
                ))}
              {shortcuts.length === 0 && (
                <p className="text-center text-gray-500 dark:text-gray-400 py-8">
                  No shortcuts yet. Add groups and pages to create shortcuts.
                </p>
              )}
            </div>
            <div className="mt-4 flex justify-between">
              <Button 
                variant="outline" 
                size="sm"
                onClick={handleAddShortcut}
              >
                <Plus className="w-4 h-4 mr-1" />
                Add Shortcut
              </Button>
              <Button size="sm" onClick={handleShortcutDialogClose}>
                Done
              </Button>
    </div>
          </DialogContent>
    </Dialog>
      </div>
    </>
  );
});

Sidebar.displayName = 'Sidebar';

export default Sidebar;
