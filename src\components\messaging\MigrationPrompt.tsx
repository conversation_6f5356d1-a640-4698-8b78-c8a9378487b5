import React from 'react';
import { MessageCircle, Zap, Keyboard, Bell, Users, ArrowRight } from 'lucide-react';

interface MigrationPromptProps {
  onStartMigration: () => void; onSkipMigration: () => void; canSkip: boolean;
}

const MigrationPrompt: React.FC<MigrationPromptProps> = ({
  onStartMigration,
  onSkipMigration,
  canSkip
}) => {
  const features = [
    {
      icon: <Zap className="w-5 h-5 text-yellow-500" />
        title: 'Message Reactions',
      description: 'React to messages with emojis and see who reacted'
    },
    {
      icon: <Users className="w-5 h-5 text-blue-500" />
        title: 'Message Threads',
      description: 'Reply to specific messages and keep conversations organized'
    },
    {
      icon: <Keyboard className="w-5 h-5 text-green-500" />
        title: 'Keyboard Navigation',
      description: 'Navigate messages and conversations with keyboard shortcuts'
    },
    {
      icon: <Bell className="w-5 h-5 text-purple-500" />
        title: 'Smart Notifications',
      description: 'Get contextual notifications with better privacy controls'
    }
  ];

  return (
    <div className="flex h-[calc(100vh-4rem)] bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      <div className="flex items-center justify-center w-full p-4">
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl max-w-4xl w-full overflow-hidden">
          {/* Header */}
          <div className="bg-gradient-to-r from-blue-600 to-indigo-600 p-8 text-white">
            <div className="flex items-center gap-4 mb-4">
              <div className="bg-white/20 p-3 rounded-full">
                <MessageCircle className="w-8 h-8" />
    </div>
              <div>
                <h1 className="text-3xl font-bold">Messaging System Upgrade</h1>
                <p className="text-blue-100 text-lg">
                  Unlock powerful new features for better communication
                </p>
    </div>
            </div>
    </div>
          {/* Content */}
          <div className="p-8">
            <div className="mb-8">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                What's New in Advanced Messaging
              </h2>
              <p className="text-gray-600 dark:text-gray-300 mb-6">
                We've enhanced your messaging experience with new features that make communication 
                more interactive, accessible, and efficient. Your existing conversations will be 
                preserved and enhanced with these new capabilities.
              </p>
    </div>
            {/* Features Grid */}
            <div className="grid md:grid-cols-2 gap-6 mb-8">
              {features.map((feature, index) => (
                <div 
                  key={index} className="flex items-start gap-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg"
                >
                  <div className="flex-shrink-0 p-2 bg-white dark:bg-gray-600 rounded-lg shadow-sm">
                    {feature.icon}
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 dark:text-white mb-1">
                      {feature.title}
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-300">
                      {feature.description}
                    </p>
    </div>
                </div>
              ))}
            </div>

            {/* Migration Info */}
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6 mb-8">
              <div className="flex items-start gap-3">
                <div className="flex-shrink-0 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center mt-0.5">
                  <ArrowRight className="w-3 h-3 text-white" />
    </div>
                <div>
                  <h3 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">
                    Safe & Seamless Upgrade
                  </h3>
                  <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
                    <li>• All your existing conversations will be preserved</li>
                    <li>• Message history remains intact and searchable</li>
                    <li>• You can always switch back to the classic view</li>
                    <li>• The upgrade process takes just a few seconds</li>
    </ul>
                </div>
    </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4">
              <button
                onClick={onStartMigration} className="flex-1 bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-6 py-3 rounded-lg font-semibold hover:from-blue-700 hover:to-indigo-700 transition-all duration-200 shadow-lg hover:shadow-xl flex items-center justify-center gap-2"
              >
                <Zap className="w-5 h-5" />
                Upgrade to Advanced Messaging
              </button>
              
              {canSkip && (
                <button
                  onClick={onSkipMigration} className="flex-1 sm:flex-none bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 px-6 py-3 rounded-lg font-medium hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors"
                >
                  Maybe Later
                </button>
              )}
            </div>

            {/* Footer Note */}
            <div className="mt-6 text-center">
              <p className="text-xs text-gray-500 dark:text-gray-400">
                You can change your messaging preferences anytime in settings
              </p>
    </div>
          </div>
    </div>
      </div>
    </div>
  );
};

export default MigrationPrompt;