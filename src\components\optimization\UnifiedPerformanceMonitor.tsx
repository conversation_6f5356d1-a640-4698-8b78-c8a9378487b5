import React, { memo, useEffect, useState, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Activity, 
  Cpu, 
  HardDrive, 
  Wifi, 
  Zap, 
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  TrendingDown,
  RefreshCw,
  BarChart3,
  Monitor,
  Smartphone,
  Globe,
  Database,
  Clock,
  Target
} from 'lucide-react';

interface PerformanceMetrics {
  // Core Web Vitals
  lcp: number; // Largest Contentful Paint
  fid: number; // First Input Delay
  cls: number; // Cumulative Layout Shift
  fcp: number; // First Contentful Paint
  ttfb: number; // Time to First Byte
  
  // Runtime Metrics
  fps: number, memoryUsage: number, bundleSize: number, renderTime: number;
  
  // Network Metrics
  networkSpeed: string, cacheHitRate: number, requestCount: number;
  
  // User Experience
  errorRate: number, loadTime: number, interactionTime: number;
  
  // Advanced Metrics
  jsHeapSize: number, domNodes: number, eventListeners: number, componentCount: number;
}

interface OptimizationSuggestion {
  id: string, type: 'critical' | 'warning' | 'info', title: string, description: string, impact: 'high' | 'medium' | 'low', effort: 'low' | 'medium' | 'high', category: 'performance' | 'memory' | 'network' | 'ux';
}

const UnifiedPerformanceMonitor: React.FC = memo(() => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    lcp: 0,
    fid: 0,
    cls: 0,
    fcp: 0,
    ttfb: 0,
    fps: 60,
    memoryUsage: 45,
    bundleSize: 2.1,
    renderTime: 12,
    networkSpeed: '4g',
    cacheHitRate: 85,
    requestCount: 0,
    errorRate: 0.1,
    loadTime: 1800,
    interactionTime: 50,
    jsHeapSize: 25,
    domNodes: 1250,
    eventListeners: 45,
    componentCount: 127
  });

  const [isMonitoring, setIsMonitoring] = useState(false);
  const [suggestions, setSuggestions] = useState<OptimizationSuggestion[]>([]);
  const [selectedTab, setSelectedTab] = useState('overview');

  // Initialize performance monitoring
  useEffect(() => {
    if (!isMonitoring) return;

    const startMonitoring = () => {
      // Core Web Vitals monitoring
      if ('PerformanceObserver' in window) {
        // LCP Observer
        try {
          const lcpObserver = new PerformanceObserver((list) => {
            const entries = list.getEntries();
            const lastEntry = entries[entries.length - 1] as PerformanceEntry & { startTime: number };
            setMetrics(prev => ({ ...prev, lcp: lastEntry.startTime }));
          });
          lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
        } catch (e) {
          console.warn('LCP observer not supported');
        }

        // FID Observer
        try {
          const fidObserver = new PerformanceObserver((list) => {
            const entries = list.getEntries();
            entries.forEach((entry: PerformanceEntry & { processingStart?: number, startTime: number }) => {
              setMetrics(prev => ({ ...prev, fid: entry.processingStart - entry.startTime }));
            });
          });
          fidObserver.observe({ entryTypes: ['first-input'] });
        } catch (e) {
          console.warn('FID observer not supported');
        }

        // CLS Observer
        try {
          let clsValue = 0;
          const clsObserver = new PerformanceObserver((list) => {
            const entries = list.getEntries();
            entries.forEach((entry: PerformanceEntry & { hadRecentInput?: boolean; value?: number }) => {
              if (!entry.hadRecentInput) {
                clsValue += entry.value;
              }
            });
            setMetrics(prev => ({ ...prev, cls: clsValue }));
          });
          clsObserver.observe({ entryTypes: ['layout-shift'] });
        } catch (e) {
          console.warn('CLS observer not supported');
        }
      }

      // Memory monitoring
      const updateMemoryMetrics = () => {
        if ('memory' in performance) {
          const memory = (performance as Performance & { memory?: { usedJSHeapSize: number, totalJSHeapSize: number, jsHeapSizeLimit: number } }).memory;
          setMetrics(prev => ({
            ...prev,
            memoryUsage: Math.round(memory.usedJSHeapSize / 1048576),
            jsHeapSize: Math.round(memory.totalJSHeapSize / 1048576)
          }));
        }

        // DOM metrics
        setMetrics(prev => ({
          ...prev,
          domNodes: document.querySelectorAll('*').length,
          eventListeners: getEventListenerCount()
        }));
      };

      // FPS monitoring
      let frameCount = 0;
      let lastTime = performance.now();
      
      const measureFPS = () => {
        frameCount++;
        const currentTime = performance.now();
        
        if (currentTime - lastTime >= 1000) {
          setMetrics(prev => ({ ...prev, fps: frameCount }));
          frameCount = 0;
          lastTime = currentTime;
        }
        
        if (isMonitoring) {
          requestAnimationFrame(measureFPS);
        }
      };
      
      requestAnimationFrame(measureFPS);

      // Update metrics every second
      const interval = setInterval(() => {
        updateMemoryMetrics();
        generateOptimizationSuggestions();
      }, 1000);

      return () => {
        clearInterval(interval);
      };
    };

    const cleanup = startMonitoring();
    return cleanup;
  }, [isMonitoring]);

  const getEventListenerCount = (): number => {
    // Estimate event listeners (simplified)
    const elements = document.querySelectorAll('*');
    let count = 0;
    elements.forEach(el => {
      // Check for common event attributes
      if (el.onclick || el.onmouseover || el.onkeydown) count++;
    });
    return count;
  };

  const generateOptimizationSuggestions = useCallback(() => {
    const newSuggestions: OptimizationSuggestion[] = [];

    // Performance suggestions
    if (metrics.lcp > 2500) {
      newSuggestions.push({
        id: 'lcp-slow',
        type: 'critical',
        title: 'Slow Largest Contentful Paint',
        description: 'LCP is above 2.5s. Consider optimizing images and critical resources.',
        impact: 'high',
        effort: 'medium',
        category: 'performance'
      });
    }

    if (metrics.fid > 100) {
      newSuggestions.push({
        id: 'fid-slow',
        type: 'warning',
        title: 'High First Input Delay',
        description: 'FID is above 100ms. Reduce JavaScript execution time.',
        impact: 'high',
        effort: 'high',
        category: 'performance'
      });
    }

    if (metrics.cls > 0.1) {
      newSuggestions.push({
        id: 'cls-high',
        type: 'warning',
        title: 'High Cumulative Layout Shift',
        description: 'CLS is above 0.1. Add size attributes to images and avoid dynamic content.',
        impact: 'medium',
        effort: 'low',
        category: 'ux'
      });
    }

    // Memory suggestions
    if (metrics.memoryUsage > 100) {
      newSuggestions.push({
        id: 'memory-high',
        type: 'critical',
        title: 'High Memory Usage',
        description: 'Memory usage is above 100MB. Check for memory leaks.',
        impact: 'high',
        effort: 'high',
        category: 'memory'
      });
    }

    if (metrics.domNodes > 1500) {
      newSuggestions.push({
        id: 'dom-large',
        type: 'warning',
        title: 'Large DOM Size',
        description: 'DOM has over 1500 nodes. Consider virtualization for large lists.',
        impact: 'medium',
        effort: 'medium',
        category: 'performance'
      });
    }

    // Network suggestions
    if (metrics.cacheHitRate < 80) {
      newSuggestions.push({
        id: 'cache-low',
        type: 'warning',
        title: 'Low Cache Hit Rate',
        description: 'Cache hit rate is below 80%. Optimize caching strategy.',
        impact: 'medium',
        effort: 'low',
        category: 'network'
      });
    }

    setSuggestions(newSuggestions);
  }, [metrics]);

  const getScoreColor = (score: number, thresholds: { good: number, needs: number }) => {
    if (score <= thresholds.good) return 'text-green-600';
    if (score <= thresholds.needs) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreIcon = (score: number, thresholds: { good: number, needs: number }) => {
    if (score <= thresholds.good) return <CheckCircle className="w-4 h-4 text-green-600" />;
    if (score <= thresholds.needs) return <AlertTriangle className="w-4 h-4 text-yellow-600" />;
    return <AlertTriangle className="w-4 h-4 text-red-600" />;
  };

  const calculateOverallScore = (): number => {
    let score = 100;
    
    // Core Web Vitals (40% weight)
    if (metrics.lcp > 2500) score -= 15;
    else if (metrics.lcp > 1200) score -= 8;
    
    if (metrics.fid > 100) score -= 15;
    else if (metrics.fid > 50) score -= 8;
    
    if (metrics.cls > 0.25) score -= 10;
    else if (metrics.cls > 0.1) score -= 5;
    
    // Performance metrics (30% weight)
    if (metrics.fps < 30) score -= 10;
    else if (metrics.fps < 55) score -= 5;
    
    if (metrics.memoryUsage > 150) score -= 10;
    else if (metrics.memoryUsage > 100) score -= 5;
    
    // Network metrics (20% weight)
    if (metrics.cacheHitRate < 70) score -= 8;
    else if (metrics.cacheHitRate < 85) score -= 4;
    
    // UX metrics (10% weight)
    if (metrics.errorRate > 1) score -= 5;
    else if (metrics.errorRate > 0.5) score -= 2;
    
    return Math.max(0, Math.round(score));
  };

  const overallScore = calculateOverallScore();

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
            Ultimate Performance Monitor
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Comprehensive real-time performance analysis and optimization
          </p>
    </div>
        <div className="flex items-center space-x-4">
          <div className="text-center">
            <div className={`text-3xl font-bold ${getScoreColor(100 - overallScore, { good: 10, needs: 25 })}`}>
              {overallScore}
            </div>
            <div className="text-sm text-gray-500">Performance Score</div>
    </div>
          <Button
            onClick={() => setIsMonitoring(!isMonitoring)} variant={isMonitoring ? 'destructive' : 'default'}, size="lg"
          >
            <RefreshCw className={`w-5 h-5 mr-2 ${isMonitoring ? 'animate-spin' : ''}`} />
            {isMonitoring ? 'Stop Monitoring' : 'Start Monitoring'}
          </Button>
    </div>
      </div>

      {/* Performance Tabs */}
      <Tabs value={selectedTab} onValueChange={setSelectedTab}, className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="vitals">Core Vitals</TabsTrigger>
          <TabsTrigger value="runtime">Runtime</TabsTrigger>
          <TabsTrigger value="network">Network</TabsTrigger>
          <TabsTrigger value="suggestions">Suggestions</TabsTrigger>
    </TabsList>
        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Overall Score */}
            <Card className="col-span-full">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Target className="w-5 h-5 mr-2" />
                  Performance Overview
                </CardTitle>
    </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between mb-4">
                  <div className="text-4xl font-bold text-blue-600">{overallScore}/100</div>
                  <Badge variant={overallScore >= 90 ? 'default' : overallScore >= 70 ? 'secondary' : 'destructive'}>
                    {overallScore >= 90 ? 'Excellent' : overallScore >= 70 ? 'Good' : 'Needs Improvement'}
                  </Badge>
    </div>
                <Progress value={overallScore} className="mb-2" />
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Based on Core Web Vitals, runtime performance, and user experience metrics
                </p>
    </CardContent>
            </Card>

            {/* Quick Metrics */}
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">LCP</CardTitle>
    </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <span className="text-2xl font-bold">{metrics.lcp.toFixed(0)}ms</span>
                  {getScoreIcon(metrics.lcp, { good: 1200, needs: 2500 })}
                </div>
    </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">FPS</CardTitle>
    </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <span className="text-2xl font-bold">{metrics.fps}</span>
                  {getScoreIcon(60 - metrics.fps, { good: 5, needs: 30 })}
                </div>
    </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">Memory</CardTitle>
    </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <span className="text-2xl font-bold">{metrics.memoryUsage}MB</span>
                  {getScoreIcon(metrics.memoryUsage, { good: 50, needs: 100 })}
                </div>
    </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">Cache Hit</CardTitle>
    </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <span className="text-2xl font-bold">{metrics.cacheHitRate}%</span>
                  {getScoreIcon(100 - metrics.cacheHitRate, { good: 15, needs: 30 })}
                </div>
    </CardContent>
            </Card>
    </div>
        </TabsContent>

        {/* Core Web Vitals Tab */}
        <TabsContent value="vitals" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Clock className="w-5 h-5 mr-2" />
                  Largest Contentful Paint
                </CardTitle>
    </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-3xl font-bold">{metrics.lcp.toFixed(0)}ms</span>
                    {getScoreIcon(metrics.lcp, { good: 1200, needs: 2500 })}
                  </div>
                  <Progress value={Math.min(100, (metrics.lcp / 4000) * 100)} />
                  <div className="text-sm space-y-1">
                    <div className="flex justify-between">
                      <span>Good</span>
                      <span>&lt; 1.2s</span>
    </div>
                    <div className="flex justify-between">
                      <span>Needs Improvement</span>
                      <span>1.2s - 2.5s</span>
    </div>
                    <div className="flex justify-between">
                      <span>Poor</span>
                      <span>&gt; 2.5s</span>
    </div>
                  </div>
    </div>
              </CardContent>
    </Card>
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Zap className="w-5 h-5 mr-2" />
                  First Input Delay
                </CardTitle>
    </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-3xl font-bold">{metrics.fid.toFixed(0)}ms</span>
                    {getScoreIcon(metrics.fid, { good: 50, needs: 100 })}
                  </div>
                  <Progress value={Math.min(100, (metrics.fid / 300) * 100)} />
                  <div className="text-sm space-y-1">
                    <div className="flex justify-between">
                      <span>Good</span>
                      <span>&lt; 50ms</span>
    </div>
                    <div className="flex justify-between">
                      <span>Needs Improvement</span>
                      <span>50ms - 100ms</span>
    </div>
                    <div className="flex justify-between">
                      <span>Poor</span>
                      <span>&gt; 100ms</span>
    </div>
                  </div>
    </div>
              </CardContent>
    </Card>
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <BarChart3 className="w-5 h-5 mr-2" />
                  Cumulative Layout Shift
                </CardTitle>
    </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-3xl font-bold">{metrics.cls.toFixed(3)}</span>
                    {getScoreIcon(metrics.cls, { good: 0.1, needs: 0.25 })}
                  </div>
                  <Progress value={Math.min(100, (metrics.cls / 0.5) * 100)} />
                  <div className="text-sm space-y-1">
                    <div className="flex justify-between">
                      <span>Good</span>
                      <span>&lt; 0.1</span>
    </div>
                    <div className="flex justify-between">
                      <span>Needs Improvement</span>
                      <span>0.1 - 0.25</span>
    </div>
                    <div className="flex justify-between">
                      <span>Poor</span>
                      <span>&gt; 0.25</span>
    </div>
                  </div>
    </div>
              </CardContent>
    </Card>
          </div>
    </TabsContent>
        {/* Runtime Tab */}
        <TabsContent value="runtime" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Activity className="w-5 h-5 mr-2" />
                  Frame Rate
                </CardTitle>
    </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="text-3xl font-bold">{metrics.fps} FPS</div>
                  <Progress value={(metrics.fps / 60) * 100} />
                  <p className="text-sm text-gray-600">Target: 60 FPS for smooth animations</p>
    </div>
              </CardContent>
    </Card>
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <HardDrive className="w-5 h-5 mr-2" />
                  Memory Usage
                </CardTitle>
    </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="text-3xl font-bold">{metrics.memoryUsage} MB</div>
                  <Progress value={Math.min(100, (metrics.memoryUsage / 200) * 100)} />
                  <div className="text-sm space-y-1">
                    <div>JS Heap: {metrics.jsHeapSize} MB</div>
                    <div>DOM Nodes: {metrics.domNodes.toLocaleString()}</div>
                    <div>Event Listeners: {metrics.eventListeners}</div>
    </div>
                </div>
    </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Cpu className="w-5 h-5 mr-2" />
                  Render Performance
                </CardTitle>
    </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="text-3xl font-bold">{metrics.renderTime.toFixed(1)} ms</div>
                  <Progress value={Math.min(100, (metrics.renderTime / 50) * 100)} />
                  <div className="text-sm space-y-1">
                    <div>Components: {metrics.componentCount}</div>
                    <div>Target: &lt; 16ms (60 FPS)</div>
    </div>
                </div>
    </CardContent>
            </Card>
    </div>
        </TabsContent>

        {/* Network Tab */}
        <TabsContent value="network" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Wifi className="w-5 h-5 mr-2" />
                  Connection
                </CardTitle>
    </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="text-3xl font-bold">{metrics.networkSpeed.toUpperCase()}</div>
                  <div className="text-sm space-y-1">
                    <div>Cache Hit Rate: {metrics.cacheHitRate}%</div>
                    <div>Requests: {metrics.requestCount}</div>
    </div>
                </div>
    </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Database className="w-5 h-5 mr-2" />
                  Bundle Size
                </CardTitle>
    </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="text-3xl font-bold">{metrics.bundleSize.toFixed(1)} MB</div>
                  <Progress value={Math.min(100, (metrics.bundleSize / 5) * 100)} />
                  <p className="text-sm text-gray-600">Gzipped bundle size</p>
    </div>
              </CardContent>
    </Card>
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Globe className="w-5 h-5 mr-2" />
                  Load Time
                </CardTitle>
    </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="text-3xl font-bold">{metrics.loadTime.toFixed(0)} ms</div>
                  <Progress value={Math.min(100, (metrics.loadTime / 5000) * 100)} />
                  <div className="text-sm space-y-1">
                    <div>TTFB: {metrics.ttfb.toFixed(0)}ms</div>
                    <div>FCP: {metrics.fcp.toFixed(0)}ms</div>
    </div>
                </div>
    </CardContent>
            </Card>
    </div>
        </TabsContent>

        {/* Suggestions Tab */}
        <TabsContent value="suggestions" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <AlertTriangle className="w-5 h-5 mr-2" />
                Optimization Suggestions
              </CardTitle>
    </CardHeader>
            <CardContent>
              {suggestions.length === 0 ? (
                <div className="text-center py-8">
                  <CheckCircle className="w-12 h-12 text-green-500 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No Issues Found</h3>
                  <p className="text-gray-600">Your application is performing well!</p>
    </div>
              ) : (
                <div className="space-y-4">
                  {suggestions.map((suggestion) => (
                    <div
                      key={suggestion.id} className={`p-4 rounded-lg border-l-4 ${
                        suggestion.type === 'critical'
                          ? 'border-red-500 bg-red-50 dark:bg-red-950'
                          : suggestion.type === 'warning'
                          ? 'border-yellow-500 bg-yellow-50 dark:bg-yellow-950'
                          : 'border-blue-500 bg-blue-50 dark:bg-blue-950'
                      }`}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h4 className="font-semibold mb-1">{suggestion.title}</h4>
                          <p className="text-sm text-gray-600 dark:text-gray-300 mb-2">
                            {suggestion.description}
                          </p>
                          <div className="flex items-center space-x-4 text-xs">
                            <Badge variant="outline">
                              Impact: {suggestion.impact}
                            </Badge>
                            <Badge variant="outline">
                              Effort: {suggestion.effort}
                            </Badge>
                            <Badge variant="outline">
                              {suggestion.category}
                            </Badge>
    </div>
                        </div>
                        <AlertTriangle
                          className={`w-5 h-5 ${
                            suggestion.type === 'critical'
                              ? 'text-red-500'
                              : suggestion.type === 'warning'
                              ? 'text-yellow-500'
                              : 'text-blue-500'
                          }`}
                        />
    </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
    </Card>
        </TabsContent>
    </Tabs>
    </div>
  );
});

UnifiedPerformanceMonitor.displayName = 'UnifiedPerformanceMonitor';

export default UnifiedPerformanceMonitor;

// Also export the component with its previous name for compatibility
export { UnifiedPerformanceMonitor as UltimatePerformanceMonitor };