import React, { memo, useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Activity, 
  Monitor, 
  Gauge, 
  TrendingUp, 
  AlertTriangle,
  CheckCircle,
  Clock,
  HardDrive,
  Cpu,
  Network,
  Package,
  Database,
  X,
  Play,
  Pause,
  Download
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useRealTimePerformance } from '@/hooks/useRealTimePerformance';
import { useIntelligentCache } from '@/hooks/useIntelligentCache';

interface MetricCardProps {
  title: string, value: number, unit: string, icon: React.ReactNode, color: string;
  trend?: 'up' | 'down' | 'stable';
  target?: number;
}

const MetricCard: React.FC<MetricCardProps> = memo(({ 
  title, 
  value, 
  unit, 
  icon, 
  color, 
  trend, 
  target 
}) => {
  const percentage = target ? Math.min((value / target) * 100, 100) : undefined;
  
  return (
    <motion.div
      layout
      initial={{ opacity: 0, scale: 0.9 }}, animate={{ opacity: 1, scale: 1 }}, transition={{ duration: 0.3 }}
    >
      <Card className="relative overflow-hidden">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm font-medium">{title}</CardTitle>
            <div className={`p-1 rounded ${color}`}>
              {icon}
            </div>
    </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-baseline gap-2">
            <span className="text-2xl font-bold">
              {typeof value === 'number' ? value.toFixed(1) : value}
            </span>
            <span className="text-sm text-muted-foreground">{unit}</span>
            {trend && (
              <div className={`flex items-center text-xs ${
                trend === 'up' ? 'text-green-600' : 
                trend === 'down' ? 'text-red-600' : 
                'text-gray-600'
              }`}>
                <TrendingUp className={`h-3 w-3 ${
                  trend === 'down' ? 'rotate-180' : ''
                }`} />
    </div>
            )}
          </div>
          {percentage !== undefined && (
            <div className="mt-2">
              <Progress 
                value={percentage} className={`h-2 ${percentage > 80 ? 'bg-red-100' : 'bg-green-100'}`}
              />
              <span className="text-xs text-muted-foreground mt-1">
                {percentage.toFixed(0)}% of target
              </span>
    </div>
          )}
        </CardContent>
    </Card>
    </motion.div>
  );
});

MetricCard.displayName = 'MetricCard';

const RealTimePerformanceDashboard: React.FC = () => {
  const {
    metrics,
    alerts,
    isMonitoring,
    startMonitoring,
    stopMonitoring,
    resolveAlert,
    clearAlerts,
    getPerformanceScore,
    getPerformanceGrade;
  } = useRealTimePerformance();

  const cache = useIntelligentCache({
    maxSize: 10,
    maxEntries: 100,
    enablePredictive: true
  });

  const [showAlerts, setShowAlerts] = useState(true);
  const [exportData, setExportData] = useState<string>('');

  const performanceScore = getPerformanceScore();
  const performanceGrade = getPerformanceGrade();
  const activeAlerts = alerts.filter(alert => !alert.resolved);

  useEffect(() => {
    // Cache performance data for analysis
    if (isMonitoring) {
      const cacheKey = `perf_${Date.now()}`;
      cache.set(cacheKey, {
        metrics,
        timestamp: Date.now(),
        score: performanceScore
      }, { priority: 'medium', ttl: 300000 }); // 5 minutes
    }
  }, [metrics, performanceScore, isMonitoring, cache]);

  const exportPerformanceData = () => {
    const data = {
      timestamp: new Date().toISOString(),
      metrics,
      alerts: alerts.slice(0, 10),
      score: performanceScore,
      grade: performanceGrade,
      cacheStats: cache.stats
    };
    
    const jsonData = JSON.stringify(data, null, 2);
    setExportData(jsonData);
    
    // Create downloadable file
    const blob = new Blob([jsonData], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `performance-report-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="space-y-6">
      {/* Header Controls */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Real-Time Performance Monitor</h2>
          <p className="text-muted-foreground">
            Live metrics and intelligent caching analytics
          </p>
    </div>
        <div className="flex items-center gap-3">
          <Badge 
            variant={performanceScore >= 80 ? 'default' : 'destructive'} className="text-lg px-3 py-1"
          >
            {performanceGrade.grade} Score: {performanceScore}
          </Badge>
          <Button
            onClick={isMonitoring ? stopMonitoring : startMonitoring} variant={isMonitoring ? 'destructive' : 'default'}, size="sm"
          >
            {isMonitoring ? (
              <>
                <Pause className="h-4 w-4 mr-2" />
                Stop
              </>
            ) : (
              <>
                <Play className="h-4 w-4 mr-2" />
                Start
              </>
            )}
          </Button>
          <Button
            onClick={exportPerformanceData} variant="outline"
            size="sm"
          >
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
    </div>
      </div>

      {/* Alerts Section */}
      <AnimatePresence>
        {showAlerts && activeAlerts.length > 0 && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}, animate={{ opacity: 1, height: 'auto' }}, exit={{ opacity: 0, height: 0 }}, className="space-y-2"
          >
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <AlertTriangle className="h-5 w-5 text-yellow-500" />
                Performance Alerts ({activeAlerts.length})
              </h3>
              <div className="flex gap-2">
                <Button
                  onClick={clearAlerts} variant="outline"
                  size="sm"
                >
                  Clear All
                </Button>
                <Button
                  onClick={() => setShowAlerts(false)} variant="ghost"
                  size="sm"
                >
                  <X className="h-4 w-4" />
    </Button>
              </div>
    </div>
            <div className="space-y-2 max-h-40 overflow-y-auto">
              {activeAlerts.map(alert => (
                <motion.div
                  key={alert.id}, layout
                  initial={{ opacity: 0, x: -20 }}, animate={{ opacity: 1, x: 0 }}, exit={{ opacity: 0, x: 20 }}
                >
                  <Alert className={`border-l-4 ${
                    alert.type === 'error' ? 'border-l-red-500' :
                    alert.type === 'warning' ? 'border-l-yellow-500' :
                    'border-l-blue-500'
                  }`}>
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription className="flex items-center justify-between">
                      <span>{alert.message}</span>
                      <Button
                        onClick={() => resolveAlert(alert.id)} variant="ghost"
                        size="sm"
                      >
                        <CheckCircle className="h-4 w-4" />
    </Button>
                    </AlertDescription>
    </Alert>
                </motion.div>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Performance Metrics Grid */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
        <MetricCard
          title="Render Time"
          value={metrics.renderTime} unit="ms"
          icon={<Clock className="h-4 w-4" />} color="bg-blue-100 text-blue-600"
          target={16.67} trend={metrics.renderTime > 16.67 ? 'up' : 'stable'}
        />
        
        <MetricCard
          title="Memory Usage"
          value={metrics.memoryUsage} unit="MB"
          icon={<HardDrive className="h-4 w-4" />} color="bg-green-100 text-green-600"
          target={100} trend={metrics.memoryUsage > 50 ? 'up' : 'stable'}
        />
        
        <MetricCard
          title="Frame Rate"
          value={metrics.fps} unit="fps"
          icon={<Monitor className="h-4 w-4" />} color="bg-purple-100 text-purple-600"
          target={60} trend={metrics.fps < 30 ? 'down' : 'stable'}
        />
        
        <MetricCard
          title="DOM Nodes"
          value={metrics.domNodes} unit="nodes"
          icon={<Cpu className="h-4 w-4" />} color="bg-orange-100 text-orange-600"
          target={3000} trend={metrics.domNodes > 2000 ? 'up' : 'stable'}
        />
        
        <MetricCard
          title="Network Latency"
          value={metrics.networkLatency} unit="ms"
          icon={<Network className="h-4 w-4" />} color="bg-red-100 text-red-600"
          target={100} trend={metrics.networkLatency > 100 ? 'up' : 'stable'}
        />
        
        <MetricCard
          title="Bundle Size"
          value={metrics.bundleSize} unit="KB"
          icon={<Package className="h-4 w-4" />} color="bg-yellow-100 text-yellow-600"
          target={1000} trend="stable"
        />
        
        <MetricCard
          title="Cache Hit Rate"
          value={metrics.cacheHitRate} unit="%"
          icon={<Database className="h-4 w-4" />} color="bg-cyan-100 text-cyan-600"
          target={95} trend={metrics.cacheHitRate > 80 ? 'up' : 'down'}
        />
        
        <MetricCard
          title="Performance Score"
          value={performanceScore} unit="/100"
          icon={<Gauge className="h-4 w-4" />} color={`${performanceScore >= 80 ? 'bg-green-100 text-green-600' : 'bg-red-100 text-red-600'}`}, target={100} trend={performanceScore >= 80 ? 'up' : 'down'}
        />
    </div>
      {/* Cache Analytics */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Intelligent Cache Analytics
          </CardTitle>
    </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold">{cache.stats.totalEntries}</div>
              <div className="text-sm text-muted-foreground">Cached Items</div>
    </div>
            <div className="text-center">
              <div className="text-2xl font-bold">{cache.stats.totalSize.toFixed(1)}</div>
              <div className="text-sm text-muted-foreground">Size (MB)</div>
    </div>
            <div className="text-center">
              <div className="text-2xl font-bold">{cache.stats.hitRate.toFixed(1)}</div>
              <div className="text-sm text-muted-foreground">Hit Rate (%)</div>
    </div>
            <div className="text-center">
              <div className="text-2xl font-bold">{cache.stats.evictions}</div>
              <div className="text-sm text-muted-foreground">Evictions</div>
    </div>
          </div>
          
          <div className="mt-4 pt-4 border-t">
            <div className="flex items-center justify-between text-sm">
              <span>Cache Efficiency</span>
              <Badge variant={cache.stats.hitRate > 80 ? 'default' : 'destructive'}>
                {cache.stats.hitRate > 80 ? 'Excellent' : 'Needs Optimization'}
              </Badge>
    </div>
            <Progress 
              value={cache.stats.hitRate} className="mt-2 h-2"
            />
    </div>
        </CardContent>
    </Card>
      {/* Real-time Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Monitoring Status
          </CardTitle>
    </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className={`w-3 h-3 rounded-full ${
                isMonitoring ? 'bg-green-500 animate-pulse' : 'bg-gray-400'
              }`} />
              <span className="font-medium">
                {isMonitoring ? 'Active Monitoring' : 'Monitoring Stopped'}
              </span>
    </div>
            <div className="text-sm text-muted-foreground">
              Last Update: {new Date(metrics.lastUpdate).toLocaleTimeString()}
            </div>
    </div>
          {isMonitoring && (
            <div className="mt-4 space-y-2">
              <div className="flex justify-between text-sm">
                <span>Data Collection Rate</span>
                <span>Every 2 seconds</span>
    </div>
              <div className="flex justify-between text-sm">
                <span>Alert Threshold</span>
                <span>Performance Score &lt; 60</span>
    </div>
              <div className="flex justify-between text-sm">
                <span>Auto Cleanup</span>
                <span>Every 60 seconds</span>
    </div>
            </div>
          )}
        </CardContent>
    </Card>
    </div>
  );
};

export default memo(RealTimePerformanceDashboard);