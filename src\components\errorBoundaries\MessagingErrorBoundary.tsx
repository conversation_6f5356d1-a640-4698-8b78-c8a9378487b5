import React from 'react';
import { AlertTriangle, MessageCircle, RefreshCw, ArrowLeft } from 'lucide-react';
import { Button } from '../ui/button';
import UniversalErrorBoundary from '../ui/UniversalErrorBoundary';

interface MessagingErrorFallbackProps {
  error?: Error;
  onRetry?: () => void;
}

const MessagingErrorFallback: React.FC<MessagingErrorFallbackProps> = ({ error, onRetry }) => {
  return (
    <div className="h-full flex items-center justify-center p-6 bg-gray-50 dark:bg-gray-900">
      <div className="text-center max-w-sm">
        <div className="relative mb-6">
          <MessageCircle className="w-16 h-16 mx-auto text-blue-500" />
          <AlertTriangle className="w-6 h-6 absolute -top-1 -right-1 text-orange-500" />
    </div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
          Messaging Error
        </h3>
        
        <p className="text-gray-600 dark:text-gray-400 text-sm mb-6">
          {error?.message || 'Unable to load messages. Connection may be unstable.'}
        </p>
        
        <div className="space-y-3">
          <Button 
            onClick={onRetry || (() => window.location.reload())} className="w-full"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Retry Connection
          </Button>
          
          <Button 
            variant="outline" 
            onClick={() => window.history.back()} className="w-full"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Home
          </Button>
    </div>
        <p className="text-xs text-gray-500 dark:text-gray-500 mt-4">
          If this continues, try refreshing the page or check your connection.
        </p>
    </div>
    </div>
  );
};

interface MessagingErrorBoundaryProps {
  children: React.ReactNode;
  onError?: (error: Error) => void;
}

const MessagingErrorBoundary: React.FC<MessagingErrorBoundaryProps> = ({ children, onError }) => {
  const [retryKey, setRetryKey] = React.useState(0);

  const handleRetry = () => {
    setRetryKey(prev => prev + 1);
  };

  return (
    <UniversalErrorBoundary
      key={retryKey} level="messaging"
      onError={onError} fallback={<MessagingErrorFallback onRetry={handleRetry} />}
    >
      {children}
    </UniversalErrorBoundary>
  );
};

export default MessagingErrorBoundary;
