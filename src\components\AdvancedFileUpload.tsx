import React, { useState, useRef, useCallback, useEffect } from 'react';
import {
  Upload,
  X,
  File,
  Image,
  Video,
  Music,
  FileText,
  Download,
  RefreshCw,
  Check,
  AlertCircle,
  Trash2,
  Eye,
  Copy
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { toast } from 'sonner';
import { motion, AnimatePresence } from 'framer-motion';
import { FileUploadService } from '@/services/FileUploadService';
import { FileUploadItem, FileUploadConfig, UploadProgress, FileUploadResult, FileUploadError } from '@/types/fileUpload';

interface UploadFile {
  id: string, file: File, name: string, size: number, type: string, status: 'pending' | 'uploading' | 'completed' | 'error', progress: number;
  url?: string;
  error?: string;
  preview?: string;
  metadata?: {
    width?: number;
    height?: number;
    duration?: number;
  };
}

interface AdvancedFileUploadProps {
  onFilesUploaded?: (files: FileUploadItem[]) => void;
  options?: Partial<FileUploadConfig>;
  className?: string;
  multiple?: boolean;
  accept?: string;
  disabled?: boolean;
}

const AdvancedFileUpload: React.FC<AdvancedFileUploadProps> = ({
  onFilesUploaded,
  options = {},
  className = '',
  multiple = true,
  accept,
  disabled = false
}) => {
  const [uploads, setUploads] = useState<UploadFile[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const [previewFile, setPreviewFile] = useState<UploadFile | null>(null);
  const [showPreview, setShowPreview] = useState(false);
  
  const fileInputRef = useRef<HTMLInputElement>(null);
  const uploadService = FileUploadService.getInstance();

  // Subscribe to upload events
  useEffect(() => {
    const handleUploadProgress = (item: FileUploadItem, progress: UploadProgress) => {
      setUploads(prev => prev.map(f => f.id === item.id ? { ...f, progress: progress.percentage } : f));
    };

    const handleUploadCompleted = (item: FileUploadItem, result: FileUploadResult) => {
      setUploads(prev => prev.map(f => f.id === item.id ? { ...f, status: 'completed', url: result.url } : f));
      toast.success(`${item.file?.name || 'File'} uploaded successfully`);
    };

    const handleUploadError = (item: FileUploadItem, error: FileUploadError) => {
      setUploads(prev => prev.map(f => f.id === item.id ? { ...f, status: 'error', error: error.message } : f));
      toast.error(`Failed to upload ${item.file?.name || 'file'}: ${error.message}`);
    };

    const handleValidationError = (file: File, error: FileUploadError) => {
      toast.error(`${file.name}: ${error.message}`);
    };

    uploadService.on('onProgress', handleUploadProgress);
    uploadService.on('onComplete', handleUploadCompleted);
    uploadService.on('onError', handleUploadError);
    uploadService.on('onValidationError', handleValidationError);

    return () => {
      uploadService.off('onProgress');
      uploadService.off('onComplete');
      uploadService.off('onError');
      uploadService.off('onValidationError');
    };
  }, [uploadService]);

  const handleFileSelect = useCallback(async (files: FileList | File[]) => {
    if (disabled) return;

    try {
      const fileArray = Array.from(files);
      const uploadItems = await uploadService.addFiles(fileArray);

      // Convert to UploadFile format for state
      const uploadFiles = uploadItems.map(item => ({
        id: item.id,
        file: item.file,
        name: item.file.name,
        size: item.file.size,
        type: item.file.type,
        status: item.status as 'pending' | 'uploading' | 'completed' | 'error',
        progress: item.progress?.percentage || 0,
        url: item.uploadedUrl,
        error: item.error?.message
      }));

      setUploads(prev => [...uploadFiles, ...prev]);

      // Start uploads
      for (const item of uploadItems) {
        uploadService.startUpload(item.id);
      }
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Upload failed');
    }
  }, [uploadService, onFilesUploaded, disabled]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    if (!disabled) {
      setIsDragOver(true);
    }
  }, [disabled]);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    if (disabled) return;

    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelect(files);
    }
  }, [handleFileSelect, disabled]);

  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileSelect(files);
    }
    // Reset input value to allow selecting the same file again
    e.target.value = '';
  }, [handleFileSelect]);

  const handleRetry = useCallback((uploadId: string) => {
    uploadService.startUpload(uploadId);
  }, [uploadService]);

  const handleCancel = useCallback((uploadId: string) => {
    uploadService.cancelUpload(uploadId);
    setUploads(prev => prev.filter(f => f.id !== uploadId));
  }, [uploadService]);

  const handleRemove = useCallback((uploadId: string) => {
    setUploads(prev => prev.filter(f => f.id !== uploadId));
  }, []);

  const handlePreview = useCallback((uploadFile: UploadFile) => {
    setPreviewFile(uploadFile);
    setShowPreview(true);
  }, []);

  const handleCopyUrl = useCallback((url: string) => {
    navigator.clipboard.writeText(url);
    toast.success('URL copied to clipboard');
  }, []);

  const getFileIcon = (type: string) => {
    if (type.startsWith('image/')) return Image;
    if (type.startsWith('video/')) return Video;
    if (type.startsWith('audio/')) return Music;
    if (type.includes('pdf') || type.startsWith('text/')) return FileText;
    return File;
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return Check;
      case 'error': return AlertCircle;
      case 'uploading': return Upload;
      case 'cancelled': return X;
      default: return Upload;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-500';
      case 'error': return 'text-red-500';
      case 'uploading': return 'text-blue-500';
      case 'cancelled': return 'text-gray-500';
      default: return 'text-gray-500';
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Drop Zone */}
      <Card
        className={`border-2 border-dashed transition-colors cursor-pointer ${
          isDragOver
            ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
            : disabled
            ? 'border-gray-300 bg-gray-50 dark:bg-gray-800'
            : 'border-gray-300 hover:border-gray-400'
        }`}, onDragOver={handleDragOver} onDragLeave={handleDragLeave}, onDrop={handleDrop} onClick={() => !disabled && fileInputRef.current?.click()}
      >
        <CardContent className="p-8 text-center">
          <Upload className={`w-12 h-12 mx-auto mb-4 ${
            disabled ? 'text-gray-400' : 'text-gray-500'
          }`} />
          <h3 className="text-lg font-semibold mb-2">
            {isDragOver ? 'Drop files here' : 'Upload Files'}
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            Drag and drop files here, or click to select files
          </p>
          <Button variant="outline" disabled={disabled}>
            Choose Files
          </Button>
          
          {options.maxFileSize && (
            <p className="text-xs text-gray-500 mt-2">
              Max file size: {formatFileSize(options.maxFileSize)}
            </p>
          )}
        </CardContent>
    </Card>
      {/* Hidden File Input */}
      <input
        ref={fileInputRef} type="file"
        multiple={multiple} accept={accept}, onChange={handleInputChange} className="hidden"
        disabled={disabled}
      />

      {/* Upload List */}
      {uploads.length > 0 && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="font-semibold">Uploads</h3>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setUploads(prev => prev.filter(f => f.status !== 'completed'))}
              >
                Clear Completed
              </Button>
    </div>
            <div className="space-y-3">
              <AnimatePresence>
                {uploads.map((uploadFile) => {
                  const FileIcon = getFileIcon(uploadFile.type);
                  const StatusIcon = getStatusIcon(uploadFile.status);
                  const statusColor = getStatusColor(uploadFile.status);

                  return (
                    <motion.div
                      key={uploadFile.id} initial={{ opacity: 0, y: 20 }}, animate={{ opacity: 1, y: 0 }}, exit={{ opacity: 0, y: -20 }}, className="flex items-center space-x-3 p-3 border rounded-lg"
                    >
                      {/* File Preview/Icon */}
                      <div className="flex-shrink-0">
                        {uploadFile.preview ? (
                          <img
                            src={uploadFile.preview} alt={uploadFile.name}, className="w-12 h-12 object-cover rounded cursor-pointer"
                            onClick={() => handlePreview(uploadFile)}
                          />
                        ) : (
                          <div className="w-12 h-12 bg-gray-100 dark:bg-gray-700 rounded flex items-center justify-center">
                            <FileIcon className="w-6 h-6 text-gray-500" />
    </div>
                        )}
                      </div>

                      {/* File Info */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <p className="text-sm font-medium truncate">
                            {uploadFile.name}
                          </p>
                          <div className="flex items-center space-x-2">
                            <StatusIcon className={`w-4 h-4 ${statusColor}`} />
                            <Badge variant="outline" className="text-xs">
                              {uploadFile.status}
                            </Badge>
    </div>
                        </div>

                        <div className="flex items-center justify-between mt-1">
                          <p className="text-xs text-gray-500">
                            {formatFileSize(uploadFile.size)}
                          </p>
                          {uploadFile.status === 'uploading' && (
                            <p className="text-xs text-gray-500">
                              {uploadFile.progress}%
                            </p>
                          )}
                        </div>

                        {/* Progress Bar */}
                        {uploadFile.status === 'uploading' && (
                          <Progress value={uploadFile.progress} className="mt-2" />
                        )}

                        {/* Error Message */}
                        {uploadFile.status === 'error' && uploadFile.error && (
                          <p className="text-xs text-red-500 mt-1">
                            {uploadFile.error}
                          </p>
                        )}
                      </div>

                      {/* Actions */}
                      <div className="flex items-center space-x-1">
                        {uploadFile.status === 'completed' && uploadFile.url && (
                          <>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleCopyUrl(uploadFile.url!)} className="h-8 w-8 p-0"
                            >
                              <Copy className="w-4 h-4" />
    </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handlePreview(uploadFile)} className="h-8 w-8 p-0"
                            >
                              <Eye className="w-4 h-4" />
    </Button>
                          </>
                        )}

                        {uploadFile.status === 'error' && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleRetry(uploadFile.id)} className="h-8 w-8 p-0"
                          >
                            <RefreshCw className="w-4 h-4" />
    </Button>
                        )}

                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => 
                            uploadFile.status === 'uploading' 
                              ? handleCancel(uploadFile.id)
                              : handleRemove(uploadFile.id)
                          } className="h-8 w-8 p-0 text-red-500 hover:text-red-700"
                        >
                          {uploadFile.status === 'uploading' ? (
                            <X className="w-4 h-4" />
                          ) : (
                            <Trash2 className="w-4 h-4" />
                          )}
                        </Button>
    </div>
                    </motion.div>
                  );
                })}
              </AnimatePresence>
    </div>
          </CardContent>
    </Card>
      )}

      {/* Preview Dialog */}
      <Dialog open={showPreview} onOpenChange={setShowPreview}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>{previewFile?.name}</DialogTitle>
    </DialogHeader>
          {previewFile && (
            <div className="space-y-4">
              {previewFile.type.startsWith('image/') && previewFile.preview && (
                <img
                  src={previewFile.preview} alt={previewFile.name}, className="w-full h-auto max-h-96 object-contain rounded"
                />
              )}

              {previewFile.type.startsWith('video/') && previewFile.url && (
                <video
                  src={previewFile.url}, controls
                  className="w-full h-auto max-h-96 rounded"
                />
              )}

              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p><strong>Size:</strong> {formatFileSize(previewFile.size)}</p>
                  <p><strong>Type:</strong> {previewFile.type}</p>
    </div>
                {previewFile.metadata && (
                  <div>
                    {previewFile.metadata.width && (
                      <p><strong>Dimensions:</strong> {previewFile.metadata.width} × {previewFile.metadata.height}</p>
                    )}
                    {previewFile.metadata.duration && (
                      <p><strong>Duration:</strong> {Math.round(previewFile.metadata.duration)}s</p>
                    )}
                  </div>
                )}
              </div>

              {previewFile.url && (
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    onClick={() => handleCopyUrl(previewFile.url!)}
                  >
                    <Copy className="w-4 h-4 mr-2" />
                    Copy URL
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => window.open(previewFile.url, '_blank')}
                  >
                    <Download className="w-4 h-4 mr-2" />
                    Download
                  </Button>
    </div>
              )}
            </div>
          )}
        </DialogContent>
    </Dialog>
    </div>
  );
};

export default AdvancedFileUpload;
