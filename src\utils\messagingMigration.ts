import { AdvancedMessage, Conversation } from '../types/messaging';

// Legacy message and conversation types (from existing system)
interface LegacyMessage {
  id: string;
  content: string;
  senderId: string;
  timestamp: number;
  type?: string;
  reactions?: Array<{ emoji: string; userId: string; timestamp: number }>;
}

interface LegacyConversation {
  id: string;
  participants: string[];
  lastMessage?: LegacyMessage;
  type?: 'direct' | 'group';
  title?: string;
  unreadCount?: number;
  createdAt?: number;
  updatedAt?: number;
}

export interface MigrationResult {
  success: boolean;
  conversations: Conversation[];
  messages: AdvancedMessage[];
  errors?: string[];
  warnings?: string[];
}

export class MessagingMigrationService {
  private static instance: MessagingMigrationService;
  private migrationVersion = '1.0.0';
  private storageKey = 'messaging_migration_status';

  public static getInstance(): MessagingMigrationService {
    if (!MessagingMigrationService.instance) {
      MessagingMigrationService.instance = new MessagingMigrationService();
    }
    return MessagingMigrationService.instance;
  }

  /**
   * Check if migration is needed
   */
  public needsMigration(): boolean {
    const status = localStorage.getItem(this.storageKey);
    if (!status) {
      // For demo purposes, skip migration for mock data
      return false;
    }

    try {
      const migrationData = JSON.parse(status);
      return migrationData.version !== this.migrationVersion;
    } catch {
      return false; // For demo, don't require migration
    }
  }

  /**
   * Perform migration from legacy to advanced messaging format
   */
  public async performMigration(
  legacyConversations: LegacyConversation[];
    legacyMessages: LegacyMessage[]
  ): Promise<MigrationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      // Migrate conversations
      const conversations = await this.migrateConversations(legacyConversations, errors, warnings);
      
      // Migrate messages
      const messages = await this.migrateMessages(legacyMessages, errors, warnings);

      // Validate migration results
      this.validateMigration(conversations, messages, errors);

      // Save migration status
      this.saveMigrationStatus();

      return {
  success: errors.length === 0;
        conversations,
        messages,
  errors: errors.length > 0 ? errors : undefined;
        warnings: warnings.length > 0 ? warnings : undefined
      };
    } catch (error) {
      errors.push(`Migration failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return {
  success: false;
  conversations: [];
  messages: [];
        errors
      };
    }
  }

  /**
   * Migrate legacy conversations to advanced format
   */
  private async migrateConversations(
  legacyConversations: LegacyConversation[];
  errors: string[];
    warnings: string[]
  ): Promise<Conversation[]> {
    const conversations: Conversation[] = [];

    for (const legacy of legacyConversations) {
      try {
        const conversation: Conversation = {
  id: legacy.id;
  title: legacy.title || '';
  type: legacy.type || 'direct';
  participants: legacy.participants || [];
  lastMessage: legacy.lastMessage ? this.migrateLegacyMessage(legacy.lastMessage) : undefined;
  lastActivity: legacy.updatedAt || Date.now();
  unreadCount: legacy.unreadCount || 0;
  createdAt: legacy.createdAt || Date.now();
          updatedAt: legacy.updatedAt || Date.now()
        };

        // Validate conversation
        if (!conversation.id) {
          errors.push(`Conversation missing ID: ${JSON.stringify(legacy)}`);
          continue;
        }

        if (!conversation.participants || conversation.participants.length === 0) {
          warnings.push(`Conversation ${conversation.id} has no participants`);
        }

        conversations.push(conversation);
      } catch (error) {
        errors.push(`Failed to migrate conversation ${legacy.id}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    return conversations;
  }

  /**
   * Migrate legacy messages to advanced format
   */
  private async migrateMessages(
  legacyMessages: LegacyMessage[];
  errors: string[];
    warnings: string[]
  ): Promise<AdvancedMessage[]> {
    const messages: AdvancedMessage[] = [];

    for (const legacy of legacyMessages) {
      try {
        const message = this.migrateLegacyMessage(legacy);

        // Validate message
        if (!message.id) {
          errors.push(`Message missing ID: ${JSON.stringify(legacy)}`);
          continue;
        }

        if (!message.content && message.type === 'text') {
          warnings.push(`Text message ${message.id} has no content`);
        }

        messages.push(message);
      } catch (error) {
        errors.push(`Failed to migrate message ${legacy.id}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    return messages;
  }

  /**
   * Convert a legacy message to advanced message format
   */
  private migrateLegacyMessage(legacy: LegacyMessage): AdvancedMessage {
    return {
  id: legacy.id;
  content: legacy.content || '';
  senderId: legacy.senderId;
  timestamp: legacy.timestamp;
  type: (legacy.type as any) || 'text';
      status: 'delivered', // Assume delivered for legacy messages
  reactions: legacy?.reactions?.map(r => ({
        emoji: r.emoji;
  userId: r.userId;
  timestamp: r.timestamp
      })) || [];
  mentions: this.extractMentions(legacy.content || '');
      attachments: [], // Legacy messages don't have structured attachments
      replyTo: undefined, // Legacy messages don't have reply structure
      threadId: undefined, // Legacy messages don't have threads
  editedAt: undefined;
      deletedAt: undefined
    };
  }

  /**
   * Extract mentions from message content
   */
  private extractMentions(content: string): Array<{ userId: string; displayName: string }> {
    const mentionRegex = /@(\w+)/g;
    const mentions: Array<{ userId: string; displayName: string }> = [];
    let match;

    while ((match = mentionRegex.exec(content)) !== null) {
      const userId = match[1];
      mentions.push({
        userId,
        displayName: userId // Use userId as displayName for legacy mentions
      });
    }

    return mentions;
  }

  /**
   * Validate migration results
   */
  private validateMigration(
  conversations: Conversation[];
  messages: AdvancedMessage[];
    errors: string[]
  ): void {
    // Check for duplicate conversation IDs
    const conversationIds = new Set<string>();
    for (const conv of conversations) {
      if (conversationIds.has(conv.id)) {
        errors.push(`Duplicate conversation ID: ${conv.id}`);
      }
      conversationIds.add(conv.id);
    }

    // Check for duplicate message IDs
    const messageIds = new Set<string>();
    for (const msg of messages) {
      if (messageIds.has(msg.id)) {
        errors.push(`Duplicate message ID: ${msg.id}`);
      }
      messageIds.add(msg.id);
    }

    // Check for orphaned messages (messages without corresponding conversations)
    // Note: We can't validate this without conversation-message relationships in legacy format
    // This would need to be implemented based on the specific legacy data structure
  }

  /**
   * Save migration status to localStorage
   */
  private saveMigrationStatus(): void {
    const status = {
  version: this.migrationVersion;
  timestamp: Date.now();
      completed: true
    };

    localStorage.setItem(this.storageKey, JSON.stringify(status));
  }

  /**
   * Reset migration status (for testing or re-migration)
   */
  public resetMigrationStatus(): void {
    localStorage.removeItem(this.storageKey);
  }

  /**
   * Get migration status
   */
  public getMigrationStatus(): { version: string; timestamp: number; completed: boolean } | null {
    const status = localStorage.getItem(this.storageKey);
    if (!status) return null;

    try {
      return JSON.parse(status);
    } catch {
      return null;
    }
  }

  /**
   * Rollback migration (restore to legacy format)
   */
  public async rollbackMigration(
  conversations: Conversation[];
    messages: AdvancedMessage[]
  ): Promise<{ conversations: LegacyConversation[]; messages: LegacyMessage[] }> {
    const legacyConversations: LegacyConversation[] = conversations.map(conv => ({
  id: conv.id;
  participants: conv.participants;
  lastMessage: conv.lastMessage ? this.convertToLegacyMessage(conv.lastMessage) : undefined;
  type: conv.type;
  title: conv.title || undefined;
  unreadCount: conv.unreadCount;
  createdAt: conv.createdAt;
      updatedAt: conv.updatedAt
    }));

    const legacyMessages: LegacyMessage[] = messages.map(msg => this.convertToLegacyMessage(msg));

    // Reset migration status
    this.resetMigrationStatus();

    return {
  conversations: legacyConversations;
      messages: legacyMessages
    };
  }

  /**
   * Convert advanced message back to legacy format
   */
  private convertToLegacyMessage(message: AdvancedMessage): LegacyMessage {
    return {
  id: message.id;
  content: message.content;
  senderId: message.senderId;
  timestamp: message.timestamp;
  type: message.type;
  reactions: message?.reactions?.map(r => ({
        emoji: r.emoji;
  userId: r.userId;
        timestamp: r.timestamp
      }))
    };
  }

  /**
   * Create backup of legacy data before migration
   */
  public createBackup(
  conversations: LegacyConversation[];
  messages: LegacyMessage[]
  ): void {
    const backup={conversations;
      messages}, timestamp: Date.now();
      version: 'legacy'
    };

    localStorage.setItem('messaging_backup', JSON.stringify(backup));
  }

  /**
   * Restore from backup
   */
  public restoreFromBackup(): { conversations: LegacyConversation[]; messages: LegacyMessage[] } | null {
    const backup = localStorage.getItem('messaging_backup');
    if (!backup) return null;

    try {
      const data = JSON.parse(backup);
      return {
  conversations: data.conversations || [];
        messages: data.messages || []
      };
    } catch {
      return null;
    }
  }

  /**
   * Clean up old backups and migration data
   */
  public cleanup(): void {
    localStorage.removeItem('messaging_backup');
    // Keep migration status for future reference
  }
}

export default MessagingMigrationService;