import React, { RefObject } from 'react';
import { Button } from '@/components/ui/button';
import { Play, Pause, Volume2, VolumeX, UserPlus, Share } from 'lucide-react';
import { toast } from 'sonner';

interface WatchTogetherVideoPlayerProps {
  videoRef: RefObject<HTMLVideoElement>, progressBarRef: RefObject<HTMLDivElement>, isPlaying: boolean, isMuted: boolean, currentTime: number, duration: number, showControls: boolean, progressPercentage: number, onPlay: () => void; onMute: () => void; onProgressClick: (e: React.MouseEvent<HTMLDivElement>) => void; onMouseMove: () => void; onInviteFriends: () => void; formatTime: (seconds: number) => string;
  videoUrl?: string;
}

const WatchTogetherVideoPlayer: React.FC<WatchTogetherVideoPlayerProps> = ({
  videoRef,
  progressBarRef,
  isPlaying,
  isMuted,
  currentTime,
  duration,
  showControls,
  progressPercentage,
  onPlay,
  onMute,
  onProgressClick,
  onMouseMove,
  onInviteFriends,
  formatTime,
  videoUrl = 'https://storage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4'
}) => {
  return (
    <div className="relative bg-black" onMouseMove={onMouseMove}>
      <video
        ref={videoRef} src={videoUrl}, className="w-full h-full"
        onClick={onPlay}, autoPlay
        muted={isMuted}, aria-label="Watch together video player"
      />
      
      {/* Video Controls */}
      {showControls && (
        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-4">
          {/* Progress Bar */}
          <div 
            ref={progressBarRef} className="w-full h-2 bg-white/30 rounded-full mb-3 cursor-pointer"
            onClick={onProgressClick} role="slider"
            aria-label="Video progress"
            aria-valuemin={0}, aria-valuemax={duration}, aria-valuenow={currentTime}
          >
            <div 
              className="h-full bg-blue-600 rounded-full transition-all"
              style={{ width: `${progressPercentage}%` }}
            />
    </div>
          {/* Controls */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={onPlay} className="text-white hover:bg-white/20"
                aria-label={isPlaying ? 'Pause video' : 'Play video'}
              >
                {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={onMute} className="text-white hover:bg-white/20"
                aria-label={isMuted ? 'Unmute video' : 'Mute video'}
              >
                {isMuted ? <VolumeX className="w-4 h-4" /> : <Volume2 className="w-4 h-4" />}
              </Button>
              
              <span className="text-white text-sm" aria-live="polite">
                {formatTime(currentTime)} / {formatTime(duration)}
              </span>
    </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={onInviteFriends} className="text-white hover:bg-white/20"
                aria-label="Invite friends"
              >
                <UserPlus className="w-4 h-4 mr-1" />
                <span className="text-sm">Invite</span>
    </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  navigator.clipboard.writeText(window.location.href);
                  toast.success('Watch party link copied to clipboard!');
                }}, className="text-white hover:bg-white/20"
                aria-label="Share video"
              >
                <Share className="w-4 h-4" />
    </Button>
            </div>
    </div>
        </div>
      )}
      
      {/* Play Indicator */}
      {!isPlaying && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="w-16 h-16 bg-black/50 rounded-full flex items-center justify-center">
            <Play className="w-8 h-8 text-white ml-1" />
    </div>
        </div>
      )}
    </div>
  );
};

export default WatchTogetherVideoPlayer;