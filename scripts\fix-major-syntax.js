#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

function fixMajorSyntaxIssues(content) {
  let lines = content.split('\n');
  let modified = false;
  
  for (let i = 0; i < lines.length; i++) {
    let line = lines[i];
    let originalLine = line;
    
    // Fix 1: JSX props with commas - prop={value}, prop2={value2}
    if (line.includes('={') && line.includes('},')) {
      line = line.replace(/(\w+)=\{([^}]+)\},\s*(\w+)=/g, '$1={$2}\n          $3=');
    }
    
    // Fix 2: Multiple JSX props on same line without proper spacing
    if (line.includes('={') && line.match(/(\w+)=\{[^}]+\}\s+(\w+)=/)) {
      line = line.replace(/(\w+)=\{([^}]+)\}\s+(\w+)=/g, '$1={$2}\n          $3=');
    }
    
    // Fix 3: setTimeout with semicolon
    if (line.includes('setTimeout(') && line.includes(';')) {
      line = line.replace(/setTimeout\(([^,]+);\s*(\d+)\)/g, 'setTimeout($1, $2)');
    }
    
    // Fix 4: Object property syntax issues
    if (line.includes('}: {') && line.includes('}')) {
      // Fix malformed object properties
      line = line.replace(/\}\s*([^,}\s]+):\s*'([^']+)'/g, '},\n    $1: \'$2\'');
    }
    
    // Fix 5: Missing commas in object literals
    if (line.trim().endsWith('}') && !line.trim().endsWith('},') && !line.trim().endsWith('};')) {
      const nextLine = lines[i + 1];
      if (nextLine && nextLine.trim().match(/^\w+:/)) {
        line = line.replace(/}$/, '},');
      }
    }
    
    // Fix 6: Function parameter issues
    if (line.includes('(') && line.includes(';') && !line.includes('//')) {
      line = line.replace(/([^;]+);\s*([^)]+)\)/g, '$1, $2)');
    }
    
    if (line !== originalLine) {
      lines[i] = line;
      modified = true;
    }
  }
  
  return modified ? lines.join('\n') : content;
}

function fixFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const fixedContent = fixMajorSyntaxIssues(content);
    
    if (fixedContent !== content) {
      fs.writeFileSync(filePath, fixedContent, 'utf8');
      console.log(`Fixed: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`Error fixing ${filePath}:`, error.message);
    return false;
  }
}

function walkDirectory(dir, extensions = ['.tsx', '.ts', '.jsx', '.js']) {
  const files = [];
  
  function walk(currentDir) {
    try {
      const items = fs.readdirSync(currentDir);
      
      for (const item of items) {
        const fullPath = path.join(currentDir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          walk(fullPath);
        } else if (stat.isFile() && extensions.some(ext => item.endsWith(ext))) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      console.error(`Error reading directory ${currentDir}:`, error.message);
    }
  }
  
  walk(dir);
  return files;
}

function main() {
  const srcDir = path.join(__dirname, '..', 'src');
  const files = walkDirectory(srcDir);
  
  console.log(`Found ${files.length} files to check for major syntax issues...`);
  
  let fixedCount = 0;
  
  files.forEach(file => {
    if (fixFile(file)) {
      fixedCount++;
    }
  });
  
  console.log(`\nFixed major syntax issues in ${fixedCount} files out of ${files.length} total files.`);
  
  if (fixedCount > 0) {
    console.log('\nRecommend running TypeScript check again to verify fixes...');
  }
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}
