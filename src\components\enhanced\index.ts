// Enhanced Components Index
// This file exports all the enhanced components for easy importing

// Core Enhanced Components
// Use unified performance monitor for all performance-related exports
export { UnifiedPerformanceMonitor as PerformanceDashboard } from '../optimization';
export { UnifiedPerformanceMonitor as AdvancedPerformanceMonitor } from '../optimization';
export { UnifiedPerformanceMonitor as RealTimePerformanceMonitor } from '../optimization';
export { UnifiedPerformanceMonitor as PerformanceAnalytics } from '../optimization';

// Core Enhanced Components
export { default as BundleAnalyzer } from './BundleAnalyzer';
export { default as OptimizationDashboard } from './OptimizationDashboard';
export { default as EnhancedComponentsDemo } from './EnhancedComponentsDemo';
export { default as AIOptimizationAdvisor } from './AIOptimizationAdvisor';
export { default as AdvancedNetworkMonitor } from './AdvancedNetworkMonitor';

// Enhanced Components Documentation
/**
 * Enhanced Components Overview
 * 
 * This collection of enhanced components provides comprehensive performance monitoring,
 * optimization tracking, and bundle analysis capabilities for the Facebook Clone application.
 * 
 * Components included:
 * 
 * 1. PerformanceDashboard
 *    - Real-time performance metrics monitoring
 *    - Core Web Vitals tracking (LCP, FCP, FID, CLS, TTFB)
 *    - Memory and network usage monitoring
 *    - Optimization suggestions based on metrics
 *    - Performance report export functionality
 * 
 * 2. BundleAnalyzer
 *    - Comprehensive bundle size analysis
 *    - Chunk and module breakdown
 *    - Dependency analysis with usage tracking
 *    - Tree-shaking optimization identification
 *    - Bundle optimization recommendations
 * 
 * 3. AdvancedPerformanceMonitor
 *    - Real-time CPU, memory, and network monitoring
 *    - FPS tracking for smooth animations
 *    - Web Vitals calculation and tracking
 *    - Resource timing analysis
 *    - Performance trend visualization
 * 
 * 4. OptimizationDashboard
 *    - Centralized optimization task management
 *    - Performance goal tracking
 *    - Optimization score calculation
 *    - Integration with all other enhanced components
 *    - Progress tracking and reporting
 * 
 * Integration with Existing Infrastructure:
 * 
 * These components are designed to work seamlessly with the existing optimization
 * infrastructure in the application, including:
 * - VirtualizedList component for large datasets
 * - LazyLoadManager with error boundaries
 * - ImageOptimizationService with intersection observers
 * - PerformanceMonitor class for Core Web Vitals
 * - BundleAnalyzer for size optimization
 * - Mobile-responsive design framework
 * 
 * Usage Examples:
 * 
 * // Import individual components
 * import { PerformanceDashboard, BundleAnalyzer } from '@/components/enhanced';
 * 
 * // Use OptimizationDashboard as main entry point
 * import { OptimizationDashboard } from '@/components/enhanced';
 * 
 * // The OptimizationDashboard includes all other components as tabs
 * function App() {
 *   return <OptimizationDashboard />;
 * }
 * 
 * Features: * - TypeScript support with comprehensive type definitions
 * - Mobile-responsive design with proper breakpoints
 * - Dark mode compatibility
 * - Export functionality for all reports and data
 * - Real-time updates with configurable refresh intervals
 * - Performance-optimized with React.memo and useCallback
 * - Accessible UI components with proper ARIA labels
 * - Integration with existing toast notification system
 * - Comprehensive error handling and fallback states
 */

// Type definitions for enhanced components
export interface EnhancedComponentsConfig {
  performanceDashboard: {
    refreshInterval: number, enableRealTimeMonitoring: boolean, showOptimizationSuggestions: boolean;
  };
  bundleAnalyzer: {
    enableAutoAnalysis: boolean, showTreeShakingRecommendations: boolean, trackDependencyUsage: boolean;
  };
  performanceMonitor: {
    monitoringInterval: number, enableFPSTracking: boolean, enableResourceTiming: boolean;
  };
  optimizationDashboard: {
    enableTaskManagement: boolean, enableGoalTracking: boolean, enableProgressReporting: boolean;
  };
}

// Default configuration
export const defaultEnhancedComponentsConfig: EnhancedComponentsConfig = {
  performanceDashboard: {
    refreshInterval: 5000, // 5 seconds
    enableRealTimeMonitoring: true,
    showOptimizationSuggestions: true
  },
  bundleAnalyzer: {
    enableAutoAnalysis: true,
    showTreeShakingRecommendations: true,
    trackDependencyUsage: true
  },
  performanceMonitor: {
    monitoringInterval: 2000, // 2 seconds
    enableFPSTracking: true,
    enableResourceTiming: true
  },
  optimizationDashboard: {
    enableTaskManagement: true,
    enableGoalTracking: true,
    enableProgressReporting: true
  }
};

// Utility function to create enhanced component configuration
export const createEnhancedComponentsConfig = (
  overrides: Partial<EnhancedComponentsConfig> = {}
): EnhancedComponentsConfig => {
  return {
    ...defaultEnhancedComponentsConfig,
    ...overrides,
    performanceDashboard: {
      ...defaultEnhancedComponentsConfig.performanceDashboard,
      ...overrides.performanceDashboard
    },
    bundleAnalyzer: {
      ...defaultEnhancedComponentsConfig.bundleAnalyzer,
      ...overrides.bundleAnalyzer
    },
    performanceMonitor: {
      ...defaultEnhancedComponentsConfig.performanceMonitor,
      ...overrides.performanceMonitor
    },
    optimizationDashboard: {
      ...defaultEnhancedComponentsConfig.optimizationDashboard,
      ...overrides.optimizationDashboard
    }
  };
};
