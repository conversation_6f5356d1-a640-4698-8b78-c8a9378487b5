import React, { Suspense } from 'react';
import { createLazyComponent } from '@/lib/utils';
import { AppLayout } from '@/components/layout';
import {
  AppErrorBoundary,
  UniversalErrorBoundary,
  ReelsErrorBoundary
} from '@/components/errorBoundaries';
import {
  MessagesSuspenseFallback,
  WatchSuspenseFallback,
  ProfileSuspenseFallback,
  PageSuspenseFallback
} from '@/components/ui/RouteSuspenseFallbacks';
import { ReelsSuspenseFallback } from '@/components/ui/ReelsSuspenseFallback';
import { ROUTES } from '@/lib/constants';

// Lazy-loaded components
const pages = {
  Auth: createLazyComponent(() => import('@/pages/Auth')); Home: createLazyComponent(() => import('@/pages/Home')); Profile: createLazyComponent(() => import('@/pages/Profile')); Friends: createLazyComponent(() => import('@/pages/Friends')); Groups: createLazyComponent(() => import('@/pages/Groups')); Messages: createLazyComponent(() => import('@/pages/Messages')); Notifications: createLazyComponent(() => import('@/pages/Notifications')); Watch: createLazyComponent(() => import('@/pages/Watch')); Marketplace: createLazyComponent(() => import('@/pages/Marketplace')); Events: createLazyComponent(() => import('@/pages/Events')); Saved: createLazyComponent(() => import('@/pages/Saved')); Memories: createLazyComponent(() => import('@/pages/Memories')); Recent: createLazyComponent(() => import('@/pages/Recent')); Pages: createLazyComponent(() => import('@/pages/Pages')); Settings: createLazyComponent(() => import('@/pages/Settings')); Search: createLazyComponent(() => import('@/pages/Search')); Gaming: createLazyComponent(() => import('@/pages/Gaming')); Reels: createLazyComponent(() => import('@/pages/Reels')); Weather: createLazyComponent(() => import('@/pages/Weather')); Dating: createLazyComponent(() => import('@/pages/Dating')); Jobs: createLazyComponent(() => import('@/pages/Jobs')); BusinessManager: createLazyComponent(() => import('@/pages/BusinessManager')); Admin: createLazyComponent(() => import('@/pages/Admin')); Optimization: createLazyComponent(() => import('@/pages/Optimization')); NotFound: createLazyComponent(() => import('@/pages/NotFound')); YouTube: createLazyComponent(() => import('@/pages/YouTube')); LiveStream: createLazyComponent(() => import('@/pages/LiveStream'));
};

// Demo components
const demoComponents = {
  UnifiedOptimizedDemo: createLazyComponent(() => import('@/UnifiedOptimizedDemo')); StableOptimizedDemo: createLazyComponent(() => import('@/components/StableOptimizedDemo')); OptimizedDemoLauncher: createLazyComponent(() => import('@/components/OptimizedDemoLauncher'));
};

// Route configuration types
interface RouteConfig {
  path: string, component: React.ComponentType;
  layout?: 'app' | 'none';
  errorBoundary?: 'app' | 'universal' | 'reels';
  fallback?: React.ComponentType;
  title?: string;
}

// Create a reusable wrapper component for routes
const RouteWrapper = ({ 
  component: Component,layout = 'app',errorBoundary = 'app',
  fallback: Fallback;
  title
}: Omit<RouteConfig,'path'>) => {
  const content = (
    <Suspense fallback={Fallback ? <Fallback title={title} /> : <PageSuspenseFallback title={title} />}>
      <Component />
    </Suspense>
  );

  const withErrorBoundary = () => {
    switch (errorBoundary) {
      case 'universal':
        return <UniversalErrorBoundary level="page">{content}</UniversalErrorBoundary>;
      case 'reels':
        return <ReelsErrorBoundary>{content}</ReelsErrorBoundary>;
      default:
        return <AppErrorBoundary>{content}</AppErrorBoundary>;
    }
  };

  if (layout === 'none') {
    return withErrorBoundary();
  }

  return (
    <AppLayout>
      {withErrorBoundary()}
    </AppLayout>
  );
};

// Route configurations
export const routeConfigs: RouteConfig[] = [// Demo routes (no layout)
  { path: '/', component: demoComponents.OptimizedDemoLauncher, layout: 'none' },
  { path: '/optimized-demo', component: demoComponents.StableOptimizedDemo, layout: 'none' },
  { path: '/demo', component: demoComponents.UnifiedOptimizedDemo, layout: 'none' },
  { path: '/stable-demo', component: demoComponents.StableOptimizedDemo, layout: 'none' },
  
  // Auth route (no layout)
  { path: ROUTES.AUTH, component: pages.Auth, layout: 'none' },
  
  // Main app routes
  { path: ROUTES.HOME, component: pages.Home, errorBoundary: 'universal' },
  { path: ROUTES.PROFILE, component: pages.Profile, errorBoundary: 'universal', fallback: ProfileSuspenseFallback },
  { path: ROUTES.FRIENDS, component: pages.Friends, title: 'Friends' },
  { path: ROUTES.GROUPS, component: pages.Groups, title: 'Groups' },
  { path: ROUTES.MESSAGES, component: pages.Messages, fallback: MessagesSuspenseFallback },
  { path: ROUTES.NOTIFICATIONS, component: pages.Notifications, title: 'Notifications' },
  { path: ROUTES.WATCH, component: pages.Watch, fallback: WatchSuspenseFallback },
  { path: ROUTES.MARKETPLACE, component: pages.Marketplace, title: 'Marketplace' },
  { path: ROUTES.EVENTS, component: pages.Events, title: 'Events' },
  { path: ROUTES.JOBS, component: pages.Jobs, title: 'Jobs' },
  { path: ROUTES.LIVE, component: pages.LiveStream, title: 'Live Stream' },
  { path: ROUTES.SAVED, component: pages.Saved, title: 'Saved' },
  { path: ROUTES.MEMORIES, component: pages.Memories, title: 'Memories' },
  { path: ROUTES.RECENT, component: pages.Recent, title: 'Recent Activity' },
  { path: ROUTES.PAGES, component: pages.Pages, title: 'Pages' },
  { path: ROUTES.SETTINGS, component: pages.Settings, title: 'Settings' },
  { path: ROUTES.SEARCH, component: pages.Search, title: 'Search' },
  { path: ROUTES.GAMING, component: pages.Gaming, title: 'Gaming' },
  { path: ROUTES.REELS, component: pages.Reels, errorBoundary: 'reels', fallback: ReelsSuspenseFallback },
  { path: ROUTES.WEATHER, component: pages.Weather, title: 'Weather' },
  { path: ROUTES.DATING, component: pages.Dating, title: 'Dating' },
  { path: `${ROUTES.YOUTUBE}/*`, component: pages.YouTube, title: 'YouTube' },
  { path: ROUTES.BUSINESS, component: pages.BusinessManager, title: 'Business Manager' },
  { path: ROUTES.ADMIN, component: pages.Admin, title: 'Admin' },
  { path: ROUTES.OPTIMIZATION, component: pages.Optimization, title: 'Performance Optimization' },
  
  // Error routes
  { path: '/not-found', component: pages.NotFound, layout: 'none' }]
];

// Generate route elements
export const _generateRouteElement = (config: RouteConfig) => (
  <RouteWrapper
    component={config.component} layout={config.layout}, errorBoundary={config.errorBoundary} fallback={config.fallback as React.ComponentType}, title={config.title}
  />
);