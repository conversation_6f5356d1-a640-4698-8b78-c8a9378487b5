import React from 'react';
import { APP_CONFIG } from "@/lib/constants";

// Optimized mobile detection with reduced re-renders
export function useIsMobile() {
  const [isMobile, setIsMobile] = React.useState<boolean>(() => 
    typeof window !== 'undefined' ? window.innerWidth < APP_CONFIG.MOBILE_BREAKPOINT : false);

  React.useEffect(() => {
    if (typeof window === 'undefined') return;
    
    // Optimize with functional state update to avoid dependency
    const checkMobile = () => {
      const width = window.innerWidth;
      const currentValue = width < APP_CONFIG.MOBILE_BREAKPOINT;
      setIsMobile(prevValue => {
        // Only update if value actually changed
        return prevValue !== currentValue ? currentValue : prevValue;
      });
    };
    
    // Debounce the resize check for performance
    const debouncedCheck = debounce(checkMobile, 100);
    
    window.addEventListener("resize", debouncedCheck);
    
    return () => window.removeEventListener("resize", debouncedCheck);
  }, []); // Remove dependency to prevent re-render loops

  return isMobile;
}

export function useIsTablet() {
  const [isTablet, setIsTablet] = React.useState<boolean>(() => {
    if (typeof window === 'undefined') return false;
    const width = window.innerWidth;
    return width >= APP_CONFIG.MOBILE_BREAKPOINT && width < APP_CONFIG.DESKTOP_BREAKPOINT;
  });

  React.useEffect(() => {
    if (typeof window === 'undefined') return;
    
    const checkTablet = debounce(() => {
      const width = window.innerWidth;
      const currentValue = width >= APP_CONFIG.MOBILE_BREAKPOINT && width < APP_CONFIG.DESKTOP_BREAKPOINT;
      setIsTablet(prevValue => {
        return prevValue !== currentValue ? currentValue : prevValue;
      });
    }, 100);

    window.addEventListener("resize", checkTablet);
    
    return () => window.removeEventListener("resize", checkTablet);
  }, []); // Remove dependency to prevent re-render loops

  return isTablet;
}

export function useIsDesktop() {
  const [isDesktop, setIsDesktop] = React.useState<boolean>(() => 
    typeof window !== 'undefined' ? window.innerWidth >= APP_CONFIG.DESKTOP_BREAKPOINT : true
  );

  React.useEffect(() => {
    if (typeof window === 'undefined') return;
    
    const checkDesktop = () => {
      const width = window.innerWidth;
      const currentValue = width >= APP_CONFIG.DESKTOP_BREAKPOINT;
      setIsDesktop(prevValue => {
        return prevValue !== currentValue ? currentValue : prevValue;
      });
    };
    
    const debouncedResize = debounce(checkDesktop, 250);
    window.addEventListener("resize", debouncedResize);
    
    return () => window.removeEventListener("resize", debouncedResize);
  }, []); // Remove dependency to prevent re-render loops

  return isDesktop;
}

export function useViewportSize() {
  const [size, setSize] = React.useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 0,
    height: typeof window !== 'undefined' ? window.innerHeight : 0
  });

  const handleResize = React.useCallback(() => {
    setSize({
      width: window.innerWidth,
      height: window.innerHeight
    });
  }, []);

  React.useEffect(() => {
    if (typeof window === 'undefined') return;
    
    const debouncedResize = debounce(handleResize, 250);
    window.addEventListener("resize", debouncedResize);
    
    return () => window.removeEventListener("resize", debouncedResize);
  }, [handleResize]);

  return size;
}

// Add debounce function to prevent excessive resize events
function debounce(func: (...args: unknown[]) => void, wait: number): (...args: unknown[]) => void {
  let timeout: ReturnType<typeof setTimeout> | null = null;
  
  return function(...args: unknown[]) {
    const later = () => {
      timeout = null;
      func(...args);
    };
    
    if (timeout !== null) {
      clearTimeout(timeout);
    }
    timeout = setTimeout(later, wait);
  };
}

// Returns the current device and orientation
export function useDevice() {
  const { width } = useViewportSize();
  
  return React.useMemo(() => {
    let device: 'mobile' | 'tablet' | 'desktop' = 'desktop';
    
    if (width < APP_CONFIG.MOBILE_BREAKPOINT) {
      device = 'mobile';
    } else if (width < APP_CONFIG.DESKTOP_BREAKPOINT) {
      device = 'tablet';
    }
    
    return {
      device,
      isMobile: device === 'mobile',
      isTablet: device === 'tablet',
      isDesktop: device === 'desktop'
    };
  }, [width]);
}

// Enhanced mobile detection with keyboard and orientation
export function useMobileFeatures() {
  const [isKeyboardOpen, setIsKeyboardOpen] = React.useState(false);
  const [orientation, setOrientation] = React.useState<'portrait' | 'landscape'>(
    typeof window !== 'undefined' && window.innerHeight > window.innerWidth ? 'portrait' : 'landscape'
  );
  const [isTouch, setIsTouch] = React.useState(false);
  const [breakpoint, setBreakpoint] = React.useState<'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl'>('lg');
  const { isMobile } = useIsMobile();
  const { width, height } = useViewportSize();

  React.useEffect(() => {
    if (typeof window === 'undefined') return;
    
    // Touch detection
    const touchSupported = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    setIsTouch(touchSupported);
    
    // Breakpoint detection
    let bp: typeof breakpoint = 'lg';
    if (width < 320) bp = 'xs';
    else if (width < 480) bp = 'sm';
    else if (width < 768) bp = 'md';
    else if (width < 1024) bp = 'lg';
    else if (width < 1280) bp = 'xl';
    else bp = '2xl';
    setBreakpoint(bp);
    
    // Orientation detection
    setOrientation(height > width ? 'portrait' : 'landscape');
  }, [width, height]);

  // Keyboard detection
  React.useEffect(() => {
    if (typeof window === 'undefined' || !isMobile) return;
    
    let previousHeight = window.innerHeight;
    
    const handleResize = () => {
      const currentHeight = window.innerHeight;
      const heightDiff = previousHeight - currentHeight;
      
      // Keyboard likely open if height decreased significantly
      setIsKeyboardOpen(heightDiff > 150);
      
      // Visual viewport API for better keyboard detection
      if ('visualViewport' in window && window.visualViewport) {
        const hasKeyboard = window.visualViewport.height < previousHeight * 0.75;
        setIsKeyboardOpen(hasKeyboard);
      }
      
      previousHeight = currentHeight;
    };
    
    const handleViewportChange = () => {
      if ('visualViewport' in window && window.visualViewport) {
        const hasKeyboard = window.visualViewport.height < window.innerHeight * 0.75;
        setIsKeyboardOpen(hasKeyboard);
      }
    };
    
    window.addEventListener('resize', handleResize);
    if ('visualViewport' in window && window.visualViewport) {
      window.visualViewport.addEventListener('resize', handleViewportChange);
    }
    
    return () => {
      window.removeEventListener('resize', handleResize);
      if ('visualViewport' in window && window.visualViewport) {
        window.visualViewport.removeEventListener('resize', handleViewportChange);
      }
    };
  }, [isMobile]);

  return {
    isKeyboardOpen,
    orientation,
    isTouch,
    breakpoint,
    screenWidth: width,
    screenHeight: height
  };
}

// Mobile-specific utilities for styling
export const _mobileUtils = {
  // Prevent iOS zoom on input focus
  preventZoom: {
    fontSize: '16px',
    WebkitAppearance: 'none',
    transform: 'scale(1)'
  } as React.CSSProperties,
  // Touch-friendly minimum sizes
  touchTarget: 'min-w-[44px] min-h-[44px]',
  // Mobile-optimized spacing
  spacing: {
    mobile: 'p-3 space-y-2',
    tablet: 'p-4 space-y-3',
    desktop: 'p-6 space-y-4'
  },
  // Responsive grid patterns
  grid: {
    mobile: 'grid-cols-1',
    tablet: 'grid-cols-2',
    desktop: 'grid-cols-3'
  }
};

// Backward compatibility exports
export { useIsMobile as useMobile };
export { useMobileFeatures as useMobileDetection };
