/// <reference types="vite/client" />

interface ImportMetaEnv {
  readonly VITE_WS_URL?: string;
  readonly VITE_USE_WEBSOCKET?: string;
  readonly DEV: boolean;
  readonly PROD: boolean;
  readonly VITE_API_URL?: string;
  readonly VITE_SUPABASE_URL?: string;
  readonly VITE_SUPABASE_ANON_KEY?: string;
  readonly VITE_YOUTUBE_API_KEY?: string;
  readonly VITE_GOOGLE_SEARCH_API_KEY?: string;
  readonly VITE_GOOGLE_SEARCH_ENGINE_ID?: string;
  readonly VITE_ENABLE_ANALYTICS?: string;
  readonly VITE_DEBUG_PERFORMANCE?: string;
  readonly VITE_ENABLE_VIDEO_CALLS?: string;
  readonly VITE_ENABLE_NOTIFICATIONS?: string;
  readonly VITE_ENABLE_MARKETPLACE?: string;
  readonly VITE_MOCK_DATA?: string;
  readonly VITE_ENABLE_DEVTOOLS?: string;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}

// Global type declarations for the project
declare global {
  interface Window {
    webkitSpeechRecognition: any;
    SpeechRecognition: any;
    webkit: any;
    requestIdleCallback: (
      callback: (deadline: { didTimeout: boolean; timeRemaining: () => number }) => void,
      opts?: { timeout: number }
    ) => number;
    cancelIdleCallback: (handle: number) => void;
    navigator: Navigator & {
      userAgent: string;
      mediaDevices: MediaDevices;
      permissions: Permissions;
      serviceWorker: ServiceWorkerContainer;
    };
  }

  // Fix for testing library types
  namespace jest {
    interface Matchers<R> {
      toBeInTheDocument(): R;
      toHaveClass(className: string): R;
      toHaveTextContent(text: string): R;
    }
  }

  // Fix for Node.js globals
  declare var process: {
    env: {
      NODE_ENV: string;
      VITE_API_URL?: string;
      VITE_WS_URL?: string;
      [key: string]: string | undefined;
    };
  };

  // DOM types
  interface HTMLElement {
    webkitRequestFullscreen?: () => Promise<void>;
    mozRequestFullScreen?: () => Promise<void>;
    msRequestFullscreen?: () => Promise<void>;
  }

  interface Document {
    webkitExitFullscreen?: () => Promise<void>;
    mozCancelFullScreen?: () => Promise<void>;
    msExitFullscreen?: () => Promise<void>;
  }

  // Observer interfaces
  interface IntersectionObserverEntry {
    readonly boundingClientRect: DOMRectReadOnly;
    readonly intersectionRatio: number;
    readonly intersectionRect: DOMRectReadOnly;
    readonly isIntersecting: boolean;
    readonly rootBounds: DOMRectReadOnly | null;
    readonly target: Element;
    readonly time: number;
  }

  interface ResizeObserverEntry {
    readonly borderBoxSize: ReadonlyArray<ResizeObserverSize>;
    readonly contentBoxSize: ReadonlyArray<ResizeObserverSize>;
    readonly contentRect: DOMRectReadOnly;
    readonly devicePixelContentBoxSize: ReadonlyArray<ResizeObserverSize>;
    readonly target: Element;
  }

  interface ResizeObserverSize {
    readonly blockSize: number;
    readonly inlineSize: number;
  }
}

// Module declarations
declare module '*.svg' {
  const content: string;
  export default content;
}

declare module '*.png' {
  const content: string;
  export default content;
}

declare module '*.jpg' {
  const content: string;
  export default content;
}

declare module '*.jpeg' {
  const content: string;
  export default content;
}

declare module '*.gif' {
  const content: string;
  export default content;
}

declare module '*.webp' {
  const content: string;
  export default content;
}

// Fix for motion-dom types
declare module 'motion-dom' {
  export * from 'framer-motion';
}

export {};