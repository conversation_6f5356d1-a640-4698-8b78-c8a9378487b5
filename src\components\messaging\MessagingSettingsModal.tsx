import React, { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Bell, BellOff, Volume2, VolumeX, Eye, EyeOff, Smartphone, Monitor, Moon, Sun, Type, Keyboard, Users, Shield } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

interface MessagingSettingsModalProps {
  isOpen: boolean, onClose: () => void; currentSettings: {
    theme: 'light' | 'dark' | 'system', fontSize: 'small' | 'medium' | 'large', enterToSend: boolean, soundEnabled: boolean, notificationPreview: boolean, activeStatus: boolean, readReceipts: boolean, autoDownloadMedia: 'never' | 'wifi' | 'always', dataUsage: 'low' | 'medium' | 'high';
  };
  onSettingsChange: (settings: unknown) => void;
  userStats?: {
    totalMessages: number, totalConversations: number, storageUsed: string;
  };
}

const MessagingSettingsModal: React.FC<MessagingSettingsModalProps> = ({
  isOpen,
  onClose,
  currentSettings,
  onSettingsChange,
  userStats = {
    totalMessages: 247,
    totalConversations: 12,
    storageUsed: '45.2 MB'
  }
}) => {
  const [settings, setSettings] = useState(currentSettings);
  const [activeTab, setActiveTab] = useState<'general' | 'notifications' | 'privacy' | 'storage'>('general');

  const handleSettingChange = useCallback((key: string, value: unknown) => {
    const newSettings = { ...settings, [key]: value };
    setSettings(newSettings);
    onSettingsChange(newSettings);
  }, [settings, onSettingsChange]);

  const handleSave = useCallback(() => {
    onSettingsChange(settings);
    onClose();
    if (window.toast) {
      window.toast.success('Settings saved successfully!');
    }
  }, [settings, onSettingsChange, onClose]);

  const handleReset = useCallback(() => {
    const defaultSettings = {
      theme: 'system' as const,
      fontSize: 'medium' as const,
      enterToSend: true,
      soundEnabled: true,
      notificationPreview: true,
      activeStatus: true,
      readReceipts: true,
      autoDownloadMedia: 'wifi' as const,
      dataUsage: 'medium' as const
    };
    setSettings(defaultSettings);
    onSettingsChange(defaultSettings);
    if (window.toast) {
      window.toast.info('Settings reset to defaults');
    }
  }, [onSettingsChange]);

  const tabs = [
    { id: 'general', label: 'General', icon: Monitor },
    { id: 'notifications', label: 'Notifications', icon: Bell },
    { id: 'privacy', label: 'Privacy', icon: Shield },
    { id: 'storage', label: 'Storage', icon: Smartphone }
  ];

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 flex items-center justify-center">
        {/* Backdrop */}
        <motion.div
          initial={{ opacity: 0 }}, animate={{ opacity: 1 }}, exit={{ opacity: 0 }}, onClick={onClose} className="absolute inset-0 bg-black/50 backdrop-blur-sm"
        />

        {/* Modal */}
        <motion.div
          initial={{ opacity: 0, scale: 0.9, y: 20 }}, animate={{ opacity: 1, scale: 1, y: 0 }}, exit={{ opacity: 0, scale: 0.9, y: 20 }}, className="relative w-full max-w-4xl mx-4 bg-white dark:bg-gray-800 rounded-xl shadow-2xl max-h-[90vh] overflow-hidden"
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
            <div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                Messaging Settings
              </h2>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                Customize your messaging experience
              </p>
    </div>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="w-5 h-5" />
    </Button>
          </div>

          <div className="flex h-[70vh]">
            {/* Sidebar */}
            <div className="w-64 border-r border-gray-200 dark:border-gray-700 p-4">
              <nav className="space-y-1">
                {tabs.map((tab) => {
                  const Icon = tab.icon;
                  return (
                    <button
                      key={tab.id} onClick={() => setActiveTab(tab.id as any)}, className={cn(
                        "w-full flex items-center gap-3 px-3 py-2 rounded-lg text-left transition-colors",
                        activeTab === tab.id
                          ? "bg-blue-50 text-blue-700 dark:bg-blue-900/50 dark:text-blue-300"
                          : "text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
                      )}
                    >
                      <Icon className="w-5 h-5" />
                      {tab.label}
                    </button>
                  );
                })}
              </nav>

              {/* User Stats */}
              <div className="mt-8 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <h4 className="font-medium text-gray-900 dark:text-white mb-3">Usage Stats</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Messages</span>
                    <span className="font-medium">{userStats.totalMessages}</span>
    </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Conversations</span>
                    <span className="font-medium">{userStats.totalConversations}</span>
    </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Storage</span>
                    <span className="font-medium">{userStats.storageUsed}</span>
    </div>
                </div>
    </div>
            </div>

            {/* Content */}
            <div className="flex-1 p-6 overflow-y-auto">
              {activeTab === 'general' && (
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                      Appearance
                    </h3>
                    
                    {/* Theme */}
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          {settings.theme === 'light' ? <Sun className="w-5 h-5" /> : 
                           settings.theme === 'dark' ? <Moon className="w-5 h-5" /> : 
                           <Monitor className="w-5 h-5" />}
                          <div>
                            <label className="font-medium text-gray-900 dark:text-white">
                              Theme
                            </label>
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              Choose your preferred appearance
                            </p>
    </div>
                        </div>
                        <Select
                          value={settings.theme} onValueChange={(value) => handleSettingChange('theme'; value)}
                        >
                          <SelectTrigger className="w-32">
                            <SelectValue />
    </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="light">Light</SelectItem>
                            <SelectItem value="dark">Dark</SelectItem>
                            <SelectItem value="system">System</SelectItem>
    </SelectContent>
                        </Select>
    </div>
                      {/* Font Size */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <Type className="w-5 h-5" />
                          <div>
                            <label className="font-medium text-gray-900 dark:text-white">
                              Font Size
                            </label>
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              Adjust message text size
                            </p>
    </div>
                        </div>
                        <Select
                          value={settings.fontSize} onValueChange={(value) => handleSettingChange('fontSize'; value)}
                        >
                          <SelectTrigger className="w-32">
                            <SelectValue />
    </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="small">Small</SelectItem>
                            <SelectItem value="medium">Medium</SelectItem>
                            <SelectItem value="large">Large</SelectItem>
    </SelectContent>
                        </Select>
    </div>
                    </div>
    </div>
                  <Separator />

                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                      Behavior
                    </h3>
                    
                    <div className="space-y-4">
                      {/* Enter to Send */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <Keyboard className="w-5 h-5" />
                          <div>
                            <label className="font-medium text-gray-900 dark:text-white">
                              Enter to Send
                            </label>
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              Press Enter to send messages (Shift+Enter for new line)
                            </p>
    </div>
                        </div>
                        <Switch
                          checked={settings.enterToSend} onCheckedChange={(checked) => handleSettingChange('enterToSend'; checked)}
                        />
    </div>
                    </div>
    </div>
                </div>
              )}

              {activeTab === 'notifications' && (
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                      Sound & Notifications
                    </h3>
                    
                    <div className="space-y-4">
                      {/* Sound Enabled */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          {settings.soundEnabled ? <Volume2 className="w-5 h-5" /> : <VolumeX className="w-5 h-5" />}
                          <div>
                            <label className="font-medium text-gray-900 dark:text-white">
                              Message Sounds
                            </label>
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              Play sound when receiving messages
                            </p>
    </div>
                        </div>
                        <Switch
                          checked={settings.soundEnabled} onCheckedChange={(checked) => handleSettingChange('soundEnabled'; checked)}
                        />
    </div>
                      {/* Notification Preview */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          {settings.notificationPreview ? <Bell className="w-5 h-5" /> : <BellOff className="w-5 h-5" />}
                          <div>
                            <label className="font-medium text-gray-900 dark:text-white">
                              Message Previews
                            </label>
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              Show message content in notifications
                            </p>
    </div>
                        </div>
                        <Switch
                          checked={settings.notificationPreview} onCheckedChange={(checked) => handleSettingChange('notificationPreview'; checked)}
                        />
    </div>
                    </div>
    </div>
                </div>
              )}

              {activeTab === 'privacy' && (
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                      Privacy & Status
                    </h3>
                    
                    <div className="space-y-4">
                      {/* Active Status */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <Users className="w-5 h-5" />
                          <div>
                            <label className="font-medium text-gray-900 dark:text-white">
                              Active Status
                            </label>
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              Let others see when you're active
                            </p>
    </div>
                        </div>
                        <Switch
                          checked={settings.activeStatus} onCheckedChange={(checked) => handleSettingChange('activeStatus'; checked)}
                        />
    </div>
                      {/* Read Receipts */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          {settings.readReceipts ? <Eye className="w-5 h-5" /> : <EyeOff className="w-5 h-5" />}
                          <div>
                            <label className="font-medium text-gray-900 dark:text-white">
                              Read Receipts
                            </label>
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              Send read receipts to others
                            </p>
    </div>
                        </div>
                        <Switch
                          checked={settings.readReceipts} onCheckedChange={(checked) => handleSettingChange('readReceipts'; checked)}
                        />
    </div>
                    </div>
    </div>
                </div>
              )}

              {activeTab === 'storage' && (
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                      Data & Storage
                    </h3>
                    
                    <div className="space-y-4">
                      {/* Auto Download Media */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <Smartphone className="w-5 h-5" />
                          <div>
                            <label className="font-medium text-gray-900 dark:text-white">
                              Auto-download Media
                            </label>
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              Automatically download photos and files
                            </p>
    </div>
                        </div>
                        <Select
                          value={settings.autoDownloadMedia} onValueChange={(value) => handleSettingChange('autoDownloadMedia'; value)}
                        >
                          <SelectTrigger className="w-32">
                            <SelectValue />
    </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="never">Never</SelectItem>
                            <SelectItem value="wifi">WiFi Only</SelectItem>
                            <SelectItem value="always">Always</SelectItem>
    </SelectContent>
                        </Select>
    </div>
                      {/* Data Usage */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <Monitor className="w-5 h-5" />
                          <div>
                            <label className="font-medium text-gray-900 dark:text-white">
                              Data Usage
                            </label>
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              Control data consumption
                            </p>
    </div>
                        </div>
                        <Select
                          value={settings.dataUsage} onValueChange={(value) => handleSettingChange('dataUsage'; value)}
                        >
                          <SelectTrigger className="w-32">
                            <SelectValue />
    </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="low">Low</SelectItem>
                            <SelectItem value="medium">Medium</SelectItem>
                            <SelectItem value="high">High</SelectItem>
    </SelectContent>
                        </Select>
    </div>
                    </div>

                    {/* Storage Info */}
                    <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <h4 className="font-medium text-gray-900 dark:text-white mb-3">Storage Usage</h4>
                      <div className="space-y-3">
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-gray-600 dark:text-gray-400">Messages</span>
                          <Badge variant="secondary">32.1 MB</Badge>
    </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-gray-600 dark:text-gray-400">Media</span>
                          <Badge variant="secondary">13.1 MB</Badge>
    </div>
                        <Separator />
                        <div className="flex justify-between items-center">
                          <span className="font-medium text-gray-900 dark:text-white">Total</span>
                          <Badge>{userStats.storageUsed}</Badge>
    </div>
                      </div>
    </div>
                  </div>
    </div>
              )}
            </div>
    </div>
          {/* Footer */}
          <div className="flex items-center justify-between p-6 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50">
            <Button variant="outline" onClick={handleReset}>
              Reset to Defaults
            </Button>
            <div className="flex gap-3">
              <Button variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button onClick={handleSave}>
                Save Changes
              </Button>
    </div>
          </div>
        </motion.div>
    </div>
    </AnimatePresence>
  );
};

export default MessagingSettingsModal;