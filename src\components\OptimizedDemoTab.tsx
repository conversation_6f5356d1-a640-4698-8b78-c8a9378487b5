import React, { memo, useMemo, Suspense, lazy } from 'react';
import { ErrorBoundary } from 'react-error-boundary';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';

import { codeSplitting } from '@/utils/codeSplittingStrategy';

// Optimized lazy loading with performance monitoring
const EnhancedVirtualFeed = codeSplitting.createOptimizedLazyComponent(
  'EnhancedVirtualFeed',
  () => import('./EnhancedVirtualFeed'),
  { priority: codeSplitting.BundlePriority.HIGH }
);

const AdvancedVirtualizedFeed = codeSplitting.createOptimizedLazyComponent(
  'AdvancedVirtualizedFeed', 
  () => import('./advanced/AdvancedVirtualizedFeed'),
  { priority: codeSplitting.BundlePriority.HIGH }
);

const WebWorkerDashboard = codeSplitting.createOptimizedLazyComponent(
  'WebWorkerDashboard',
  () => import('./WebWorkerDashboardWrapper'),
  { priority: codeSplitting.BundlePriority.MEDIUM }
);

const PerformanceDashboard = codeSplitting.createOptimizedLazyComponent(
  'PerformanceDashboard',
  () => import('./PerformanceDashboard'),
  { priority: codeSplitting.BundlePriority.MEDIUM }
);

interface OptimizedDemoTabProps {
  tabType: 'feed' | 'virtualized' | 'workers' | 'performance', data: any[]; // Using any[] for flexibility across different component types
  config: {
    mode: string, features: Record<string, boolean>;
    performance: Record<string, boolean>;
  };
  onPostInteraction?: (postId: string, action: string) => void;
}

// Memoized error fallback
const ErrorFallback = memo(({ error, resetErrorBoundary }: { error: Error, resetErrorBoundary: () => void }) => (
  <Card className="m-2 md:m-4">
    <CardHeader>
      <CardTitle className="text-red-600 text-sm md:text-base">Component Error</CardTitle>
    </CardHeader>
    <CardContent className="p-3 md:p-4">
      <p className="text-xs md:text-sm text-muted-foreground mb-4">
        {error?.message || 'An unexpected error occurred'}
      </p>
      <Button onClick={resetErrorBoundary} variant="outline" size="sm">
        Retry
      </Button>
    </CardContent>
  </Card>
));

// Smart loading fallbacks with performance optimization hints
const createLoadingFallback = (componentName: string, size?: number) => 
  memo(({ message }: { message?: string }) => {
    const SmartFallback = codeSplitting.createSmartFallback(componentName, size);
    return message ? (
      <Card className="m-2 md:m-4">
        <CardContent className="flex items-center justify-center p-4 md:p-8">
          <div className="text-center">
            <Loader2 className="h-6 w-6 md:h-8 md:w-8 animate-spin mx-auto mb-4 text-primary" />
            <p className="text-xs md:text-sm text-muted-foreground">{message}</p>
    </div>
        </CardContent>
    </Card>
    ) : <SmartFallback />;
  });

// Simple post card for non-virtualized feeds
const SimplePostCard = memo(({ post }: { post: any }) => (
  <Card>
    <CardContent className="p-3 md:p-4">
      <div className="flex items-center gap-2 md:gap-3 mb-3">
        <img
          src={post.author.avatar} alt={post.author.name}, className="w-8 h-8 rounded-full flex-shrink-0"
          loading="lazy"
        />
        <div className="min-w-0">
          <p className="font-semibold text-sm md:text-base truncate">{post.author.name}</p>
          <p className="text-xs md:text-sm text-muted-foreground truncate">@{post.author.username}</p>
    </div>
      </div>
      <p className="mb-3 text-sm md:text-base leading-relaxed">{post.content}</p>
      <div className="flex items-center gap-3 md:gap-4 text-xs md:text-sm text-muted-foreground">
        <span>❤️ {post.metrics.likes > 999 ? `${Math.floor(post.metrics.likes/1000)}k` : post.metrics.likes}</span>
        <span>💬 {post.metrics.comments > 999 ? `${Math.floor(post.metrics.comments/1000)}k` : post.metrics.comments}</span>
        <span>🔄 {post.metrics.shares > 999 ? `${Math.floor(post.metrics.shares/1000)}k` : post.metrics.shares}</span>
    </div>
    </CardContent>
    </Card>
));

const OptimizedDemoTab: React.FC<OptimizedDemoTabProps> = memo(({
  tabType,
  data,
  config,
  onPostInteraction = () => {}
}) => {
  // Memoize component selection logic
  const TabContent = useMemo(() => {
    switch (tabType) {
      case 'feed':
        if (config.features?.virtualizedFeed) {
          const LoadingFallback = createLoadingFallback('EnhancedVirtualFeed', 35);
          return (
            <ErrorBoundary FallbackComponent={ErrorFallback}>
              <Suspense fallback={<LoadingFallback message="Loading Enhanced Feed..." />}>
                <EnhancedVirtualFeed
                  posts={data} onPostInteraction={onPostInteraction}, onLike={(postId) => onPostInteraction?.(postId, 'like')} onUnlike={(postId) => onPostInteraction?.(postId, 'unlike')}, onComment={(postId) => onPostInteraction?.(postId, 'comment')} onShare={(postId) => onPostInteraction?.(postId, 'share')}, onBookmark={(postId) => onPostInteraction?.(postId, 'bookmark')} onUserClick={(userId) => console.log('User clicked:'; userId)}, onLoadMore={() => console.log('Load more')} hasMore={false}, isLoading={false}
                />
    </Suspense>
            </ErrorBoundary>
          );
        }
        
        // Fallback to simple posts
        return (
          <div className="space-y-4">
            {data.slice(0, 10).map((post) => (
              <SimplePostCard key={post.id} post={post} />
            ))}
          </div>
        );

      case 'virtualized': {
        const VirtualizedLoadingFallback = createLoadingFallback('AdvancedVirtualizedFeed', 45);
        return (
          <div className="h-[600px] border rounded-lg">
            <ErrorBoundary FallbackComponent={ErrorFallback}>
              <Suspense fallback={<VirtualizedLoadingFallback message="Loading Virtualized Feed..." />}>
                <AdvancedVirtualizedFeed
                  posts={data} onPostInteraction={onPostInteraction}, className="h-full"
                />
    </Suspense>
            </ErrorBoundary>
    </div>
        );
      }

      case 'workers': {
        const WorkersLoadingFallback = createLoadingFallback('WebWorkerDashboard', 30);
        return (
          <ErrorBoundary FallbackComponent={ErrorFallback}>
            <Suspense fallback={<WorkersLoadingFallback message="Loading Web Workers..." />}>
              <WebWorkerDashboard posts={data} />
    </Suspense>
          </ErrorBoundary>
        );
      }

      case 'performance': {
        if (!config.features?.performanceMonitoring) {
          return (
            <Card>
              <CardContent className="p-8 text-center">
                <p className="text-muted-foreground mb-4">Performance monitoring is disabled</p>
                <Button variant="outline">
                  Enable Performance Monitoring
                </Button>
    </CardContent>
            </Card>
          );
        }

        const PerformanceLoadingFallback = createLoadingFallback('PerformanceDashboard', 25);
        return (
          <ErrorBoundary FallbackComponent={ErrorFallback}>
            <Suspense fallback={<PerformanceLoadingFallback message="Loading Performance Dashboard..." />}>
              <PerformanceDashboard />
    </Suspense>
          </ErrorBoundary>
        );
      }

      default:
        return (
          <Card>
            <CardContent className="p-8 text-center">
              <p className="text-muted-foreground">Tab content not available</p>
    </CardContent>
          </Card>
        );
    }
  }, [tabType, data, config, onPostInteraction]);

  return <div className="mt-6">{TabContent}</div>;
});

OptimizedDemoTab.displayName = 'OptimizedDemoTab';

export default OptimizedDemoTab;