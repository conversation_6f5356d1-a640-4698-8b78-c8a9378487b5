import React, { useState, useRef, useEffect, use<PERSON><PERSON>back, useMemo } from 'react';
import { 
  <PERSON>, 
  VideoOff, 
  Mic, 
  <PERSON>c<PERSON><PERSON>, 
  Users, 
  MessageCircle, 
  Share2, 
  Settings, 
  Eye,
  Heart,
  Send
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { <PERSON><PERSON><PERSON><PERSON> } from '@/components/ui/scroll-area';
import { toast } from 'sonner';
import { formatTimeAgo } from '@/lib/utils';

interface LiveStreamProps {
  onStreamEnd?: () => void;
  className?: string;
}

interface LiveComment {
  id: string, userId: string, userName: string, userAvatar: string, message: string, timestamp: Date, reactions: number;
}

interface LiveViewer {
  id: string, name: string, avatar: string, joinedAt: Date;
}

interface StreamSettings {
  title: string, description: string, privacy: 'public' | 'friends' | 'close_friends', allowComments: boolean, allowReactions: boolean, recordStream: boolean, category: string;
}

const LiveStreaming: React.FC<LiveStreamProps> = React.memo(({ onStreamEnd, className }) => {
  const [isStreaming, setIsStreaming] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [isVideoEnabled, setIsVideoEnabled] = useState(true);
  const [isAudioEnabled, setIsAudioEnabled] = useState(true);
  const [viewers, setViewers] = useState<LiveViewer[]>([]);
  const [comments, setComments] = useState<LiveComment[]>([]);
  const [newComment, setNewComment] = useState('');
  const [showSettings, setShowSettings] = useState(false);
  const [streamDuration, setStreamDuration] = useState(0);
  const [totalViewers, setTotalViewers] = useState(0);
  const [heartCount, setHeartCount] = useState(0);
  const [settings, setSettings] = useState<StreamSettings>({
    title: '',
    description: '',
    privacy: 'friends',
    allowComments: true,
    allowReactions: true,
    recordStream: false,
    category: 'general'
  });

  const videoRef = useRef<HTMLVideoElement>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const startTimeRef = useRef<number>(0);

  // Mock data for demonstration
  const mockViewers = useMemo(() => [
    { id: '1', name: 'Alice Johnson', avatar: '/api/placeholder/32/32', joinedAt: new Date() },
    { id: '2', name: 'Bob Smith', avatar: '/api/placeholder/32/32', joinedAt: new Date() },
    { id: '3', name: 'Carol Davis', avatar: '/api/placeholder/32/32', joinedAt: new Date() }
  ], []);

  const mockComments = useMemo(() => [
    {
      id: '1',
      userId: '1',
      userName: 'Alice Johnson',
      userAvatar: '/api/placeholder/32/32',
      message: 'Great stream! Keep it up!',
      timestamp: new Date(),
      reactions: 5
    },
    {
      id: '2',
      userId: '2',
      userName: 'Bob Smith',
      userAvatar: '/api/placeholder/32/32',
      message: 'This is so cool!',
      timestamp: new Date(),
      reactions: 3
    }
  ], []);

  useEffect(() => {
    if (isStreaming) {
      setViewers(mockViewers);
      setComments(mockComments);
      setTotalViewers(mockViewers.length);
      startTimeRef.current = Date.now();
    }
  }, [isStreaming, mockViewers, mockComments]);

  // Update stream duration timer
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isStreaming && !isPaused) {
      interval = setInterval(() => {
        setStreamDuration(Math.floor((Date.now() - startTimeRef.current) / 1000));
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isStreaming, isPaused]);

  const startStream = useCallback(async () => {
    if (!settings.title) {
      toast.error('Please enter a stream title');
      return;
    }

    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: true,
        audio: true
      });

      streamRef.current = stream;
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
      }

      setIsStreaming(true);
      setShowSettings(false);
      toast.success('Live stream started!');
    } catch {
      toast.error('Failed to start stream. Please check your camera and microphone permissions.');
    }
  }, [settings.title]);

  const endStream = useCallback(() => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }

    setIsStreaming(false);
    setIsPaused(false);
    setStreamDuration(0);
    setViewers([]);
    setComments([]);
    setHeartCount(0);
    
    toast.success('Live stream ended');
    onStreamEnd?.();
  }, [onStreamEnd]);

  const toggleVideo = useCallback(() => {
    if (streamRef.current) {
      const videoTrack = streamRef.current.getVideoTracks()[0];
      if (videoTrack) {
        videoTrack.enabled = !isVideoEnabled;
        setIsVideoEnabled(!isVideoEnabled);
      }
    }
  }, [isVideoEnabled]);

  const toggleAudio = useCallback(() => {
    if (streamRef.current) {
      const audioTrack = streamRef.current.getAudioTracks()[0];
      if (audioTrack) {
        audioTrack.enabled = !isAudioEnabled;
        setIsAudioEnabled(!isAudioEnabled);
      }
    }
  }, [isAudioEnabled]);

  const sendComment = useCallback(() => {
    if (newComment.trim() && settings.allowComments) {
      const comment: LiveComment = {
        id: Date.now().toString(),
        userId: 'current_user',
        userName: 'You',
        userAvatar: '/api/placeholder/32/32',
        message: newComment,
        timestamp: new Date(),
        reactions: 0
      };
      setComments(prev => [...prev; comment]);
      setNewComment('');
    }
  }, [newComment, settings.allowComments]);

  const sendHeart = useCallback(() => {
    if (settings.allowReactions) {
      setHeartCount(prev => prev + 1);
      // Animate heart effect
      setTimeout(() => setHeartCount(prev => Math.max(0; prev - 1)), 2000);
    }
  }, [settings.allowReactions]);

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  if (!isStreaming) {
    return (
      <div className={className}>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Video className="w-5 h-5" />
              Go Live
            </CardTitle>
    </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <Label htmlFor="title">Stream Title *</Label>
                <Input
                  id="title"
                  value={settings.title} onChange={(e) => setSettings(prev => ({ ...prev, title: e.target.value }))}, placeholder="What's your stream about?"
                />
    </div>
              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={settings.description} onChange={(e) => setSettings(prev => ({ ...prev, description: e.target.value }))}, placeholder="Tell viewers what to expect..."
                  rows={3}
                />
    </div>
              <div>
                <Label htmlFor="privacy">Privacy</Label>
                <Select value={settings.privacy} onValueChange={(value: 'public' | 'friends' | 'close_friends') => 
                  setSettings(prev => ({ ...prev, privacy: value }))}>
                  <SelectTrigger>
                    <SelectValue />
    </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="public">Public</SelectItem>
                    <SelectItem value="friends">Friends</SelectItem>
                    <SelectItem value="close_friends">Close Friends</SelectItem>
    </SelectContent>
                </Select>
    </div>
              <div>
                <Label htmlFor="category">Category</Label>
                <Select value={settings.category} onValueChange={(value) => 
                  setSettings(prev => ({ ...prev, category: value }))}>
                  <SelectTrigger>
                    <SelectValue />
    </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="general">General</SelectItem>
                    <SelectItem value="gaming">Gaming</SelectItem>
                    <SelectItem value="music">Music</SelectItem>
                    <SelectItem value="education">Education</SelectItem>
                    <SelectItem value="fitness">Fitness</SelectItem>
                    <SelectItem value="cooking">Cooking</SelectItem>
    </SelectContent>
                </Select>
    </div>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="comments"
                    checked={settings.allowComments} onCheckedChange={(checked) => setSettings(prev => ({ ...prev, allowComments: checked }))}
                  />
                  <Label htmlFor="comments">Allow comments</Label>
    </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="reactions"
                    checked={settings.allowReactions} onCheckedChange={(checked) => setSettings(prev => ({ ...prev, allowReactions: checked }))}
                  />
                  <Label htmlFor="reactions">Allow reactions</Label>
    </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="record"
                    checked={settings.recordStream} onCheckedChange={(checked) => setSettings(prev => ({ ...prev, recordStream: checked }))}
                  />
                  <Label htmlFor="record">Record stream</Label>
    </div>
              </div>
              <Button onClick={startStream} className="w-full">
                <Video className="w-4 h-4 mr-2" />
                Start Live Stream
              </Button>
    </div>
          </CardContent>
    </Card>
      </div>
    );
  }

  return (
    <div className={`${className}, max-w-6xl mx-auto`}>
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Stream Area */}
        <div className="lg:col-span-2">
          <Card>
            <CardContent className="p-0">
              <div className="relative bg-black aspect-video rounded-t-lg overflow-hidden">
                <video
                  ref={videoRef}, autoPlay
                  muted
                  className="w-full h-full object-cover"
                />
                
                {/* Stream Overlay */}
                <div className="absolute top-4 left-4 right-4 flex justify-between items-start">
                  <div className="flex items-center space-x-2">
                    <Badge variant="destructive" className="animate-pulse">
                      LIVE
                    </Badge>
                    <Badge variant="secondary">
                      {formatDuration(streamDuration)}
                    </Badge>
    </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="secondary" className="flex items-center gap-1">
                      <Eye className="w-3 h-3" />
                      {viewers.length}
                    </Badge>
                    {heartCount > 0 && (
                      <Badge variant="secondary" className="flex items-center gap-1 animate-pulse">
                        <Heart className="w-3 h-3 fill-red-500 text-red-500" />
                        {heartCount}
                      </Badge>
                    )}
                  </div>
    </div>
                {/* Stream Controls */}
                <div className="absolute bottom-4 left-4 right-4 flex justify-between items-center">
                  <div className="flex items-center space-x-2">
                    <Button
                      size="sm"
                      variant={isVideoEnabled ? "default" : "destructive"} onClick={toggleVideo}
                    >
                      {isVideoEnabled ? <Video className="w-4 h-4" /> : <VideoOff className="w-4 h-4" />}
                    </Button>
                    <Button
                      size="sm"
                      variant={isAudioEnabled ? "default" : "destructive"} onClick={toggleAudio}
                    >
                      {isAudioEnabled ? <Mic className="w-4 h-4" /> : <MicOff className="w-4 h-4" />}
                    </Button>
    </div>
                  <div className="flex items-center space-x-2">
                    <Button size="sm" variant="outline" onClick={() => setShowSettings(true)}>
                      <Settings className="w-4 h-4" />
    </Button>
                    <Button size="sm" variant="outline">
                      <Share2 className="w-4 h-4" />
    </Button>
                    <Button size="sm" variant="destructive" onClick={endStream}>
                      End Stream
                    </Button>
    </div>
                </div>
    </div>
              {/* Stream Info */}
              <div className="p-4">
                <h3 className="font-semibold text-lg">{settings.title}</h3>
                {settings.description && (
                  <p className="text-sm text-muted-foreground mt-1">{settings.description}</p>
                )}
                <div className="flex items-center justify-between mt-2">
                  <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                    <span>{totalViewers} viewers</span>
                    <span>Started {formatTimeAgo(new Date(Date.now() - streamDuration * 1000))}</span>
    </div>
                  <Badge variant="outline">{settings.category}</Badge>
    </div>
              </div>
    </CardContent>
          </Card>
    </div>
        {/* Chat & Viewers Sidebar */}
        <div className="space-y-4">
          {/* Live Chat */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MessageCircle className="w-4 h-4" />
                Live Chat
              </CardTitle>
    </CardHeader>
            <CardContent>
              <ScrollArea className="h-64 mb-4">
                <div className="space-y-2">
                  {comments.map((comment) => (
                    <div key={comment.id} className="flex items-start space-x-2">
                      <Avatar className="w-6 h-6">
                        <AvatarImage src={comment.userAvatar} />
                        <AvatarFallback>{comment.userName[0]}</AvatarFallback>
    </Avatar>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-1">
                          <span className="text-sm font-medium">{comment.userName}</span>
                          <span className="text-xs text-muted-foreground">
                            {formatTimeAgo(comment.timestamp)}
                          </span>
    </div>
                        <p className="text-sm">{comment.message}</p>
    </div>
                    </div>
                  ))}
                </div>
    </ScrollArea>
              {settings.allowComments && (
                <div className="flex items-center space-x-2">
                  <Input
                    value={newComment} onChange={(e) => setNewComment(e.target.value)}, placeholder="Say something..."
                    onKeyPress={(e) => e.key === 'Enter' && sendComment()}
                  />
                  <Button size="sm" onClick={sendComment}>
                    <Send className="w-4 h-4" />
    </Button>
                </div>
              )}
              
              {settings.allowReactions && (
                <div className="mt-2 text-center">
                  <Button variant="outline" size="sm" onClick={sendHeart}>
                    <Heart className="w-4 h-4 mr-1" />
                    Send Love
                  </Button>
    </div>
              )}
            </CardContent>
    </Card>
          {/* Viewers */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="w-4 h-4" />
                Viewers ({viewers.length})
              </CardTitle>
    </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {viewers.map((viewer) => (
                  <div key={viewer.id} className="flex items-center space-x-2">
                    <Avatar className="w-8 h-8">
                      <AvatarImage src={viewer.avatar} />
                      <AvatarFallback>{viewer.name[0]}</AvatarFallback>
    </Avatar>
                    <div>
                      <p className="text-sm font-medium">{viewer.name}</p>
                      <p className="text-xs text-muted-foreground">
                        Joined {formatTimeAgo(viewer.joinedAt)}
                      </p>
    </div>
                  </div>
                ))}
              </div>
    </CardContent>
          </Card>
    </div>
      </div>

      {/* Settings Dialog */}
      <Dialog open={showSettings} onOpenChange={setShowSettings}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Stream Settings</DialogTitle>
    </DialogHeader>
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Switch
                id="comments"
                checked={settings.allowComments} onCheckedChange={(checked) => setSettings(prev => ({ ...prev, allowComments: checked }))}
              />
              <Label htmlFor="comments">Allow comments</Label>
    </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="reactions"
                checked={settings.allowReactions} onCheckedChange={(checked) => setSettings(prev => ({ ...prev, allowReactions: checked }))}
              />
              <Label htmlFor="reactions">Allow reactions</Label>
    </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="record"
                checked={settings.recordStream} onCheckedChange={(checked) => setSettings(prev => ({ ...prev, recordStream: checked }))}
              />
              <Label htmlFor="record">Record stream</Label>
    </div>
          </div>
          <DialogFooter>
            <Button onClick={() => setShowSettings(false)}>Close</Button>
    </DialogFooter>
        </DialogContent>
    </Dialog>
    </div>
  );
});

LiveStreaming.displayName = 'LiveStreaming';

export default LiveStreaming;