import React, { memo, useMemo } from 'react';
import { FixedSizeList as List } from 'react-window';
import { motion } from 'framer-motion';
import PostCard from '@/components/posts/PostCard';
import NewsFeedSkeleton from '@/components/NewsFeedSkeleton';
import { BasePost } from '@/types/shared';
import { cn } from '@/lib/utils';

interface VirtualizedFeedListProps {
  posts: BasePost[], isLoading: boolean;
  isLoadingMore?: boolean;
  hasNextPage?: boolean;
  onLoadMore?: () => void;
  onPostInteraction?: (postId: string, action: string, data?: unknown) => void;
  enableVirtualization?: boolean;
  itemHeight?: number;
  containerHeight?: string | number;
  className?: string;
}

// Constants for virtualization
const OVERSCAN_COUNT = 2;
const DEFAULT_ITEM_HEIGHT = 400;
const CONTAINER_HEIGHT = '80vh';
const ITEM_SPACING = 24; // 24px spacing between posts

// Memoized post item component for virtualization
const PostItem = memo(({ 
  index, 
  style, 
  data 
}: { 
  index: number, style: React.CSSProperties, data: { 
    posts: BasePost[]; 
    onPostInteraction?: (postId: string, action: string, data?: unknown) => void;
    onLoadMore?: () => void;
    hasNextPage?: boolean;
    isLoadingMore?: boolean;
  } 
}) => {
  const { posts, onPostInteraction, onLoadMore, hasNextPage, isLoadingMore } = data;
  const post = posts[index];

  // Trigger load more when near the end
  const shouldLoadMore = index >= posts.length - 3 && hasNextPage && !isLoadingMore && onLoadMore;
  
  React.useEffect(() => {
    if (shouldLoadMore) {
      onLoadMore();
    }
  }, [shouldLoadMore, onLoadMore]);

  if (!post) {
    return (
      <div style={style} className="px-4">
        <NewsFeedSkeleton />
    </div>
    );
  }

  return (
    <motion.div
      style={style} initial={{ opacity: 0, y: 20 }}, animate={{ opacity: 1, y: 0 }}, transition={{ duration: 0.3, delay: index * 0.05 }}, className="px-4"
    >
      <div style={{ paddingBottom: ITEM_SPACING }}>
        <PostCard
          post={post} onInteraction={onPostInteraction}, className="shadow-sm hover:shadow-md transition-shadow duration-200"
        />
    </div>
    </motion.div>
  );
});

PostItem.displayName = 'PostItem';

const VirtualizedFeedList: React.FC<VirtualizedFeedListProps> = memo(({
  posts,
  isLoading,
  isLoadingMore = false,
  hasNextPage = false,
  onLoadMore,
  onPostInteraction,
  enableVirtualization = true,
  itemHeight = DEFAULT_ITEM_HEIGHT,
  containerHeight = CONTAINER_HEIGHT,
  className = ''
}) => {
  // Memoize the data object to prevent unnecessary re-renders
  const itemData = useMemo(() => ({
    posts,
    onPostInteraction,
    onLoadMore,
    hasNextPage,
    isLoadingMore
  }), [posts, onPostInteraction, onLoadMore, hasNextPage, isLoadingMore]);

  // Calculate total height including spacing
  const adjustedItemHeight = itemHeight + ITEM_SPACING;

  // Calculate height properly - moved before conditional return to follow Rules of Hooks
  const listHeight = useMemo(() => {
    if (typeof containerHeight === 'string') {
      return parseInt(containerHeight.replace(/\D/g, '')) || 600;
    }
    return (containerHeight as number) || 600;
  }, [containerHeight]);

  // Development-only diagnostics to validate render path and heights
  if (process.env.NODE_ENV === 'development') {
    // eslint-disable-next-line no-console
    console.debug('[VirtualizedFeedList] config', {
      enableVirtualization,
      postsCount: posts.length,
      isLoading,
      isLoadingMore,
      hasNextPage,
      listHeight,
      itemHeight: adjustedItemHeight
    });
  }

  // Non-virtualized fallback for smaller lists or when disabled
  if (!enableVirtualization || posts.length < 10) {
    return (
      <div className={cn("space-y-6", className)}>
        {isLoading && (
          <div className="space-y-6">
            {Array.from({ length: Math.min(3, Math.max(1, posts.length || 1)) }).map((_, i) => (
              <NewsFeedSkeleton key={`loading-skeleton-${i}`} />
            ))}
          </div>
        )}

        {posts.map((post, index) => (
          <motion.div
            key={post.id} initial={{ opacity: 0, y: 20 }}, animate={{ opacity: 1, y: 0 }}, transition={{ duration: 0.3, delay: index * 0.05 }}
          >
            <PostCard
              post={post} onInteraction={onPostInteraction}, className="shadow-sm hover:shadow-md transition-shadow duration-200"
            />
          </motion.div>
        ))}
        
        {(isLoadingMore || (isLoading && posts.length === 0)) && (
          <div className="space-y-6">
            {Array.from({ length: 3 }).map((_, i) => (
              <NewsFeedSkeleton key={`skeleton-${i}`} />
            ))}
          </div>
        )}
      </div>
    );
  }

  // Virtualized list for better performance with large datasets
  return (
    <div className={className}>
      <List
        height={listHeight} width="100%"
        itemCount={posts.length + (isLoadingMore ? 3 : 0)} itemSize={adjustedItemHeight}, itemData={itemData} overscanCount={OVERSCAN_COUNT}, className="scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 dark:scrollbar-thumb-gray-600 dark:scrollbar-track-gray-800"
      >
        {PostItem}
      </List>
    </div>
  );
});

VirtualizedFeedList.displayName = 'VirtualizedFeedList';

export default VirtualizedFeedList;
