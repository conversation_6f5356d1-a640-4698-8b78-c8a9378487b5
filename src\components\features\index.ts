// Feature Components - Lazy loaded by feature area
import { lazy } from 'react';

// Feed Features
export const VirtualizedFeed = lazy(() => import('../optimized/VirtualizedFeed'));
export const AdvancedVirtualizedFeed = lazy(() => import('../advanced/AdvancedVirtualizedFeed'));
export const NewsFeedTabs = lazy(() => import('../NewsFeedTabs'));
export const UnifiedNewsFeedFilters = lazy(() => import('../UnifiedNewsFeedFilters'));

// Post Features
export const PostCard = lazy(() => import('../posts/PostCard'));
export const EnhancedPostCard = lazy(() => import('../posts/EnhancedPostCard'));
export const CreatePostNew = lazy(() => import('../posts/CreatePostNew'));
export const PostComments = lazy(() => import('../posts/PostComments'));

// Messaging Features
export const MessagingContainer = lazy(() => import('../messaging/MessagingContainer'));
export const OptimizedMessagingInterface = lazy(() => import('../OptimizedMessagingInterface'));
export const MessageList = lazy(() => import('../messages/MessageList'));
export const MessageComposer = lazy(() => import('../messages/MessageComposer'));

// Advanced Features
export const WebWorkerDashboard = lazy(() => import('../WebWorkerDashboardWrapper'));
export const RealTimeCollaborationDemo = lazy(() => import('../advanced/RealTimeCollaborationDemo'));
export const FuzzySearchDemo = lazy(() => import('../advanced/FuzzySearchDemo'));

// Notification Features
export const NotificationsCenter = lazy(() => import('../notifications/NotificationsCenter'));
export const OptimizedNotificationCenter = lazy(() => import('../OptimizedNotificationCenter'));

// Performance Features
export const PerformanceDashboard = lazy(() => import('../PerformanceDashboard'));
export const RealTimePerformanceDashboard = lazy(() => import('../enhanced/RealTimePerformanceDashboard'));
export const OptimizationDashboard = lazy(() => import('../enhanced/OptimizationDashboard'));

// YouTube Features
export const YouTubeLayout = lazy(() => import('../youtube/layout/YouTubeLayout'));
export const VideoGrid = lazy(() => import('../youtube/video/VideoGrid'));
export const YouTubePlayer = lazy(() => import('../youtube/YouTubePlayer'));

// Search Features
export const EnhancedSearchInterface = lazy(() => import('../search/EnhancedSearchInterface'));
export const GlobalSearchHeader = lazy(() => import('../GlobalSearchHeader'));

// Social Features
export const GroupManagementPanel = lazy(() => import('../GroupManagementPanel'));
export const FriendSuggestions = lazy(() => import('../friends/FriendSuggestions'));
export const PeopleYouMayKnow = lazy(() => import('../PeopleYouMayKnow'));

// Media Features
export const EnhancedVideoPlayer = lazy(() => import('../EnhancedVideoPlayer'));
export const ReelsViewer = lazy(() => import('../ReelsViewer'));
export const PhotoAlbumManager = lazy(() => import('../PhotoAlbumManager'));

// Privacy & Security
export const PrivacyDashboard = lazy(() => import('../PrivacyDashboard'));
export const SecurityDashboard = lazy(() => import('../messaging/SecurityDashboard'));
export const TwoFactorSetup = lazy(() => import('../privacy/TwoFactorSetup'));

// Analytics & Monitoring
export const SearchAnalytics = lazy(() => import('../SearchAnalytics'));
export const ReactionAnalytics = lazy(() => import('../ReactionAnalytics'));