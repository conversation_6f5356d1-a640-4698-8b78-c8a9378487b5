/**
 * OptimizedMessageBubble - High-performance message component with advanced memoization
 */

import React, { useCallback, useMemo } from 'react';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import { 
  MoreHorizontal, 
  Reply, 
  Edit3, 
  Trash2, 
  <PERSON><PERSON>,
  Heart,
  Thum<PERSON>Up,
  <PERSON>,
  <PERSON><PERSON>he<PERSON>,
  Check
} from 'lucide-react';

import { Message, MessageReaction } from '@/types/enhanced-messaging';
import { Button } from '@/components/ui/button';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipTrigger, TooltipContent } from '@/components/ui/tooltip';

import {
  createMemoizedMessage,
  useStableCallback,
  useRenderPerformance,
  messageEqual
} from '@/utils/messagingMemoization';

interface OptimizedMessageBubbleProps {
  message: Message, currentUserId: string;
  isOwn?: boolean;
  showActions?: boolean;
  compact?: boolean;
  onReply?: (message: Message) => void;
  onEdit?: (message: Message) => void;
  onDelete?: (messageId: string) => void;
  onReaction?: (messageId: string, emoji: string) => void;
  onCopy?: (content: string) => void;
}

// Memoized reaction button component
const ReactionButton = React.memo<{
  reaction: MessageReaction, onClick: () => void; isActive: boolean;
}>(({ reaction, onClick, isActive }) => (
  <Tooltip>
    <TooltipTrigger asChild>
      <Button
        variant="ghost"
        size="sm"
        onClick={onClick} className={cn(
          "h-7 px-2 text-xs transition-all duration-200",
          isActive && "bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300"
        )}
      >
        <span className="mr-1">{reaction.emoji}</span>
        {reaction.count > 0 && <span>{reaction.count}</span>}
      </Button>
    </TooltipTrigger>
    <TooltipContent>
      <p>{reaction.users?.map(u => u.name).join(', ') || 'Users'} reacted with {reaction.emoji}</p>
    </TooltipContent>
  </Tooltip>
), (prevProps, nextProps) => {
  return (
    prevProps.reaction.emoji === nextProps.reaction.emoji &&
    prevProps.reaction.count === nextProps.reaction.count &&
    prevProps.isActive === nextProps.isActive &&
    prevProps.onClick === nextProps.onClick
  );
});

ReactionButton.displayName = 'ReactionButton';

// Memoized message status component
const MessageStatus = React.memo<{
  status: Message['status'], timestamp: Date;
  editedAt?: Date;
  compact: boolean;
}>(({ status, timestamp, editedAt, compact }) => {
  const timeString = useMemo(() => {
    try {
      return format(timestamp, compact ? 'HH:mm' : 'MMM d, HH:mm');
    } catch {
      return '';
    }
  }, [timestamp, compact]);

  const statusIcon = useMemo(() => {
    switch (status) {
      case 'sending':
        return <div className="w-3 h-3 rounded-full bg-gray-400 animate-pulse" />;
      case 'sent':
        return <Check className="w-3 h-3 text-gray-400" />;
      case 'delivered':
        return <CheckCheck className="w-3 h-3 text-gray-400" />;
      case 'read':
        return <CheckCheck className="w-3 h-3 text-blue-500" />;
      case 'failed':
        return <div className="w-3 h-3 rounded-full bg-red-500" />;
      default:
        return null;
    }
  }, [status]);

  return (
    <div className="flex items-center gap-1 text-xs text-gray-500 mt-1">
      <span>{timeString}</span>
      {editedAt && <span className="italic">(edited)</span>}
      {statusIcon}
    </div>
  );
}, (prevProps, nextProps) => {
  return (
    prevProps.status === nextProps.status &&
    prevProps.timestamp === nextProps.timestamp &&
    prevProps.editedAt === nextProps.editedAt &&
    prevProps.compact === nextProps.compact
  );
});

MessageStatus.displayName = 'MessageStatus';

// Main message bubble component
const OptimizedMessageBubbleComponent: React.FC<OptimizedMessageBubbleProps> = ({
  message,
  currentUserId,
  isOwn,
  showActions = true,
  compact = false,
  onReply,
  onEdit,
  onDelete,
  onReaction,
  onCopy
}) => {
  // Performance monitoring in development
  useRenderPerformance('OptimizedMessageBubble', process.env.NODE_ENV === 'development');

  // Memoized calculations
  const isMessageOwn = useMemo(() => 
    isOwn ?? message.senderId === currentUserId, 
    [isOwn, message.senderId, currentUserId]
  );

  const messageTimestamp = useMemo(() => 
    new Date(message.timestamp), 
    [message.timestamp]
  );

  const processedReactions = useMemo(() => {
    if (!message.reactions || message.reactions.length === 0) return [];
    
    // Group reactions by emoji
    const reactionMap = new Map<string, MessageReaction>();
    
    for (const reaction of message.reactions) {
      const existing = reactionMap.get(reaction.emoji);
      if (existing) {
        existing.count += 1;
        existing.users = existing.users || [];
        if (reaction.user && !existing.users.some(u => u.id === reaction.user!.id)) {
          existing.users.push(reaction.user);
        }
      } else {
        reactionMap.set(reaction.emoji, {
          ...reaction,
          count: 1,
          users: reaction.user ? [reaction.user] : []
        });
      }
    }
    
    return Array.from(reactionMap.values()).sort((a, b) => b.count - a.count);
  }, [message.reactions]);

  // Stable callback handlers
  const handleReply = useStableCallback(() => {
    onReply?.(message);
  }, [onReply, message]);

  const handleEdit = useStableCallback(() => {
    onEdit?.(message);
  }, [onEdit, message]);

  const handleDelete = useStableCallback(() => {
    onDelete?.(message.id);
  }, [onDelete, message.id]);

  const handleCopy = useStableCallback(() => {
    onCopy?.(message.content);
  }, [onCopy, message.content]);

  const handleReactionClick = useStableCallback((emoji: string) => {
    onReaction?.(message.id, emoji);
  }, [onReaction, message.id]);

  // Quick reactions
  const quickReactions = useMemo(() => ['👍', '❤️', '😊', '😂', '😮', '😢'], []);

  // Don't render deleted messages for non-owners
  if (message.isDeleted && !isMessageOwn) {
    return null;
  }

  return (
    <div
      className={cn(
        "group flex gap-3 p-3 transition-colors duration-150",
        compact && "p-2",
        isMessageOwn ? "flex-row-reverse" : "flex-row",
        "hover:bg-gray-50 dark:hover:bg-gray-800/50"
      )}
    >
      {/* Avatar (only for non-own messages in non-compact mode) */}
      {!isMessageOwn && !compact && (
        <div className="flex-shrink-0">
          <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white text-sm font-medium">
            {message.sender?.name?.charAt(0).toUpperCase() || 'U'}
          </div>
    </div>
      )}

      {/* Message content */}
      <div className={cn(
        "flex-1 min-w-0",
        isMessageOwn ? "text-right" : "text-left"
      )}>
        {/* Sender name (only for non-own messages) */}
        {!isMessageOwn && !compact && (
          <div className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-1">
            {message.sender?.name || 'Unknown User'}
          </div>
        )}

        {/* Message bubble */}
        <div className={cn(
          "relative inline-block max-w-[80%]",
          isMessageOwn ? "ml-auto" : "mr-auto"
        )}>
          {/* Reply indicator */}
          {message.replyTo && (
            <div className={cn(
              "text-xs text-gray-500 mb-1 p-2 rounded-lg bg-gray-100 dark:bg-gray-800",
              compact && "p-1"
            )}>
              <div className="flex items-center gap-1">
                <Reply className="w-3 h-3" />
                <span>Replying to:</span>
    </div>
              <div className="truncate italic">
                {message.replyTo.content?.substring(0, 50)}...
              </div>
    </div>
          )}

          {/* Main message bubble */}
          <div
            className={cn(
              "px-4 py-2 rounded-2xl shadow-sm transition-all duration-200",
              compact && "px-3 py-1 text-sm",
              isMessageOwn
                ? "bg-blue-500 text-white"
                : "bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 border border-gray-200 dark:border-gray-700",
              message.isDeleted && "opacity-60 italic"
            )}
          >
            {message.isDeleted ? (
              <span className="text-gray-500">This message was deleted</span>
            ) : (
              <>
                {/* Message content */}
                <div className="whitespace-pre-wrap break-words">
                  {message.content}
                </div>

                {/* Attachments */}
                {message.attachments && message.attachments.length > 0 && (
                  <div className="mt-2 space-y-2">
                    {message.attachments.map((attachment, index) => (
                      <div
                        key={index} className="flex items-center gap-2 p-2 rounded-lg bg-black/10"
                      >
                        <div className="text-sm truncate">
                          {attachment.name}
                        </div>
                        <Badge variant="secondary" className="text-xs">
                          {attachment.type}
                        </Badge>
    </div>
                    ))}
                  </div>
                )}

                {/* Thread indicator */}
                {message.threadId && (
                  <div className="mt-2 text-xs opacity-70">
                    Part of thread
                  </div>
                )}
              </>
            )}
          </div>

          {/* Message status and timestamp */}
          <div className={cn(
            "mt-1",
            isMessageOwn ? "text-right" : "text-left"
          )}>
            <MessageStatus
              status={message.status} timestamp={messageTimestamp}, editedAt={message.editedAt} compact={compact}
            />
    </div>
          {/* Reactions */}
          {processedReactions.length > 0 && (
            <div className={cn(
              "flex flex-wrap gap-1 mt-2",
              isMessageOwn ? "justify-end" : "justify-start"
            )}>
              {processedReactions.map((reaction) => {
                const isActive = reaction.users?.some(u => u.id === currentUserId) || false;
                return (
                  <ReactionButton
                    key={reaction.emoji} reaction={reaction}, isActive={isActive} onClick={() => handleReactionClick(reaction.emoji)}
                  />
                );
              })}
            </div>
          )}

          {/* Actions menu */}
          {showActions && !message.isDeleted && (
            <div className={cn(
              "absolute top-0 opacity-0 group-hover:opacity-100 transition-opacity duration-150",
              isMessageOwn ? "-left-10" : "-right-10"
            )}>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 rounded-full bg-white dark:bg-gray-800 shadow-lg border"
                  >
                    <MoreHorizontal className="w-4 h-4" />
    </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align={isMessageOwn ? "end" : "start"}>
                  {onReply && (
                    <DropdownMenuItem onClick={handleReply}>
                      <Reply className="w-4 h-4 mr-2" />
                      Reply
                    </DropdownMenuItem>
                  )}
                  
                  {/* Quick reactions */}
                  <div className="px-2 py-1">
                    <div className="text-xs text-gray-500 mb-1">Quick reactions</div>
                    <div className="flex gap-1">
                      {quickReactions.map((emoji) => (
                        <Button
                          key={emoji} variant="ghost"
                          size="sm"
                          onClick={() => handleReactionClick(emoji)} className="h-7 w-7 p-0 text-sm"
                        >
                          {emoji}
                        </Button>
                      ))}
                    </div>
    </div>
                  {onCopy && (
                    <DropdownMenuItem onClick={handleCopy}>
                      <Copy className="w-4 h-4 mr-2" />
                      Copy text
                    </DropdownMenuItem>
                  )}

                  {isMessageOwn && onEdit && (
                    <DropdownMenuItem onClick={handleEdit}>
                      <Edit3 className="w-4 h-4 mr-2" />
                      Edit
                    </DropdownMenuItem>
                  )}

                  {isMessageOwn && onDelete && (
                    <DropdownMenuItem 
                      onClick={handleDelete} className="text-red-600 dark:text-red-400"
                    >
                      <Trash2 className="w-4 h-4 mr-2" />
                      Delete
                    </DropdownMenuItem>
                  )}
                </DropdownMenuContent>
    </DropdownMenu>
            </div>
          )}
        </div>
    </div>
    </div>
  );
};

// Apply memoization with specialized comparison
export const OptimizedMessageBubble = createMemoizedMessage(OptimizedMessageBubbleComponent);

export default OptimizedMessageBubble;