import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Book, 
  MessageSquare, 
  Zap, 
  Users, 
  Keyboard, 
  Bell, 
  Settings, 
  ChevronRight,
  ChevronDown,
  Play,
  ExternalLink,
  Search,
  Filter
} from 'lucide-react';

interface GuideSection {
  id: string, title: string, icon: React.ReactNode, description: string, content: React.ReactNode, difficulty: 'beginner' | 'intermediate' | 'advanced', estimatedTime: string;
}

interface UserGuideProps {
  isOpen: boolean, onClose: () => void;
  initialSection?: string;
}

const UserGuide: React.FC<UserGuideProps> = ({
  isOpen,
  onClose,
  initialSection
}) => {
  const [activeSection, setActiveSection] = useState(initialSection || 'getting-started');
  const [searchTerm, setSearchTerm] = useState('');
  const [difficultyFilter, setDifficultyFilter] = useState<string>('all');
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set(['getting-started']));

  const sections: GuideSection[] = [
    {
      id: 'getting-started',
      title: 'Getting Started',
      icon: <Play className="w-5 h-5" />
        description: 'Learn the basics of the messaging system',
      difficulty: 'beginner',
      estimatedTime: '5 min',
      content: (
        <div className="guide-content">
          <h3>Welcome to Advanced Messaging</h3>
          <p>
            Our advanced messaging system provides a rich, interactive communication experience 
            with features like reactions, threads, keyboard navigation, and smart notifications.
          </p>
          
          <div className="guide-steps">
            <div className="guide-step">
              <div className="step-number">1</div>
              <div className="step-content">
                <h4>Start a Conversation</h4>
                <p>Click on any conversation in the sidebar or create a new one using the + button.</p>
    </div>
            </div>
            
            <div className="guide-step">
              <div className="step-number">2</div>
              <div className="step-content">
                <h4>Send Your First Message</h4>
                <p>Type your message in the input field and press Enter or click Send.</p>
    </div>
            </div>
            
            <div className="guide-step">
              <div className="step-number">3</div>
              <div className="step-content">
                <h4>Explore Features</h4>
                <p>Try adding reactions, replying to messages, or using keyboard shortcuts.</p>
    </div>
            </div>
    </div>
          <div className="guide-tip">
            <strong>Pro Tip:</strong> Press F1 at any time to see all available keyboard shortcuts.
          </div>
    </div>
      )
    },
    {
      id: 'messaging-basics',
      title: 'Messaging Basics',
      icon: <MessageSquare className="w-5 h-5" />
        description: 'Learn how to send, edit, and manage messages',
      difficulty: 'beginner',
      estimatedTime: '8 min',
      content: (
        <div className="guide-content">
          <h3>Sending Messages</h3>
          <ul>
            <li><strong>Basic Message:</strong> Type and press Enter or click Send</li>
            <li><strong>New Line:</strong> Press Shift+Enter to add a line break</li>
            <li><strong>Quick Send:</strong> Press Ctrl+Enter to send immediately</li>
    </ul>
          <h3>Message Actions</h3>
          <div className="guide-actions">
            <div className="action-item">
              <strong>Edit:</strong> Click the edit icon or press 'E' when a message is selected
            </div>
            <div className="action-item">
              <strong>Delete:</strong> Click the delete icon or press 'Delete' key
            </div>
            <div className="action-item">
              <strong>Copy:</strong> Click copy icon or press Ctrl+C
            </div>
            <div className="action-item">
              <strong>Reply:</strong> Click reply icon or press 'R'
            </div>
    </div>
          <h3>Message Formatting</h3>
          <div className="formatting-examples">
            <div className="format-item">
              <code>**bold text**</code> → <strong>bold text</strong>
    </div>
            <div className="format-item">
              <code>*italic text*</code> → <em>italic text</em>
    </div>
            <div className="format-item">
              <code>`code text`</code> → <code>code text</code>
    </div>
          </div>
    </div>
      )
    },
    {
      id: 'reactions-threads',
      title: 'Reactions & Threads',
      icon: <Zap className="w-5 h-5" />
        description: 'Add reactions and create threaded conversations',
      difficulty: 'intermediate',
      estimatedTime: '10 min',
      content: (
        <div className="guide-content">
          <h3>Message Reactions</h3>
          <p>Express yourself quickly with emoji reactions:</p>
          <ul>
            <li>Hover over any message to see reaction options</li>
            <li>Click an emoji to add your reaction</li>
            <li>Click again to remove your reaction</li>
            <li>See who reacted by hovering over the reaction count</li>
    </ul>
          <h3>Message Threads</h3>
          <p>Keep conversations organized with threaded replies:</p>
          <div className="guide-steps">
            <div className="guide-step">
              <div className="step-number">1</div>
              <div className="step-content">
                <h4>Start a Thread</h4>
                <p>Click "Reply in thread" on any message or press 'R' when selected.</p>
    </div>
            </div>
            
            <div className="guide-step">
              <div className="step-number">2</div>
              <div className="step-content">
                <h4>View Thread</h4>
                <p>Click on a message with thread replies to expand the thread view.</p>
    </div>
            </div>
            
            <div className="guide-step">
              <div className="step-number">3</div>
              <div className="step-content">
                <h4>Navigate Threads</h4>
                <p>Use arrow keys or click to navigate between thread messages.</p>
    </div>
            </div>
    </div>
          <div className="guide-tip">
            <strong>Thread Tip:</strong> Threads help keep the main conversation clean while allowing detailed discussions on specific topics.
          </div>
    </div>
      )
    },
    {
      id: 'keyboard-navigation',
      title: 'Keyboard Navigation',
      icon: <Keyboard className="w-5 h-5" />
        description: 'Master keyboard shortcuts for efficient messaging',
      difficulty: 'intermediate',
      estimatedTime: '12 min',
      content: (
        <div className="guide-content">
          <h3>Essential Shortcuts</h3>
          <div className="shortcuts-grid">
            <div className="shortcut-category">
              <h4>Navigation</h4>
              <div className="shortcut-item">
                <kbd>↑ ↓</kbd>
                <span>Navigate messages</span>
    </div>
              <div className="shortcut-item">
                <kbd>Ctrl + ↑ ↓</kbd>
                <span>Switch conversations</span>
    </div>
              <div className="shortcut-item">
                <kbd>Ctrl + F</kbd>
                <span>Search messages</span>
    </div>
              <div className="shortcut-item">
                <kbd>Esc</kbd>
                <span>Close current view</span>
    </div>
            </div>

            <div className="shortcut-category">
              <h4>Messaging</h4>
              <div className="shortcut-item">
                <kbd>Ctrl + Enter</kbd>
                <span>Send message</span>
    </div>
              <div className="shortcut-item">
                <kbd>Shift + Enter</kbd>
                <span>New line</span>
    </div>
              <div className="shortcut-item">
                <kbd>R</kbd>
                <span>Reply to message</span>
    </div>
              <div className="shortcut-item">
                <kbd>E</kbd>
                <span>Edit message</span>
    </div>
            </div>

            <div className="shortcut-category">
              <h4>Actions</h4>
              <div className="shortcut-item">
                <kbd>Ctrl + C</kbd>
                <span>Copy message</span>
    </div>
              <div className="shortcut-item">
                <kbd>Delete</kbd>
                <span>Delete message</span>
    </div>
              <div className="shortcut-item">
                <kbd>Ctrl + E</kbd>
                <span>Toggle emoji picker</span>
    </div>
              <div className="shortcut-item">
                <kbd>F1</kbd>
                <span>Show all shortcuts</span>
    </div>
            </div>
    </div>
          <div className="guide-tip">
            <strong>Navigation Tip:</strong> Click anywhere in the message area to activate keyboard navigation, then use arrow keys to move around.
          </div>
    </div>
      )
    },
    {
      id: 'notifications',
      title: 'Notifications & Privacy',
      icon: <Bell className="w-5 h-5" />
        description: 'Configure notifications and privacy settings',
      difficulty: 'intermediate',
      estimatedTime: '8 min',
      content: (
        <div className="guide-content">
          <h3>Notification Types</h3>
          <div className="notification-types">
            <div className="notification-type">
              <h4>🔔 Browser Notifications</h4>
              <p>Desktop notifications that appear even when the app isn't active.</p>
    </div>
            <div className="notification-type">
              <h4>🔊 Sound Alerts</h4>
              <p>Audio notifications for new messages, reactions, and mentions.</p>
    </div>
            <div className="notification-type">
              <h4>📱 In-App Notifications</h4>
              <p>Visual notifications within the messaging interface.</p>
    </div>
          </div>

          <h3>Privacy Controls</h3>
          <ul>
            <li><strong>Read Receipts:</strong> Control whether others can see when you've read messages</li>
            <li><strong>Typing Indicators:</strong> Show or hide when you're typing</li>
            <li><strong>Online Status:</strong> Control your visibility to other users</li>
            <li><strong>Message Preview:</strong> Choose whether notifications show message content</li>
    </ul>
          <h3>Quiet Hours</h3>
          <p>Set specific times when you don't want to receive notifications:</p>
          <ol>
            <li>Go to Settings → Notifications</li>
            <li>Enable "Quiet Hours"</li>
            <li>Set your preferred start and end times</li>
            <li>Choose which notification types to silence</li>
    </ol>
        </div>
      )
    },
    {
      id: 'group-messaging',
      title: 'Group Conversations',
      icon: <Users className="w-5 h-5" />
        description: 'Manage group chats and team communications',
      difficulty: 'intermediate',
      estimatedTime: '10 min',
      content: (
        <div className="guide-content">
          <h3>Creating Groups</h3>
          <div className="guide-steps">
            <div className="guide-step">
              <div className="step-number">1</div>
              <div className="step-content">
                <h4>Start New Group</h4>
                <p>Click the "+" button and select "New Group" or use Ctrl+G.</p>
    </div>
            </div>
            
            <div className="guide-step">
              <div className="step-number">2</div>
              <div className="step-content">
                <h4>Add Participants</h4>
                <p>Search and select users to add to your group.</p>
    </div>
            </div>
            
            <div className="guide-step">
              <div className="step-number">3</div>
              <div className="step-content">
                <h4>Set Group Details</h4>
                <p>Give your group a name and optional description.</p>
    </div>
            </div>
    </div>
          <h3>Group Features</h3>
          <ul>
            <li><strong>@Mentions:</strong> Type @ followed by a name to mention someone</li>
            <li><strong>Group Reactions:</strong> See who reacted to messages</li>
            <li><strong>Thread Discussions:</strong> Keep side conversations organized</li>
            <li><strong>File Sharing:</strong> Share documents, images, and files</li>
    </ul>
          <h3>Group Management</h3>
          <div className="management-actions">
            <div className="action-item">
              <strong>Add Members:</strong> Click group name → Add People
            </div>
            <div className="action-item">
              <strong>Remove Members:</strong> Group Settings → Manage Members
            </div>
            <div className="action-item">
              <strong>Change Group Name:</strong> Click group name → Edit Details
            </div>
            <div className="action-item">
              <strong>Leave Group:</strong> Group Settings → Leave Group
            </div>
    </div>
        </div>
      )
    },
    {
      id: 'advanced-features',
      title: 'Advanced Features',
      icon: <Settings className="w-5 h-5" />
        description: 'Explore power user features and customizations',
      difficulty: 'advanced',
      estimatedTime: '15 min',
      content: (
        <div className="guide-content">
          <h3>Message Search</h3>
          <p>Find messages quickly with powerful search:</p>
          <ul>
            <li><strong>Basic Search:</strong> Type keywords in the search box</li>
            <li><strong>User Filter:</strong> <code>from:username</code> to search messages from specific users</li>
            <li><strong>Date Filter:</strong> <code>after:2024-01-01</code> or <code>before:2024-12-31</code></li>
            <li><strong>Content Type:</strong> <code>has:image</code>, <code>has:file</code>, <code>has:link</code></li>
    </ul>
          <h3>Custom Themes</h3>
          <p>Personalize your messaging experience:</p>
          <ul>
            <li>Choose from light, dark, or auto themes</li>
            <li>Adjust message bubble colors</li>
            <li>Customize notification sounds</li>
            <li>Set custom status messages</li>
    </ul>
          <h3>Keyboard Shortcuts Customization</h3>
          <p>Create your own shortcuts:</p>
          <ol>
            <li>Go to Settings → Keyboard Shortcuts</li>
            <li>Click "Add Custom Shortcut"</li>
            <li>Choose an action and key combination</li>
            <li>Save and test your new shortcut</li>
    </ol>
          <h3>Integration Features</h3>
          <div className="integration-features">
            <div className="feature-item">
              <h4>🔗 Link Previews</h4>
              <p>Automatic previews for shared links and media</p>
    </div>
            <div className="feature-item">
              <h4>📎 File Integration</h4>
              <p>Drag and drop files directly into conversations</p>
    </div>
            <div className="feature-item">
              <h4>🤖 Bot Commands</h4>
              <p>Use slash commands for quick actions</p>
    </div>
          </div>
    </div>
      )
    }
  ];

  const filteredSections = sections.filter(section => {
    const matchesSearch = searchTerm === '' || 
      section.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      section.description.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesDifficulty = difficultyFilter === 'all' || section.difficulty === difficultyFilter;
    
    return matchesSearch && matchesDifficulty;
  });

  const toggleSection = (sectionId: string) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(sectionId)) {
      newExpanded.delete(sectionId);
    } else {
      newExpanded.add(sectionId);
    }
    setExpandedSections(newExpanded);
  };

  if (!isOpen) return null;

  return (
    <div className="user-guide-overlay">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}, animate={{ opacity: 1, scale: 1 }}, exit={{ opacity: 0, scale: 0.95 }}, className="user-guide-modal"
      >
        {/* Header */}
        <div className="guide-header">
          <div className="guide-title">
            <Book className="w-6 h-6 text-blue-600" />
            <h2>User Guide</h2>
    </div>
          <button onClick={onClose} className="guide-close">
            ×
          </button>
    </div>
        {/* Search and Filters */}
        <div className="guide-controls">
          <div className="search-container">
            <Search className="w-4 h-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search guide..."
              value={searchTerm} onChange={(e) => setSearchTerm(e.target.value)}, className="search-input"
            />
    </div>
          <div className="filter-container">
            <Filter className="w-4 h-4 text-gray-400" />
            <select
              value={difficultyFilter} onChange={(e) => setDifficultyFilter(e.target.value)}, className="filter-select"
            >
              <option value="all">All Levels</option>
              <option value="beginner">Beginner</option>
              <option value="intermediate">Intermediate</option>
              <option value="advanced">Advanced</option>
    </select>
          </div>
    </div>
        {/* Content */}
        <div className="guide-body">
          {/* Sidebar */}
          <div className="guide-sidebar">
            <nav className="guide-nav">
              {filteredSections.map((section) => (
                <div key={section.id} className="nav-section">
                  <button
                    onClick={() => toggleSection(section.id)} className={`nav-item ${expandedSections.has(section.id) ? 'active' : ''}`}
                  >
                    <div className="nav-item-content">
                      <div className="nav-item-header">
                        {section.icon}
                        <span className="nav-item-title">{section.title}</span>
                        <div className="nav-item-meta">
                          <span className={`difficulty-badge ${section.difficulty}`}>
                            {section.difficulty}
                          </span>
                          <span className="time-badge">{section.estimatedTime}</span>
    </div>
                      </div>
                      <p className="nav-item-description">{section.description}</p>
    </div>
                    {expandedSections.has(section.id) ? (
                      <ChevronDown className="w-4 h-4" />
                    ) : (
                      <ChevronRight className="w-4 h-4" />
                    )}
                  </button>
    </div>
              ))}
            </nav>
    </div>
          {/* Main Content */}
          <div className="guide-main">
            <AnimatePresence mode="wait">
              {filteredSections
                .filter(section => expandedSections.has(section.id))
                .map((section) => (
                  <motion.div
                    key={section.id} initial={{ opacity: 0, y: 20 }}, animate={{ opacity: 1, y: 0 }}, exit={{ opacity: 0, y: -20 }}, className="guide-section-content"
                  >
                    <div className="section-header">
                      <div className="section-title">
                        {section.icon}
                        <h3>{section.title}</h3>
    </div>
                      <div className="section-meta">
                        <span className={`difficulty-badge ${section.difficulty}`}>
                          {section.difficulty}
                        </span>
                        <span className="time-badge">{section.estimatedTime}</span>
    </div>
                    </div>
                    {section.content}
                  </motion.div>
                ))}
            </AnimatePresence>

            {filteredSections.length === 0 && (
              <div className="no-results">
                <Search className="w-12 h-12 text-gray-300 mb-4" />
                <h3>No results found</h3>
                <p>Try adjusting your search terms or filters.</p>
    </div>
            )}
          </div>
    </div>
        {/* Footer */}
        <div className="guide-footer">
          <div className="footer-links">
            <a href="#" className="footer-link">
              <ExternalLink className="w-4 h-4" />
              Video Tutorials
            </a>
            <a href="#" className="footer-link">
              <ExternalLink className="w-4 h-4" />
              FAQ
            </a>
            <a href="#" className="footer-link">
              <ExternalLink className="w-4 h-4" />
              Contact Support
            </a>
    </div>
        </div>
      </motion.div>
    </div>
  );
};

export default UserGuide;