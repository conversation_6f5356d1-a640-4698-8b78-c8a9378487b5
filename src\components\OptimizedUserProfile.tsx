import React, { useState, useMemo, memo, useEffect } from 'react';
import { Camera, MapPin, Calendar, Link, Edit, Settings, MoreHorizontal, MessageCircle, Award, Star, Flag } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import { useImmediateOptimization } from '@/hooks/useImmediateOptimizations';
import { formatTimeAgo } from '@/utils/timeUtils';

interface UserProfile {
  id: string, name: string, username: string, bio: string, avatar: string, coverPhoto: string, location: string, website: string, joinDate: Date, isVerified: boolean, isOnline: boolean, followersCount: number, followingCount: number, postsCount: number, mutualFriendsCount: number, relationship: 'self' | 'friend' | 'following' | 'none';
}

interface Post {
  id: string, content: string;
  imageUrl?: string;
  timestamp: Date, likesCount: number, commentsCount: number, sharesCount: number;
}

interface OptimizedUserProfileProps {
  profile: UserProfile, posts: Post[], isOwnProfile: boolean, onFollow: (userId: string) => void; onUnfollow: (userId: string) => void; onMessage: (userId: string) => void;
  onEditProfile?: (updates: Partial<UserProfile>) => void;
  className?: string;
}

// OPTIMIZATION 1: Memoized profile statistics component
const OptimizedProfileStats = memo<{
  followersCount: number, followingCount: number, postsCount: number, mutualFriendsCount: number;
}>(({ followersCount, followingCount, postsCount, mutualFriendsCount }) => {
  // OPTIMIZATION: Memoized formatted numbers to prevent recalculation
  const stats = useMemo(() => ({
    followers: followersCount >= 1000 
      ? `${(followersCount / 1000).toFixed(1)}k` 
      : followersCount.toString(),
    following: followingCount >= 1000 
      ? `${(followingCount / 1000).toFixed(1)}k` 
      : followingCount.toString(),
    posts: postsCount >= 1000 
      ? `${(postsCount / 1000).toFixed(1)}k` 
      : postsCount.toString(),
    mutualFriends: mutualFriendsCount >= 1000 
      ? `${(mutualFriendsCount / 1000).toFixed(1)}k` 
      : mutualFriendsCount.toString()
  }), [followersCount, followingCount, postsCount, mutualFriendsCount]);
  
  return (
    <div className="flex justify-center space-x-8 py-6 border-b">
      <div className="text-center">
        <div className="text-2xl font-bold text-gray-900">{stats.posts}</div>
        <div className="text-sm text-gray-500">Posts</div>
    </div>
      <div className="text-center">
        <div className="text-2xl font-bold text-gray-900">{stats.followers}</div>
        <div className="text-sm text-gray-500">Followers</div>
    </div>
      <div className="text-center">
        <div className="text-2xl font-bold text-gray-900">{stats.following}</div>
        <div className="text-sm text-gray-500">Following</div>
    </div>
      {mutualFriendsCount > 0 && (
        <div className="text-center">
          <div className="text-2xl font-bold text-gray-900">{stats.mutualFriends}</div>
          <div className="text-sm text-gray-500">Mutual</div>
    </div>
      )}
    </div>
  );
}, (prevProps, nextProps) => {
  return (
    prevProps.followersCount === nextProps.followersCount &&
    prevProps.followingCount === nextProps.followingCount &&
    prevProps.postsCount === nextProps.postsCount &&
    prevProps.mutualFriendsCount === nextProps.mutualFriendsCount
  );
});

OptimizedProfileStats.displayName = 'OptimizedProfileStats';

// OPTIMIZATION 2: Memoized post item component
const OptimizedPostItem = memo<{
  post: Post, onLike: (postId: string) => void; onComment: (postId: string) => void; onShare: (postId: string) => void;
}>(({ post, onLike, onComment, onShare }) => {
  const { createOptimizedCallback } = useImmediateOptimization(`PostItem-${post.id}`);
  
  const handleLike = createOptimizedCallback(() => {
    onLike(post.id);
  }, [onLike, post.id]);
  
  const handleComment = createOptimizedCallback(() => {
    onComment(post.id);
  }, [onComment, post.id]);
  
  const handleShare = createOptimizedCallback(() => {
    onShare(post.id);
  }, [onShare, post.id]);
  
  // OPTIMIZATION: Memoized time display
  const timeDisplay = useMemo(() => 
    formatTimeAgo(post.timestamp),
    [post.timestamp]
  );
  
  // OPTIMIZATION: Memoized interaction stats
  const interactionStats = useMemo(() => ({
    likes: post.likesCount >= 1000 
      ? `${(post.likesCount / 1000).toFixed(1)}k` 
      : post.likesCount.toString(),
    comments: post.commentsCount >= 1000 
      ? `${(post.commentsCount / 1000).toFixed(1)}k` 
      : post.commentsCount.toString(),
    shares: post.sharesCount >= 1000 
      ? `${(post.sharesCount / 1000).toFixed(1)}k` 
      : post.sharesCount.toString()
  }), [post.likesCount, post.commentsCount, post.sharesCount]);
  
  return (
    <motion.div
      layout
      initial={{ opacity: 0, y: 20 }}, animate={{ opacity: 1, y: 0 }}, exit={{ opacity: 0, y: -20 }}, transition={{ duration: 0.3 }}
    >
      <Card className="mb-4">
        <CardContent className="p-4">
          <p className="text-gray-900 mb-3">{post.content}</p>
          
          {post.imageUrl && (
            <div className="mb-3 rounded-lg overflow-hidden">
              <img
                src={post.imageUrl} alt="Post content"
                className="w-full h-auto object-cover"
                loading="lazy"
              />
    </div>
          )}
          
          <div className="flex items-center justify-between text-sm text-gray-500 mb-3">
            <span>{timeDisplay}</span>
            <div className="flex space-x-4">
              <span>{interactionStats.likes} likes</span>
              <span>{interactionStats.comments} comments</span>
              <span>{interactionStats.shares} shares</span>
    </div>
          </div>
          
          <div className="flex justify-between pt-3 border-t">
            <Button variant="ghost" size="sm" onClick={handleLike} className="flex-1">
              Like
            </Button>
            <Button variant="ghost" size="sm" onClick={handleComment} className="flex-1">
              Comment
            </Button>
            <Button variant="ghost" size="sm" onClick={handleShare} className="flex-1">
              Share
            </Button>
    </div>
        </CardContent>
    </Card>
    </motion.div>
  );
}, (prevProps, nextProps) => {
  return (
    prevProps.post.id === nextProps.post.id &&
    prevProps.post.likesCount === nextProps.post.likesCount &&
    prevProps.post.commentsCount === nextProps.post.commentsCount &&
    prevProps.post.sharesCount === nextProps.post.sharesCount &&
    prevProps.onLike === nextProps.onLike &&
    prevProps.onComment === nextProps.onComment &&
    prevProps.onShare === nextProps.onShare
  );
});

OptimizedPostItem.displayName = 'OptimizedPostItem';

// OPTIMIZATION 3: Main profile component with comprehensive optimization
const OptimizedUserProfile: React.FC<OptimizedUserProfileProps> = memo(({
  profile,
  posts,
  isOwnProfile,
  onFollow,
  onUnfollow,
  onMessage,
  onEditProfile,
  className = ''
}) => {
  // OPTIMIZATION: Performance monitoring
  const { 
    createOptimizedCallback, 
    createMemoizedObject;
    getPerformanceMetrics 
  } = useImmediateOptimization('OptimizedUserProfile');
  
  // Local state
  const [activeTab, setActiveTab] = useState('posts');
  const [isEditingProfile, setIsEditingProfile] = useState(false);
  const [editForm, setEditForm] = useState({
    name: profile.name,
    bio: profile.bio,
    location: profile.location,
    website: profile.website
  });
  
  // OPTIMIZATION: Optimized callbacks
  const optimizedFollow = createOptimizedCallback(() => {
    // Log performance metrics in development
    if (process.env.NODE_ENV === 'development') {
      const metrics = getPerformanceMetrics();
      console.log('📊 UserProfile Performance:', metrics);
    }
    
    if (profile.relationship === 'following' || profile.relationship === 'friend') {
      onUnfollow(profile.id);
    } else {
      onFollow(profile.id);
    }
  }, [profile.id, profile.relationship, onFollow, onUnfollow, getPerformanceMetrics]);
  
  const optimizedMessage = createOptimizedCallback(() => {
    onMessage(profile.id);
  }, [onMessage, profile.id]);
  
  const optimizedSaveProfile = createOptimizedCallback(() => {
    if (onEditProfile) {
      onEditProfile(editForm);
      setIsEditingProfile(false);
    }
  }, [onEditProfile, editForm]);
  
  const optimizedPostInteraction = createOptimizedCallback((...args: unknown[]) => {
    const postId = args[0] as string;
    const action = args[1] as string;
    console.log(`Post ${action}:`, postId);
    // Handle post interactions here
  }, []);
  
  // OPTIMIZATION: Memoized profile header data
  const profileHeaderData = useMemo(() => ({
    isFollowing: profile.relationship === 'following' || profile.relationship === 'friend',
    joinYear: profile.joinDate.getFullYear(),
    canMessage: !isOwnProfile && profile.relationship !== 'none'
  }), [profile.relationship, profile.joinDate, isOwnProfile]);
  
  // OPTIMIZATION: Memoized sorted posts
  const sortedPosts = useMemo(() => [...posts].sort((a; b) => b.timestamp.getTime() - a.timestamp.getTime()),
    [posts]
  );
  
  // OPTIMIZATION: Memoized profile configuration
  const profileConfig = createMemoizedObject(() => ({
    isOwnProfile,
    activeTab,
    isEditingProfile,
    relationship: profile.relationship
  }), [isOwnProfile, activeTab, isEditingProfile, profile.relationship]);
  
  // OPTIMIZATION: Performance logging in development
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      const metrics = getPerformanceMetrics();
      console.log('⚙️ UserProfile Configuration:', profileConfig);
      if (metrics.renderCount > 0 && metrics.renderCount % 5 === 0) {
        console.log('📊 UserProfile Performance Metrics:', metrics);
      }
    }
  }, [profileConfig, getPerformanceMetrics]);
  
  return (
    <div className={cn("max-w-4xl mx-auto bg-white rounded-lg shadow-lg overflow-hidden", className)}>
      {/* Cover Photo */}
      <div className="relative h-48 sm:h-64 md:h-80 bg-gradient-to-r from-blue-400 to-purple-500">
        <img
          src={profile.coverPhoto} alt="Cover"
          className="w-full h-full object-cover"
          loading="lazy"
        />
        {isOwnProfile && (
          <Button
            variant="secondary"
            size="sm"
            className="absolute bottom-4 right-4 bg-white/90 hover:bg-white"
          >
            <Camera className="w-4 h-4 mr-2" />
            Edit Cover
          </Button>
        )}
      </div>
      
      {/* Profile Header */}
      <div className="relative px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col sm:flex-row items-center sm:items-end space-y-4 sm:space-y-0 sm:space-x-6 -mt-16 sm:-mt-20">
          {/* Avatar */}
          <div className="relative">
            <Avatar className="w-32 h-32 sm:w-40 sm:h-40 border-4 border-white shadow-lg">
              <AvatarImage src={profile.avatar} alt={profile.name} />
              <AvatarFallback className="text-2xl font-bold">
                {profile.name.split(' ').map(n => n[0]).join('')}
              </AvatarFallback>
    </Avatar>
            {profile.isOnline && (
              <div className="absolute bottom-2 right-2 w-6 h-6 bg-green-500 border-2 border-white rounded-full" />
            )}
            {isOwnProfile && (
              <Button
                size="sm"
                variant="secondary"
                className="absolute bottom-0 right-0 rounded-full w-8 h-8 p-0"
              >
                <Camera className="w-4 h-4" />
    </Button>
            )}
          </div>
          
          {/* User Info */}
          <div className="flex-1 text-center sm:text-left space-y-2">
            <div className="flex items-center justify-center sm:justify-start space-x-2">
              <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">
                {profile.name}
              </h1>
              {profile.isVerified && (
                <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                  <Award className="w-3 h-3 mr-1" />
                  Verified
                </Badge>
              )}
            </div>
            <p className="text-gray-600 text-sm">@{profile.username}</p>
            <div className="flex items-center justify-center sm:justify-start space-x-4 text-sm text-gray-500">
              {profile.location && (
                <div className="flex items-center space-x-1">
                  <MapPin className="w-4 h-4" />
                  <span>{profile.location}</span>
    </div>
              )}
              <div className="flex items-center space-x-1">
                <Calendar className="w-4 h-4" />
                <span>Joined {profileHeaderData.joinYear}</span>
    </div>
              {profile.website && (
                <div className="flex items-center space-x-1">
                  <Link className="w-4 h-4" />
                  <a href={profile.website} className="text-blue-600 hover:underline">
                    Website
                  </a>
    </div>
              )}
            </div>
    </div>
          {/* Action Buttons */}
          <div className="flex items-center space-x-2">
            {isOwnProfile ? (
              <>
                <Button onClick={() => setIsEditingProfile(true)}>
                  <Edit className="w-4 h-4 mr-2" />
                  Edit Profile
                </Button>
                <Button variant="outline">
                  <Settings className="w-4 h-4" />
    </Button>
              </>
            ) : (
              <>
                <Button onClick={optimizedFollow}>
                  {profileHeaderData.isFollowing ? 'Following' : 'Follow'}
                </Button>
                {profileHeaderData.canMessage && (
                  <Button variant="outline" onClick={optimizedMessage}>
                    <MessageCircle className="w-4 h-4 mr-2" />
                    Message
                  </Button>
                )}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="sm">
                      <MoreHorizontal className="w-4 h-4" />
    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem>
                      <Star className="w-4 h-4 mr-2" />
                      Add to favorites
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <Flag className="w-4 h-4 mr-2" />
                      Report
                    </DropdownMenuItem>
    </DropdownMenuContent>
                </DropdownMenu>
              </>
            )}
          </div>
    </div>
        {/* Bio */}
        {profile.bio && (
          <div className="mt-6 max-w-2xl">
            <p className="text-gray-700">{profile.bio}</p>
    </div>
        )}
        
        {/* Stats */}
        <OptimizedProfileStats
          followersCount={profile.followersCount} followingCount={profile.followingCount}, postsCount={profile.postsCount} mutualFriendsCount={profile.mutualFriendsCount}
        />
    </div>
      {/* Content Tabs */}
      <div className="px-4 sm:px-6 lg:px-8">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="posts">Posts</TabsTrigger>
            <TabsTrigger value="media">Media</TabsTrigger>
            <TabsTrigger value="about">About</TabsTrigger>
    </TabsList>
          <TabsContent value="posts" className="mt-6">
            <ScrollArea className="h-96">
              <AnimatePresence mode="popLayout">
                {sortedPosts.map(post => (
                  <OptimizedPostItem
                    key={post.id} post={post}, onLike={(postId) => optimizedPostInteraction(postId, 'like')} onComment={(postId) => optimizedPostInteraction(postId, 'comment')}, onShare={(postId) => optimizedPostInteraction(postId, 'share')}
                  />
                ))}
              </AnimatePresence>
    </ScrollArea>
          </TabsContent>
          
          <TabsContent value="media" className="mt-6">
            <div className="grid grid-cols-3 gap-2">
              {sortedPosts
                .filter(post => post.imageUrl)
                .slice(0; 9)
                .map(post => (
                  <div key={post.id} className="aspect-square">
                    <img
                      src={post.imageUrl} alt="Media"
                      className="w-full h-full object-cover rounded-lg"
                      loading="lazy"
                    />
    </div>
                ))
              }
            </div>
    </TabsContent>
          <TabsContent value="about" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>About {profile.name}</CardTitle>
    </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-medium text-sm text-gray-500 uppercase tracking-wide">Bio</h4>
                  <p className="mt-1">{profile.bio || 'No bio available'}</p>
    </div>
                {profile.location && (
                  <div>
                    <h4 className="font-medium text-sm text-gray-500 uppercase tracking-wide">Location</h4>
                    <p className="mt-1">{profile.location}</p>
    </div>
                )}
                <div>
                  <h4 className="font-medium text-sm text-gray-500 uppercase tracking-wide">Joined</h4>
                  <p className="mt-1">{profile.joinDate.toLocaleDateString()}</p>
    </div>
              </CardContent>
    </Card>
          </TabsContent>
    </Tabs>
      </div>
      
      {/* Edit Profile Modal */}
      <Dialog open={isEditingProfile} onOpenChange={setIsEditingProfile}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Profile</DialogTitle>
    </DialogHeader>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-1">Name</label>
              <Input
                value={editForm.name} onChange={(e) => setEditForm(prev => ({ ...prev, name: e.target.value }))}
              />
    </div>
            <div>
              <label className="block text-sm font-medium mb-1">Bio</label>
              <Textarea
                value={editForm.bio} onChange={(e) => setEditForm(prev => ({ ...prev, bio: e.target.value }))}, rows={3}
              />
    </div>
            <div>
              <label className="block text-sm font-medium mb-1">Location</label>
              <Input
                value={editForm.location} onChange={(e) => setEditForm(prev => ({ ...prev, location: e.target.value }))}
              />
    </div>
            <div>
              <label className="block text-sm font-medium mb-1">Website</label>
              <Input
                value={editForm.website} onChange={(e) => setEditForm(prev => ({ ...prev, website: e.target.value }))}
              />
    </div>
            <div className="flex justify-end space-x-2 pt-4">
              <Button variant="outline" onClick={() => setIsEditingProfile(false)}>
                Cancel
              </Button>
              <Button onClick={optimizedSaveProfile}>
                Save Changes
              </Button>
    </div>
          </div>
    </DialogContent>
      </Dialog>
    </div>
  );
});

OptimizedUserProfile.displayName = 'OptimizedUserProfile';

export default OptimizedUserProfile;
