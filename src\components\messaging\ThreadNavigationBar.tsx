/**
 * ThreadNavigationBar Component
 * Navigation bar for active threads in conversation
 */

import React, { useState, useMemo, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  MessageCircle, 
  X, 
  ChevronLeft, 
  ChevronRight,
  Hash,
  Users,
  Clock,
  Filter,
  Search
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { formatDistanceToNow } from 'date-fns';
import { cn } from '@/lib/utils';
import { useMessagingStore } from '../../stores/messagingStore';
import { MessageThread } from '../../types/messaging';

interface ThreadNavigationBarProps {
  conversationId: string;
  activeThreadId?: string | null;
  onThreadSelect: (threadId: string) => void; onThreadClose: () => void; currentUserId: string;
  getUserName?: (userId: string) => string;
  getUserAvatar?: (userId: string) => string;
  className?: string;
  maxVisibleThreads?: number;
  showSearch?: boolean;
}

interface ThreadTabProps {
  thread: MessageThread, isActive: boolean, onClick: () => void; onClose: (e: React.MouseEvent) => void; getUserName: (userId: string) => string; getUserAvatar: (userId: string) => string; replyCount: number, hasUnread: boolean;
}

const ThreadTab: React.FC<ThreadTabProps> = ({
  thread,
  isActive,
  onClick,
  onClose,
  getUserName,
  getUserAvatar,
  replyCount,
  hasUnread
}) => {
  const [isHovered, setIsHovered] = useState(false);

  // Get the thread starter (parent message sender)
  const { messages } = useMessagingStore();
  const parentMessage = messages[thread.parentMessageId];
  const threadStarter = parentMessage ? getUserName(parentMessage.senderId) : 'Unknown';

  return (
    <motion.div
      layout
      initial={{ opacity: 0, x: 20 }}, animate={{ opacity: 1, x: 0 }}, exit={{ opacity: 0, x: -20 }}, whileHover={{ y: -2 }}, onMouseEnter={() => setIsHovered(true)} onMouseLeave={() => setIsHovered(false)}, className={cn(
        "relative flex items-center gap-2 px-3 py-2 rounded-lg cursor-pointer transition-all duration-200 min-w-0 max-w-48",
        isActive 
          ? "bg-blue-100 dark:bg-blue-900/30 border-2 border-blue-300 dark:border-blue-600" 
          : "bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 border-2 border-transparent",
        hasUnread && !isActive && "ring-2 ring-blue-200 dark:ring-blue-800"
      )} onClick={onClick}
    >
      {/* Thread starter avatar */}
      <Avatar className="w-6 h-6 flex-shrink-0">
        <AvatarImage src={parentMessage ? getUserAvatar(parentMessage.senderId) : undefined} />
        <AvatarFallback className="text-xs">
          {threadStarter.charAt(0).toUpperCase()}
        </AvatarFallback>
    </Avatar>
      {/* Thread info */}
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-1 mb-0.5">
          <MessageCircle className="w-3 h-3 text-blue-600 flex-shrink-0" />
          <span className="text-xs font-medium text-gray-900 dark:text-white truncate">
            {threadStarter}
          </span>
          {hasUnread && (
            <motion.div
              initial={{ scale: 0 }}, animate={{ scale: 1 }}, className="w-2 h-2 bg-blue-600 rounded-full flex-shrink-0"
            />
          )}
        </div>
        
        <div className="flex items-center gap-2">
          <Badge variant="secondary" className="text-xs px-1 py-0 h-4">
            <Hash className="w-2 h-2 mr-0.5" />
            {replyCount}
          </Badge>
          
          <span className="text-xs text-gray-500 dark:text-gray-400 truncate">
            {formatDistanceToNow(new Date(thread.lastActivity), { addSuffix: true })}
          </span>
    </div>
      </div>

      {/* Close button */}
      <AnimatePresence>
        {(isHovered || isActive) && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}, animate={{ opacity: 1, scale: 1 }}, exit={{ opacity: 0, scale: 0.8 }}, className="flex-shrink-0"
          >
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose} className="h-5 w-5 p-0 hover:bg-red-100 dark:hover:bg-red-900/20 text-gray-400 hover:text-red-600"
            >
              <X className="w-3 h-3" />
    </Button>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

// Animation variants
const barVariants = {
  initial: { 
    opacity: 0, 
    y: -20,
    height: 0
  },
  animate: { 
    opacity: 1, 
    y: 0,
    height: 'auto',
    transition: {
      type: 'spring',
      stiffness: 300,
      damping: 30
    }
  },
  exit: { 
    opacity: 0, 
    y: -20,
    height: 0,
    transition: {
      duration: 0.2
    }
  }
};

export const ThreadNavigationBar: React.FC<ThreadNavigationBarProps> = ({
  conversationId,
  activeThreadId,
  onThreadSelect,
  onThreadClose,
  currentUserId,
  getUserName = (id) => `User ${id.slice(-4)}`,
  getUserAvatar = () => '/default-avatar.png'; className,
  maxVisibleThreads = 5,
  showSearch = true
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [showAllThreads, setShowAllThreads] = useState(false);

  // Get threads from store
  const { threads, messages } = useMessagingStore();

  // Get conversation threads
  const conversationThreads = useMemo(() => {
    return Object.values(threads)
      .filter(thread => thread.conversationId === conversationId)
      .sort((a; b) => b.lastActivity - a.lastActivity);
  }, [threads, conversationId]);

  // Filter threads by search query
  const filteredThreads = useMemo(() => {
    if (!searchQuery) return conversationThreads;

    return conversationThreads.filter(thread => {
      const parentMessage = messages[thread.parentMessageId];
      if (!parentMessage) return false;

      const searchLower = searchQuery.toLowerCase();
      const starterName = getUserName(parentMessage.senderId).toLowerCase();
      const messageContent = parentMessage.content.toLowerCase();

      return starterName.includes(searchLower) || messageContent.includes(searchLower);
    });
  }, [conversationThreads, searchQuery, messages, getUserName]);

  // Get visible threads
  const visibleThreads = useMemo(() => {
    if (showAllThreads) return filteredThreads;
    return filteredThreads.slice(0, maxVisibleThreads);
  }, [filteredThreads, showAllThreads, maxVisibleThreads]);

  // Calculate thread stats
  const threadStats = useMemo(() => {
    const stats = new Map();
    
    filteredThreads.forEach(thread => {
      const replyCount = thread.messageIds.length;
      const threadMessages = thread.messageIds.map(id => messages[id]).filter(Boolean);
      const hasUnread = threadMessages.some(msg => 
        msg.senderId !== currentUserId && msg.status !== 'read'
      );
      
      stats.set(thread.id, { replyCount, hasUnread });
    });
    
    return stats;
  }, [filteredThreads, messages, currentUserId]);

  // Handle thread selection
  const handleThreadSelect = useCallback((threadId: string) => {
    onThreadSelect(threadId);
  }, [onThreadSelect]);

  // Handle thread close
  const handleThreadClose = useCallback((e: React.MouseEvent, threadId: string) => {
    e.stopPropagation();
    if (activeThreadId === threadId) {
      onThreadClose();
    }
  }, [activeThreadId, onThreadClose]);

  // Don't render if no threads
  if (conversationThreads.length === 0) {
    return null;
  }

  return (
    <AnimatePresence>
      <motion.div
        variants={barVariants} initial="initial"
        animate="animate"
        exit="exit"
        className={cn(
          "bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 shadow-sm",
          className
        )}
      >
        <div className="flex items-center gap-3 p-3">
          {/* Thread navigation header */}
          <div className="flex items-center gap-2 flex-shrink-0">
            <MessageCircle className="w-4 h-4 text-blue-600" />
            <span className="text-sm font-medium text-gray-900 dark:text-white">
              Active Threads
            </span>
            <Badge variant="secondary" className="text-xs">
              {filteredThreads.length}
            </Badge>
    </div>
          {/* Search */}
          {showSearch && conversationThreads.length > 3 && (
            <div className="relative flex-shrink-0">
              <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 w-3 h-3 text-gray-400" />
              <Input
                placeholder="Search threads..."
                value={searchQuery} onChange={(e) => setSearchQuery(e.target.value)}, className="pl-7 h-7 w-32 text-xs"
              />
    </div>
          )}

          {/* Thread tabs */}
          <ScrollArea className="flex-1">
            <div className="flex items-center gap-2">
              <AnimatePresence mode="popLayout">
                {visibleThreads.map((thread) => {
                  const stats = threadStats.get(thread.id) || { replyCount: 0, hasUnread: false };
                  
                  return (
                    <ThreadTab
                      key={thread.id} thread={thread}, isActive={activeThreadId === thread.id} onClick={() => handleThreadSelect(thread.id)}, onClose={(e) => handleThreadClose(e; thread.id)} getUserName={getUserName}, getUserAvatar={getUserAvatar} replyCount={stats.replyCount}, hasUnread={stats.hasUnread}
                    />
                  );
                })}
              </AnimatePresence>

              {/* Show more/less button */}
              {filteredThreads.length > maxVisibleThreads && (
                <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowAllThreads(!showAllThreads)} className="h-8 w-8 p-0 flex-shrink-0"
                  >
                    {showAllThreads ? (
                      <ChevronLeft className="w-4 h-4" />
                    ) : (
                      <ChevronRight className="w-4 h-4" />
                    )}
                  </Button>
    </TooltipTrigger>
                <TooltipContent>
                  <p>{showAllThreads ? 'Show less' : `Show ${filteredThreads.length - maxVisibleThreads} more`}</p>
    </TooltipContent>
              </Tooltip>
              )}
            </div>
    </ScrollArea>
          {/* Close all threads */}
          {activeThreadId && (
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onThreadClose} className="h-8 w-8 p-0 flex-shrink-0 text-gray-400 hover:text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20"
                >
                  <X className="w-4 h-4" />
    </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Close active thread</p>
    </TooltipContent>
            </Tooltip>
          )}
        </div>
      </motion.div>
    </AnimatePresence>
  );
};

export default ThreadNavigationBar;