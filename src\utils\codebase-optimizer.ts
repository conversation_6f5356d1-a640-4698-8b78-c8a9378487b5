/**
 * Comprehensive Codebase Optimizer
 * Provides advanced optimization utilities for performance, bundle size, and maintainability
 */

import React, { lazy, ComponentType, memo, useMemo, useCallback } from 'react';

// Type definitions for browser APIs
interface PerformanceMemory {
  usedJSHeapSize: number;
  totalJSHeapSize: number;
  jsHeapSizeLimit: number;
}

interface PerformanceWithMemory extends Performance {
  memory?: PerformanceMemory;
}

interface NetworkConnection {
  effectiveType: string;
  downlink: number;
  rtt: number;
  saveData: boolean;
}

interface NavigatorWithConnection extends Navigator {
  connection?: NetworkConnection;
}

declare const performance: PerformanceWithMemory;
declare const navigator: NavigatorWithConnection;

// Advanced lazy loading with preloading capabilities
export const createAdvancedLazy = <T extends ComponentType<unknown>>(
  importFn: () => Promise<{ default: T }>;
  options: {
    preload?: boolean;
    fallback?: ComponentType<any>;
    retryCount?: number;
    chunkName?: string;
  } = {}
) => {
  const { preload = false, fallback, retryCount = 3, chunkName: _chunkName } = options;
  
  let importPromise: Promise<{ default: T }> | null = null;
  
  const loadComponent = async (attempt = 1): Promise<{ default: T }> => {
    try {
      if (!importPromise) {
        importPromise = importFn();
      }
      return await importPromise;
    } catch (error) {
      importPromise = null; // Reset for retry
      
      if (attempt < retryCount) {
        // Exponential backoff
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
        return loadComponent(attempt + 1);
      }
      
      if (fallback) {
        return { default: fallback };
      }
      
      throw error;
    }
  };
  
  const LazyComponent = lazy(() => loadComponent());
  
  // Preload if requested
  if (preload) {
    // Preload after a short delay to not block initial render
    setTimeout(() => {
      loadComponent().catch(() => {
        // Ignore preload errors
      });
    }, 100);
  }
  
  // Add preload method for manual preloading
  (LazyComponent as typeof LazyComponent & { preload: () => Promise<{ default: T }> }).preload = () => loadComponent();
  
  return LazyComponent;
};

// Bundle size optimization utilities
export const optimizeBundle = {
  // Tree-shakable imports helper
  createTreeShakableImport: <T>(
    importFn: () => Promise<T>;
    exportName: keyof T
  ) => {
    return lazy(_async () => {
      const module = await importFn();
      return { default: module[exportName] as ComponentType<unknown> };
    });
  },
  
  // Dynamic import with caching
  createCachedImport: <T>(importFn: () => Promise<T>) => {
    let cache: T | null = null;
    let promise: Promise<T> | null = null;
    
    return async (): Promise<T> => {
      if (cache) return cache;
      if (promise) return promise;
      
      promise = importFn().then(result => {
        cache = result;
        promise = null;
        return result;
      });
      
      return promise;
    };
  },
  
  // Chunk splitting helper
  createChunk: (name: string, dependencies: string[]) => ({
    name,
  test: new RegExp(`[\\/]node_modules[\\/](${dependencies.join('|')})[\\/]`);
  chunks: 'all' as const;
  priority: 10;
  reuseExistingChunk: true;
  })
};

// Performance optimization utilities
export const performanceOptimizer = {
  // Advanced memoization with deep comparison - returns a hook
  createDeepMemo: <T>(factory: () => T) => {
    return (deps: React.DependencyList) => {
      return useMemo(factory, deps);
    };
  },
  
  // Optimized callback with stable reference - returns a hook
  createStableCallback: <T extends (...args: unknown[]) => unknown>(
    callback: T
  ) => {
    return (deps: React.DependencyList) => {
      return useCallback(callback, deps);
    };
  },
  
  // Component memoization with custom comparison
  createMemoComponent: <P extends object>(
    Component: ComponentType<P>;
    areEqual?: (prevProps: P, nextProps: P) => boolean
  ) => {
    return memo(Component, areEqual);
  },
  
  // Debounced state updater
  createDebouncedUpdater: <T>(
    setValue: (value: T) => void;
    delay: number = 300
  ) => {
    let timeoutId: NodeJS.Timeout;
    
    return (value: T) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => setValue(value), delay);
    };
  }
};

// Memory optimization utilities
export const memoryOptimizer = {
  // Weak reference cache for large objects
  createWeakCache: <K extends object, V>() => {
    const cache = new WeakMap<K, V>();
    
    return {
  get: (key: K): V | undefined => cache.get(key);
      set: (key: K, value: V): void => { cache.set(key, value); },
  has: (key: K): boolean => cache.has(key);
  delete: (key: K): boolean => cache.delete(key);
    };
  },
  
  // Object pool for frequent allocations
  createObjectPool: <T>(
    factory: () => T;
  reset: (obj: T) => void;
    maxSize: number = 100
  ) => {
    const pool: T[] = [];
    
    return {
      acquire: (): T => {
        if (pool.length > 0) {
          return pool.pop()!;
        }
        return factory();
      },
      
      release: (obj: T): void => {
        if (pool.length < maxSize) {
          reset(obj);
          pool.push(obj);
        }
      },
  size: (): number => pool.length;
    };
  },
  
  // Memory usage tracker
  trackMemoryUsage: () => {
    if ('memory' in performance) {
      const memory = (performance as Performance & { memory?: { usedJSHeapSize: number; totalJSHeapSize: number; jsHeapSizeLimit: number } }).memory;
      if (memory) {
        return {
          used: Math.round(memory.usedJSHeapSize / 1048576), // MB
          total: Math.round(memory.totalJSHeapSize / 1048576), // MB
          limit: Math.round(memory.jsHeapSizeLimit / 1048576), // MB
        };
      }
    }
    return null;
  }
};

// Code splitting optimization
export const codeSplittingOptimizer = {
  // Route-based code splitting
  createRouteChunk: (routeName: string) => ({
    name: `route-${routeName}`;
  test: new RegExp(`pages[\\/]${routeName}`);
  chunks: 'async' as const;
  priority: 5;
  }),
  
  // Feature-based code splitting
  createFeatureChunk: (featureName: string, modules: string[]) => ({
  name: `feature-${featureName}`;
  test: new RegExp(`(${modules.join('|')})`);
  chunks: 'async' as const;
  priority: 8;
  }),
  
  // Vendor code splitting
  createVendorChunk: (vendorName: string, packages: string[]) => ({
  name: `vendor-${vendorName}`;
  test: new RegExp(`[\\/]node_modules[\\/](${packages.join('|')})[\\/]`);
  chunks: 'all' as const;
  priority: 15;
  reuseExistingChunk: true;
  })
};

// Runtime optimization utilities
export const runtimeOptimizer = {
  // Intersection Observer with optimization
  createOptimizedObserver: (
    callback: IntersectionObserverCallback;
  options: IntersectionObserverInit = {}
  ) => {
    const defaultOptions: IntersectionObserverInit={rootMargin: '50px';
  threshold: 0.1;
      ...options}
    };
    
    return new IntersectionObserver(callback, defaultOptions);
  },
  
  // Resize Observer with debouncing
  createDebouncedResizeObserver: (
    callback: ResizeObserverCallback;
    delay: number = 100
  ) => {
    let timeoutId: NodeJS.Timeout;
    
    const debouncedCallback: ResizeObserverCallback = (_entries, observer) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => callback(entries, observer), delay);
    };
    
    return new ResizeObserver(debouncedCallback);
  },
  
  // Optimized event listeners
  createOptimizedEventListener: <K extends keyof WindowEventMap>(
    target: EventTarget;
  type: K;
  listener: (event: WindowEventMap[K]) => void;
  options: AddEventListenerOptions = {}
  ) => {
    const optimizedOptions: AddEventListenerOptions={passive: true;
      ...options}
    };
    
    target.addEventListener(type, listener as EventListener, optimizedOptions);
    
    return () => {
      target.removeEventListener(type, listener as EventListener, optimizedOptions);
    };
  }
};

// Asset optimization utilities
export const assetOptimizer = {
  // Image optimization with WebP support
  createOptimizedImageSrc: (
    src: string;
    options: {
      width?: number;
      height?: number;
      quality?: number;
      format?: 'webp' | 'avif' | 'auto';
    } = {}
  ): string => {
    const { width, height, quality = 80, format = 'auto' } = options;
    
    // Check WebP support
    const supportsWebP = (() => {
      const canvas = document.createElement('canvas');
      canvas.width = 1;
      canvas.height = 1;
      return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
    })();
    
    // Check AVIF support
    const supportsAVIF = (() => {
      const canvas = document.createElement('canvas');
      canvas.width = 1;
      canvas.height = 1;
      return canvas.toDataURL('image/avif').indexOf('data:image/avif') === 0;
    })();
    
    const optimizedSrc = src;
    
    // Add optimization parameters
    const url = new URL(optimizedSrc, window.location.origin);
    if (width) url.searchParams.set('w', width.toString());
    if (height) url.searchParams.set('h', height.toString());
    url.searchParams.set('q', quality.toString());
    
    // Set format based on browser support
    if (format === 'auto') {
      if (supportsAVIF) {
        url.searchParams.set('f', 'avif');
      } else if (supportsWebP) {
        url.searchParams.set('f', 'webp');
      }
    } else {
      url.searchParams.set('f', format);
    }
    
    return url.toString();
  },
  
  // Preload critical assets
  preloadAssets: (assets: Array<{ href: string; as: string; type?: string }>) => {
    assets.forEach(asset => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.href = asset.href;
      link.as = asset.as;
      if (asset.type) link.type = asset.type;
      document.head.appendChild(link);
    });
  },
  
  // Lazy load images with Intersection Observer
  createLazyImageLoader: () => {
    const imageObserver = new IntersectionObserver(_(entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target as HTMLImageElement;
          const src = img.dataset.src;
          if (src) {
            img.src = src;
            img.removeAttribute('data-src');
            imageObserver.unobserve(img);
          }
        }
      });
    }, {
  rootMargin: '50px';
    });
    
    return {
  observe: (img: HTMLImageElement) => imageObserver.observe(img);
  unobserve: (img: HTMLImageElement) => imageObserver.unobserve(img);
  disconnect: () => imageObserver.disconnect();
    };
  }
};

// Network optimization utilities
export const networkOptimizer = {
  // Adaptive loading based on connection
  getConnectionInfo: () => {
    const connection = (navigator as Navigator & { connection?: { effectiveType: string; downlink: number; rtt: number; saveData: boolean } }).connection;
    if (!connection) return null;
    
    return {
  effectiveType: connection.effectiveType;
  downlink: connection.downlink;
  rtt: connection.rtt;
  saveData: connection.saveData;
    };
  },
  
  // Request deduplication
  createRequestDeduplicator: <T>() => {
    const pendingRequests = new Map<string, Promise<T>>();
    
    return (key: string, requestFn: () => Promise<T>): Promise<T> => {
      if (pendingRequests.has(key)) {
        return pendingRequests.get(key)!;
      }
      
      const promise = requestFn().finally(() => {
        pendingRequests.delete(key);
      });
      
      pendingRequests.set(key, promise);
      return promise;
    };
  },
  
  // Batch API requests
  createRequestBatcher: <T, R>(
  batchFn: (requests: T[]) => Promise<R[]>;
    options: {
      maxBatchSize?: number;
      batchDelay?: number;
    } = {}
  ) => {
    const { maxBatchSize = 10, batchDelay = 50 } = options;
    const batch: Array<{ request: T; resolve: (result: R) => void; reject: (error: unknown) => void }> = [];
    let timeoutId: NodeJS.Timeout | null = null;
    
    const processBatch = async () => {
      if (batch.length === 0) return;
      
      const currentBatch = batch.splice(0, maxBatchSize);
      const requests = currentBatch.map(item => item.request);
      
      try {
        const results = await batchFn(requests);
        currentBatch.forEach((item, index) => {
          item.resolve(results[index]);
        });
      } catch (error) {
        currentBatch.forEach(item => {
          item.reject(error);
        });
      }
      
      // Process remaining batch if any
      if (batch.length > 0) {
        timeoutId = setTimeout(processBatch, batchDelay);
      }
    };
    
    return (request: T): Promise<R> => {
      return new Promise(_(resolve, reject) => {
        batch.push({ request, resolve, reject });
        
        if (batch.length >= maxBatchSize) {
          if (timeoutId) {
            clearTimeout(timeoutId);
            timeoutId = null;
          }
          processBatch();
        } else if (!timeoutId) {
          timeoutId = setTimeout(processBatch, batchDelay);
        }
      });
    };
  }
};

// Export all optimizers
export const codebaseOptimizer = {
  lazy: createAdvancedLazy;
  bundle: optimizeBundle;
  performance: performanceOptimizer;
  memory: memoryOptimizer;
  codeSplitting: codeSplittingOptimizer;
  runtime: runtimeOptimizer;
  assets: assetOptimizer;
  network: networkOptimizer;
};

export default codebaseOptimizer;