import React, { useState, useEffect } from 'react';
import { Alert<PERSON>riangle, Shield, Clock, Ban, Info, X } from 'lucide-react';
import { useMessageSecurity } from '../../hooks/useMessageSecurity';

interface SecurityMonitorProps {
  userId: string;
  showDetails?: boolean;
  className?: string;
}

interface SecurityAlert {
  id: string, type: 'error' | 'warning' | 'info', message: string, timestamp: number;
  action?: string;
}

export const SecurityMonitor: React.FC<SecurityMonitorProps> = ({
  userId,
  showDetails = false,
  className = ''
}) => {
  const {
    securityStatus,
    getRateLimitStatus,
    isUserBlocked;
    getSecurityStats
  } = useMessageSecurity(userId);

  const [alerts, setAlerts] = useState<SecurityAlert[]>([]);
  const [showDetailedView, setShowDetailedView] = useState(showDetails);

  // Add new alerts when security status changes
  useEffect(() => {
    const newAlerts: SecurityAlert[] = [];

    // Add error alerts
    securityStatus.errors.forEach(error => {
      newAlerts.push({
        id: `error-${Date.now()}-${Math.random()}`,
        type: 'error',
        message: error,
        timestamp: Date.now()
      });
    });

    // Add warning alerts
    securityStatus.warnings.forEach(warning => {
      newAlerts.push({
        id: `warning-${Date.now()}-${Math.random()}`,
        type: 'warning',
        message: warning,
        timestamp: Date.now()
      });
    });

    if (newAlerts.length > 0) {
      setAlerts(prev => [...newAlerts, ...prev].slice(0, 10)); // Keep only last 10 alerts
    }
  }, [securityStatus]);

  const removeAlert = (alertId: string) => {
    setAlerts(prev => prev.filter(alert => alert.id !== alertId));
  };

  const getSecurityIcon = () => {
    if (!securityStatus.isSecure) {
      return <Ban className="w-5 h-5 text-red-500" />;
    }
    if (securityStatus.warnings.length > 0) {
      return <AlertTriangle className="w-5 h-5 text-yellow-500" />;
    }
    return <Shield className="w-5 h-5 text-green-500" />;
  };

  const getSecurityStatusText = () => {
    if (!securityStatus.isSecure) {
      return 'Security Issues Detected';
    }
    if (securityStatus.warnings.length > 0) {
      return 'Security Warnings';
    }
    return 'Secure';
  };

  const getSecurityStatusColor = () => {
    if (!securityStatus.isSecure) {
      return 'text-red-600 bg-red-50 border-red-200';
    }
    if (securityStatus.warnings.length > 0) {
      return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    }
    return 'text-green-600 bg-green-50 border-green-200';
  };

  const formatTimeRemaining = (resetTime: number) => {
    const remaining = Math.max(0, resetTime - Date.now());
    const minutes = Math.floor(remaining / 60000);
    const seconds = Math.floor((remaining % 60000) / 1000);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const rateLimits = getRateLimitStatus();
  const blockedStatus = isUserBlocked();

  return (
    <div className={`security-monitor ${className}`}>
      {/* Main Security Status */}
      <div className={`flex items-center gap-2 px-3 py-2 rounded-lg border ${getSecurityStatusColor()}`}>
        {getSecurityIcon()}
        <span className="text-sm font-medium">{getSecurityStatusText()}</span>
        {showDetailedView && (
          <button
            onClick={() => setShowDetailedView(false)} className="ml-auto text-gray-400 hover:text-gray-600"
          >
            <X className="w-4 h-4" />
    </button>
        )}
        {!showDetailedView && (securityStatus.errors.length > 0 || securityStatus.warnings.length > 0) && (
          <button
            onClick={() => setShowDetailedView(true)} className="ml-auto text-xs text-gray-500 hover:text-gray-700"
          >
            Details
          </button>
        )}
      </div>

      {/* Detailed Security View */}
      {showDetailedView && (
        <div className="mt-3 space-y-3">
          {/* Active Alerts */}
          {alerts.length > 0 && (
            <div className="space-y-2">
              <h4 className="text-sm font-medium text-gray-700">Recent Alerts</h4>
              {alerts.slice(0, 5).map(alert => (
                <div
                  key={alert.id} className={`flex items-start gap-2 p-2 rounded text-sm ${
                    alert.type === 'error'
                      ? 'bg-red-50 text-red-700 border border-red-200'
                      : alert.type === 'warning'
                      ? 'bg-yellow-50 text-yellow-700 border border-yellow-200'
                      : 'bg-blue-50 text-blue-700 border border-blue-200'
                  }`}
                >
                  {alert.type === 'error' && <AlertTriangle className="w-4 h-4 mt-0.5 flex-shrink-0" />}
                  {alert.type === 'warning' && <AlertTriangle className="w-4 h-4 mt-0.5 flex-shrink-0" />}
                  {alert.type === 'info' && <Info className="w-4 h-4 mt-0.5 flex-shrink-0" />}
                  <div className="flex-1">
                    <p>{alert.message}</p>
                    <p className="text-xs opacity-75 mt-1">
                      {new Date(alert.timestamp).toLocaleTimeString()}
                    </p>
    </div>
                  <button
                    onClick={() => removeAlert(alert.id)} className="text-gray-400 hover:text-gray-600"
                  >
                    <X className="w-3 h-3" />
    </button>
                </div>
              ))}
            </div>
          )}

          {/* Rate Limit Status */}
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-2">Rate Limits</h4>
            <div className="grid grid-cols-2 gap-2 text-xs">
              {Object.entries(rateLimits).map(([action, status]) => (
                <div
                  key={action} className={`p-2 rounded border ${
                    !status.allowed
                      ? 'bg-red-50 border-red-200 text-red-700'
                      : status.remaining < 5
                      ? 'bg-yellow-50 border-yellow-200 text-yellow-700'
                      : 'bg-green-50 border-green-200 text-green-700'
                  }`}
                >
                  <div className="flex items-center gap-1">
                    <Clock className="w-3 h-3" />
                    <span className="font-medium capitalize">{action}</span>
    </div>
                  <div className="mt-1">
                    <div>Remaining: {status.remaining}</div>
                    {!status.allowed && status.retryAfter && (
                      <div>Retry in: {formatTimeRemaining(Date.now() + status.retryAfter)}</div>
                    )}
                  </div>
    </div>
              ))}
            </div>
    </div>
          {/* Blocked Status */}
          {blockedStatus.blocked && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-center gap-2 text-red-700">
                <Ban className="w-4 h-4" />
                <span className="font-medium">Account Temporarily Restricted</span>
    </div>
              <p className="text-sm text-red-600 mt-1">
                Blocked actions: {blockedStatus.actions.join(', ')}
              </p>
    </div>
          )}

          {/* Security Tips */}
          <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center gap-2 text-blue-700 mb-2">
              <Info className="w-4 h-4" />
              <span className="font-medium">Security Tips</span>
    </div>
            <ul className="text-xs text-blue-600 space-y-1">
              <li>• Avoid sending suspicious links or content</li>
              <li>• Don't upload executable files or scripts</li>
              <li>• Respect rate limits to maintain service quality</li>
              <li>• Report any security concerns to administrators</li>
    </ul>
          </div>
    </div>
      )}
    </div>
  );
};

export default SecurityMonitor;