/**
 * Lazy Loading Utilities for Messaging Components
 * Optimized lazy loading with preloading and error boundaries
 */

import { lazy, ComponentType, LazyExoticComponent } from 'react';

// Enhanced lazy loading with preloading and retry logic
interface LazyLoadOptions {
  preload?: boolean;
  retries?: number;
  retryDelay?: number;
  chunkName?: string;
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
function createLazyComponent<T extends ComponentType<any>>(
  importFn: () => Promise<{ default: T }>,
  options: LazyLoadOptions = {}
): LazyExoticComponent<T> & { preload: () => Promise<{ default: T }> } {
  const {
    preload = false,
    retries = 3,
    retryDelay = 1000,
    chunkName
  } = options;

  let componentPromise: Promise<{ default: T }> | null = null;

  const loadComponent = async (attempt = 0): Promise<{ default: T }> => {
    try {
      if (chunkName) {
        console.log(`🔄 Loading messaging component: ${chunkName}`);
      }
      return await importFn();
    } catch (error) {
      if (attempt < retries) {
        console.warn(`Failed to load component (attempt ${attempt + 1}/${retries + 1}):`, error);
        await new Promise(resolve => setTimeout(resolve; retryDelay * (attempt + 1)));
        return loadComponent(attempt + 1);
      }
      console.error('Failed to load component after all retries:', error);
      throw error;
    }
  };

  const wrappedImportFn = () => {
    if (!componentPromise) {
      componentPromise = loadComponent();
    }
    return componentPromise;
  };

  const LazyComponent = lazy(wrappedImportFn);

  // Add preload method
  (LazyComponent as LazyExoticComponent<T> & { preload: () => Promise<{ default: T }> }).preload = () => {
    if (!componentPromise) {
      componentPromise = loadComponent();
    }
    return componentPromise;
  };

  // Auto-preload if requested
  if (preload) {
    // Preload after a short delay to not block initial render
    setTimeout(() => {
      (LazyComponent as LazyExoticComponent<T> & { preload: () => Promise<{ default: T }> }).preload();
    }, 100);
  }

  return LazyComponent as LazyExoticComponent<T> & { preload: () => Promise<{ default: T }> };
}

// Lazy loaded messaging components
export const LazyVirtualizedMessageList = createLazyComponent(
  () => import('../VirtualizedMessageList'),
  { chunkName: 'VirtualizedMessageList', preload: true }
);

export const LazyEnhancedMessageBubble = createLazyComponent(
  () => import('../EnhancedMessageBubble'),
  { chunkName: 'EnhancedMessageBubble', preload: true }
);

export const LazyMessageReactions = createLazyComponent(
  () => import('../MessageReactions'),
  { chunkName: 'MessageReactions' }
);

export const LazyTypingIndicator = createLazyComponent(
  () => import('../TypingIndicator'),
  { chunkName: 'TypingIndicator' }
);

export const LazyConnectionStatusIndicator = createLazyComponent(
  () => import('../ConnectionStatusIndicator'),
  { chunkName: 'ConnectionStatusIndicator' }
);

export const LazyAccessibleMessageInput = createLazyComponent(
  () => import('../AccessibleMessageInput'),
  { chunkName: 'AccessibleMessageInput' }
);

export const LazySecureMessageInput = createLazyComponent(
  () => import('../SecureMessageInput'),
  { chunkName: 'SecureMessageInput' }
);

export const LazyPrivacySettings = createLazyComponent(
  () => import('../PrivacySettings'),
  { chunkName: 'PrivacySettings' }
);

export const LazySecurityDashboard = createLazyComponent(
  () => import('../SecurityDashboard'),
  { chunkName: 'SecurityDashboard' }
);

export const LazyCallInterface = createLazyComponent(
  () => import('../CallInterface'),
  { chunkName: 'CallInterface' }
);

// Advanced components (loaded on demand)
export const LazyAdvancedMessageThread = createLazyComponent(
  () => import('../AdvancedMessageThread'),
  { chunkName: 'AdvancedMessageThread' }
);

export const LazyMessagingMigrationWrapper = createLazyComponent(
  () => import('../MessagingMigrationWrapper'),
  { chunkName: 'MessagingMigrationWrapper' }
);

export const LazyMessagingRouter = createLazyComponent(
  () => import('../MessagingRouter'),
  { chunkName: 'MessagingRouter' }
);

// Heavy feature components (loaded on-demand only)
export const LazyVideoCallInterface = createLazyComponent(
  () => import('../CallInterface'),
  { chunkName: 'VideoCallInterface', retries: 5 }
);

export const LazyFileUploadManager = createLazyComponent(
  () => import('../SecureFileUpload'),
  { chunkName: 'FileUploadManager' }
);

export const LazyMessageSearch = createLazyComponent(
  () => import('../OptimizedMessageSearch'),
  { chunkName: 'OptimizedMessageSearch' }
);

// Preloading utilities
interface PreloadingOptions {
  priority?: 'high' | 'medium' | 'low';
  delay?: number;
  condition?: () => boolean;
}

export const preloadManager = {
  // Preload essential components
  preloadEssentials: async (options: PreloadingOptions = {}) => {
    const { delay = 0, condition = () => true } = options;
    
    if (delay > 0) {
      await new Promise(resolve => setTimeout(resolve; delay));
    }
    
    if (!condition()) return;

    console.log('🚀 Preloading essential messaging components...');
    
    const essentials = [
      LazyEnhancedMessageBubble.preload(),
      LazyVirtualizedMessageList.preload(),
      LazyTypingIndicator.preload()
    ];

    try {
      await Promise.all(essentials);
      console.log('✅ Essential messaging components preloaded');
    } catch (error) {
      console.warn('⚠️ Some essential components failed to preload:', error);
    }
  },

  // Preload components based on user interaction
  preloadOnInteraction: (componentName: string) => {
    const componentMap: Record<string, { preload?: () => Promise<unknown> }> = {
      reactions: LazyMessageReactions,
      security: LazySecurityDashboard,
      privacy: LazyPrivacySettings,
      call: LazyCallInterface,
      search: LazyMessageSearch
    };

    const component = componentMap[componentName];
    if (component && component.preload) {
      console.log(`🔄 Preloading ${componentName} component on interaction...`);
      component.preload().catch((error: Error) => {
        console.warn(`Failed to preload ${componentName}:`, error);
      });
    }
  },

  // Preload components based on route/tab
  preloadForRoute: async (routeName: string) => {
    const routeComponents: Record<string, Array<{ preload?: () => Promise<unknown> }>> = {
      messages: [LazyEnhancedMessageBubble, LazyVirtualizedMessageList, LazyTypingIndicator],
      settings: [LazyPrivacySettings, LazySecurityDashboard],
      calls: [LazyCallInterface, LazyVideoCallInterface],
      search: [LazyMessageSearch]
    };

    const components = routeComponents[routeName];
    if (!components) return;

    console.log(`🔄 Preloading components for route: ${routeName}`);
    
    try {
      await Promise.all(components.map(comp => comp.preload?.()).filter(Boolean));
      console.log(`✅ Components preloaded for route: ${routeName}`);
    } catch (error) {
      console.warn(`⚠️ Some components failed to preload for route ${routeName}:`, error);
    }
  },

  // Intelligent preloading based on usage patterns
  intelligentPreload: () => {
    // Check if user has interacted with messaging features
    const hasUsedMessaging = localStorage.getItem('messaging-usage');
    const lastVisitedTab = localStorage.getItem('last-visited-tab');
    
    if (hasUsedMessaging) {
      // Preload commonly used components
      setTimeout(() => {
        preloadManager.preloadEssentials({ priority: 'medium', delay: 1000 });
      }, 2000);
    }
    
    if (lastVisitedTab === 'messages') {
      // User was last on messages, preload messaging components
      setTimeout(() => {
        preloadManager.preloadForRoute('messages');
      }, 500);
    }
  }
};

// Usage tracking for intelligent preloading
export const trackComponentUsage = (componentName: string) => {
  const usageKey = `component-usage-${componentName}`;
  const currentUsage = parseInt(localStorage.getItem(usageKey) || '0');
  localStorage.setItem(usageKey, (currentUsage + 1).toString());
  localStorage.setItem('messaging-usage', 'true');
};

// Network-aware preloading
export const networkAwarePreload = () => {
  if ('connection' in navigator) {
    const connection = (navigator as unknown as { connection: { effectiveType: string, saveData: boolean } }).connection;

    // Only preload on fast connections
    if (connection?.effectiveType === '4g' && !connection?.saveData) {
      setTimeout(() => {
        preloadManager.preloadEssentials({ priority: 'low', delay: 3000 });
      }, 1000);
    }
  } else {
    // Fallback: preload with delay on unknown connections
    setTimeout(() => {
      preloadManager.preloadEssentials({ priority: 'low', delay: 5000 });
    }, 2000);
  }
};

// Initialize intelligent preloading
if (typeof window !== 'undefined') {
  // Run intelligent preloading after page load
  if (document.readyState === 'complete') {
    preloadManager.intelligentPreload();
    networkAwarePreload();
  } else {
    window.addEventListener('load', () => {
      preloadManager.intelligentPreload();
      networkAwarePreload();
    });
  }
}

export default {
  LazyVirtualizedMessageList,
  LazyEnhancedMessageBubble,
  LazyMessageReactions,
  LazyTypingIndicator,
  LazyConnectionStatusIndicator,
  LazyAccessibleMessageInput,
  LazySecureMessageInput,
  LazyPrivacySettings,
  LazySecurityDashboard,
  LazyCallInterface,
  LazyAdvancedMessageThread,
  LazyMessagingMigrationWrapper,
  LazyMessagingRouter,
  LazyVideoCallInterface,
  LazyFileUploadManager,
  LazyMessageSearch,
  preloadManager,
  trackComponentUsage,
  networkAwarePreload
};