import React, { useState, useEffect, useCallback, Suspense } from 'react';
import {
  Play,
  Search,
  TrendingUp,
  Users,
  Radio,
  Star,
  Grid,
  List,
  Settings,
  Bookmark,
  History,
  ThumbsUp,
  Eye
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { motion } from 'framer-motion';
import VideoRecommendationService, { Video, UserPreferences } from '@/services/VideoRecommendationService';

// Lazy load the enhanced video player
const EnhancedVideoPlayer = React.lazy(() => import('@/components/EnhancedVideoPlayer'));

const FacebookWatch: React.FC = () => {
  const [selectedVideo, setSelectedVideo] = useState<Video | null>(null);
  const [showPlayer, setShowPlayer] = useState(false);
  const [activeTab, setActiveTab] = useState('for-you');
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showSettings, setShowSettings] = useState(false);
  
  // Video data
  const [forYouVideos, setForYouVideos] = useState<Video[]>([]);
  const [trendingVideos, setTrendingVideos] = useState<Video[]>([]);
  const [liveVideos, setLiveVideos] = useState<Video[]>([]);
  const [watchPartyVideos, setWatchPartyVideos] = useState<Video[]>([]);
  const [searchResults, setSearchResults] = useState<Video[]>([]);
  const [watchHistory, setWatchHistory] = useState<Video[]>([]);
  const [savedVideos] = useState<Video[]>([]);
  
  const [userPreferences, setUserPreferences] = useState<UserPreferences | null>(null);
  
  const videoService = VideoRecommendationService.getInstance();

  const loadVideos = useCallback(() => {
    setForYouVideos(videoService.getRecommendations());
    setTrendingVideos(videoService.getTrendingVideos());
    setLiveVideos(videoService.getLiveVideos());
    setWatchPartyVideos(videoService.getWatchPartyVideos());
    
    // Load watch history (mock implementation)
    const history = videoService.getWatchHistory();
    const historyVideos = history
      .map(h => videoService.getVideo(h.videoId))
      .filter(Boolean) as Video[];
    setWatchHistory(historyVideos);
  }, [videoService]);

  const loadUserPreferences = useCallback(() => {
    const prefs = videoService.getPreferences();
    setUserPreferences(prefs);
  }, [videoService]);

  // Load data on component mount
  useEffect(() => {
    loadVideos();
    loadUserPreferences();
  }, [loadVideos, loadUserPreferences]);

  // Search functionality
  useEffect(() => {
    if (searchQuery.trim()) {
      const results = videoService.searchVideos(searchQuery);
      setSearchResults(results);
    } else {
      setSearchResults([]);
    }
  }, [searchQuery, videoService]);

  const handleVideoClick = useCallback((video: Video) => {
    setSelectedVideo(video);
    setShowPlayer(true);
  }, []);

  const handleVideoEnd = useCallback(() => {
    // Auto-play next recommended video
    if (selectedVideo) {
      const relatedVideos = videoService.getRelatedVideos(selectedVideo.id);
      if (relatedVideos.length > 0) {
        setSelectedVideo(relatedVideos[0]);
      }
    }
  }, [selectedVideo, videoService]);

  const formatDuration = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  const formatViews = (views: number): string => {
    if (views >= 1000000) {
      return `${(views / 1000000).toFixed(1)}M`;
    } else if (views >= 1000) {
      return `${(views / 1000).toFixed(1)}K`;
    }
    return views.toString();
  };

  const VideoCard: React.FC<{ video: Video; size?: 'small' | 'medium' | 'large' }> = ({ 
    video, 
    size = 'medium' 
  }) => {
    const cardSizes = {
      small: 'w-48',
      medium: 'w-64',
      large: 'w-80'
    };

    return (
      <motion.div
        whileHover={{ scale: 1.02 }}, whileTap={{ scale: 0.98 }}, className={`${cardSizes[size]}, cursor-pointer`}, onClick={() => handleVideoClick(video)}
      >
        <Card className="overflow-hidden">
          <div className="relative">
            <img
              src={video.thumbnail} alt={video.title}, className="w-full h-36 object-cover"
            />
            
            {/* Duration Badge */}
            <Badge className="absolute bottom-2 right-2 bg-black/80 text-white text-xs">
              {video.isLive ? 'LIVE' : formatDuration(video.duration)}
            </Badge>

            {/* Watch Party Badge */}
            {video.isWatchParty && (
              <Badge className="absolute top-2 right-2 bg-purple-500 text-white text-xs">
                <Users className="w-3 h-3 mr-1" />
                Party
              </Badge>
            )}

            {/* Play Button Overlay */}
            <div className="absolute inset-0 bg-black/0 hover:bg-black/20 transition-colors flex items-center justify-center opacity-0 hover:opacity-100">
              <div className="w-12 h-12 bg-white/90 rounded-full flex items-center justify-center">
                <Play className="w-6 h-6 text-black ml-1" />
    </div>
            </div>
    </div>
          <CardContent className="p-3">
            <h3 className="font-semibold text-sm line-clamp-2 mb-2">
              {video.title}
            </h3>
            
            <div className="flex items-center space-x-2 mb-2">
              <Avatar className="w-6 h-6">
                <AvatarImage src={video.creator.avatar} />
                <AvatarFallback className="text-xs">
                  {video.creator.name[0]}
                </AvatarFallback>
    </Avatar>
              <span className="text-sm text-gray-600 dark:text-gray-400 truncate">
                {video.creator.name}
              </span>
              {video.creator.verified && (
                <Badge variant="secondary" className="text-xs">✓</Badge>
              )}
            </div>

            <div className="flex items-center justify-between text-xs text-gray-500">
              <div className="flex items-center space-x-3">
                <span className="flex items-center">
                  <Eye className="w-3 h-3 mr-1" />
                  {formatViews(video.views)}
                </span>
                <span className="flex items-center">
                  <ThumbsUp className="w-3 h-3 mr-1" />
                  {formatViews(video.likes)}
                </span>
    </div>
              <span>{video.uploadDate.toLocaleDateString()}</span>
    </div>
          </CardContent>
    </Card>
      </motion.div>
    );
  };

  const VideoList: React.FC<{ videos: Video[], title: string }> = ({ videos, title }) => (
    <div className="mb-8">
      <h2 className="text-xl font-bold mb-4">{title}</h2>
      <div className={
        viewMode === 'grid' 
          ? "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4"
          : "space-y-4"
      }>
        {videos.map((video) => (
          <VideoCard key={video.id} video={video} />
        ))}
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 p-4">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <h1 className="text-2xl font-bold">Watch</h1>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search videos..."
                value={searchQuery} onChange={(e) => setSearchQuery(e.target.value)}, className="pl-10 w-64"
              />
    </div>
          </div>

          <div className="flex items-center space-x-2">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'outline'} size="sm"
              onClick={() => setViewMode('grid')}
            >
              <Grid className="w-4 h-4" />
    </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'outline'} size="sm"
              onClick={() => setViewMode('list')}
            >
              <List className="w-4 h-4" />
    </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowSettings(true)}
            >
              <Settings className="w-4 h-4" />
    </Button>
          </div>
    </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto p-4">
        {searchQuery ? (
          // Search Results
          <VideoList videos={searchResults} title={`Search results for "${searchQuery}"`} />
        ) : (
          // Main Content Tabs
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="mb-6">
              <TabsTrigger value="for-you" className="flex items-center space-x-2">
                <Star className="w-4 h-4" />
                <span>For You</span>
    </TabsTrigger>
              <TabsTrigger value="trending" className="flex items-center space-x-2">
                <TrendingUp className="w-4 h-4" />
                <span>Trending</span>
    </TabsTrigger>
              <TabsTrigger value="live" className="flex items-center space-x-2">
                <Radio className="w-4 h-4" />
                <span>Live</span>
    </TabsTrigger>
              <TabsTrigger value="watch-party" className="flex items-center space-x-2">
                <Users className="w-4 h-4" />
                <span>Watch Party</span>
    </TabsTrigger>
              <TabsTrigger value="history" className="flex items-center space-x-2">
                <History className="w-4 h-4" />
                <span>History</span>
    </TabsTrigger>
              <TabsTrigger value="saved" className="flex items-center space-x-2">
                <Bookmark className="w-4 h-4" />
                <span>Saved</span>
    </TabsTrigger>
            </TabsList>

            <TabsContent value="for-you">
              <VideoList videos={forYouVideos} title="Recommended for You" />
    </TabsContent>
            <TabsContent value="trending">
              <VideoList videos={trendingVideos} title="Trending Now" />
    </TabsContent>
            <TabsContent value="live">
              <VideoList videos={liveVideos} title="Live Videos" />
    </TabsContent>
            <TabsContent value="watch-party">
              <VideoList videos={watchPartyVideos} title="Watch Parties" />
    </TabsContent>
            <TabsContent value="history">
              <VideoList videos={watchHistory} title="Watch History" />
    </TabsContent>
            <TabsContent value="saved">
              <VideoList videos={savedVideos} title="Saved Videos" />
    </TabsContent>
          </Tabs>
        )}
      </div>

      {/* Video Player Dialog */}
      <Dialog open={showPlayer} onOpenChange={setShowPlayer}>
        <DialogContent className="max-w-6xl h-[90vh] p-0">
          {selectedVideo && (
            <div className="h-full flex flex-col">
              <Suspense fallback={
                <div className="flex-1 bg-gray-900 rounded-lg flex items-center justify-center">
                  <div className="text-white text-center">
                    <div className="w-12 h-12 border-4 border-t-white border-gray-600 rounded-full animate-spin mx-auto mb-4"></div>
                    <div>Loading video player...</div>
    </div>
                </div>
              }>
                <EnhancedVideoPlayer
                  video={selectedVideo} autoplay={true}, onVideoEnd={handleVideoEnd} className="flex-1"
                />
    </Suspense>
            </div>
          )}
        </DialogContent>
    </Dialog>
      {/* Settings Dialog */}
      <Dialog open={showSettings} onOpenChange={setShowSettings}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Watch Settings</DialogTitle>
    </DialogHeader>
          {userPreferences && (
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium">Autoplay</label>
                <div className="flex items-center space-x-2 mt-1">
                  <input
                    type="checkbox"
                    checked={userPreferences.autoplay} onChange={(e) => {
                      const newPrefs = { ...userPreferences, autoplay: e.target.checked };
                      setUserPreferences(newPrefs);
                      videoService.updatePreferences(newPrefs);
                    }}
                  />
                  <span className="text-sm">Automatically play next video</span>
    </div>
              </div>

              <div>
                <label className="text-sm font-medium">Quality Preference</label>
                <select
                  value={userPreferences.qualityPreference} onChange={(e) => {
                    const newPrefs = { ...userPreferences, qualityPreference: e.target.value };
                    setUserPreferences(newPrefs);
                    videoService.updatePreferences(newPrefs);
                  }}, className="w-full mt-1 p-2 border rounded"
                >
                  <option value="360p">360p</option>
                  <option value="480p">480p</option>
                  <option value="720p">720p</option>
                  <option value="1080p">1080p</option>
                  <option value="4K">4K</option>
    </select>
              </div>

              <div>
                <label className="text-sm font-medium">Subtitles</label>
                <div className="flex items-center space-x-2 mt-1">
                  <input
                    type="checkbox"
                    checked={userPreferences.subtitles} onChange={(e) => {
                      const newPrefs = { ...userPreferences, subtitles: e.target.checked };
                      setUserPreferences(newPrefs);
                      videoService.updatePreferences(newPrefs);
                    }}
                  />
                  <span className="text-sm">Show subtitles by default</span>
    </div>
              </div>

              <div>
                <label className="text-sm font-medium">Notifications</label>
                <div className="flex items-center space-x-2 mt-1">
                  <input
                    type="checkbox"
                    checked={userPreferences.notifications} onChange={(e) => {
                      const newPrefs = { ...userPreferences, notifications: e.target.checked };
                      setUserPreferences(newPrefs);
                      videoService.updatePreferences(newPrefs);
                    }}
                  />
                  <span className="text-sm">Receive video recommendations</span>
    </div>
              </div>
    </div>
          )}
        </DialogContent>
    </Dialog>
    </div>
  );
};

export default FacebookWatch;
