/**
 * Production Monitoring for React Errors and Performance
 * 
 * This module provides automated monitoring for production environments
 * to catch React errors and performance issues before they impact users.
 */
import React from 'react';

interface ErrorReport {
  id: string, timestamp: number, type: 'react-error' | 'react-warning' | 'performance' | 'memory', message: string;
  stack?: string;
  component?: string;
  props?: Record<string, unknown>;
  userAgent: string, url: string;
  userId?: string;
}

interface PerformanceMetrics {
  componentRenderTime: number, memoryUsage: number, bundleSize: number, networkRequests: number;
}

class ProductionMonitor {
  private errorQueue: ErrorReport[] = [];
  private performanceMetrics: PerformanceMetrics[] = [];
  private isEnabled: boolean = false;
  private reportingEndpoint: string = '';
  private maxQueueSize: number = 100;
  private flushInterval: number = 30000; // 30 seconds
  private intervalId: NodeJS.Timeout | null = null;

  constructor() {
    // Only enable in production
    this.isEnabled = process.env.NODE_ENV === 'production';
    
    if (this.isEnabled) {
      this.initialize();
    }
  }

  /**
   * Initialize monitoring systems
   */
  private initialize(): void {
    this.setupReactErrorCapture();
    this.setupPerformanceMonitoring();
    this.setupMemoryMonitoring();
    this.startReportingInterval();
    
    console.log('✅ Production monitoring initialized');
  }

  /**
   * Configure monitoring settings
   */
  public configure(options: { reportingEndpoint?: string;
    maxQueueSize?: number;
    flushInterval?: number; }): void {
    if (options.reportingEndpoint) {
      this.reportingEndpoint = options.reportingEndpoint;
    }
    if (options.maxQueueSize) {
      this.maxQueueSize = options.maxQueueSize;
    }
    if (options.flushInterval) {
      this.flushInterval = options.flushInterval;
      this.restartReportingInterval();
    }
  }

  /**
   * Capture React-specific errors and warnings
   */
  private setupReactErrorCapture(): void {
    // Capture console errors that mention React
    const originalError = console.error;
    console.error = (...args: unknown[]) => {
  const message = args.join(' ');
      
      if (this.isReactError(message)) {
        this.reportError({
  type: 'react-error';
          message
}
          stack: new Error().stack
        });
      }
      
      originalError.apply(console, args);
    };

    // Capture console warnings that mention React
    const originalWarn = console.warn;
    console.warn = (...args: unknown[]) => {
  const message = args.join(' ');
      
      if (this.isReactWarning(message)) {
        this.reportError({
  type: 'react-warning';
          message
}
          stack: new Error().stack
        });
      }
      
      originalWarn.apply(console, args);
    };

    // Capture unhandled React errors
    window.addEventListener('error',_(event) => {
      if (this.isReactError(event.message)) {
        this.reportError({
  type: 'react-error', message: event.message, stack: event?.error?.stack
        });
      }
    });
  }

  /**
   * Set up performance monitoring
   */
  private setupPerformanceMonitoring(): void {
    // Monitor component render performance using React Profiler
    if (typeof window !== 'undefined' && window.React) {
      // This would be integrated with React DevTools Profiler API
      // For now, we'll monitor general performance metrics
    }

    // Monitor bundle loading performance
    if ('performance' in window) {
      window.addEventListener('load',() => {
        setTimeout(() => {
          const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
          const loadTime = navigation.loadEventEnd - navigation.fetchStart;
          
          if (loadTime > 3000) { // > 3 seconds
            this.reportError({
  type: 'performance', message: `Slow page load: ${loadTime}ms`
            });
          }
        }, 1000);
      });
    }
  }

  /**
   * Set up memory monitoring
   */
  private setupMemoryMonitoring(): void {
    if ('memory' in performance) {
      setInterval(() => {
        const memory = (performance as Performance & { memory?: { usedJSHeapSize: number, totalJSHeapSize: number } }).memory;
        if (memory) {
          const usedMB = Math.round(memory.usedJSHeapSize / 1048576);
          const totalMB = Math.round(memory.totalJSHeapSize / 1048576);
          
          // Alert if memory usage is concerning
          if (usedMB > 100 || (usedMB / totalMB) > 0.9) {
            this.reportError({
  type: 'memory', message: `High memory usage: ${usedMB}MB / ${totalMB}MB`
            });
          }
        }
      }, 60000); // Check every minute
    }
  }

  /**
   * Check if error message indicates React issue
   */
  private isReactError(message: string): boolean {
    const reactKeywords = ['Warning: React',
      'Error: React',
      'hooks',
      'useState',
      'useEffect',
      'setState',
      'component',
      'render']
      'React DevTools'
    ];
    
    return reactKeywords.some(keyword => 
      message.toLowerCase().includes(keyword.toLowerCase())
    );
  }

  /**
   * Check if warning message indicates React issue
   */
  private isReactWarning(message: string): boolean {
    const warningKeywords = ['Warning:',
      'React has detected',
      'Cannot update',
      'Invalid hook',
      'Memory leak']
      'Deprecated'
    ];
    
    return warningKeywords.some(keyword => 
      message.includes(keyword)
    );
  }

  /**
   * Report an error to the monitoring system
   */
  private reportError(error: Partial<ErrorReport>): void {
    if (!this.isEnabled) return;

    const errorReport: ErrorReport = {
  id: this.generateId(), timestamp: Date.now(), type: error.type || 'react-error', message: error.message || 'Unknown error', stack: error.stack, component: error.component, props: error.props, userAgent: navigator.userAgent, url: window.location.href, userId: this.getCurrentUserId()
    };

    this.errorQueue.push(errorReport);
    
    // Trim queue if too large
    if (this.errorQueue.length > this.maxQueueSize) {
      this.errorQueue = this.errorQueue.slice(-this.maxQueueSize);
    }

    // Immediate flush for critical errors
    if (error.type === 'react-error') {
      this.flushErrors();
    }
  }

  /**
   * Start periodic reporting
   */
  private startReportingInterval(): void {
    this.intervalId = setInterval(() => {
      this.flushErrors();
    }, this.flushInterval);
  }

  /**
   * Restart reporting interval with new settings
   */
  private restartReportingInterval(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
    }
    this.startReportingInterval();
  }

  /**
   * Flush errors to reporting endpoint
   */
  private async flushErrors(): Promise<void> {
    if (this.errorQueue.length === 0 || !this.reportingEndpoint) {
      return;
    }

    const errorsToSend = [...this.errorQueue];
    this.errorQueue = [];

    try {
      await fetch(this.reportingEndpoint, {
  method: 'POST', headers: { 'Content-Type': 'application/json' }
        };
  body: JSON.stringify({
          errors: errorsToSend, metadata: { timestamp: Date.now(), userAgent: navigator.userAgent }
            url: window.location.href
          }
        })
      });
    } catch (error) {
      // Silently fail - don't want monitoring to break the app
      console.warn('Failed to send error reports:', error);
      // Add errors back to queue for retry
      this.errorQueue.unshift(...errorsToSend);
    }
  }

  /**
   * Generate unique ID for error reports
   */
  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  /**
   * Get current user ID (implement based on your auth system)
   */
  private getCurrentUserId(): string | undefined {
    // Implement based on your authentication system
    try {
      const userStr = localStorage.getItem('user');
      if (userStr) {
        const user = JSON.parse(userStr);
        return user.id || user.userId;
      }
    } catch {
      // Ignore errors
    }
    return undefined;
  }

  /**
   * Manually report a React component error
   */
  public reportComponentError(
  component: string, error: Error;
    props?: Record<string, unknown>
  ): void {
    this.reportError({
  type: 'react-error', message: `Component error in ${component}: ${error.message}`;
  stack: error.stack;
      component,
      props
    });
  }

  /**
   * Report performance metrics
   */
  public reportPerformanceMetric(metric: Partial<PerformanceMetrics>): void {
    if (!this.isEnabled) return;

    const fullMetric: PerformanceMetrics = {
  componentRenderTime: metric.componentRenderTime || 0, memoryUsage: metric.memoryUsage || 0, bundleSize: metric.bundleSize || 0, networkRequests: metric.networkRequests || 0
    };

    this.performanceMetrics.push(fullMetric);
  }

  /**
   * Get current error queue (for debugging)
   */
  public getErrorQueue(): ErrorReport[] {
    return [...this.errorQueue];
  }

  /**
   * Clear error queue
   */
  public clearErrorQueue(): void {
    this.errorQueue = [];
  }

  /**
   * Disable monitoring
   */
  public disable(): void {
    this.isEnabled = false;
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
  }

  /**
   * Force flush all pending errors
   */
  public async flush(): Promise<void> {
    await this.flushErrors();
  }
}

// Global instance
export const productionMonitor = new ProductionMonitor();

/**
 * HOC to wrap components with error monitoring
 */
export function withErrorMonitoring<P extends object>(
  Component: React.ComponentType<P>;
  _componentName?: string
) {
  const WrappedComponent = (props: P) => {
    try {
      return React.createElement(Component, props);
    } catch (error) {
      productionMonitor.reportComponentError(
        _componentName || Component.name || 'Unknown',
        error as Error,
        props as Record<string, unknown>
      );
      throw error; // Re-throw to trigger error boundary
    }
  };

  WrappedComponent.displayName = `withErrorMonitoring(${_componentName || Component.displayName || Component.name})`;
  return WrappedComponent;
}

/**
 * Hook for reporting performance metrics
 */
export function usePerformanceMonitoring(componentName: string) {
  const startTime = React.useRef<number>(Date.now());
  
  React.useEffect(() => {
    const renderTime = Date.now() - startTime.current;

    if (renderTime > 16) { // > 1 frame at 60fps
      productionMonitor.reportPerformanceMetric({
  componentRenderTime: renderTime, memoryUsage: 0, bundleSize: 0, networkRequests: 0
      });
    }
  }, [componentName]);

  const reportMetric = React.useCallback((metric: Partial<PerformanceMetrics>) => {
    productionMonitor.reportPerformanceMetric(metric);
  }, []);

  return { reportMetric };
}

// Initialize monitoring on import
if (typeof window !== 'undefined') {
  // Configure with your actual reporting endpoint
  productionMonitor.configure({
  reportingEndpoint: process.env.REACT_APP_ERROR_REPORTING_ENDPOINT || '', maxQueueSize: 50, flushInterval: 30000
  });
}

export default productionMonitor;