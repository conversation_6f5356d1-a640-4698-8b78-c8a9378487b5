import React, { useState, useRef, useCallback, useEffect } from 'react';
import { Send, Paperclip, AlertTriangle, Shield, X, Upload } from 'lucide-react';
import { useMessageSecurity } from '../../hooks/useMessageSecurity';

interface SecureMessageInputProps {
  userId: string, onSendMessage: (content: string, files?: File[]) => Promise<void>;
  placeholder?: string;
  maxLength?: number;
  allowFileUpload?: boolean;
  allowFormatting?: boolean;
  allowLinks?: boolean;
  className?: string;
  disabled?: boolean;
}

interface ValidationError {
  type: 'message' | 'file', message: string;
  field?: string;
}

export const SecureMessageInput: React.FC<SecureMessageInputProps> = ({
  userId,
  onSendMessage,
  placeholder = "Type a message...",
  maxLength = 4000,
  allowFileUpload = true,
  allowFormatting = true,
  allowLinks = true,
  className = '',
  disabled = false
}) => {
  const [message, setMessage] = useState('');
  const [files, setFiles] = useState<File[]>([]);
  const [isValidating, setIsValidating] = useState(false);
  const [isSending, setIsSending] = useState(false);
  const [errors, setErrors] = useState<ValidationError[]>([]);
  const [warnings, setWarnings] = useState<string[]>([]);
  const [showSecurityInfo, setShowSecurityInfo] = useState(false);

  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const {
    validateMessage,
    validateFileUploads,
    getRateLimitStatus;
    securityStatus
  } = useMessageSecurity(userId);

  // Auto-resize textarea
  const adjustTextareaHeight = useCallback(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      textarea.style.height = `${Math.min(textarea.scrollHeight, 120)}px`;
    }
  }, []);

  useEffect(() => {
    adjustTextareaHeight();
  }, [message, adjustTextareaHeight]);

  // Real-time validation
  useEffect(() => {
    const validateContent = async () => {
      if (!message.trim() && files.length === 0) {
        setErrors([]);
        setWarnings([]);
        return;
      }

      setIsValidating(true);
      const newErrors: ValidationError[] = [];
      const newWarnings: string[] = [];

      // Validate message content
      if (message.trim()) {
        const messageValidation = await validateMessage(message, {
          allowLinks,
          allowFormatting
        });

        if (!messageValidation.allowed) {
          newErrors.push({
            type: 'message',
            message: messageValidation.errors.join(', ')
          });
        }

        newWarnings.push(...messageValidation.warnings);
      }

      // Validate files
      if (files.length > 0) {
        const fileValidation = await validateFileUploads(files);
        if (!fileValidation.allowed) {
          newErrors.push({
            type: 'file',
            message: fileValidation.errors.join(', ')
          });
        }
        newWarnings.push(...fileValidation.warnings);
      }

      setErrors(newErrors);
      setWarnings(newWarnings);
      setIsValidating(false);
    };

    const debounceTimer = setTimeout(validateContent, 300);
    return () => clearTimeout(debounceTimer);
  }, [message, files, validateMessage, validateFileUploads, allowLinks, allowFormatting]);

  const handleMessageChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    if (value.length <= maxLength) {
      setMessage(value);
    }
  };

  const handleFileSelect = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = Array.from(e.target.files || []);
    if (selectedFiles.length === 0) return;

    // Validate files immediately
    const validation = await validateFileUploads(selectedFiles);
    if (validation.allowed) {
      setFiles(prev => [...prev, ...selectedFiles]);
    } else {
      setErrors(prev => [...prev, {
        type: 'file',
        message: validation.errors.join(', ')
      }]);
    }

    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const removeFile = (index: number) => {
    setFiles(prev => prev.filter((_; i) => i !== index));
  };

  const handleSend = async () => {
    if (isSending || (!message.trim() && files.length === 0)) return;

    setIsSending(true);
    setErrors([]);
    setWarnings([]);

    try {
      // Final validation before sending
      let finalContent = message;
      let finalFiles = files;

      if (message.trim()) {
        const messageValidation = await validateMessage(message, {
          allowLinks,
          allowFormatting
        });

        if (!messageValidation.allowed) {
          setErrors([{
            type: 'message',
            message: messageValidation.errors.join(', ')
          }]);
          return;
        }, finalContent = messageValidation.sanitizedContent || message;
      }

      if (files.length > 0) {
        const fileValidation = await validateFileUploads(files);
        if (!fileValidation.allowed) {
          setErrors([{
            type: 'file',
            message: fileValidation.errors.join(', ')
          }]);
          return;
        }
      }

      // Send message
      await onSendMessage(finalContent, finalFiles.length > 0 ? finalFiles : undefined);

      // Clear input after successful send
      setMessage('');
      setFiles([]);
      setErrors([]);
      setWarnings([]);
    } catch (error) {
      setErrors([{
        type: 'message',
        message: 'Failed to send message. Please try again.'
      }]);
    } finally {
      setIsSending(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  const rateLimits = getRateLimitStatus();
  const messageRateLimit = rateLimits.message;
  const canSend = !disabled && !isSending && errors.length === 0 && 
                  (message.trim() || files.length > 0) && messageRateLimit.allowed;

  const formatFileSize = (bytes: number) => {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }
    return `${size.toFixed(1)} ${units[unitIndex]}`;
  };

  return (
    <div className={`secure-message-input ${className}`}>
      {/* Security Status Bar */}
      {(errors.length > 0 || warnings.length > 0 || !securityStatus.isSecure) && (
        <div className="mb-2">
          {errors.map((error, index) => (
            <div key={index} className="flex items-center gap-2 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700 mb-1">
              <AlertTriangle className="w-4 h-4 flex-shrink-0" />
              <span className="flex-1">{error.message}</span>
    </div>
          ))}
          {warnings.map((warning, index) => (
            <div key={index} className="flex items-center gap-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-sm text-yellow-700 mb-1">
              <AlertTriangle className="w-4 h-4 flex-shrink-0" />
              <span className="flex-1">{warning}</span>
    </div>
          ))}
        </div>
      )}

      {/* File Attachments */}
      {files.length > 0 && (
        <div className="mb-2 p-2 bg-gray-50 border border-gray-200 rounded">
          <div className="flex items-center gap-2 mb-2">
            <Paperclip className="w-4 h-4 text-gray-500" />
            <span className="text-sm text-gray-700">Attachments ({files.length})</span>
    </div>
          <div className="space-y-1">
            {files.map((file, index) => (
              <div key={index} className="flex items-center gap-2 p-1 bg-white border border-gray-200 rounded">
                <div className="flex-1 min-w-0">
                  <div className="text-sm font-medium text-gray-900 truncate">{file.name}</div>
                  <div className="text-xs text-gray-500">{formatFileSize(file.size)}</div>
    </div>
                <button
                  onClick={() => removeFile(index)} className="p-1 text-gray-400 hover:text-red-500 rounded"
                >
                  <X className="w-3 h-3" />
    </button>
              </div>
            ))}
          </div>
    </div>
      )}

      {/* Main Input Area */}
      <div className="relative border border-gray-300 rounded-lg focus-within:border-blue-500 focus-within:ring-1 focus-within:ring-blue-500">
        <textarea
          ref={textareaRef} value={message}, onChange={handleMessageChange} onKeyPress={handleKeyPress}, placeholder={placeholder} disabled={disabled || isSending}, className="w-full px-3 py-2 pr-20 resize-none border-0 rounded-lg focus:outline-none focus:ring-0 min-h-[40px] max-h-[120px]"
          rows={1}
        />

        {/* Input Controls */}
        <div className="absolute right-2 bottom-2 flex items-center gap-1">
          {/* File Upload Button */}
          {allowFileUpload && (
            <>
              <input
                ref={fileInputRef} type="file"
                multiple
                onChange={handleFileSelect} className="hidden"
                accept="image/*,video/*,audio/*,.pdf,.txt,.doc,.docx"
              />
              <button
                onClick={() => fileInputRef.current?.click()} disabled={disabled || isSending}, className="p-1.5 text-gray-400 hover:text-gray-600 rounded transition-colors disabled:opacity-50"
                title="Attach files"
              >
                <Paperclip className="w-4 h-4" />
    </button>
            </>
          )}

          {/* Security Info Button */}
          <button
            onClick={() => setShowSecurityInfo(!showSecurityInfo)} className={`p-1.5 rounded transition-colors ${
              securityStatus.isSecure
                ? 'text-green-500 hover:text-green-600'
                : 'text-red-500 hover:text-red-600'
            }`}, title="Security status"
          >
            <Shield className="w-4 h-4" />
    </button>
          {/* Send Button */}
          <button
            onClick={handleSend} disabled={!canSend}, className={`p-1.5 rounded transition-colors ${
              canSend
                ? 'text-blue-500 hover:text-blue-600 hover:bg-blue-50'
                : 'text-gray-300 cursor-not-allowed'
            }`}, title={canSend ? 'Send message' : 'Cannot send message'}
          >
            {isSending ? (
              <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
            ) : (
              <Send className="w-4 h-4" />
            )}
          </button>
    </div>
      </div>

      {/* Character Count and Rate Limit Info */}
      <div className="flex justify-between items-center mt-1 text-xs text-gray-500">
        <div className="flex items-center gap-4">
          <span>{message.length}/{maxLength}</span>
          {messageRateLimit.remaining < 10 && (
            <span className="text-yellow-600">
              {messageRateLimit.remaining} messages remaining
            </span>
          )}
          {isValidating && (
            <span className="text-blue-600">Validating...</span>
          )}
        </div>
        {!messageRateLimit.allowed && (
          <span className="text-red-600">
            Rate limited. Try again in {Math.ceil((messageRateLimit.resetTime - Date.now()) / 1000)}s
          </span>
        )}
      </div>

      {/* Security Info Panel */}
      {showSecurityInfo && (
        <div className="mt-2 p-3 bg-gray-50 border border-gray-200 rounded-lg text-sm">
          <div className="flex items-center gap-2 mb-2">
            <Shield className="w-4 h-4 text-gray-600" />
            <span className="font-medium text-gray-700">Security Information</span>
    </div>
          <div className="space-y-1 text-gray-600">
            <div>• Messages are automatically scanned for security threats</div>
            <div>• Suspicious content will be blocked or sanitized</div>
            <div>• File uploads are scanned for malware and viruses</div>
            <div>• Rate limiting prevents spam and abuse</div>
            {allowLinks && <div>• Links are validated for safety</div>}
            {allowFormatting && <div>• HTML content is sanitized</div>}
          </div>
    </div>
      )}
    </div>
  );
};

export default SecureMessageInput;