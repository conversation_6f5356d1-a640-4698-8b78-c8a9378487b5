{"name": "social-nexus-reborn", "private": true, "version": "1.0.0", "type": "module", "scripts": {"start": "vite", "dev": "vite", "dev:polling": "cross-env FORCE_POLLING=true vite --host localhost --port 5173", "dev:stable": "npm run clean && cross-env FORCE_POLLING=true npm run dev:polling", "build": "vite build", "build-with-types": "tsc --noEmit && vite build", "build:analyze": "ANALYZE=true vite build && echo 'Bundle analysis available at dist/stats.html'", "analyze:bundle": "npm run build && node scripts/build-analyzer.js", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "type-check": "tsc --noEmit", "clean": "rimraf dist node_modules/.vite .vite", "fresh-start": "npm run clean && npm install && npm run dev", "dev:debug": "DEBUG=vite:* vite"}, "dependencies": {"@apollo/client": "^3.13.8", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-dialog": "^1.1.0", "@radix-ui/react-dropdown-menu": "^2.1.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.0", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-scroll-area": "^1.2.0", "@radix-ui/react-select": "^2.2.0", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.3.0", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-tooltip": "^1.2.0", "@tanstack/react-query": "^5.80.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.0", "date-fns": "^3.6.0", "dompurify": "^3.2.6", "framer-motion": "^12.0.0", "graphql-ws": "^6.0.6", "lucide-react": "^0.525.0", "react": "^18.3.1", "react-day-picker": "^9.8.1", "react-dom": "^18.3.1", "react-error-boundary": "^6.0.0", "react-intersection-observer": "^9.16.0", "react-is": "^19.1.1", "react-router-dom": "^6.30.0", "react-virtualized-auto-sizer": "^1.0.26", "react-window": "^1.8.11", "react-window-infinite-loader": "^1.0.10", "recharts": "^3.1.0", "socket.io-client": "^4.7.4", "sonner": "^2.0.0", "tailwind-merge": "^2.5.0", "tailwindcss-animate": "^1.0.7", "zustand": "^4.5.0"}, "devDependencies": {"@eslint/js": "^9.32.0", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/node": "^24.1.0", "@types/react": "^18.3.12", "@types/react-devtools": "^3.6.2", "@types/react-dom": "^18.3.1", "@types/react-window": "^1.8.8", "@types/react-window-infinite-loader": "^1.0.9", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "@vitejs/plugin-react": "^4.7.0", "autoprefixer": "^10.4.20", "cross-env": "^7.0.3", "eslint": "^9.32.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-import": "^2.32.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "postcss": "^8.4.49", "rimraf": "^6.0.1", "rollup-plugin-visualizer": "^5.14.0", "tailwindcss": "^3.4.14", "typescript": "^5.9.2", "vite": "^6.3.5", "vitest": "^3.2.4"}, "resolutions": {"react": "18.3.1", "react-dom": "18.3.1"}}