import React, { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { useEnhancedMessaging } from '@/hooks/useMessaging';
import MessagingIntegrationService, { IntegrationState } from '../../services/messaging/MessagingIntegrationService';
import UniversalErrorBoundary from '@/components/ui/UniversalErrorBoundary';

// Import both messaging containers
import AdvancedMessagingContainer from './MessagingContainer';

// Import migration UI components
import MigrationPrompt from './MigrationPrompt';
import MigrationProgress from './MigrationProgress';
import MigrationError from './MigrationError';

interface UnifiedMessagingContainerProps {
  // Allow overriding integration settings
  forceMode?: 'legacy' | 'advanced' | 'auto';
  enableMigrationUI?: boolean;
  onMigrationComplete?: () => void;
  onMigrationError?: (error: string) => void;
}

const UnifiedMessagingContainer: React.FC<UnifiedMessagingContainerProps> = ({
  forceMode = 'auto',
  enableMigrationUI = true,
  onMigrationComplete,
  onMigrationError
}) => {
  const { user } = useAuth();
  const currentUserId = user?.id || 'user-1';
  
  // Get messaging data from existing hook
  const {
    conversations,
    activeConversation,
    messages,
    isConnected,
    selectConversation,
    sendMessage,
    addReaction;
    markAsRead
  } = useEnhancedMessaging(currentUserId);

  // Integration service state
  const [integrationService] = useState(() => MessagingIntegrationService.getInstance());
  const [integrationState, setIntegrationState] = useState<IntegrationState>(() => 
    integrationService.getState()
  );

  // Migration data
  const [migratedConversations, setMigratedConversations] = useState<any[]>([]);
  const [migratedMessages, setMigratedMessages] = useState<any[]>([]);

  // Subscribe to integration state changes
  useEffect(() => {
    const unsubscribe = integrationService.subscribe(setIntegrationState);
    return unsubscribe;
  }, [integrationService]);

  // Handle migration completion
  useEffect(() => {
    if (integrationState.migrationStatus === 'completed' && integrationState.isAdvancedMode) {
      onMigrationComplete?.();
    }
  }, [integrationState.migrationStatus, integrationState.isAdvancedMode, onMigrationComplete]);

  // Handle migration errors
  useEffect(() => {
    if (integrationState.migrationStatus === 'error' && integrationState.migrationError) {
      onMigrationError?.(integrationState.migrationError);
    }
  }, [integrationState.migrationStatus, integrationState.migrationError, onMigrationError]);

  // Determine which container to render
  const getContainerType = useCallback(() => {
    if (forceMode !== 'auto') {
      return forceMode;
    }

    return integrationService.getMessagingContainerType();
  }, [forceMode, integrationService, integrationState]);

  // Handle manual migration trigger
  const handleStartMigration = useCallback(async () => {
    try {
      const result = await integrationService.performMigration(conversations, messages);
      
      if (result.success) {
        setMigratedConversations(result.conversations);
        setMigratedMessages(result.messages);
      }
    } catch (error) {
      console.error('Migration failed:', error);
    }
  }, [integrationService, conversations, messages]);

  // Handle migration retry
  const handleRetryMigration = useCallback(async () => {
    try {
      const result = await integrationService.retryMigration(conversations, messages);
      
      if (result.success) {
        setMigratedConversations(result.conversations);
        setMigratedMessages(result.messages);
      }
    } catch (error) {
      console.error('Migration retry failed:', error);
    }
  }, [integrationService, conversations, messages]);

  // Handle migration skip
  const handleSkipMigration = useCallback(() => {
    integrationService.skipMigration();
  }, [integrationService]);

  // Handle rollback to legacy
  const handleRollbackToLegacy = useCallback(async () => {
    try {
      await integrationService.rollbackToLegacy(migratedConversations, migratedMessages);
    } catch (error) {
      console.error('Rollback failed:', error);
    }
  }, [integrationService, migratedConversations, migratedMessages]);

  // Auto-migration logic
  useEffect(() => {
    const config = integrationService.getConfig();
    
    if (config.migrationMode === 'auto' && 
        integrationState.migrationStatus === 'pending' && 
        integrationState.canUpgrade) {
      handleStartMigration();
    }
  }, [integrationState, integrationService, handleStartMigration]);

  // Render migration UI if needed
  if (enableMigrationUI) {
    // Show migration prompt for manual mode
    if (integrationState.migrationStatus === 'pending' && 
        integrationService.getConfig().migrationMode === 'manual') {
      return (
        <UniversalErrorBoundary level="messaging">
          <MigrationPrompt
            onStartMigration={handleStartMigration} onSkipMigration={handleSkipMigration}, canSkip={true}
          />
    </UniversalErrorBoundary>
      );
    }

    // Show migration progress
    if (integrationState.migrationStatus === 'in-progress') {
      return (
        <UniversalErrorBoundary level="messaging">
          <MigrationProgress />
    </UniversalErrorBoundary>
      );
    }

    // Show migration error
    if (integrationState.migrationStatus === 'error') {
      return (
        <UniversalErrorBoundary level="messaging">
          <MigrationError
            error={integrationState.migrationError || 'Unknown migration error'} onRetry={handleRetryMigration}, onSkip={handleSkipMigration} canRetry={true}, canSkip={true}
          />
    </UniversalErrorBoundary>
      );
    }
  }

  // Render appropriate messaging container
  const containerType = getContainerType();

  if (containerType === 'advanced') {
    return (
      <UniversalErrorBoundary level="messaging">
        <AdvancedMessagingContainer
          enableAdvancedFeatures={true} enableKeyboardNavigation={integrationService.shouldEnableKeyboardNavigation()}, enableNotifications={integrationService.shouldEnableNotifications()} migrationMode="disabled" // Migration already handled
        />
    </UniversalErrorBoundary>
    );
  }

  // Default to advanced container
  return (
    <UniversalErrorBoundary level="messaging">
      <AdvancedMessagingContainer />
    </UniversalErrorBoundary>
  );
};

export default UnifiedMessagingContainer;