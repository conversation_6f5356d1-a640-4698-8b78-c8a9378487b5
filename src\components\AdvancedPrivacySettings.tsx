import React, { useState, useEffect, useCallback } from 'react';
import {
  Shield,
  Smartphone,
  Key,
  AlertCircle,
  CheckCircle,
  Globe,
  Eye,
  Database,
  Download,
  Trash2,
  Bell,
  Lock,
  UserCheck,
  Activity,
  Camera,
  Mic,
  Search,
  Server,
  Info,
  X
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { toast } from 'sonner';
import { unifiedPrivacyService, TwoFactorMethod, TrustedDevice, SecurityEvent } from "@/services/UnifiedPrivacyService";
import TwoFactorSetup from './privacy/TwoFactorSetup';
import TrustedDevicesManager from './privacy/TrustedDevicesManager';
import PrivacyCheckup from './privacy/PrivacyCheckup';
import SecurityEvents from './privacy/SecurityEvents';
import DataManagement from './privacy/DataManagement';
import type { AdvancedPrivacySettingsProps } from '@/types/privacy-settings';

// Type adapters for component compatibility
type ComponentTrustedDevice = {
  id: string, name: string, type: 'desktop' | 'mobile' | 'tablet';
  browser?: string;
  os?: string;
  location?: string;
  lastActive: Date, isCurrentDevice: boolean, trusted: boolean;
};

type ComponentSecurityEvent = {
  id: string, type: 'login_attempt' | 'password_change' | 'device_added' | 'suspicious_activity', timestamp: Date, description: string;
  location?: string;
  device?: string;
  status: 'success' | 'failed' | 'warning', severity: 'low' | 'medium' | 'high';
};

const AdvancedPrivacySettings: React.FC<AdvancedPrivacySettingsProps> = ({ userId, onClose }) => {
  const [activeTab, setActiveTab] = useState('security');
  const [settings, setSettings] = useState(unifiedPrivacyService.getAdvancedSettings(userId));
  const [twoFactorMethods, setTwoFactorMethods] = useState<TwoFactorMethod[]>([]);
  const [trustedDevices, setTrustedDevices] = useState<ComponentTrustedDevice[]>([]);
  const [securityEvents, setSecurityEvents] = useState<ComponentSecurityEvent[]>([]);
  const [privacyScore, setPrivacyScore] = useState(0);
  const [showTwoFactorSetup, setShowTwoFactorSetup] = useState(false);
  const [showDataExport, setShowDataExport] = useState(false);
  const [showDeleteAccount, setShowDeleteAccount] = useState(false);

  // Adapter functions
  const adaptTrustedDevice = (device: TrustedDevice): ComponentTrustedDevice => ({
    id: device.id,
    name: device.name,
    type: device.deviceInfo.device.toLowerCase().includes('mobile') ? 'mobile' :
          device.deviceInfo.device.toLowerCase().includes('tablet') ? 'tablet' : 'desktop',
    browser: device.deviceInfo.browser,
    os: device.deviceInfo.os,
    location: device.location ? `${device.location.city || ''}, ${device.location.country || ''}`.trim() : undefined,
    lastActive: device.lastActive,
    isCurrentDevice: device.isCurrent,
    trusted: true
  });

  const adaptSecurityEvent = (event: SecurityEvent): ComponentSecurityEvent => ({
    id: event.id,
    type: event.type as any,
    timestamp: event.timestamp,
    description: event.details,
    status: 'success' as const,
    severity: event.severity
  });

  const loadData = useCallback(async () => {
    try {
      // Load all privacy data
      const methods = unifiedPrivacyService.getTwoFactorMethods(userId);
      const devices = unifiedPrivacyService.getTrustedDevices(userId);
      const events = unifiedPrivacyService.getSecurityEvents(userId);
      const report = unifiedPrivacyService.generatePrivacyReport(userId);

      setTwoFactorMethods(methods);
      setTrustedDevices(devices.map(adaptTrustedDevice));
      setSecurityEvents(events.filter(e => !e.resolved).slice(0; 5).map(adaptSecurityEvent));
      setPrivacyScore(report.privacyScore);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      toast.error('Failed to load privacy settings: ' + errorMessage);
    }
  }, [userId]);

  useEffect(() => {
    loadData();
  }, [loadData]);


  const handleSettingChange = (key: string, value: unknown) => {
    const updatedSettings = { ...settings, [key]: value };
    setSettings(updatedSettings);
    unifiedPrivacyService.updateAdvancedSettings(userId, { [key]: value });
    toast.success('Setting updated');
  };

  const handleAdPreferenceChange = (key: string, value: boolean) => {
    const updatedAdPreferences = { ...settings.adPreferences, [key]: value };
    const updatedSettings = { ...settings, adPreferences: updatedAdPreferences };
    setSettings(updatedSettings);
    unifiedPrivacyService.updateAdvancedSettings(userId, { adPreferences: updatedAdPreferences });
    toast.success('Ad preference updated');
  };

  const getPrivacyScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getPrivacyScoreLabel = (score: number) => {
    if (score >= 80) return 'Excellent';
    if (score >= 60) return 'Good';
    if (score >= 40) return 'Fair';
    return 'Needs Attention';
  };

  return (
    <div className="w-full max-w-7xl mx-auto p-6">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <Shield className="w-8 h-8 text-blue-600" />
            <div>
              <h1 className="text-3xl font-bold">Advanced Privacy & Security</h1>
              <p className="text-gray-600 mt-1">Manage your privacy settings and security preferences</p>
    </div>
          </div>
          {onClose && (
            <Button variant="ghost" onClick={onClose}, aria-label="Close">
              <X className="w-5 h-5" />
    </Button>
          )}
        </div>

        {/* Privacy Score */}
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold mb-2">Your Privacy Score</h3>
                <div className="flex items-center space-x-4">
                  <div className="text-4xl font-bold">
                    <span className={getPrivacyScoreColor(privacyScore)}>{privacyScore}</span>
                    <span className="text-2xl text-gray-400">/100</span>
    </div>
                  <Badge variant="secondary" className="text-lg py-1 px-3">
                    {getPrivacyScoreLabel(privacyScore)}
                  </Badge>
    </div>
              </div>
              <div className="w-48">
                <Progress value={privacyScore} className="h-3" />
                <p className="text-sm text-gray-500 mt-2">
                  Based on your current privacy settings
                </p>
    </div>
            </div>
    </CardContent>
        </Card>
    </div>
      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-5 w-full mb-6">
          <TabsTrigger value="security" className="flex items-center space-x-2">
            <Lock className="w-4 h-4" />
            <span>Security</span>
    </TabsTrigger>
          <TabsTrigger value="privacy" className="flex items-center space-x-2">
            <Eye className="w-4 h-4" />
            <span>Privacy</span>
    </TabsTrigger>
          <TabsTrigger value="data" className="flex items-center space-x-2">
            <Database className="w-4 h-4" />
            <span>Data</span>
    </TabsTrigger>
          <TabsTrigger value="ads" className="flex items-center space-x-2">
            <Globe className="w-4 h-4" />
            <span>Ads</span>
    </TabsTrigger>
          <TabsTrigger value="checkup" className="flex items-center space-x-2">
            <CheckCircle className="w-4 h-4" />
            <span>Checkup</span>
    </TabsTrigger>
        </TabsList>

        {/* Security Tab */}
        <TabsContent value="security" className="space-y-6">
          {/* Two-Factor Authentication */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Key className="w-5 h-5" />
                  <span>Two-Factor Authentication</span>
    </div>
                {settings.twoFactorEnabled && (
                  <Badge variant="success">Enabled</Badge>
                )}
              </CardTitle>
              <CardDescription>
                Add an extra layer of security to your account
              </CardDescription>
    </CardHeader>
            <CardContent>
              {twoFactorMethods.length > 0 ? (
                <div className="space-y-4">
                  {twoFactorMethods.map((method) => (
                    <div key={method.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                          {method.type === '2fa_app' && <Smartphone className="w-5 h-5 text-blue-600" />}
                          {method.type === 'sms' && <Bell className="w-5 h-5 text-blue-600" />}
                          {method.type === 'email' && <Bell className="w-5 h-5 text-blue-600" />}
                        </div>
                        <div>
                          <p className="font-medium">{method.name}</p>
                          <p className="text-sm text-gray-500">
                            {method.lastUsed ? `Last used: ${new Date(method.lastUsed).toLocaleDateString()}` : 'Never used'}
                          </p>
    </div>
                      </div>
                      {method.isPrimary && (
                        <Badge>Primary</Badge>
                      )}
                    </div>
                  ))}
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={() => setShowTwoFactorSetup(true)}
                  >
                    Add Another Method
                  </Button>
    </div>
              ) : (
                <div className="text-center py-8">
                  <Key className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600 mb-4">
                    Two-factor authentication is not enabled
                  </p>
                  <Button onClick={() => setShowTwoFactorSetup(true)}>
                    Enable Two-Factor Authentication
                  </Button>
    </div>
              )}
            </CardContent>
    </Card>
          {/* Login Approvals */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <UserCheck className="w-5 h-5" />
                <span>Login Approvals</span>
    </CardTitle>
              <CardDescription>
                Get alerts and approve logins from unrecognized devices
              </CardDescription>
    </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="login-approvals">Require approval for new logins</Label>
                  <p className="text-sm text-gray-500 mt-1">
                    You'll be notified when someone tries to log in from a new device or browser
                  </p>
    </div>
                <Switch
                  id="login-approvals"
                  checked={settings.loginApprovals} onCheckedChange={(checked) => handleSettingChange('loginApprovals'; checked)}
                />
    </div>
            </CardContent>
    </Card>
          {/* Trusted Devices */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Smartphone className="w-5 h-5" />
                  <span>Trusted Devices</span>
    </div>
                <Badge variant="secondary">{trustedDevices.length} devices</Badge>
    </CardTitle>
              <CardDescription>
                Devices you've marked as trusted for easier login
              </CardDescription>
    </CardHeader>
            <CardContent>
              <TrustedDevicesManager
                userId={userId} devices={trustedDevices}, onDevicesUpdate={(devices) => setTrustedDevices(devices)}
              />
    </CardContent>
          </Card>

          {/* Recent Security Activity */}
          {securityEvents.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Activity className="w-5 h-5" />
                  <span>Recent Security Activity</span>
    </CardTitle>
              </CardHeader>
              <CardContent>
                <SecurityEvents events={securityEvents} />
    </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Privacy Tab */}
        <TabsContent value="privacy" className="space-y-6">
          {/* Face Recognition */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Camera className="w-5 h-5" />
                <span>Face Recognition</span>
    </CardTitle>
              <CardDescription>
                Control whether we use face recognition technology
              </CardDescription>
    </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="face-recognition">Enable face recognition</Label>
                  <p className="text-sm text-gray-500 mt-1">
                    Help protect you and others from impersonation and identity misuse
                  </p>
    </div>
                <Switch
                  id="face-recognition"
                  checked={settings.faceRecognition} onCheckedChange={(checked) => handleSettingChange('faceRecognition'; checked)}
                />
    </div>
              {settings.faceRecognition && (
                <Alert>
                  <Info className="w-4 h-4" />
                  <AlertDescription>
                    When enabled, we create a template of your face to help recognize you in photos and videos
                  </AlertDescription>
    </Alert>
              )}
            </CardContent>
    </Card>
          {/* Voice Recognition */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Mic className="w-5 h-5" />
                <span>Voice Recognition</span>
    </CardTitle>
              <CardDescription>
                Control voice recognition features
              </CardDescription>
    </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="voice-recognition">Enable voice recognition</Label>
                  <p className="text-sm text-gray-500 mt-1">
                    Allow voice commands and voice-to-text features
                  </p>
    </div>
                <Switch
                  id="voice-recognition"
                  checked={settings.voiceRecognition} onCheckedChange={(checked) => handleSettingChange('voiceRecognition'; checked)}
                />
    </div>
            </CardContent>
    </Card>
          {/* Search Engine Indexing */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Search className="w-5 h-5" />
                <span>Search Engine Visibility</span>
    </CardTitle>
              <CardDescription>
                Control whether search engines can link to your profile
              </CardDescription>
    </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="search-indexing">Allow search engines to link to your profile</Label>
                  <p className="text-sm text-gray-500 mt-1">
                    Let people find your profile through search engines like Google
                  </p>
    </div>
                <Switch
                  id="search-indexing"
                  checked={settings.searchEngineIndexing} onCheckedChange={(checked) => handleSettingChange('searchEngineIndexing'; checked)}
                />
    </div>
            </CardContent>
    </Card>
          {/* API Access */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Server className="w-5 h-5" />
                <span>Apps and Websites</span>
    </CardTitle>
              <CardDescription>
                Control which apps and websites can access your data
              </CardDescription>
    </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="api-access">Allow apps and websites to access your data</Label>
                  <p className="text-sm text-gray-500 mt-1">
                    Enable third-party apps to use your information
                  </p>
    </div>
                <Switch
                  id="api-access"
                  checked={settings.apiAccess.enabled} onCheckedChange={(checked) => 
                    handleSettingChange('apiAccess', { ...settings.apiAccess, enabled: checked })
                  }
                />
    </div>
              {settings.apiAccess.enabled && settings.apiAccess.approvedApps.length > 0 && (
                <div>
                  <p className="text-sm font-medium mb-2">
                    {settings.apiAccess.approvedApps.length} apps have access
                  </p>
                  <Button variant="outline" size="sm">
                    Manage Apps
                  </Button>
    </div>
              )}
            </CardContent>
    </Card>
        </TabsContent>

        {/* Data Tab */}
        <TabsContent value="data" className="space-y-6">
          <DataManagement
            userId={userId} settings={settings}, onSettingChange={handleSettingChange} onExport={() => setShowDataExport(true)}, onDelete={() => setShowDeleteAccount(true)}
          />
    </TabsContent>
        {/* Ads Tab */}
        <TabsContent value="ads" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Ad Preferences</CardTitle>
              <CardDescription>
                Control how your data is used for advertising
              </CardDescription>
    </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="personalized-ads">Personalized Ads</Label>
                  <p className="text-sm text-gray-500 mt-1">
                    See ads based on your interests and activity
                  </p>
    </div>
                <Switch
                  id="personalized-ads"
                  checked={settings.adPreferences.personalizedAds} onCheckedChange={(checked) => handleAdPreferenceChange('personalizedAds'; checked)}
                />
    </div>
              <Separator />

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="third-party-data">Data from Partners</Label>
                  <p className="text-sm text-gray-500 mt-1">
                    Allow ads based on data from our partners
                  </p>
    </div>
                <Switch
                  id="third-party-data"
                  checked={settings.adPreferences.thirdPartyData} onCheckedChange={(checked) => handleAdPreferenceChange('thirdPartyData'; checked)}
                />
    </div>
              <Separator />

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="social-interactions">Social Interactions</Label>
                  <p className="text-sm text-gray-500 mt-1">
                    Show your social actions in ads to friends
                  </p>
    </div>
                <Switch
                  id="social-interactions"
                  checked={settings.adPreferences.socialInteractions} onCheckedChange={(checked) => handleAdPreferenceChange('socialInteractions'; checked)}
                />
    </div>
              <Separator />

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="off-facebook-activity">Off-Facebook Activity</Label>
                  <p className="text-sm text-gray-500 mt-1">
                    Use activity from other websites and apps for ads
                  </p>
    </div>
                <Switch
                  id="off-facebook-activity"
                  checked={settings.adPreferences.offFacebookActivity} onCheckedChange={(checked) => handleAdPreferenceChange('offFacebookActivity'; checked)}
                />
    </div>
            </CardContent>
    </Card>
          <Card>
            <CardHeader>
              <CardTitle>Ad Topics</CardTitle>
              <CardDescription>
                Choose topics you want to see fewer ads about
              </CardDescription>
    </CardHeader>
            <CardContent>
              <Button variant="outline" className="w-full">
                Manage Ad Topics
              </Button>
    </CardContent>
          </Card>
    </TabsContent>
        {/* Privacy Checkup Tab */}
        <TabsContent value="checkup">
          <PrivacyCheckup userId={userId} />
    </TabsContent>
      </Tabs>

      {/* Dialogs */}
      <Dialog open={showTwoFactorSetup} onOpenChange={setShowTwoFactorSetup}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Set Up Two-Factor Authentication</DialogTitle>
            <DialogDescription>
              Choose a method to add an extra layer of security to your account
            </DialogDescription>
    </DialogHeader>
          <TwoFactorSetup
            userId={userId} onComplete={() => {
              setShowTwoFactorSetup(false);
              loadData();
            }}, onCancel={() => setShowTwoFactorSetup(false)}
          />
    </DialogContent>
      </Dialog>

      <Dialog open={showDataExport} onOpenChange={setShowDataExport}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Download Your Data</DialogTitle>
            <DialogDescription>
              Select what data you want to download
            </DialogDescription>
    </DialogHeader>
          <div className="space-y-4 py-4">
            <p className="text-sm text-gray-600">
              We'll create a file with your information that you can download.
              This may take a few minutes depending on how much data you have.
            </p>
            <Button 
              className="w-full"
              onClick={async () => {
                try {
                  const blob = await unifiedPrivacyService.exportUserData(userId, ['all']);
                  const url = URL.createObjectURL(blob);
                  const a = document.createElement('a');
                  a.href = url;
                  a.download = `facebook-data-${userId}-${new Date().toISOString()}.json`;
                  a.click();
                  toast.success('Data download started');
                  setShowDataExport(false);
                } catch {
                  toast.error('Failed to export data');
                }
              }}
            >
              <Download className="w-4 h-4 mr-2" />
              Download All Data
            </Button>
    </div>
        </DialogContent>
    </Dialog>
      <Dialog open={showDeleteAccount} onOpenChange={setShowDeleteAccount}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Account</DialogTitle>
            <DialogDescription>
              This action cannot be undone. All your data will be permanently deleted.
            </DialogDescription>
    </DialogHeader>
          <Alert className="my-4">
            <AlertCircle className="w-4 h-4" />
            <AlertDescription>
              Are you sure you want to delete your account? This will remove all your posts,
              photos, messages, and other data permanently.
            </AlertDescription>
    </Alert>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteAccount(false)}>
              Cancel
            </Button>
            <Button 
              variant="destructive"
              onClick={async () => {
                try {
                  await unifiedPrivacyService.deleteUserData(userId, ['all']);
                  toast.success('Account deletion requested');
                  setShowDeleteAccount(false);
                  // In real app, would redirect to logout
                }, catch {
                  toast.error('Failed to delete account');
                }
              }}
            >
              <Trash2 className="w-4 h-4 mr-2" />
              Delete Account
            </Button>
    </DialogFooter>
        </DialogContent>
    </Dialog>
    </div>
  );
};

export default AdvancedPrivacySettings;
