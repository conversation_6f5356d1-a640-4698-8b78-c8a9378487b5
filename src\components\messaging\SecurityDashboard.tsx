import React, { useState, useEffect, useCallback } from 'react';
import { SecurityService } from '../../services/messaging/SecurityService';
import { ContentModerationService, ModerationConfig } from '../../services/messaging/ContentModerationService';

interface SecurityDashboardProps {
  userId: string;
  isAdmin?: boolean;
  className?: string;
}

interface SecurityStats {
  rateLimitStatus: Record<string, { remaining: number, resetTime: number }>;
  moderationConfig: ModerationConfig, recentValidations: Array<{
    timestamp: number, type: 'message' | 'file' | 'reaction', isValid: boolean, flags: string[];
  }>;
}

export const SecurityDashboard: React.FC<SecurityDashboardProps> = ({
  userId,
  isAdmin = false,
  className = ""
}) => {
  const [stats, setStats] = useState<SecurityStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'overview' | 'moderation' | 'rate-limits'>('overview');
  const [customBlockedWords, setCustomBlockedWords] = useState<string>('');

  const securityService = SecurityService.getInstance();
  const moderationService = ContentModerationService.getInstance();

  // Load security stats
  const loadStats = useCallback(async () => {
    setLoading(true);
    try {
      const rateLimitStatus = securityService.getRateLimitStatus(userId);
      const moderationConfig = moderationService.getConfig();
      
      setStats({
        rateLimitStatus,
        moderationConfig,
        recentValidations: [] // In a real app, this would come from a logging service
      });
    } catch (error) {
      console.error('Failed to load security stats:', error);
    } finally {
      setLoading(false);
    }
  }, [userId, securityService, moderationService]);

  useEffect(() => {
    loadStats();
    const interval = setInterval(loadStats, 30000); // Refresh every 30 seconds
    return () => clearInterval(interval);
  }, [loadStats]);

  // Handle moderation config updates
  const handleModerationConfigUpdate = useCallback((updates: Partial<ModerationConfig>) => {
    moderationService.updateConfig(updates);
    loadStats();
  }, [moderationService, loadStats]);

  // Handle custom blocked words
  const handleAddBlockedWords = useCallback(() => {
    if (customBlockedWords.trim()) {
      const words = customBlockedWords.split(',').map(word => word.trim()).filter(word => word);
      moderationService.addBlockedWords(words);
      setCustomBlockedWords('');
      loadStats();
    }
  }, [customBlockedWords, moderationService, loadStats]);

  // Clear rate limits (admin only)
  const handleClearRateLimits = useCallback(() => {
    if (isAdmin) {
      securityService.clearRateLimits(userId);
      loadStats();
    }
  }, [isAdmin, securityService, userId, loadStats]);

  // Format time remaining
  const formatTimeRemaining = (resetTime: number): string => {
    const remaining = Math.max(0, resetTime - Date.now());
    const minutes = Math.floor(remaining / 60000);
    const seconds = Math.floor((remaining % 60000) / 1000);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  if (loading) {
    return (
      <div className={`security-dashboard ${className}`}>
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded"></div>
    </div>
        </div>
    </div>
    );
  }

  if (!stats) {
    return (
      <div className={`security-dashboard ${className}`}>
        <div className="text-center text-gray-500 py-8">
          Failed to load security information
        </div>
    </div>
    );
  }

  return (
    <div className={`security-dashboard bg-white rounded-lg shadow-sm border ${className}`}>
      {/* Header */}
      <div className="border-b border-gray-200 px-6 py-4">
        <h2 className="text-lg font-semibold text-gray-900">Security Dashboard</h2>
        <p className="text-sm text-gray-600 mt-1">
          Monitor and manage messaging security settings
        </p>
    </div>
      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8 px-6">
          {[
            { id: 'overview', label: 'Overview' },
            { id: 'moderation', label: 'Content Moderation' },
            { id: 'rate-limits', label: 'Rate Limits' }
          ].map(tab => (
            <button
              key={tab.id} onClick={() => setActiveTab(tab.id as any)}, className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {tab.label}
            </button>
          ))}
        </nav>
    </div>
      {/* Content */}
      <div className="p-6">
        {activeTab === 'overview' && (
          <div className="space-y-6">
            {/* Security Status */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Security Status</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <svg className="h-8 w-8 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
                    </div>
                    <div className="ml-3">
                      <p className="text-sm font-medium text-green-800">Input Validation</p>
                      <p className="text-sm text-green-600">Active</p>
    </div>
                  </div>
    </div>
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <svg className="h-8 w-8 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
    </svg>
                    </div>
                    <div className="ml-3">
                      <p className="text-sm font-medium text-green-800">Content Sanitization</p>
                      <p className="text-sm text-green-600">Active</p>
    </div>
                  </div>
    </div>
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <svg className="h-8 w-8 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
    </svg>
                    </div>
                    <div className="ml-3">
                      <p className="text-sm font-medium text-green-800">Rate Limiting</p>
                      <p className="text-sm text-green-600">Active</p>
    </div>
                  </div>
    </div>
              </div>
    </div>
            {/* Rate Limit Overview */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Current Rate Limits</h3>
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {Object.entries(stats.rateLimitStatus).map(([action, status]) => (
                    <div key={action} className="text-center">
                      <div className="text-2xl font-bold text-gray-900">{status.remaining}</div>
                      <div className="text-sm text-gray-600 capitalize">{action}s Remaining</div>
                      <div className="text-xs text-gray-500 mt-1">
                        Resets in {formatTimeRemaining(status.resetTime)}
                      </div>
    </div>
                  ))}
                </div>
    </div>
            </div>
    </div>
        )}

        {activeTab === 'moderation' && (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Content Moderation Settings</h3>
              
              {/* Moderation toggles */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-gray-700">Profanity Filter</label>
                    <p className="text-sm text-gray-500">Automatically filter inappropriate language</p>
    </div>
                  <button
                    onClick={() => handleModerationConfigUpdate({ 
                      enableProfanityFilter: !stats.moderationConfig.enableProfanityFilter 
                    })}, className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                      stats.moderationConfig.enableProfanityFilter ? 'bg-blue-600' : 'bg-gray-200'
                    }`}
                  >
                    <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      stats.moderationConfig.enableProfanityFilter ? 'translate-x-6' : 'translate-x-1'
                    }`} />
    </button>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-gray-700">Spam Detection</label>
                    <p className="text-sm text-gray-500">Detect and prevent spam messages</p>
    </div>
                  <button
                    onClick={() => handleModerationConfigUpdate({ 
                      enableSpamDetection: !stats.moderationConfig.enableSpamDetection 
                    })}, className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                      stats.moderationConfig.enableSpamDetection ? 'bg-blue-600' : 'bg-gray-200'
                    }`}
                  >
                    <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      stats.moderationConfig.enableSpamDetection ? 'translate-x-6' : 'translate-x-1'
                    }`} />
    </button>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-gray-700">Toxicity Detection</label>
                    <p className="text-sm text-gray-500">Identify potentially harmful content</p>
    </div>
                  <button
                    onClick={() => handleModerationConfigUpdate({ 
                      enableToxicityDetection: !stats.moderationConfig.enableToxicityDetection 
                    })}, className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                      stats.moderationConfig.enableToxicityDetection ? 'bg-blue-600' : 'bg-gray-200'
                    }`}
                  >
                    <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      stats.moderationConfig.enableToxicityDetection ? 'translate-x-6' : 'translate-x-1'
                    }`} />
    </button>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-gray-700">Strict Mode</label>
                    <p className="text-sm text-gray-500">Apply stricter content filtering</p>
    </div>
                  <button
                    onClick={() => handleModerationConfigUpdate({ 
                      strictMode: !stats.moderationConfig.strictMode 
                    })}, className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                      stats.moderationConfig.strictMode ? 'bg-blue-600' : 'bg-gray-200'
                    }`}
                  >
                    <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      stats.moderationConfig.strictMode ? 'translate-x-6' : 'translate-x-1'
                    }`} />
    </button>
                </div>
    </div>
            </div>

            {/* Custom blocked words */}
            <div>
              <h4 className="text-md font-medium text-gray-900 mb-3">Custom Blocked Words</h4>
              <div className="space-y-3">
                <div className="flex space-x-2">
                  <input
                    type="text"
                    value={customBlockedWords} onChange={(e) => setCustomBlockedWords(e.target.value)}, placeholder="Enter words separated by commas"
                    className="flex-1 border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                  />
                  <button
                    onClick={handleAddBlockedWords} disabled={!customBlockedWords.trim()}, className="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed"
                  >
                    Add
                  </button>
    </div>
                {stats.moderationConfig.customBlockedWords.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {stats.moderationConfig.customBlockedWords.map((word, index) => (
                      <span
                        key={index} className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800"
                      >
                        {word}
                        <button
                          onClick={() => {
                            moderationService.removeBlockedWords([word]);
                            loadStats();
                          }}, className="ml-1 text-red-600 hover:text-red-800"
                        >
                          ×
                        </button>
    </span>
                    ))}
                  </div>
                )}
              </div>
    </div>
          </div>
        )}

        {activeTab === 'rate-limits' && (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Rate Limit Details</h3>
              
              <div className="space-y-4">
                {Object.entries(stats.rateLimitStatus).map(([action, status]) => (
                  <div key={action} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="text-md font-medium text-gray-900 capitalize">{action}s</h4>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        status.remaining > 10 
                          ? 'bg-green-100 text-green-800'
                          : status.remaining > 0
                          ? 'bg-yellow-100 text-yellow-800'
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {status.remaining} remaining
                      </span>
    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                      <div
                        className={`h-2 rounded-full ${
                          status.remaining > 10 ? 'bg-green-500' : status.remaining > 0 ? 'bg-yellow-500' : 'bg-red-500'
                        }`}, style={{ 
                          width: `${Math.max(5, (status.remaining / 100) * 100)}%` 
                        }}
                      ></div>
    </div>
                    <div className="text-sm text-gray-600">
                      Resets in {formatTimeRemaining(status.resetTime)}
                    </div>
    </div>
                ))}
              </div>

              {isAdmin && (
                <div className="mt-6 pt-6 border-t border-gray-200">
                  <button
                    onClick={handleClearRateLimits} className="px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-md hover:bg-red-700"
                  >
                    Clear All Rate Limits (Admin)
                  </button>
    </div>
              )}
            </div>
    </div>
        )}
      </div>
    </div>
  );
};

export default SecurityDashboard;