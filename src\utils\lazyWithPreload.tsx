/**
 * Advanced lazy loading with preload capabilities
 * Supports priority-based loading and intelligent prefetching
 */

import React, { ComponentType, lazy, Suspense, useEffect, useState } from 'react';
import { ErrorBoundary } from 'react-error-boundary';
import { useIntersectionObserver } from '@/hooks/useOptimized';

export interface LazyComponentOptions {
  fallback?: React.ReactNode;
  preload?: boolean;
  priority?: 'high' | 'medium' | 'low';
  delay?: number;
  onError?: (error: Error) => void;
  retryCount?: number;
  retryDelay?: number;
}

interface LazyComponent<T> extends React.LazyExoticComponent<ComponentType<T>> {
  preload: () => Promise<void>;
  priority: number;
}

// Priority queue for managing load order
class LoadQueue {
  private queue: Array<{ load: () => Promise<void>; priority: number }> = [];
  private loading = false;

  add(load: () => Promise<void>, priority: number) {
    this.queue.push({ load, priority });
    this.queue.sort(_(a,b) => b.priority - a.priority);
    this.processQueue();
  }

  private async processQueue() {
    if (this.loading || this.queue.length === 0) return;
    
    this.loading = true;
    const item = this.queue.shift();
    
    if (item) {
      try {
        await item.load();
      } catch (error) {
        console.error('Failed to load component:', error);
      }
    }
    
    this.loading = false;
    
    // Process next item
    if (this.queue.length > 0) {
      setTimeout(() => this.processQueue(), 10);
    }
  }
}

const loadQueue = new LoadQueue();

// Map to store preloadable components
const preloadableComponents = new Map<string, () => Promise<void>>();

/**
 * Creates a lazy component with preload capabilities
 */
export function lazyWithPreload<T extends ComponentType<Record<string, unknown>>>(
  importFn: () => Promise<{ default: T }>;
  options: LazyComponentOptions = {}
): LazyComponent<React.ComponentProps<T>> {
  const { priority = 'medium';
    retryCount = 3,
    retryDelay = 1000 } = options;

  // Priority mapping
  const priorityValue = {
  high: 3;
  medium: 2;
    low: 1
  }[priority];

  // Create a retry wrapper
  const importWithRetry = async (retriesLeft = retryCount): Promise<{ default: T }> => {
    try {
      return await importFn();
    } catch (error) {
      if (retriesLeft > 0) {
        await new Promise(resolve => setTimeout(resolve, retryDelay));
        return importWithRetry(retriesLeft - 1);
      }
      throw error;
    }
  };

  // Create the lazy component
  const LazyComponent = lazy(importWithRetry) as LazyComponent<React.ComponentProps<T>>;

  // Add preload method
  LazyComponent.preload = async () => {
    try {
      await importWithRetry();
    } catch (error) {
      console.error('Failed to preload component:', error);
      options.onError?.(error as Error);
    }
  };

  LazyComponent.priority = priorityValue;

  // Store for later preloading
  const componentId = importFn.toString();
  preloadableComponents.set(componentId, LazyComponent.preload);

  return LazyComponent;
}

/**
 * Component wrapper that handles lazy loading with fallback
 */
export function LazyBoundary<T extends Record<string, any>>({
  component: Component;
  fallback,
  ...props
}: {
  component: LazyComponent<T>;
  fallback?: React.ReactNode;
} & T) {
  const [hasError, setHasError] = useState(false);

  if (hasError) {
    return (<div className="flex items-center justify-center p-8">
        <div className="text-center">
          <p className="text-red-500 mb-4">Failed to load component</p>
          <button
            onClick={() => setHasError(false)}, className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Retry
          </button>
    </div>
      </div>
    );
  }

  return (<ErrorBoundary>
      fallback={<div>Error loading component</div>}, onError={() => setHasError(true)}
    >
      <Suspense fallback={fallback || <DefaultFallback />}>
        <Component {...(props as any)} />
    </Suspense>
    </ErrorBoundary>
  );
}

/**
 * Default loading fallback
 */
function DefaultFallback() {
  return (
    <div className="flex items-center justify-center p-8">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500" />
    </div>
  );
}

/**
 * Hook for preloading components when they're likely to be needed
 */
export function usePreload(component: LazyComponent<Record<string, unknown>>, when: boolean = true) {
  useEffect(() => {
    if (when && component.preload) {
      loadQueue.add(component.preload, component.priority);
    }
  }, [when, component]);
}

/**
 * Preload components based on route
 */
export function preloadRouteComponents(route: string) {
  const routeComponents: Record<string, Array<LazyComponent<Record<string, unknown>>>> = {
    '/': [], // Add home route components
    '/profile': [], // Add profile route components
    '/messages': [], // Add messages route components
    // Add more routes
  };

  const components = routeComponents[route] || [];
  components.forEach(component => {
    if (component.preload) {
      loadQueue.add(component.preload, component.priority);
    }
  });
}

/**
 * Component that preloads when visible
 */
export function PreloadOnView({
  component,
  children,
  threshold = 0.1,
  rootMargin = '50px'
}: {
  component: LazyComponent<Record<string, unknown>>;
  children: React.ReactNode;
  threshold?: number;
  rootMargin?: string;
}) {
  const ref = React.useRef<HTMLDivElement>(null);
  const entry = useIntersectionObserver(ref, { threshold, rootMargin });
  
  useEffect(() => {
    if (entry?.isIntersecting && component.preload) {
      component.preload();
    }
  }, [entry?.isIntersecting, component]);

  return <div ref={ref}>{children}</div>;
}

/**
 * Preload components based on user interaction
 */
export function preloadOnInteraction(
  component: LazyComponent<Record<string, unknown>>,
  element: HTMLElement | null
) {
  if (!element || !component.preload) return;

  const handlers = {
  mouseenter: () => component.preload();
  focus: () => component.preload();
  touchstart: () => component.preload();
  };

  Object.entries(handlers).forEach(([event,handler]) => {
    element.addEventListener(event, handler, { once: true, passive: true });
  });

  return () => {
    Object.entries(handlers).forEach(([event,handler]) => {
      element.removeEventListener(event, handler);
    });
  };
}

/**
 * Batch preload multiple components
 */
export async function batchPreload(
  components: LazyComponent<Record<string,unknown>>[],
  options: {
    parallel?: boolean;
    onProgress?: (loaded: number, total: number) => void;
  } = {}
) {
  const { parallel = true, onProgress } = options;
  let loaded = 0;

  if (parallel) {
    await Promise.all(_components.map(async (component) => {
        try {
          await component.preload();
          loaded++;
          onProgress?.(loaded, components.length);
        } catch (error) {
          console.error('Failed to preload component:', error);
        }
      })
    );
  } else {
    for (const component of components) {
      try {
        await component.preload();
        loaded++;
        onProgress?.(loaded, components.length);
      } catch (error) {
        console.error('Failed to preload component:', error);
      }
    }
  }
}
