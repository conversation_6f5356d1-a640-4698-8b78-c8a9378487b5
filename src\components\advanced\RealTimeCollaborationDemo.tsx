import React, { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Users, 
  Wifi, 
  WifiOff, 
  <PERSON><PERSON>ointer, 
  MessageCircle, 
  Eye, 
  Settings,
  Play,
  Square,
  RefreshCw,
  User
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui';
import { useRealTimeCollaboration } from '@/hooks/useRealTimeCollaboration';

interface RealTimeCollaborationDemoProps {
  className?: string;
}

const RealTimeCollaborationDemo: React.FC<RealTimeCollaborationDemoProps> = ({ 
  className = '' 
}) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [demoUser, setDemoUser] = useState({
    name: 'Demo User',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=demo'
  });
  const [cursorTrackingEnabled, setCursorTrackingEnabled] = useState(true);
  const [typingIndicatorsEnabled, setTypingIndicatorsEnabled] = useState(true);
  const [scrollSyncEnabled, setScrollSyncEnabled] = useState(false);
  const [simulateTyping, setSimulateTyping] = useState(false);

  // Real-time collaboration hook
  const {
    isConnected,
    isConnecting,
    localUser,
    connectedUsers,
    typingUsers,
    cursors,
    error,
    connect,
    disconnect,
    setTyping,
    editPost,
    addComment,
    addReaction;
    stats
  } = useRealTimeCollaboration({
    enableCursorTracking: cursorTrackingEnabled,
    enableTypingIndicators: typingIndicatorsEnabled,
    enableScrollSync: scrollSyncEnabled,
    user: demoUser
  });

  const handleConnect = useCallback(() => {
    connect(demoUser);
  }, [connect, demoUser]);

  const handleDisconnect = useCallback(() => {
    disconnect();
  }, [disconnect]);

  const handleSimulateTyping = useCallback(() => {
    if (simulateTyping) {
      setTyping(false);
      setSimulateTyping(false);
    } else {
      setTyping(true);
      setSimulateTyping(true);
      
      // Auto-stop after 3 seconds
      setTimeout(() => {
        setTyping(false);
        setSimulateTyping(false);
      }, 3000);
    }
  }, [simulateTyping, setTyping]);

  const handleSimulateEdit = useCallback(() => {
    editPost('demo-post-1', 'This post was edited in real-time!');
  }, [editPost]);

  const handleSimulateComment = useCallback(() => {
    addComment('demo-post-1', 'Great post! Added via real-time collaboration.');
  }, [addComment]);

  const handleSimulateReaction = useCallback(() => {
    const reactions = ['👍', '❤️', '😊', '🔥', '🎉'];
    const randomReaction = reactions[Math.floor(Math.random() * reactions.length)];
    addReaction('demo-post-1', randomReaction);
  }, [addReaction]);

  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Real-Time Collaboration
            {isConnected ? (
              <Badge variant="default" className="ml-2">
                <Wifi className="h-3 w-3 mr-1" />
                Connected
              </Badge>
            ) : (
              <Badge variant="secondary" className="ml-2">
                <WifiOff className="h-3 w-3 mr-1" />
                Disconnected
              </Badge>
            )}
          </CardTitle>
    </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
            <div className="text-center">
              <div className="text-2xl font-bold">{connectedUsers.length + (isConnected ? 1 : 0)}</div>
              <div className="text-sm text-muted-foreground">Active Users</div>
    </div>
            <div className="text-center">
              <div className="text-2xl font-bold">{cursors.size}</div>
              <div className="text-sm text-muted-foreground">Active Cursors</div>
    </div>
            <div className="text-center">
              <div className="text-2xl font-bold">{typingUsers.length}</div>
              <div className="text-sm text-muted-foreground">Users Typing</div>
    </div>
            <div className="text-center">
              <div className="text-2xl font-bold">{stats.version}</div>
              <div className="text-sm text-muted-foreground">Sync Version</div>
    </div>
          </div>

          {error && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-lg mb-4">
              <p className="text-sm text-red-600">{error}</p>
    </div>
          )}

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Input
                placeholder="Your name"
                value={demoUser.name} onChange={(e) => setDemoUser(prev => ({ ...prev, name: e.target.value }))}, className="w-40"
                disabled={isConnected}
              />
    </div>
            <div className="flex gap-2">
              {!isConnected ? (
                <Button onClick={handleConnect} disabled={isConnecting}>
                  {isConnecting ? (
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Play className="h-4 w-4 mr-2" />
                  )}
                  Connect
                </Button>
              ) : (
                <Button onClick={handleDisconnect} variant="outline">
                  <Square className="h-4 w-4 mr-2" />
                  Disconnect
                </Button>
              )}
            </div>
    </div>
        </CardContent>
    </Card>
      {/* Collaboration Features */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">
            <Eye className="h-4 w-4 mr-2" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="cursors">
            <MousePointer className="h-4 w-4 mr-2" />
            Cursors
          </TabsTrigger>
          <TabsTrigger value="typing">
            <MessageCircle className="h-4 w-4 mr-2" />
            Typing
          </TabsTrigger>
          <TabsTrigger value="settings">
            <Settings className="h-4 w-4 mr-2" />
            Settings
          </TabsTrigger>
    </TabsList>
        <TabsContent value="overview" className="mt-6">
          <div className="grid gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Connected Users</CardTitle>
    </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {localUser && (
                    <div className="flex items-center justify-between p-2 bg-blue-50 rounded-lg">
                      <div className="flex items-center gap-3">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={localUser.avatar} alt={localUser.name} />
                          <AvatarFallback>{localUser.name.slice(0, 2)}</AvatarFallback>
    </Avatar>
                        <div>
                          <p className="font-medium">{localUser.name}</p>
                          <p className="text-xs text-muted-foreground">You</p>
    </div>
                      </div>
                      <div 
                        className="w-3 h-3 rounded-full" 
                        style={{ backgroundColor: localUser.color }}
                      />
    </div>
                  )}
                  
                  {connectedUsers.map((user) => (
                    <div key={user.id} className="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
                      <div className="flex items-center gap-3">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={user.avatar} alt={user.name} />
                          <AvatarFallback>{user.name.slice(0, 2)}</AvatarFallback>
    </Avatar>
                        <div>
                          <p className="font-medium">{user.name}</p>
                          <p className="text-xs text-muted-foreground">
                            Last seen: {formatTimestamp(user.lastSeen)}
                          </p>
    </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {typingUsers.includes(user.id) && (
                          <Badge variant="outline" className="text-xs">
                            Typing...
                          </Badge>
                        )}
                        <div 
                          className="w-3 h-3 rounded-full" 
                          style={{ backgroundColor: user.color }}
                        />
    </div>
                    </div>
                  ))}
                  
                  {connectedUsers.length === 0 && !localUser && (
                    <div className="text-center py-6 text-muted-foreground">
                      <User className="h-8 w-8 mx-auto mb-2 opacity-50" />
                      <p>No users connected</p>
                      <p className="text-xs">Connect to start collaborating</p>
    </div>
                  )}
                </div>
    </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Collaboration Actions</CardTitle>
    </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-3">
                  <Button
                    onClick={handleSimulateTyping} disabled={!isConnected}, variant={simulateTyping ? "default" : "outline"}
                  >
                    {simulateTyping ? 'Stop Typing' : 'Simulate Typing'}
                  </Button>
                  
                  <Button
                    onClick={handleSimulateEdit} disabled={!isConnected}, variant="outline"
                  >
                    Edit Post
                  </Button>
                  
                  <Button
                    onClick={handleSimulateComment} disabled={!isConnected}, variant="outline"
                  >
                    Add Comment
                  </Button>
                  
                  <Button
                    onClick={handleSimulateReaction} disabled={!isConnected}, variant="outline"
                  >
                    Add Reaction
                  </Button>
    </div>
              </CardContent>
    </Card>
          </div>
    </TabsContent>
        <TabsContent value="cursors" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Cursor Tracking</CardTitle>
    </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  Move your mouse to see your cursor position tracked in real-time. 
                  Other users' cursors will appear as colored dots with their names.
                </p>
                
                <div className="relative border-2 border-dashed border-gray-300 rounded-lg h-64 overflow-hidden">
                  <div className="absolute inset-0 flex items-center justify-center text-muted-foreground">
                    <div className="text-center">
                      <MousePointer className="h-8 w-8 mx-auto mb-2 opacity-50" />
                      <p>Mouse tracking area</p>
                      <p className="text-xs">Move your mouse here</p>
    </div>
                  </div>
                  
                  {/* Render other users' cursors */}
                  <AnimatePresence>
                    {Array.from(cursors.entries()).map(([userId, cursor]) => (
                      <motion.div
                        key={userId} initial={{ opacity: 0, scale: 0 }}, animate={{ opacity: 1, scale: 1 }}, exit={{ opacity: 0, scale: 0 }}, className="absolute pointer-events-none z-10"
                        style={{
                          left: Math.min(cursor.x * 0.3, 380), // Scale to fit in container
                          top: Math.min(cursor.y * 0.15, 200)
                        }}
                      >
                        <div 
                          className="w-3 h-3 rounded-full shadow-lg"
                          style={{ backgroundColor: cursor.color }}
                        />
                        <div className="absolute top-4 left-0 bg-black text-white text-xs px-1 py-0.5 rounded whitespace-nowrap">
                          {connectedUsers.find(u => u.id === userId)?.name || 'Unknown'}
                        </div>
                      </motion.div>
                    ))}
                  </AnimatePresence>
    </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center p-3 bg-blue-50 rounded-lg">
                    <div className="text-2xl font-bold">{cursors.size}</div>
                    <div className="text-sm text-muted-foreground">Active Cursors</div>
    </div>
                  <div className="text-center p-3 bg-green-50 rounded-lg">
                    <div className="text-2xl font-bold">{cursorTrackingEnabled ? 'On' : 'Off'}</div>
                    <div className="text-sm text-muted-foreground">Cursor Tracking</div>
    </div>
                </div>
    </div>
            </CardContent>
    </Card>
        </TabsContent>

        <TabsContent value="typing" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Typing Indicators</CardTitle>
    </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  See when other users are typing in real-time. Typing indicators automatically 
                  disappear after 3 seconds of inactivity.
                </p>

                <div className="border rounded-lg p-4 min-h-32">
                  <div className="space-y-2">
                    {typingUsers.length > 0 ? (
                      typingUsers.map(userId => {
                        const user = connectedUsers.find(u => u.id === userId);
                        return user ? (
                          <motion.div
                            key={userId} initial={{ opacity: 0, y: 10 }}, animate={{ opacity: 1, y: 0 }}, exit={{ opacity: 0, y: -10 }}, className="flex items-center gap-2 text-sm"
                          >
                            <div 
                              className="w-2 h-2 rounded-full animate-pulse"
                              style={{ backgroundColor: user.color }}
                            />
                            <span className="font-medium">{user.name}</span>
                            <span className="text-muted-foreground">is typing...</span>
                          </motion.div>
                        ) : null;
                      })
                    ) : (
                      <div className="text-center py-4 text-muted-foreground">
                        <MessageCircle className="h-6 w-6 mx-auto mb-2 opacity-50" />
                        <p>No one is typing</p>
    </div>
                    )}
                  </div>
    </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center p-3 bg-purple-50 rounded-lg">
                    <div className="text-2xl font-bold">{typingUsers.length}</div>
                    <div className="text-sm text-muted-foreground">Users Typing</div>
    </div>
                  <div className="text-center p-3 bg-orange-50 rounded-lg">
                    <div className="text-2xl font-bold">{simulateTyping ? 'Yes' : 'No'}</div>
                    <div className="text-sm text-muted-foreground">You're Typing</div>
    </div>
                </div>
    </div>
            </CardContent>
    </Card>
        </TabsContent>

        <TabsContent value="settings" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Collaboration Settings</CardTitle>
    </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="cursor-tracking">Cursor Tracking</Label>
                      <p className="text-sm text-muted-foreground">
                        Share your mouse position with other users
                      </p>
    </div>
                    <Switch
                      id="cursor-tracking"
                      checked={cursorTrackingEnabled} onCheckedChange={setCursorTrackingEnabled}
                    />
    </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="typing-indicators">Typing Indicators</Label>
                      <p className="text-sm text-muted-foreground">
                        Show when users are typing
                      </p>
    </div>
                    <Switch
                      id="typing-indicators"
                      checked={typingIndicatorsEnabled} onCheckedChange={setTypingIndicatorsEnabled}
                    />
    </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="scroll-sync">Scroll Synchronization</Label>
                      <p className="text-sm text-muted-foreground">
                        Sync scroll position with other users
                      </p>
    </div>
                    <Switch
                      id="scroll-sync"
                      checked={scrollSyncEnabled} onCheckedChange={setScrollSyncEnabled}
                    />
    </div>
                </div>

                <div className="pt-4 border-t">
                  <h4 className="font-medium mb-2">Connection Info</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Session ID:</span>
                      <code className="text-xs bg-gray-100 px-1 py-0.5 rounded">
                        {stats.sessionId.slice(0, 8)}...
                      </code>
    </div>
                    <div className="flex justify-between">
                      <span>Connected Users:</span>
                      <span>{stats.userCount}</span>
    </div>
                    <div className="flex justify-between">
                      <span>Sync Version:</span>
                      <span>{stats.version}</span>
    </div>
                    <div className="flex justify-between">
                      <span>Status:</span>
                      <Badge variant={isConnected ? 'default' : 'secondary'}>
                        {isConnected ? 'Connected' : 'Disconnected'}
                      </Badge>
    </div>
                  </div>
    </div>
              </div>
    </CardContent>
          </Card>
    </TabsContent>
      </Tabs>
    </div>
  );
};

export default RealTimeCollaborationDemo;