import React, { useState, useRef } from 'react';
import { Play, Pause } from 'lucide-react';

interface VideoHoverPreviewProps {
  videoUrl: string;
  thumbnailUrl?: string;
  title?: string;
  duration?: string;
  className?: string;
}

export const VideoHoverPreview: React.FC<VideoHoverPreviewProps> = ({
  videoUrl,
  thumbnailUrl,
  title,
  duration,
  className
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);

  const handleMouseEnter = () => {
    setIsHovered(true);
    if (videoRef.current) {
      videoRef.current.play();
      setIsPlaying(true);
    }
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
    if (videoRef.current) {
      videoRef.current.pause();
      videoRef.current.currentTime = 0;
      setIsPlaying(false);
    }
  };

  return (
    <div
      className={`relative overflow-hidden rounded-lg cursor-pointer ${className}`}, onMouseEnter={handleMouseEnter} onMouseLeave={handleMouseLeave}
    >
      {!isHovered && thumbnailUrl && (
        <img
          src={thumbnailUrl} alt={title || 'Video thumbnail'}, className="w-full h-full object-cover"
        />
      )}
      
      <video
        ref={videoRef} src={videoUrl}, muted
        loop
        className={`w-full h-full object-cover transition-opacity duration-300 ${
          isHovered ? 'opacity-100' : 'opacity-0'
        }`}
      />

      {/* Play/Pause overlay */}
      <div className="absolute inset-0 flex items-center justify-center bg-black/20 opacity-0 hover:opacity-100 transition-opacity">
        {isPlaying ? (
          <Pause className="w-12 h-12 text-white" />
        ) : (
          <Play className="w-12 h-12 text-white" />
        )}
      </div>

      {/* Duration badge */}
      {duration && (
        <div className="absolute bottom-2 right-2 bg-black/80 text-white text-xs px-2 py-1 rounded">
          {duration}
        </div>
      )}

      {/* Title overlay */}
      {title && (
        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4">
          <h3 className="text-white text-sm font-medium line-clamp-2">{title}</h3>
    </div>
      )}
    </div>
  );
};

export default VideoHoverPreview;