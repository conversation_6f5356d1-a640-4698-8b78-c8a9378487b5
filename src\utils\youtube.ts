import { 
  Video, 
  VideoThumbnails, 
  VideoContentDetails,
  YouTubeError,
  VideoFilters
} from '@/types/youtube';
import { 
  REGEX_PATTERNS, 
  THUMBNAIL_SIZES, 
  DURATION_CATEGORIES,
  CACHE_KEYS,
  CACHE_TTL
} from '@/constants/youtube';

// Time and Duration Utilities
export const formatDuration = (isoDuration: string): string => {
  const match = isoDuration.match(REGEX_PATTERNS.ISO_DURATION);
  if (!match) return '0:00';

  const hours = parseInt(match[1] || '0', 10);
  const minutes = parseInt(match[2] || '0', 10);
  const seconds = parseInt(match[3] || '0', 10);

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  }
  return `${minutes}:${seconds.toString().padStart(2, '0')}`;
};

export const parseDurationToSeconds = (isoDuration: string): number => {
  const match = isoDuration.match(REGEX_PATTERNS.ISO_DURATION);
  if (!match) return 0;

  const hours = parseInt(match[1] || '0', 10);
  const minutes = parseInt(match[2] || '0', 10);
  const seconds = parseInt(match[3] || '0', 10);

  return hours * 3600 + minutes * 60 + seconds;
};

export const _formatTimeAgo = (dateString: string): string => {
  const date = new Date(dateString);
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  const intervals = [
    { label: 'year', seconds: 31536000 },
    { label: 'month', seconds: 2592000 },
    { label: 'week', seconds: 604800 },
    { label: 'day', seconds: 86400 },
    { label: 'hour', seconds: 3600 },
    { label: 'minute', seconds: 60 },
  ];

  for (const interval of intervals) {
    const count = Math.floor(diffInSeconds / interval.seconds);
    if (count >= 1) {
      return `${count} ${interval.label}${count > 1 ? 's' : ''} ago`;
    }
  }

  return 'Just now';
};

export const formatViewCount = (count: number): string => {
  if (count >= 1000000000) {
    return `${(count / 1000000000).toFixed(1)}B`;
  } else if (count >= 1000000) {
    return `${(count / 1000000).toFixed(1)}M`;
  } else if (count >= 1000) {
    return `${(count / 1000).toFixed(1)}K`;
  }
  return count.toString();
};

export const _formatSubscriberCount = (count: number): string => {
  if (count >= 1000000) {
    return `${(count / 1000000).toFixed(2)}M subscribers`;
  } else if (count >= 1000) {
    return `${(count / 1000).toFixed(1)}K subscribers`;
  }
  return `${count} subscriber${count !== 1 ? 's' : ''}`;
};

// URL and ID Utilities
export const _extractVideoId = (url: string): string | null => {
  const patterns = [
    /(?:youtube\.com\/watch\?v=|youtu\.be\/)([a-zA-Z0-9_-]{11})/,
    /youtube\.com\/embed\/([a-zA-Z0-9_-]{11})/,
    /youtube\.com\/v\/([a-zA-Z0-9_-]{11})/,
  ];

  for (const pattern of patterns) {
    const match = url.match(pattern);
    if (match) return match[1];
  }

  // If it's already a video ID
  if (REGEX_PATTERNS.YOUTUBE_VIDEO_ID.test(url)) {
    return url;
  }

  return null;
};

export const _extractChannelId = (url: string): string | null => {
  const patterns = [
    /youtube\.com\/channel\/([a-zA-Z0-9_-]+)/,
    /youtube\.com\/c\/([a-zA-Z0-9_-]+)/,
    /youtube\.com\/user\/([a-zA-Z0-9_-]+)/,
    /youtube\.com\/@([a-zA-Z0-9_-]+)/
  ];

  for (const pattern of patterns) {
    const match = url.match(pattern);
    if (match) return match[1];
  }

  // If it's already a channel ID
  if (REGEX_PATTERNS.YOUTUBE_CHANNEL_ID.test(url)) {
    return url;
  }

  return null;
};

export const _extractPlaylistId = (url: string): string | null => {
  const match = url.match(/[?&]list=([a-zA-Z0-9_-]+)/);
  return match ? match[1] : null;
};

export const _generateVideoUrl = (videoId: string, startTime?: number): string => {
  const baseUrl = `https://www.youtube.com/watch?v=${videoId}`;
  return startTime ? `${baseUrl}&t=${startTime}s` : baseUrl;
};

export const _generateChannelUrl = (channelId: string): string => {
  return `https://www.youtube.com/channel/${channelId}`;
};

export const _generatePlaylistUrl = (playlistId: string): string => {
  return `https://www.youtube.com/playlist?list=${playlistId}`;
};

// Thumbnail Utilities
export const _getBestThumbnail = (thumbnails: VideoThumbnails, preferredSize: keyof typeof THUMBNAIL_SIZES = 'HIGH'): string => {
  const sizeOrder = [THUMBNAIL_SIZES.MAXRES, THUMBNAIL_SIZES.STANDARD, THUMBNAIL_SIZES.HIGH, THUMBNAIL_SIZES.MEDIUM, THUMBNAIL_SIZES.DEFAULT];
  const startIndex = sizeOrder.indexOf(THUMBNAIL_SIZES[preferredSize]);
  
  for (let i = startIndex; i < sizeOrder.length; i++) {
    const size = sizeOrder[i] as keyof VideoThumbnails;
    if (thumbnails[size]) {
      return thumbnails[size]!.url;
    }
  }
  
  // Fallback to any available thumbnail
  return thumbnails?.default?.url || thumbnails?.medium?.url || thumbnails?.high?.url || '';
};

export const _getThumbnailDimensions = (thumbnails: VideoThumbnails, size: keyof typeof THUMBNAIL_SIZES = 'HIGH'): { width: number, height: number } => {
  const thumbnail = thumbnails[THUMBNAIL_SIZES[size] as keyof VideoThumbnails];
  return thumbnail ? { width: thumbnail.width, height: thumbnail.height } : { width: 320, height: 180 };
};

// Video Classification Utilities
export const getVideoDurationCategory = (contentDetails: VideoContentDetails): keyof typeof DURATION_CATEGORIES => {
  const seconds = parseDurationToSeconds(contentDetails.duration);
  
  if (seconds < 240) return 'SHORT'; // < 4 minutes
  if (seconds <= 1200) return 'MEDIUM'; // 4-20 minutes
  return 'LONG'; // > 20 minutes
};

export const _isVideoHD = (contentDetails: VideoContentDetails): boolean => {
  return contentDetails.definition === 'hd';
};

export const _hasClosedCaptions = (contentDetails: VideoContentDetails): boolean => {
  return contentDetails.caption;
};

export const _isVideo3D = (contentDetails: VideoContentDetails): boolean => {
  return contentDetails.dimension === '3d';
};

// Search and Filter Utilities
export const _filterVideosByDuration = (videos: Video[], category: keyof typeof DURATION_CATEGORIES): Video[] => {
  return videos.filter(video => {
    if (!video || !video.contentDetails || !video.contentDetails.duration) {
      return false;
    }
    return getVideoDurationCategory(video.contentDetails) === category;
  });
};

export const _filterVideosByUploadDate = (videos: Video[], timeframe: string): Video[] => {
  const now = new Date();
  const cutoffDate = new Date();
  
  switch (timeframe) {
    case 'hour':
      cutoffDate.setHours(now.getHours() - 1);
      break;
    case 'today':
      cutoffDate.setHours(0, 0, 0, 0);
      break;
    case 'week':
      cutoffDate.setDate(now.getDate() - 7);
      break;
    case 'month':
      cutoffDate.setMonth(now.getMonth() - 1);
      break;
    case 'year':
      cutoffDate.setFullYear(now.getFullYear() - 1);
      break;
    default:
      return videos;
  }
  
  return videos.filter(video => {
    if (!video || !video.snippet || !video.snippet.publishedAt) {
      return false;
    }
    return new Date(video.snippet.publishedAt) >= cutoffDate;
  });
};

export const _sortVideos = (videos: Video[], sortBy: string): Video[] => {
  // Filter out any undefined/null videos and videos without required properties
  const validVideos = videos.filter(video => 
    video && 
    video.snippet && 
    video.snippet.publishedAt &&
    video.statistics &&
    video.contentDetails
  );
  
  const sorted = [...validVideos];
  
  switch (sortBy) {
    case 'newest':
      return sorted.sort((a, b) => {
        const aDate = new Date(a.snippet.publishedAt).getTime();
        const bDate = new Date(b.snippet.publishedAt).getTime();
        return bDate - aDate;
      });
    case 'oldest':
      return sorted.sort((a, b) => {
        const aDate = new Date(a.snippet.publishedAt).getTime();
        const bDate = new Date(b.snippet.publishedAt).getTime();
        return aDate - bDate;
      });
    case 'popular':
      return sorted.sort((a, b) => {
        const aViews = parseInt(a.statistics?.viewCount?.toString() || '0');
        const bViews = parseInt(b.statistics?.viewCount?.toString() || '0');
        return bViews - aViews;
      });
    case 'title':
      return sorted.sort((a, b) => {
        const aTitle = a.snippet.title || '';
        const bTitle = b.snippet.title || '';
        return aTitle.localeCompare(bTitle);
      });
    case 'trending':
      // Simple trending algorithm based on views and recency
      return sorted.sort((a, b) => {
        const aViews = parseInt(a.statistics?.viewCount?.toString() || '0');
        const bViews = parseInt(b.statistics?.viewCount?.toString() || '0');
        const aTime = Date.now() - new Date(a.snippet.publishedAt).getTime();
        const bTime = Date.now() - new Date(b.snippet.publishedAt).getTime();
        const aScore = aTime > 0 ? aViews / aTime : 0;
        const bScore = bTime > 0 ? bViews / bTime : 0;
        return bScore - aScore;
      });
    case 'duration':
      return sorted.sort((a, b) => {
        const aDuration = a.contentDetails.duration ? parseDurationToSeconds(a.contentDetails.duration) : 0;
        const bDuration = b.contentDetails.duration ? parseDurationToSeconds(b.contentDetails.duration) : 0;
        return bDuration - aDuration;
      });
    default:
      return sorted;
  }
};

// Video filtering utilities
export const _filterVideos = (videos: Video[], filters: VideoFilters): Video[] => {
  return videos.filter(video => {
    // Check if video and required properties exist
    if (!video || !video.snippet || !video.contentDetails) {
      return false;
    }
    
    // Filter by upload date
    if (filters.uploadDate && filters.uploadDate !== 'all') {
      if (!video.snippet.publishedAt) {
        return false;
      }
      
      const now = new Date();
      const videoDate = new Date(video.snippet.publishedAt);
      const cutoffDate = new Date();
      
      switch (filters.uploadDate) {
        case 'today':
          cutoffDate.setHours(0, 0, 0, 0);
          break;
        case 'week':
          cutoffDate.setDate(now.getDate() - 7);
          break;
        case 'month':
          cutoffDate.setMonth(now.getMonth() - 1);
          break;
        case 'year':
          cutoffDate.setFullYear(now.getFullYear() - 1);
          break;
        default:
          return true;
      }
      
      if (videoDate < cutoffDate) {
        return false;
      }
    }
    
    // Filter by duration
    if (filters.duration) {
      if (!video.contentDetails.duration) {
        return false;
      }
      const videoDuration = parseDurationToSeconds(video.contentDetails.duration);
      switch (filters.duration) {
        case 'short':
          if (videoDuration > 240) return false; // > 4 minutes
          break;
        case 'medium':
          if (videoDuration <= 240 || videoDuration > 1200) return false; // 4-20 minutes
          break;
        case 'long':
          if (videoDuration <= 1200) return false; // > 20 minutes
          break;
      }
    }
    
    // Filter by category
    if (filters.category && video.snippet.categoryId !== filters.category) {
      return false;
    }
    
    // Filter by quality
    if (filters.quality && video.contentDetails.definition !== filters.quality) {
      return false;
    }
    
    return true;
  });
};

// Validation Utilities
export const _isValidVideoId = (id: string): boolean => {
  return REGEX_PATTERNS.YOUTUBE_VIDEO_ID.test(id);
};

export const _isValidChannelId = (id: string): boolean => {
  return REGEX_PATTERNS.YOUTUBE_CHANNEL_ID.test(id);
};

export const _isValidPlaylistId = (id: string): boolean => {
  return REGEX_PATTERNS.YOUTUBE_PLAYLIST_ID.test(id);
};

export const _isValidYouTubeUrl = (url: string): boolean => {
  return REGEX_PATTERNS.YOUTUBE_URL.test(url);
};

// Error Handling Utilities
export const _createYouTubeError = (code: number, message: string, details?: unknown): YouTubeError => {
  return {
    code,
    message,
    errors: details ? [{
      domain: 'youtube',
      reason: 'error',
      message: details.toString()
    }] : []
  };
};

export const _isQuotaExceededError = (error: YouTubeError): boolean => {
  return error.code === 403 && error.errors.some(e => e.reason === 'quotaExceeded');
};

export const _isRateLimitError = (error: YouTubeError): boolean => {
  return error.code === 429;
};

// Cache Utilities
export const _generateCacheKey = (baseKey: string, params: Record<string, unknown>): string => {
  const sortedParams = Object.keys(params)
    .sort()
    .map(key => `${key}:${params[key]}`)
    .join('|');
  return `${baseKey}:${sortedParams}`;
};

export const isCacheExpired = (timestamp: number, ttl: number): boolean => {
  return Date.now() - timestamp > ttl;
};

// Analytics Utilities
export const _calculateGrowthRate = (current: number, previous: number): number => {
  if (previous === 0) return current > 0 ? 100 : 0;
  return ((current - previous) / previous) * 100;
};

export const _formatAnalyticsValue = (value: number, type: 'views' | 'subscribers' | 'revenue' | 'percentage'): string => {
  switch (type) {
    case 'views':
      return formatViewCount(value);
    case 'subscribers':
      return formatViewCount(value);
    case 'revenue':
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 0,
        maximumFractionDigits: 2,
      }).format(value);
    case 'percentage':
      return `${value.toFixed(1)}%`;
    default:
      return value.toString();
  }
};

// SEO and Metadata Utilities
export const _generateVideoTitle = (title: string, channelName: string): string => {
  return `${title} - ${channelName} - YouTube`;
};

export const _generateVideoDescription = (description: string, maxLength: number = 160): string => {
  if (description.length <= maxLength) return description;
  return description.substring(0, maxLength - 3) + '...';
};

export const _extractHashtags = (description: string): string[] => {
  const hashtagRegex = /#[a-zA-Z0-9_]+/g;
  return description.match(hashtagRegex) || [];
};

// Accessibility Utilities
export const _generateAltText = (video: Video): string => {
  return `${video.snippet.title} by ${video.snippet.channelTitle}. Duration: ${formatDuration(video.contentDetails.duration)}. ${formatViewCount(video.statistics.viewCount)} views.`;
};

export const _generateAriaLabel = (video: Video): string => {
  return `Play ${video.snippet.title} by ${video.snippet.channelTitle}`;
};

// Performance Utilities
export const _debounce = <T extends (...args: unknown[]) => unknown>(
  func: T,
  delay: number
): ((...args: Parameters<T>) => void) => {
  let timeoutId: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
};

export const _throttle = <T extends (...args: unknown[]) => unknown>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean;
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
};

// Local Storage Utilities
export const _saveToLocalStorage = (key: string, data: unknown): void => {
  try {
    localStorage.setItem(
      key,
      JSON.stringify({
        data,
        timestamp: Date.now(),
      })
    );
  } catch (error) {
    console.warn('Failed to save to localStorage:', error);
  }
};

export const _loadFromLocalStorage = <T>(key: string, ttl: number = CACHE_TTL.MEDIUM): T | null => {
  try {
    const item = localStorage.getItem(key);
    if (!item) return null;
    
    const { data, timestamp } = JSON.parse(item);
    if (isCacheExpired(timestamp, ttl)) {
      localStorage.removeItem(key);
      return null;
    }
    
    return data;
  } catch (error) {
    console.warn('Failed to load from localStorage:', error);
    return null;
  }
};

export const _clearExpiredCache = (): void => {
  Object.values(CACHE_KEYS).forEach(key => {
    const item = localStorage.getItem(key);
    if (item) {
      try {
        const { timestamp } = JSON.parse(item);
        if (isCacheExpired(timestamp, CACHE_TTL.LONG)) {
          localStorage.removeItem(key);
        }
      } catch (_error) {
        localStorage.removeItem(key);
      }
    }
  });
};