import { useState, useEffect } from 'react';

/**
 * WebSocket Fallback and Recovery System
 * Handles HMR WebSocket connection issues gracefully
 */

interface WebSocketConfig {
  maxRetries: number, retryDelay: number, backoffMultiplier: number, maxRetryDelay: number;
}

class WebSocketFallbackManager {
  private static instance: WebSocketFallbackManager;
  private config: WebSocketConfig = {
    maxRetries: 10,
    retryDelay: 1000,
    backoffMultiplier: 1.5,
    maxRetryDelay: 30000,
  };
  private retryCount = 0;
  private isRetrying = false;
  private listeners: Set<() => void> = new Set();

  static getInstance(): WebSocketFallbackManager {
    if (!WebSocketFallbackManager.instance) {
      WebSocketFallbackManager.instance = new WebSocketFallbackManager();
    }
    return WebSocketFallbackManager.instance;
  }

  init(): void {
    if (typeof window === 'undefined' || !window.location) return;

    // Only run in development mode
    if (import.meta.env.PROD) return;

    this.setupWebSocketErrorHandling();
    this.setupPageVisibilityHandler();
  }

  private setupWebSocketErrorHandling(): void {
    // Override the default WebSocket to add better error handling
    const originalWebSocket = window.WebSocket;
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const self = this;

    window.WebSocket = class extends originalWebSocket {
      constructor(url: string | URL, protocols?: string | string[]) {
        super(url, protocols);
        
        this.addEventListener('error', (_event) => {
          console.warn('WebSocket error detected, implementing fallback strategy');
          self.handleWebSocketError();
        });

        this.addEventListener('close', (event) => {
          if (event.code !== 1000) { // Not a normal closure
            console.warn('WebSocket closed unexpectedly, attempting recovery');
            self.handleWebSocketError();
          }
        });
      }
    };

    // Handle unhandled promise rejections from WebSocket
    window.addEventListener('unhandledrejection', (event) => {
      if (event?.reason?.message?.includes('WebSocket')) {
        event.preventDefault();
        console.warn('WebSocket promise rejection handled gracefully');
        this.handleWebSocketError();
      }
    });
  }

  private setupPageVisibilityHandler(): void {
    // Reset retry count when page becomes visible
    document.addEventListener('visibilitychange', () => {
      if (!document.hidden) {
        this.retryCount = 0;
        this.isRetrying = false;
      }
    });
  }

  private handleWebSocketError(): void {
    if (this.isRetrying) return;
    
    this.isRetrying = true;
    
    // Notify listeners about the connection issue
    this.listeners.forEach(listener => listener());

    const delay = Math.min(
      this.config.retryDelay * Math.pow(this.config.backoffMultiplier, this.retryCount),
      this.config.maxRetryDelay
    );

    setTimeout(() => {
      this.attemptRecovery();
    }, delay);
  }

  private attemptRecovery(): void {
    this.retryCount++;

    if (this.retryCount > this.config.maxRetries) {
      console.warn('WebSocket recovery failed after maximum retries. Falling back to polling.');
      this.enablePollingFallback();
      return;
    }

    // Try to reconnect by triggering a page refresh in development
    if (import.meta.env.DEV) {
      console.log(`Attempting WebSocket recovery (attempt ${this.retryCount}/${this.config.maxRetries})`);
      
      // Check if the server is still responsive
      fetch(window.location.origin + '/', {
        method: 'HEAD',
        cache: 'no-cache',
      } as RequestInit)
        .then(() => {
          // Server is responsive, safe to reload
          window.location.reload();
        })
        .catch(() => {
          // Server might be down, wait longer
          this.isRetrying = false;
          setTimeout(() => this.handleWebSocketError(), 5000);
        });
    }
  }

  private enablePollingFallback(): void {
    console.log('Enabling polling fallback for development updates');
    
    // Set up basic polling to check for updates
    const pollForUpdates = () => {
      fetch(window.location.origin + '/', {
        method: 'HEAD',
        cache: 'no-cache',
      })
        .then(response => {
          // If server is back up, refresh to restore WebSocket
          if (response.ok && this.retryCount > this.config.maxRetries) {
            console.log('Server restored, refreshing to restore WebSocket connection');
            window.location.reload();
          }
        })
        .catch(() => {
          // Server still down, continue polling
        });
    };

    // Poll every 10 seconds
    setInterval(pollForUpdates, 10000);
  }

  public addConnectionListener(callback: () => void): void {
    this.listeners.add(callback);
  }

  public removeConnectionListener(callback: () => void): void {
    this.listeners.delete(callback);
  }

  public reset(): void {
    this.retryCount = 0;
    this.isRetrying = false;
  }

  public getStatus(): { retryCount: number, isRetrying: boolean, maxRetries: number } {
    return {
      retryCount: this.retryCount,
      isRetrying: this.isRetrying,
      maxRetries: this.config.maxRetries,
    };
  }
}

// Export singleton
export const webSocketFallback = WebSocketFallbackManager.getInstance();

// Auto-initialize when module loads
if (typeof window !== 'undefined') {
  webSocketFallback.init();
}

/**
 * React hook for WebSocket connection status
 */
export function useWebSocketStatus() {
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'disconnected' | 'retrying'>('connected');
  const [retryCount, setRetryCount] = useState(0);

  useEffect(() => {
    const updateStatus = () => {
      const status = webSocketFallback.getStatus();
      setRetryCount(status.retryCount);
      
      if (status.isRetrying) {
        setConnectionStatus('retrying');
      } else if (status.retryCount > 0) {
        setConnectionStatus('disconnected');
      } else {
        setConnectionStatus('connected');
      }
    };

    webSocketFallback.addConnectionListener(updateStatus);

    return () => {
      webSocketFallback.removeConnectionListener(updateStatus);
    };
  }, []);

  return { connectionStatus, retryCount };
}

export default webSocketFallback;