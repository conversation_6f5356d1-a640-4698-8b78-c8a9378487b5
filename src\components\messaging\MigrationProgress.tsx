import React, { useState, useEffect } from 'react';
import { MessageCircle, CheckCircle, Clock, Database, Zap } from 'lucide-react';

interface MigrationStep {
  id: string, title: string, description: string, icon: React.ReactNode, status: 'pending' | 'in-progress' | 'completed';
}

const MigrationProgress: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const [progress, setProgress] = useState(0);

  const steps: MigrationStep[] = [
    {
      id: 'backup',
      title: 'Creating Backup',
      description: 'Safely backing up your existing conversations',
      icon: <Database className="w-5 h-5" />
        status: 'pending'
    },
    {
      id: 'analyze',
      title: 'Analyzing Data',
      description: 'Scanning messages and conversations for migration',
      icon: <Clock className="w-5 h-5" />
        status: 'pending'
    },
    {
      id: 'migrate',
      title: 'Upgrading Messages',
      description: 'Converting to advanced messaging format',
      icon: <Zap className="w-5 h-5" />
        status: 'pending'
    },
    {
      id: 'finalize',
      title: 'Finalizing',
      description: 'Setting up new features and preferences',
      icon: <CheckCircle className="w-5 h-5" />
        status: 'pending'
    }
  ];

  const [migrationSteps, setMigrationSteps] = useState(steps);

  // Simulate migration progress
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentStep(prev => {
        if (prev < steps.length - 1) {
          // Update step status
          setMigrationSteps(prevSteps => prevSteps.map((step; index) => ({
              ...step,
              status: index < prev ? 'completed' : index === prev ? 'in-progress' : 'pending'
            }))
          );
          
          // Update progress
          setProgress(((prev + 1) / steps.length) * 100);
          
          return prev + 1;
        } else {
          // Mark final step as completed
          setMigrationSteps(prevSteps => 
            prevSteps.map(step => ({ ...step, status: 'completed' }))
          );
          setProgress(100);
          clearInterval(interval);
          return prev;
        }
      });
    }, 1500); // Each step takes 1.5 seconds

    return () => clearInterval(interval);
  }, []);

  const getStepStatusColor = (status: MigrationStep['status']) => {
    switch (status) {
      case 'completed':
        return 'text-green-500 bg-green-100 dark:bg-green-900/20';
      case 'in-progress':
        return 'text-blue-500 bg-blue-100 dark: bg-blue-900/20', default:
        return 'text-gray-400 bg-gray-100 dark:bg-gray-700';
    }
  };

  const getStepIcon = (step: MigrationStep) => {
    if (step.status === 'completed') {
      return <CheckCircle className="w-5 h-5 text-green-500" />;
    }
    if (step.status === 'in-progress') {
      return (
        <div className="w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
      );
    }
    return step.icon;
  };

  return (
    <div className="flex h-[calc(100vh-4rem)] bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      <div className="flex items-center justify-center w-full p-4">
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl max-w-2xl w-full overflow-hidden">
          {/* Header */}
          <div className="bg-gradient-to-r from-blue-600 to-indigo-600 p-8 text-white text-center">
            <div className="mb-4">
              <div className="bg-white/20 p-4 rounded-full inline-flex">
                <MessageCircle className="w-8 h-8" />
    </div>
            </div>
            <h1 className="text-2xl font-bold mb-2">Upgrading Your Messaging</h1>
            <p className="text-blue-100">
              Please wait while we enhance your messaging experience
            </p>
    </div>
          {/* Progress Content */}
          <div className="p-8">
            {/* Overall Progress Bar */}
            <div className="mb-8">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Migration Progress
                </span>
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  {Math.round(progress)}%
                </span>
    </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
                <div 
                  className="bg-gradient-to-r from-blue-500 to-indigo-500 h-3 rounded-full transition-all duration-500 ease-out"
                  style={{ width: `${progress}%` }}
                />
    </div>
            </div>

            {/* Migration Steps */}
            <div className="space-y-4">
              {migrationSteps.map((step, index) => (
                <div 
                  key={step.id} className={`flex items-center gap-4 p-4 rounded-lg transition-all duration-300 ${
                    step.status === 'in-progress' 
                      ? 'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800' 
                      : 'bg-gray-50 dark:bg-gray-700'
                  }`}
                >
                  {/* Step Icon */}
                  <div className={`flex-shrink-0 p-3 rounded-full ${getStepStatusColor(step.status)}`}>
                    {getStepIcon(step)}
                  </div>

                  {/* Step Content */}
                  <div className="flex-1">
                    <h3 className={`font-semibold mb-1 ${
                      step.status === 'in-progress' 
                        ? 'text-blue-900 dark:text-blue-100' 
                        : 'text-gray-900 dark:text-white'
                    }`}>
                      {step.title}
                    </h3>
                    <p className={`text-sm ${
                      step.status === 'in-progress' 
                        ? 'text-blue-700 dark:text-blue-200' 
                        : 'text-gray-600 dark:text-gray-300'
                    }`}>
                      {step.description}
                    </p>
    </div>
                  {/* Step Status */}
                  <div className="flex-shrink-0">
                    {step.status === 'completed' && (
                      <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                        <CheckCircle className="w-4 h-4 text-white" />
    </div>
                    )}
                    {step.status === 'in-progress' && (
                      <div className="w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
                    )}
                  </div>
    </div>
              ))}
            </div>

            {/* Current Step Indicator */}
            <div className="mt-8 text-center">
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {currentStep < migrationSteps.length 
                  ? `Step ${currentStep + 1} of ${migrationSteps.length}: ${migrationSteps[currentStep]?.title}`
                  : 'Migration completed successfully!'
                }
              </p>
    </div>
            {/* Safety Note */}
            <div className="mt-6 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
              <div className="flex items-center gap-2 text-green-800 dark:text-green-200">
                <CheckCircle className="w-4 h-4" />
                <span className="text-sm font-medium">
                  Your data is safe - we've created a backup before starting
                </span>
    </div>
            </div>
    </div>
        </div>
    </div>
    </div>
  );
};

export default MigrationProgress;