import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence, useSpring, useMotionValue } from 'framer-motion';

// Animation variants for different message states
export const messageVariants = {
  initial: {
    opacity: 0,
    y: 20,
    scale: 0.95
  },
  animate: {
    opacity: 1,
    y: 0,
    scale: 1,
    transition: {
      type: 'spring',
      stiffness: 500,
      damping: 30,
      mass: 1
    }
  },
  exit: {
    opacity: 0,
    y: -10,
    scale: 0.95,
    transition: {
      duration: 0.2
    }
  },
  hover: {
    scale: 1.02,
    transition: {
      type: 'spring',
      stiffness: 400,
      damping: 25
    }
  },
  tap: {
    scale: 0.98,
    transition: {
      duration: 0.1
    }
  }
};

// Animation variants for message reactions
export const reactionVariants = {
  initial: {
    scale: 0,
    opacity: 0,
    rotate: -180
  },
  animate: {
    scale: 1,
    opacity: 1,
    rotate: 0,
    transition: {
      type: 'spring',
      stiffness: 600,
      damping: 20,
      delay: 0.1
    }
  },
  exit: {
    scale: 0,
    opacity: 0,
    rotate: 180,
    transition: {
      duration: 0.2
    }
  },
  hover: {
    scale: 1.2,
    transition: {
      type: 'spring',
      stiffness: 400,
      damping: 15
    }
  }
};

// Animation variants for typing indicators
export const typingVariants = {
  initial: {
    opacity: 0,
    y: 10
  },
  animate: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.3,
      ease: 'easeOut'
    }
  },
  exit: {
    opacity: 0,
    y: -10,
    transition: {
      duration: 0.2
    }
  }
};

// Animation variants for conversation list items
export const conversationVariants = {
  initial: {
    opacity: 0,
    x: -20
  },
  animate: {
    opacity: 1,
    x: 0,
    transition: {
      type: 'spring',
      stiffness: 400,
      damping: 25
    }
  },
  exit: {
    opacity: 0,
    x: 20,
    transition: {
      duration: 0.2
    }
  },
  hover: {
    x: 4,
    transition: {
      type: 'spring',
      stiffness: 400,
      damping: 25
    }
  }
};

// Staggered animation for message lists
export const messageListVariants = {
  initial: {
    opacity: 0
  },
  animate: {
    opacity: 1,
    transition: {
      staggerChildren: 0.05,
      delayChildren: 0.1
    }
  },
  exit: {
    opacity: 0,
    transition: {
      staggerChildren: 0.02,
      staggerDirection: -1
    }
  }
};

// Animated message bubble component
interface AnimatedMessageBubbleProps {
  children: React.ReactNode, isOwn: boolean;
  isNew?: boolean;
  onHover?: () => void;
  onTap?: () => void;
  className?: string;
}

export const AnimatedMessageBubble: React.FC<AnimatedMessageBubbleProps> = ({
  children,
  isOwn,
  isNew = false,
  onHover,
  onTap,
  className = ''
}) => {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <motion.div
      variants={messageVariants} initial={isNew ? 'initial' : false}, animate="animate"
      exit="exit"
      whileHover="hover"
      whileTap="tap"
      onHoverStart={() => {
        setIsHovered(true);
        onHover?.();
      }}, onHoverEnd={() => setIsHovered(false)} onTap={onTap}, className={`message-bubble ${isOwn ? 'own' : 'other'} ${className}`}, style={{
        originX: isOwn ? 1 : 0,
        originY: 0.5
      }}
    >
      {children}
      
      {/* Subtle glow effect on hover */}
      <motion.div
        className="absolute inset-0 rounded-lg pointer-events-none"
        initial={{ opacity: 0 }}, animate={{ 
          opacity: isHovered ? 0.1 : 0,
          boxShadow: isHovered 
            ? `0 0 20px ${isOwn ? 'rgba(59, 130, 246, 0.5)' : 'rgba(156, 163, 175, 0.5)'}`
            : '0 0 0px rgba(0, 0, 0, 0)'
        }}, transition={{ duration: 0.2 }}
      />
    </motion.div>
  );
};

// Animated reaction component
interface AnimatedReactionProps {
  emoji: string, count: number, isActive: boolean, onClick: () => void;
  delay?: number;
}

export const AnimatedReaction: React.FC<AnimatedReactionProps> = ({
  emoji,
  count,
  isActive,
  onClick,
  delay = 0
}) => {
  return (
    <motion.button
      variants={reactionVariants} initial="initial"
      animate="animate"
      exit="exit"
      whileHover="hover"
      whileTap={{ scale: 0.9 }}, onClick={onClick} className={`reaction-button ${isActive ? 'active' : ''}`}, style={{ 
        animationDelay: `${delay}ms`,
        transformOrigin: 'center'
      }}
    >
      <motion.span
        className="reaction-emoji"
        animate={isActive ? {
          rotate: [0, -10, 10, -5, 5, 0],
          scale: [1, 1.1, 1]
        } : {}}, transition={{
          duration: 0.5,
          ease: 'easeInOut'
        }}
      >
        {emoji}
      </motion.span>
      {count > 0 && (
        <motion.span
          className="reaction-count"
          initial={{ scale: 0 }}, animate={{ scale: 1 }}, key={count}
        >
          {count}
        </motion.span>
      )}
    </motion.button>
  );
};

// Animated typing indicator
interface AnimatedTypingIndicatorProps {
  users: string[];
  className?: string;
}

export const AnimatedTypingIndicator: React.FC<AnimatedTypingIndicatorProps> = ({
  users,
  className = ''
}) => {
  const dots = [0, 1, 2];

  return (
    <AnimatePresence>
      {users.length > 0 && (
        <motion.div
          variants={typingVariants} initial="initial"
          animate="animate"
          exit="exit"
          className={`typing-indicator ${className}`}
        >
          <div className="typing-text">
            {users.length === 1 
              ? `${users[0]} is typing`
              : `${users.length} people are typing`
            }
          </div>
          <div className="typing-dots">
            {dots.map((dot) => (
              <motion.div
                key={dot} className="typing-dot"
                animate={{
                  y: [0, -8, 0],
                  opacity: [0.4, 1, 0.4]
                }}, transition={{
                  duration: 1.2,
                  repeat: Infinity,
                  delay: dot * 0.2,
                  ease: 'easeInOut'
                }}
              />
            ))}
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

// Animated conversation list item
interface AnimatedConversationItemProps {
  children: React.ReactNode, isActive: boolean;
  isNew?: boolean;
  onClick: () => void;
  className?: string;
}

export const AnimatedConversationItem: React.FC<AnimatedConversationItemProps> = ({
  children,
  isActive,
  isNew = false,
  onClick,
  className = ''
}) => {
  return (
    <motion.div
      variants={conversationVariants} initial={isNew ? 'initial' : false}, animate="animate"
      exit="exit"
      whileHover="hover"
      whileTap={{ scale: 0.98 }}, onClick={onClick} className={`conversation-item ${isActive ? 'active' : ''} ${className}`}
    >
      {children}
      
      {/* Active indicator */}
      <motion.div
        className="active-indicator"
        initial={{ scaleY: 0 }}, animate={{ 
          scaleY: isActive ? 1 : 0,
          opacity: isActive ? 1 : 0
        }}, transition={{
          type: 'spring',
          stiffness: 400,
          damping: 25
        }}
      />
    </motion.div>
  );
};

// Animated message list container
interface AnimatedMessageListProps {
  children: React.ReactNode;
  className?: string;
}

export const AnimatedMessageList: React.FC<AnimatedMessageListProps> = ({
  children,
  className = ''
}) => {
  return (
    <motion.div
      variants={messageListVariants} initial="initial"
      animate="animate"
      exit="exit"
      className={`message-list ${className}`}
    >
      {children}
    </motion.div>
  );
};

// Floating action button with pulse animation
interface FloatingActionButtonProps {
  onClick: () => void; icon: React.ReactNode;
  className?: string;
  pulse?: boolean;
}

export const FloatingActionButton: React.FC<FloatingActionButtonProps> = ({
  onClick,
  icon,
  className = '',
  pulse = false
}) => {
  return (
    <motion.button
      onClick={onClick} className={`floating-action-button ${className}`}, whileHover={{ 
        scale: 1.1,
        boxShadow: '0 8px 25px rgba(0, 0, 0, 0.15)'
      }}, whileTap={{ scale: 0.95 }}, animate={pulse ? {
        scale: [1, 1.05, 1],
        boxShadow: [
          '0 4px 15px rgba(0, 0, 0, 0.1)',
          '0 8px 25px rgba(59, 130, 246, 0.3)',
          '0 4px 15px rgba(0, 0, 0, 0.1)'
        ]
      } : {}}, transition={pulse ? {
        duration: 2,
        repeat: Infinity,
        ease: 'easeInOut'
      } : {
        type: 'spring',
        stiffness: 400,
        damping: 25
      }}
    >
      <motion.div
        animate={pulse ? { rotate: [0, 5, -5, 0] } : {}}, transition={pulse ? {
          duration: 2,
          repeat: Infinity,
          ease: 'easeInOut'
        } : {}}
      >
        {icon}
      </motion.div>
    </motion.button>
  );
};

// Page transition wrapper
interface PageTransitionProps {
  children: React.ReactNode;
  className?: string;
}

export const PageTransition: React.FC<PageTransitionProps> = ({
  children,
  className = ''
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}, animate={{ opacity: 1, y: 0 }}, exit={{ opacity: 0, y: -20 }}, transition={{
        type: 'spring',
        stiffness: 400,
        damping: 25
      }}, className={className}
    >
      {children}
    </motion.div>
  );
};

// Smooth scroll to bottom hook
export const useSmoothScrollToBottom = (dependency: unknown) => {
  const scrollRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (scrollRef.current) {
      scrollRef.current.scrollTo({
        top: scrollRef.current.scrollHeight,
        behavior: 'smooth'
      });
    }
  }, [dependency]);

  return scrollRef;
};

// Spring physics for drag interactions
export const useSpringDrag = () => {
  const x = useMotionValue(0);
  const y = useMotionValue(0);
  
  const springX = useSpring(x, { stiffness: 400, damping: 25 });
  const springY = useSpring(y, { stiffness: 400, damping: 25 });

  return { x, y, springX, springY };
};

export default {
  messageVariants,
  reactionVariants,
  typingVariants,
  conversationVariants,
  messageListVariants,
  AnimatedMessageBubble,
  AnimatedReaction,
  AnimatedTypingIndicator,
  AnimatedConversationItem,
  AnimatedMessageList,
  FloatingActionButton,
  PageTransition,
  useSmoothScrollToBottom,
  useSpringDrag
};