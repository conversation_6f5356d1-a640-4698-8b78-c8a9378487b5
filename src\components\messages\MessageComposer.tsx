import React, { useState, useCallback } from 'react';
import { FileAttachment } from '@/types/messaging';
import VoiceMessageRecorder from './VoiceMessageRecorder';
import QuickVoiceButton from './QuickVoiceButton';
import { Mi<PERSON>, Paperclip, Smile, Send } from 'lucide-react';

interface MessageComposerProps {
  onSend: (message: string) => void; onAttach: () => void; onEmoji: () => void; onAudio: () => void;
  onVoiceMessage?: (attachment: FileAttachment) => void;
}

const MessageComposer: React.FC<MessageComposerProps> = React.memo(({
  onSend,
  onAttach,
  onEmoji,
  onAudio,
  onVoiceMessage
}) => {
  const [message, setMessage] = useState('');
  const [showVoiceRecorder, setShowVoiceRecorder] = useState(false);

  const handleSubmit = useCallback((e: React.FormEvent) => {
    e.preventDefault();
    if (message.trim()) {
      onSend(message);
      setMessage('');
    }
  }, [message, onSend]);

  const handleVoiceRecordingComplete = useCallback((attachment: FileAttachment) => {
    if (onVoiceMessage) {
      onVoiceMessage(attachment);
    }
    setShowVoiceRecorder(false);
  }, [onVoiceMessage]);

  const handleVoiceRecordingCancel = useCallback(() => {
    setShowVoiceRecorder(false);
  }, []);

  const handleAudioClick = useCallback(() => {
    if (onVoiceMessage) {
      setShowVoiceRecorder(true);
    } else {
      onAudio();
    }
  }, [onVoiceMessage, onAudio]);

  const handleMessageChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setMessage(e.target.value);
  }, []);

  if (showVoiceRecorder) {
    return (
      <div className="p-2 border-t">
        <VoiceMessageRecorder
          onRecordingComplete={handleVoiceRecordingComplete} onCancel={handleVoiceRecordingCancel}
        />
    </div>
    );
  }

  return (
    <form 
      onSubmit={handleSubmit} className="flex items-center space-x-2 p-3 border-t bg-white dark:bg-gray-800" 
      data-testid="message-composer"
      role="form"
      aria-label="Message composer"
    >
      <button 
        type="button" 
        onClick={onAttach} className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
        title="Attach file"
        aria-label="Attach file"
      >
        <Paperclip size={20} className="text-gray-600 dark:text-gray-400" />
    </button>
      <input
        type="text"
        value={message} onChange={handleMessageChange}, placeholder="Type a message..."
        className="flex-1 p-3 rounded-full border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:border-blue-500 dark:focus:border-blue-400 focus:outline-none"
        aria-label="Message input"
        aria-describedby="message-help"
      />
      <span id="message-help" className="sr-only">Press Enter to send message</span>
      
      <button 
        type="button" 
        onClick={onEmoji} className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
        title="Add emoji"
        aria-label="Add emoji"
      >
        <Smile size={20} className="text-gray-600 dark:text-gray-400" />
    </button>
      {message.trim() ? (
        <button 
          type="submit" 
          className="p-3 bg-blue-500 hover:bg-blue-600 text-white rounded-full transition-colors"
          title="Send message"
          aria-label="Send message"
        >
          <Send size={20} />
    </button>
      ) : onVoiceMessage ? (
        <QuickVoiceButton 
          onVoiceMessage={onVoiceMessage} className="flex-shrink-0"
        />
      ) : (
        <button 
          type="button" 
          onClick={handleAudioClick} className="p-3 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors"
          title="Record voice message"
          aria-label="Record voice message"
        >
          <Mic size={20} className="text-gray-600 dark:text-gray-400" />
    </button>
      )}
    </form>
  );
});

MessageComposer.displayName = 'MessageComposer';

export default MessageComposer;
export { MessageComposer };
