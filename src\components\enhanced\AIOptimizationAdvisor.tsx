import React, { useState, useMemo, useCallback, memo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Card,
  CardContent
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Brain,
  Lightbulb,
  Target,
  TrendingUp,
  CheckCircle,
  XCircle,
  Star,
  Globe,
  MemoryStick,
  HardDrive,
  RefreshCw,
  ThumbsUp,
  ThumbsDown,
  BookOpen,
  ArrowRight,
  Play
} from 'lucide-react';

// Optimization categories
export enum OptimizationCategory {
  PERFORMANCE = 'performance',
  MEMORY = 'memory',
  NETWORK = 'network',
  RENDERING = 'rendering',
  BUNDLE = 'bundle',
  ACCESSIBILITY = 'accessibility',
  SEO = 'seo',
  SECURITY = 'security'
}

// Priority levels
export enum Priority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

// Implementation difficulty
export enum Difficulty {
  EASY = 'easy',
  MEDIUM = 'medium',
  HARD = 'hard'
}

// Optimization suggestion interface
interface OptimizationSuggestion {
  id: string, title: string, description: string, category: OptimizationCategory, priority: Priority, difficulty: Difficulty, estimatedImpact: number; // 1-100
  estimatedTime: string; // e.g., "5 minutes", "2 hours"
  currentValue?: number;
  targetValue?: number;
  unit?: string;
  codeExample?: string;
  steps: string[], resources: Array<{
    title: string, url: string, type: 'documentation' | 'article' | 'video';
  }>;
  isImplemented: boolean;
  implementedAt?: number;
  feedback?: 'helpful' | 'not-helpful';
}

// Mock AI-generated suggestions
const generateOptimizationSuggestions = (): OptimizationSuggestion[] => [
  {
    id: 'lazy-loading-images',
    title: 'Implement Lazy Loading for Images',
    description: 'Add lazy loading to images to improve initial page load time and reduce bandwidth usage.',
    category: OptimizationCategory.PERFORMANCE,
    priority: Priority.HIGH,
    difficulty: Difficulty.EASY,
    estimatedImpact: 85,
    estimatedTime: '15 minutes',
    currentValue: 2.3,
    targetValue: 1.8,
    unit: 'seconds (load time)',
    codeExample: `// Add loading="lazy" to image elements
<img 
  src="image.jpg" 
  alt="Description" 
  loading="lazy"
  className="transition-opacity duration-300"
/>

// Or use Intersection Observer for custom lazy loading
const useIntersectionObserver = (options) => {
  const [isVisible, setIsVisible] = useState(false);
  const targetRef = useRef(null);
  
  useEffect(() => {
    const observer = new IntersectionObserver(([entry]) => {
      if (entry.isIntersecting) {
        setIsVisible(true);
        observer.disconnect();
      }
    }, options);
    
    if (targetRef.current) {
      observer.observe(targetRef.current);
    }
    
    return () => observer.disconnect();
  }, []);
  
  return [targetRef, isVisible];
};`,
    steps: [
      'Add loading="lazy" attribute to all images',
      'Implement skeleton loading for better UX',
      'Use Intersection Observer for complex cases',
      'Add fade-in transitions for loaded images',
      'Test on slow network connections'
    ],
    resources: [
      {
        title: 'MDN - Lazy Loading Images',
        url: 'https://developer.mozilla.org/en-US/docs/Web/Performance/Lazy_loading',
        type: 'documentation'
      },
      {
        title: 'Web.dev - Lazy Loading Guide',
        url: 'https://web.dev/lazy-loading-images/',
        type: 'article'
      }
    ],
    isImplemented: false
  },
  {
    id: 'code-splitting',
    title: 'Implement Advanced Code Splitting',
    description: 'Break down large bundles into smaller chunks to improve loading performance.',
    category: OptimizationCategory.BUNDLE,
    priority: Priority.HIGH,
    difficulty: Difficulty.MEDIUM,
    estimatedImpact: 90,
    estimatedTime: '1 hour',
    currentValue: 2.4,
    targetValue: 1.2,
    unit: 'MB (bundle size)',
    codeExample: `// Route-based code splitting
const HomePage = lazy(() => import('./pages/Home'));
const ProfilePage = lazy(() => import('./pages/Profile'));

// Component-based code splitting
const HeavyComponent = lazy(() => 
  import('./components/HeavyComponent')
);

// Dynamic imports for features
const loadFeature = async (featureName) => {
  const { default: Feature } = await import(\`./features/\${featureName}\`);
  return Feature;
};`,
    steps: [
      'Analyze current bundle with webpack-bundle-analyzer',
      'Identify heavy components and libraries',
      'Implement route-based code splitting',
      'Add component-based splitting for heavy components',
      'Optimize dynamic imports with preloading',
      'Test loading performance on different connections'
    ],
    resources: [
      {
        title: 'React Code Splitting',
        url: 'https://reactjs.org/docs/code-splitting.html',
        type: 'documentation'
      },
      {
        title: 'Webpack Bundle Analyzer',
        url: 'https://github.com/webpack-contrib/webpack-bundle-analyzer',
        type: 'documentation'
      }
    ],
    isImplemented: false
  },
  {
    id: 'memory-optimization',
    title: 'Optimize Memory Usage',
    description: 'Reduce memory leaks and optimize component re-renders to improve overall performance.',
    category: OptimizationCategory.MEMORY,
    priority: Priority.MEDIUM,
    difficulty: Difficulty.MEDIUM,
    estimatedImpact: 75,
    estimatedTime: '45 minutes',
    currentValue: 78,
    targetValue: 45,
    unit: '% memory usage',
    codeExample: `// Use React.memo to prevent unnecessary re-renders
const OptimizedComponent = React.memo(({ data, onClick }) => {
  return <div onClick={onClick}>{data.name}</div>;
}, (prevProps, nextProps) => {
  return prevProps.data.id === nextProps.data.id;
});

// Clean up event listeners and timers
useEffect(() => {
  const handleScroll = () => { /* ... */ };
  window.addEventListener('scroll', handleScroll);
  
  return () => {
    window.removeEventListener('scroll', handleScroll);
  };
}, []);

// Use useMemo for expensive calculations
const expensiveValue = useMemo(() => {
  return heavyCalculation(data);
}, [data]);`,
    steps: [
      'Audit components for unnecessary re-renders',
      'Add React.memo to pure components',
      'Implement proper cleanup in useEffect',
      'Use useMemo and useCallback appropriately',
      'Profile memory usage with React DevTools',
      'Fix any detected memory leaks'
    ],
    resources: [
      {
        title: 'React Performance Optimization',
        url: 'https://reactjs.org/docs/optimizing-performance.html',
        type: 'documentation'
      }
    ],
    isImplemented: false
  },
  {
    id: 'image-optimization',
    title: 'Optimize Image Formats and Compression',
    description: 'Convert images to modern formats (WebP, AVIF) and implement responsive images.',
    category: OptimizationCategory.NETWORK,
    priority: Priority.HIGH,
    difficulty: Difficulty.EASY,
    estimatedImpact: 80,
    estimatedTime: '30 minutes',
    currentValue: 3.2,
    targetValue: 1.1,
    unit: 'MB (total image size)',
    codeExample: `// Use picture element for modern formats
<picture>
  <source srcSet="image.avif" type="image/avif" />
  <source srcSet="image.webp" type="image/webp" />
  <img src="image.jpg" alt="Description" loading="lazy" />
    </picture>
// Responsive images with srcSet
<img
  src="image-small.jpg"
  srcSet="
    image-small.jpg 480w,
    image-medium.jpg 768w,
    image-large.jpg 1200w
  "
  sizes="
    (max-width: 480px) 100vw,
    (max-width: 768px) 50vw,
    25vw
  "
  alt="Description"
/>`,
    steps: [
      'Audit current image formats and sizes',
      'Convert images to WebP/AVIF formats',
      'Implement responsive image srcSets',
      'Add proper alt texts for accessibility',
      'Set up automatic image optimization pipeline',
      'Test loading on various devices and connections'
    ],
    resources: [
      {
        title: 'Modern Image Formats',
        url: 'https://web.dev/serve-images-webp/',
        type: 'article'
      }
    ],
    isImplemented: false
  },
  {
    id: 'virtual-scrolling',
    title: 'Implement Virtual Scrolling',
    description: 'Use virtual scrolling for large lists to improve rendering performance.',
    category: OptimizationCategory.RENDERING,
    priority: Priority.MEDIUM,
    difficulty: Difficulty.HARD,
    estimatedImpact: 85,
    estimatedTime: '2 hours',
    currentValue: 5000,
    targetValue: 50,
    unit: 'DOM nodes',
    codeExample: `// Virtual scrolling implementation
const VirtualList = ({ items, itemHeight = 50 }) => {
  const [scrollTop, setScrollTop] = useState(0);
  const [containerHeight, setContainerHeight] = useState(400);
  
  const visibleItems = useMemo(() => {
    const startIndex = Math.floor(scrollTop / itemHeight);
    const endIndex = Math.min(
      startIndex + Math.ceil(containerHeight / itemHeight) + 1,
      items.length
    );
    
    return items.slice(startIndex, endIndex).map((item, index) => ({
      ...item,
      index: startIndex + index
    }));
  }, [items, scrollTop, containerHeight, itemHeight]);
  
  return (
    <div
      style={{ height: containerHeight, overflowY: 'auto' }}, onScroll={(e) => setScrollTop(e.target.scrollTop)}
    >
      <div style={{ height: items.length * itemHeight }}>
        {visibleItems.map((item) => (
          <div
            key={item.id} style={{
              position: 'absolute',
              top: item.index * itemHeight,
              height: itemHeight
            }}
          >
            {/* Item content */}
          </div>
        ))}
      </div>
    </div>
  );
};`,
    steps: [
      'Identify components with large lists',
      'Implement virtual scrolling container',
      'Calculate visible items based on scroll position',
      'Add buffer items for smooth scrolling',
      'Optimize scroll event handling',
      'Test with large datasets'
    ],
    resources: [
      {
        title: 'React Virtual Scrolling',
        url: 'https://github.com/bvaughn/react-window',
        type: 'documentation'
      }
    ],
    isImplemented: true,
    implementedAt: Date.now() - 86400000 // 1 day ago
  }
];

interface AIOptimizationAdvisorProps {
  performanceData?: {
    fps: number, memoryUsage: number, bundleSize: number, networkLatency: number;
  };
  onImplementSuggestion?: (suggestionId: string) => void;
  className?: string;
}

const AIOptimizationAdvisor: React.FC<AIOptimizationAdvisorProps> = memo(({
  onImplementSuggestion,
  className = ''
}) => {
  const [suggestions] = useState<OptimizationSuggestion[]>(generateOptimizationSuggestions());
  const [selectedCategory, setSelectedCategory] = useState<OptimizationCategory | 'all'>('all');
  const [selectedSuggestion, setSelectedSuggestion] = useState<OptimizationSuggestion | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);

  // Filter suggestions by category
  const filteredSuggestions = useMemo(() => {
    return selectedCategory === 'all' 
      ? suggestions 
      : suggestions.filter(s => s.category === selectedCategory);
  }, [suggestions, selectedCategory]);

  // Calculate overall optimization score
  const optimizationScore = useMemo(() => {
    const implementedSuggestions = suggestions.filter(s => s.isImplemented);
    const totalImpact = suggestions.reduce((sum, s) => sum + s.estimatedImpact; 0);
    const implementedImpact = implementedSuggestions.reduce((sum, s) => sum + s.estimatedImpact; 0);
    
    return totalImpact > 0 ? Math.round((implementedImpact / totalImpact) * 100) : 0;
  }, [suggestions]);

  // Get priority color
  const getPriorityColor = (priority: Priority) => {
    switch (priority) {
      case Priority.CRITICAL: return 'bg-red-100 text-red-800';
      case Priority.HIGH: return 'bg-orange-100 text-orange-800';
      case Priority.MEDIUM: return 'bg-yellow-100 text-yellow-800';
      case Priority.LOW: return 'bg-blue-100 text-blue-800';
    }
  };

  // Get difficulty color
  const getDifficultyColor = (difficulty: Difficulty) => {
    switch (difficulty) {
      case Difficulty.EASY: return 'bg-green-100 text-green-800';
      case Difficulty.MEDIUM: return 'bg-yellow-100 text-yellow-800';
      case Difficulty.HARD: return 'bg-red-100 text-red-800';
    }
  };

  // Get category icon
  const getCategoryIcon = (category: OptimizationCategory) => {
    switch (category) {
      case OptimizationCategory.PERFORMANCE: return TrendingUp;
      case OptimizationCategory.MEMORY: return MemoryStick;
      case OptimizationCategory.NETWORK: return Globe;
      case OptimizationCategory.RENDERING: return Lightbulb;
      case OptimizationCategory.BUNDLE: return HardDrive;
      case OptimizationCategory.ACCESSIBILITY: return Target;
      case OptimizationCategory.SEO: return Star;
      case OptimizationCategory.SECURITY: return CheckCircle;
    }
  };

  // Simulate AI analysis
  const runAnalysis = useCallback(async () => {
    setIsAnalyzing(true);
    // Simulate analysis time
    await new Promise(resolve => setTimeout(resolve; 2000));
    setIsAnalyzing(false);
  }, []);

  // Handle implementation
  const handleImplement = useCallback((suggestion: OptimizationSuggestion) => {
    onImplementSuggestion?.(suggestion.id);
    // In a real app, this would trigger the implementation process
  }, [onImplementSuggestion]);

  // Handle feedback
  const handleFeedback = useCallback((suggestionId: string, feedback: 'helpful' | 'not-helpful') => {
    // Update suggestion feedback
    console.log('Feedback:', suggestionId, feedback);
  }, []);

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Brain className="w-6 h-6 text-purple-600" />
          <div>
            <h3 className="text-lg font-semibold">AI Optimization Advisor</h3>
            <p className="text-sm text-gray-600">Intelligent performance recommendations</p>
    </div>
        </div>
        <Button
          onClick={runAnalysis} disabled={isAnalyzing}, className="bg-purple-600 hover:bg-purple-700"
        >
          {isAnalyzing ? (
            <>
              <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
              Analyzing...
            </>
          ) : (
            <>
              <Brain className="w-4 h-4 mr-2" />
              Run Analysis
            </>
          )}
        </Button>
    </div>
      {/* Optimization Score */}
      <Card className="bg-gradient-to-r from-purple-50 to-blue-50 border-0">
        <CardContent className="p-6">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h4 className="text-lg font-semibold mb-2">Optimization Score</h4>
              <div className="flex items-center space-x-3">
                <div className="text-3xl font-bold text-purple-600">{optimizationScore}%</div>
                <div className="flex items-center space-x-2">
                  <Star className="w-5 h-5 text-yellow-500 fill-current" />
                  <span className="text-sm font-medium">
                    {optimizationScore >= 80 ? 'Excellent' :
                     optimizationScore >= 60 ? 'Good' :
                     optimizationScore >= 40 ? 'Fair' : 'Needs Improvement'}
                  </span>
    </div>
              </div>
    </div>
            <div className="text-right">
              <div className="text-sm text-gray-600 mb-2">Suggestions</div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-4 h-4 text-green-600" />
                <span className="text-sm">
                  {suggestions.filter(s => s.isImplemented).length} / {suggestions.length} implemented
                </span>
    </div>
            </div>
    </div>
          <Progress value={optimizationScore} className="mt-4" />
    </CardContent>
      </Card>

      {/* Category Filters */}
      <div className="flex flex-wrap gap-2">
        <Button
          variant={selectedCategory === 'all' ? 'default' : 'outline'} size="sm"
          onClick={() => setSelectedCategory('all')}
        >
          All Categories
        </Button>
        {Object.values(OptimizationCategory).map((category) => {
          const Icon = getCategoryIcon(category);
          const count = suggestions.filter(s => s.category === category).length;
          
          return (
            <Button
              key={category} variant={selectedCategory === category ? 'default' : 'outline'}, size="sm"
              onClick={() => setSelectedCategory(category)} className="capitalize"
            >
              <Icon className="w-4 h-4 mr-2" />
              {category} ({count})
            </Button>
          );
        })}
      </div>

      {/* Suggestions List */}
      <div className="grid gap-4">
        {filteredSuggestions.map((suggestion) => (
          <motion.div
            key={suggestion.id} initial={{ opacity: 0, y: 20 }}, animate={{ opacity: 1, y: 0 }}, transition={{ duration: 0.3 }}
          >
            <Card className={suggestion.isImplemented ? 'border-green-200 bg-green-50' : ''}>
              <CardContent className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h4 className="text-lg font-semibold">{suggestion.title}</h4>
                      {suggestion.isImplemented && (
                        <CheckCircle className="w-5 h-5 text-green-600" />
                      )}
                    </div>
                    <p className="text-gray-600 mb-3">{suggestion.description}</p>
                    
                    <div className="flex flex-wrap gap-2 mb-3">
                      <Badge className={getPriorityColor(suggestion.priority)}>
                        {suggestion.priority} priority
                      </Badge>
                      <Badge className={getDifficultyColor(suggestion.difficulty)}>
                        {suggestion.difficulty}
                      </Badge>
                      <Badge variant="secondary">
                        {suggestion.estimatedTime}
                      </Badge>
                      <Badge variant="outline">
                        {suggestion.estimatedImpact}% impact
                      </Badge>
    </div>
                    {suggestion.currentValue && suggestion.targetValue && (
                      <div className="bg-gray-50 p-3 rounded-lg mb-3">
                        <div className="text-sm text-gray-600 mb-1">Expected Improvement</div>
                        <div className="flex items-center space-x-2">
                          <span className="text-red-600 font-medium">
                            {suggestion.currentValue} {suggestion.unit}
                          </span>
                          <ArrowRight className="w-4 h-4 text-gray-400" />
                          <span className="text-green-600 font-medium">
                            {suggestion.targetValue} {suggestion.unit}
                          </span>
    </div>
                      </div>
                    )}
                  </div>

                  <div className="flex flex-col space-y-2 ml-4">
                    {!suggestion.isImplemented ? (
                      <Button
                        onClick={() => handleImplement(suggestion)} size="sm"
                        className="bg-blue-600 hover:bg-blue-700"
                      >
                        <Play className="w-4 h-4 mr-2" />
                        Implement
                      </Button>
                    ) : (
                      <Badge className="bg-green-100 text-green-800">
                        <CheckCircle className="w-4 h-4 mr-1" />
                        Implemented
                      </Badge>
                    )}
                    
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setSelectedSuggestion(suggestion)}
                    >
                      <BookOpen className="w-4 h-4 mr-2" />
                      Details
                    </Button>
    </div>
                </div>

                {/* Quick Preview */}
                {suggestion.steps.length > 0 && (
                  <div className="border-t pt-3">
                    <div className="text-sm font-medium mb-2">Implementation Steps:</div>
                    <ol className="text-sm text-gray-600 space-y-1">
                      {suggestion.steps.slice(0, 3).map((step, index) => (
                        <li key={index} className="flex items-start space-x-2">
                          <span className="text-blue-600 font-medium">{index + 1}.</span>
                          <span>{step}</span>
    </li>
                      ))}
                      {suggestion.steps.length > 3 && (
                        <li className="text-gray-500 italic">
                          +{suggestion.steps.length - 3} more steps...
                        </li>
                      )}
                    </ol>
    </div>
                )}

                {/* Feedback */}
                {suggestion.isImplemented && (
                  <div className="flex items-center justify-between pt-3 border-t">
                    <span className="text-sm text-gray-600">Was this helpful?</span>
                    <div className="flex space-x-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleFeedback(suggestion.id, 'helpful')} className={suggestion.feedback === 'helpful' ? 'text-green-600' : ''}
                      >
                        <ThumbsUp className="w-4 h-4" />
    </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleFeedback(suggestion.id, 'not-helpful')} className={suggestion.feedback === 'not-helpful' ? 'text-red-600' : ''}
                      >
                        <ThumbsDown className="w-4 h-4" />
    </Button>
                    </div>
    </div>
                )}
              </CardContent>
    </Card>
          </motion.div>
        ))}
      </div>

      {/* Detailed View Modal */}
      <AnimatePresence>
        {selectedSuggestion && (
          <motion.div
            initial={{ opacity: 0 }}, animate={{ opacity: 1 }}, exit={{ opacity: 0 }}, className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
            onClick={() => setSelectedSuggestion(null)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}, animate={{ scale: 1, opacity: 1 }}, exit={{ scale: 0.9, opacity: 0 }}, className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-2xl font-bold">{selectedSuggestion.title}</h2>
                  <Button
                    variant="ghost"
                    onClick={() => setSelectedSuggestion(null)}
                  >
                    <XCircle className="w-5 h-5" />
    </Button>
                </div>

                <Tabs defaultValue="overview">
                  <TabsList className="grid w-full grid-cols-3">
                    <TabsTrigger value="overview">Overview</TabsTrigger>
                    <TabsTrigger value="implementation">Implementation</TabsTrigger>
                    <TabsTrigger value="resources">Resources</TabsTrigger>
    </TabsList>
                  <TabsContent value="overview" className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <h4 className="font-semibold mb-2">Details</h4>
                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span className="text-gray-600">Priority:</span>
                            <Badge className={getPriorityColor(selectedSuggestion.priority)}>
                              {selectedSuggestion.priority}
                            </Badge>
    </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Difficulty:</span>
                            <Badge className={getDifficultyColor(selectedSuggestion.difficulty)}>
                              {selectedSuggestion.difficulty}
                            </Badge>
    </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Estimated Time:</span>
                            <span>{selectedSuggestion.estimatedTime}</span>
    </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Impact:</span>
                            <span>{selectedSuggestion.estimatedImpact}%</span>
    </div>
                        </div>
    </div>
                      {selectedSuggestion.currentValue && (
                        <div>
                          <h4 className="font-semibold mb-2">Expected Results</h4>
                          <div className="bg-gray-50 p-3 rounded-lg">
                            <div className="text-sm text-gray-600 mb-2">Current Value</div>
                            <div className="text-lg font-semibold text-red-600 mb-3">
                              {selectedSuggestion.currentValue} {selectedSuggestion.unit}
                            </div>
                            <div className="text-sm text-gray-600 mb-2">Target Value</div>
                            <div className="text-lg font-semibold text-green-600">
                              {selectedSuggestion.targetValue} {selectedSuggestion.unit}
                            </div>
    </div>
                        </div>
                      )}
                    </div>
                    
                    <div>
                      <h4 className="font-semibold mb-2">Description</h4>
                      <p className="text-gray-600">{selectedSuggestion.description}</p>
    </div>
                  </TabsContent>

                  <TabsContent value="implementation" className="space-y-4">
                    <div>
                      <h4 className="font-semibold mb-3">Implementation Steps</h4>
                      <ol className="space-y-3">
                        {selectedSuggestion.steps.map((step, index) => (
                          <li key={index} className="flex items-start space-x-3">
                            <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
                              {index + 1}
                            </div>
                            <span className="flex-1">{step}</span>
    </li>
                        ))}
                      </ol>
    </div>
                    {selectedSuggestion.codeExample && (
                      <div>
                        <h4 className="font-semibold mb-3">Code Example</h4>
                        <pre className="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto text-sm">
                          <code>{selectedSuggestion.codeExample}</code>
    </pre>
                      </div>
                    )}
                  </TabsContent>

                  <TabsContent value="resources" className="space-y-4">
                    <div>
                      <h4 className="font-semibold mb-3">Helpful Resources</h4>
                      <div className="space-y-3">
                        {selectedSuggestion.resources.map((resource, index) => (
                          <div key={index} className="flex items-center space-x-3 p-3 border rounded-lg">
                            <BookOpen className="w-5 h-5 text-blue-600" />
                            <div className="flex-1">
                              <div className="font-medium">{resource.title}</div>
                              <div className="text-sm text-gray-600 capitalize">{resource.type}</div>
    </div>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => window.open(resource.url, '_blank')}
                            >
                              Open
                            </Button>
    </div>
                        ))}
                      </div>
    </div>
                  </TabsContent>
    </Tabs>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Empty State */}
      {filteredSuggestions.length === 0 && (
        <Card>
          <CardContent className="text-center py-12">
            <Lightbulb className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h4 className="text-lg font-semibold mb-2">No suggestions for this category</h4>
            <p className="text-gray-600">
              Try selecting a different category or run a new analysis.
            </p>
    </CardContent>
        </Card>
      )}
    </div>
  );
});

AIOptimizationAdvisor.displayName = 'AIOptimizationAdvisor';

export default AIOptimizationAdvisor;