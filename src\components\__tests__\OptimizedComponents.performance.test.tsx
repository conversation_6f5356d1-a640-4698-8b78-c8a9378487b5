import { useState, useEffect } from 'react';
import { render } from '@testing-library/react';
import { screen, fireEvent } from '@testing-library/dom';
import '@testing-library/jest-dom';

// Import all optimized components
import OptimizedSidebar from '../OptimizedSidebar';
import OptimizedNotificationCenter from '../OptimizedNotificationCenter';
import OptimizedMessagingInterface from '../OptimizedMessagingInterface';
import OptimizedUserProfile from '../OptimizedUserProfile';
import OptimizedFeedSystem from '../OptimizedFeedSystem';

// Mock data generators
const generateMockUser = (id: string) => ({
  id,
  name: `Test User ${id}`,
  username: `testuser${id}`,
  avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${id}`,
  bio: `This is a test bio for user ${id}`,
  isVerified: Math.random() > 0.7,
  isOnline: Math.random() > 0.5,
  followersCount: Math.floor(Math.random() * 10000),
  followingCount: Math.floor(Math.random() * 1000),
  postsCount: Math.floor(Math.random() * 500),
  mutualFriendsCount: Math.floor(Math.random() * 50),
  relationship: 'none' as const,
  location: `City ${id}`,
  website: `https://website${id}.com`,
  joinDate: new Date(2020 + Math.floor(Math.random() * 5), Math.floor(Math.random() * 12), Math.floor(Math.random() * 28)),
  coverPhoto: `https://picsum.photos/800/300?random=${id}`
});

const generateMockPost = (id: string, authorId: string) => ({
  id,
  author: {
    id: authorId,
    name: `Author ${authorId}`,
    username: `author${authorId}`,
    avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${authorId}`,
    isVerified: Math.random() > 0.8
  },
  content: `This is a test post content for post ${id}. It contains some sample text to test rendering performance.`,
  mediaUrls: Math.random() > 0.6 ? [`https://picsum.photos/400/300?random=${id}`] : [],
  imageUrl: Math.random() > 0.6 ? `https://picsum.photos/400/300?random=${id}` : undefined,
  timestamp: new Date(Date.now() - Math.floor(Math.random() * 86400000 * 30)),
  likesCount: Math.floor(Math.random() * 1000),
  commentsCount: Math.floor(Math.random() * 100),
  sharesCount: Math.floor(Math.random() * 50),
  metrics: {
    likes: Math.floor(Math.random() * 1000),
    comments: Math.floor(Math.random() * 100),
    shares: Math.floor(Math.random() * 50),
    views: Math.floor(Math.random() * 5000)
  },
  userInteractions: {
    hasLiked: Math.random() > 0.7,
    hasShared: Math.random() > 0.9,
    hasBookmarked: Math.random() > 0.8
  },
  tags: [`tag${Math.floor(Math.random() * 10)}`, `category${Math.floor(Math.random() * 5)}`],
  isPromoted: Math.random() > 0.9,
  category: ['trending', 'recent', 'following', 'recommended'][Math.floor(Math.random() * 4)] as any
});

const generateMockConversation = (id: string) => ({
  id,
  title: `Conversation ${id}`,
  type: Math.random() > 0.5 ? 'direct' : 'group' as 'direct' | 'group',
  participants: Array.from({ length: Math.floor(Math.random() * 5) + 2 }, (_, i) => ({
    id: `participant${i}`,
    name: `Participant ${i}`,
    avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=conv${i}`,
    isOnline: Math.random() > 0.5
  })),
  lastMessage: {
    id: `lastmsg${id}`,
    senderId: `sender${Math.floor(Math.random() * 5)}`,
    senderName: `Sender ${Math.floor(Math.random() * 5)}`,
    senderAvatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=sender${Math.floor(Math.random() * 5)}`,
    content: `Last message in conversation ${id}`,
    timestamp: new Date(Date.now() - Math.floor(Math.random() * 86400000)),
    isRead: Math.random() > 0.4,
    type: 'text' as 'text' | 'image' | 'file'
  },
  unreadCount: Math.floor(Math.random() * 10),
  isGroup: Math.random() > 0.7,
  messages: Array.from({ length: Math.floor(Math.random() * 50) + 10 }, (_, i) => ({
    id: `msg${i}`,
    senderId: `sender${Math.floor(Math.random() * 5)}`,
    senderName: `Sender ${Math.floor(Math.random() * 5)}`,
    senderAvatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=sender${Math.floor(Math.random() * 5)}`,
    content: `Message ${i} content in conversation ${id}`,
    timestamp: new Date(Date.now() - Math.floor(Math.random() * 86400000 * 7)),
    isRead: Math.random() > 0.3,
    type: 'text' as 'text' | 'image' | 'file'
  }))
});

// Comprehensive Test Suite for Optimized Components
describe('Optimized Components Performance Test Suite', () => {
  // Test data setup
  const mockPosts = Array.from({ length: 100 }, (_, i) => generateMockPost(`post${i}`, `author${i % 10}`));
  const mockConversations = Array.from({ length: 20 }, (_, i) => generateMockConversation(`conv${i}`));
  const mockUserProfile = generateMockUser('profile');

  // Mock callbacks
  const mockCallbacks = {
    onNavigate: jest.fn(),
    onLike: jest.fn(),
    onUnlike: jest.fn(),
    onComment: jest.fn(),
    onShare: jest.fn(),
    onBookmark: jest.fn(),
    onLoadMore: jest.fn(),
    onMarkAsRead: jest.fn(),
    onMarkAllAsRead: jest.fn(),
    onNotificationClick: jest.fn(),
    onConversationSelect: jest.fn(),
    onSendMessage: jest.fn(),
    onUserClick: jest.fn(),
    onFollow: jest.fn(),
    onUnfollow: jest.fn(),
    onMessage: jest.fn(),
    onEditProfile: jest.fn()
  };

  beforeEach(() => {
    jest.clearAllMocks();
    // Mock performance monitoring
    jest.spyOn(console, 'log').mockImplementation(() => {});
    jest.spyOn(console, 'warn').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('OptimizedSidebar Component', () => {
    test('renders without performance issues', async () => {
      const startTime = performance.now();
      
      render(<OptimizedSidebar />);

      const endTime = performance.now();
      const renderTime = endTime - startTime;

      expect(screen.getByText('Home')).toBeInTheDocument();
      expect(renderTime).toBeLessThan(100); // Should render in under 100ms
    });

    test('handles navigation clicks efficiently', async () => {
      render(<OptimizedSidebar />);

      const homeButton = screen.getByText('Home');
      const startTime = performance.now();
      
      fireEvent.click(homeButton);
      
      const endTime = performance.now();
      const interactionTime = endTime - startTime;

      expect(interactionTime).toBeLessThan(10); // Should respond in under 10ms
    });
  });

  describe('OptimizedNotificationCenter Component', () => {
    test('renders notification center efficiently', async () => {
      const startTime = performance.now();
      
      render(
        <OptimizedNotificationCenter
          isOpen={true} onClose={() => {}}
        />
      );

      const endTime = performance.now();
      const renderTime = endTime - startTime;

      expect(renderTime).toBeLessThan(150); // Should render in under 150ms
    });

    test('close functionality works efficiently', async () => {
      const mockOnClose = jest.fn();
      render(
        <OptimizedNotificationCenter
          isOpen={true} onClose={mockOnClose}
        />
      );

      const startTime = performance.now();
      
      // Simulate close action (you may need to adjust selector based on actual implementation)
      fireEvent.keyDown(document, { key: 'Escape' });
      
      const endTime = performance.now();
      const interactionTime = endTime - startTime;

      expect(interactionTime).toBeLessThan(20); // Close should complete in under 20ms
    });
  });

  describe('OptimizedMessagingInterface Component', () => {
    test('renders conversation list efficiently', async () => {
      const startTime = performance.now();
      
      render(
        <OptimizedMessagingInterface
          conversations={mockConversations}
          activeConversationId={mockConversations[0].id}
          messages={[]}
          currentUserId="current"
          onSelectConversation={mockCallbacks.onConversationSelect}
          onSendMessage={mockCallbacks.onSendMessage}
          onMarkAsRead={mockCallbacks.onMarkAsRead}
        />
      );

      const endTime = performance.now();
      const renderTime = endTime - startTime;

      expect(renderTime).toBeLessThan(200); // Should handle 20 conversations in under 200ms
    });

    test('message sending is optimized', async () => {
      render(
        <OptimizedMessagingInterface
          conversations={mockConversations}
          activeConversationId={mockConversations[0].id}
          messages={[]}
          currentUserId="current"
          onSelectConversation={mockCallbacks.onConversationSelect}
          onSendMessage={mockCallbacks.onSendMessage}
          onMarkAsRead={mockCallbacks.onMarkAsRead}
        />
      );

      const startTime = performance.now();
      
      // Simulate message sending interaction
      fireEvent.click(screen.getByRole('button', { name: /send/i }));
      
      const endTime = performance.now();
      const sendTime = endTime - startTime;

      expect(sendTime).toBeLessThan(15); // Message sending should be under 15ms
    });
  });

  describe('OptimizedUserProfile Component', () => {
    test('renders profile with posts efficiently', async () => {
      const startTime = performance.now();
      
      render(
        <OptimizedUserProfile
          profile={mockUserProfile}
          posts={mockPosts.slice(0, 20)}
          isOwnProfile={false}
          onFollow={mockCallbacks.onFollow}
          onUnfollow={mockCallbacks.onUnfollow}
          onMessage={mockCallbacks.onMessage}
        />
      );

      const endTime = performance.now();
      const renderTime = endTime - startTime;

      expect(screen.getByText(mockUserProfile.name)).toBeInTheDocument();
      expect(renderTime).toBeLessThan(250); // Should render profile with 20 posts in under 250ms
    });

    test('profile interactions are optimized', async () => {
      render(
        <OptimizedUserProfile
          profile={mockUserProfile}
          posts={[]}
          isOwnProfile={false}
          onFollow={mockCallbacks.onFollow}
          onUnfollow={mockCallbacks.onUnfollow}
          onMessage={mockCallbacks.onMessage}
        />
      );

      const followButton = screen.getByText('Follow');
      const startTime = performance.now();
      
      fireEvent.click(followButton);
      
      const endTime = performance.now();
      const interactionTime = endTime - startTime;

      expect(mockCallbacks.onFollow).toHaveBeenCalledWith(mockUserProfile.id);
      expect(interactionTime).toBeLessThan(10); // Follow action should be under 10ms
    });
  });

  describe('OptimizedFeedSystem Component', () => {
    test('renders large feed efficiently with virtualization', async () => {
      const startTime = performance.now();
      
      render(
        <OptimizedFeedSystem
          posts={mockPosts}
          isLoading={false}
          hasMore={true}
          onLoadMore={mockCallbacks.onLoadMore}
          onLike={mockCallbacks.onLike}
          onUnlike={mockCallbacks.onUnlike}
          onComment={mockCallbacks.onComment}
          onShare={mockCallbacks.onShare}
          onBookmark={mockCallbacks.onBookmark}
          onUserClick={mockCallbacks.onUserClick}
        />
      );

      const endTime = performance.now();
      const renderTime = endTime - startTime;

      expect(renderTime).toBeLessThan(300); // Should render 100 posts with virtualization in under 300ms
    });

    test('feed filtering is optimized', async () => {
      render(
        <OptimizedFeedSystem
          posts={mockPosts}
          isLoading={false}
          hasMore={true}
          onLoadMore={mockCallbacks.onLoadMore}
          onLike={mockCallbacks.onLike}
          onUnlike={mockCallbacks.onUnlike}
          onComment={mockCallbacks.onComment}
          onShare={mockCallbacks.onShare}
          onBookmark={mockCallbacks.onBookmark}
          onUserClick={mockCallbacks.onUserClick}
        />
      );

      const trendingButton = screen.getByText('Trending');
      const startTime = performance.now();
      
      fireEvent.click(trendingButton);
      
      const endTime = performance.now();
      const filterTime = endTime - startTime;

      expect(filterTime).toBeLessThan(25); // Filtering should complete in under 25ms
    });

    test('search functionality is optimized', async () => {
      render(
        <OptimizedFeedSystem
          posts={mockPosts} isLoading={false}, hasMore={true} onLoadMore={mockCallbacks.onLoadMore}, onLike={mockCallbacks.onLike} onUnlike={mockCallbacks.onUnlike}, onComment={mockCallbacks.onComment} onShare={mockCallbacks.onShare}, onBookmark={mockCallbacks.onBookmark} onUserClick={mockCallbacks.onUserClick}
        />
      );

      const searchInput = screen.getByPlaceholderText(/Search posts/i);
      const startTime = performance.now();
      
      fireEvent.change(searchInput, { target: { value: 'test search' } });
      
      const endTime = performance.now();
      const searchTime = endTime - startTime;

      expect(searchTime).toBeLessThan(30); // Search should complete in under 30ms
    });
  });

  describe('Performance Monitoring Integration', () => {
    test('performance monitoring hooks are working', () => {
      // This test verifies that performance monitoring doesn't interfere with normal operation
      const TestComponent = () => {
        const [rerenderCount, setRerenderCount] = useState(0);
        
        useEffect(() => {
          setRerenderCount(prev => prev + 1);
        }, []);

        return <div>Rerender count: {rerenderCount}</div>;
      };

      const startTime = performance.now();
      render(<TestComponent />);
      const endTime = performance.now();

      expect(endTime - startTime).toBeLessThan(50); // Performance monitoring overhead should be minimal
    });
  });

  describe('Memory Management', () => {
    test('components clean up properly on unmount', () => {
      const { unmount } = render(<OptimizedSidebar />);

      // Simple unmount test
      unmount();
      
      // Test that unmount completes without errors
      expect(true).toBe(true); // Component unmounted successfully
    });
  });
});

// Integration Test for Full Application
describe('Full Application Integration', () => {
  test('optimized components render without errors', async () => {
    const mockPosts = Array.from({ length: 10 }, (_, i) => generateMockPost(`post${i}`, `author${i % 5}`));

    const IntegratedApp = () => {
      return (
        <div className="flex h-screen">
          <OptimizedSidebar />
          <main className="flex-1">
            <OptimizedFeedSystem
              posts={mockPosts}
              isLoading={false}
              hasMore={true}
              onLoadMore={() => {}}
              onLike={() => {}}
              onUnlike={() => {}}
              onComment={() => {}}
              onShare={() => {}}
              onBookmark={() => {}}
              onUserClick={() => {}}
            />
    </main>
        </div>
      );
    };

    const startTime = performance.now();
    render(<IntegratedApp />);
    const renderTime = performance.now() - startTime;

    expect(renderTime).toBeLessThan(500); // Full integration should render in under 500ms
    expect(screen.getByText('Home')).toBeInTheDocument(); // Sidebar rendered
  });
});
