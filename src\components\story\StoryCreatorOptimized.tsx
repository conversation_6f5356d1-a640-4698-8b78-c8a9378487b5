import React, { memo, Suspense } from 'react';
import { Camera, Video, Type, Globe, Clock } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';

// Import refactored components
import StoryPreview from './StoryPreview';
import MediaUpload from './MediaUpload';
import TextStyling from './TextStyling';
import InteractiveFeatures from './InteractiveFeatures';
import StoryErrorBoundary from './StoryErrorBoundary';
import useStoryCreator from './useStoryCreator';

// Import types
import { StoryCreatorProps, DurationOption } from './types';

// Loading component for Suspense
const LoadingSpinner = () => (
  <div className="flex items-center justify-center p-8">
    <div className="w-8 h-8 border-4 border-t-blue-500 border-gray-200 rounded-full animate-spin dark:border-gray-700" />
    </div>
);

const StoryCreatorOptimized: React.FC<StoryCreatorProps> = memo(({ 
  isOpen, 
  onClose, 
  onCreateStory 
}) => {
  // Use the custom hook for all state management and handlers
  const {
    formState,
    selectedMusicTrack,
    setSelectedMusicTrack,
    resetForm,
    handleCreateStory,
    handleTypeChange,
    handlePrivacyChange,
    handleDurationChange,
    handleContentChange,
    handleCustomHoursChange,
    mediaHandlers,
    textHandlers,
    interactiveHandlers,
    privacyOptions,
    isValid;
    validationError
  } = useStoryCreator(onCreateStory);

  // Close handler with cleanup
  const handleClose = () => {
    resetForm();
    onClose();
  };

  // Error handlers
  const handleRetry = () => {
    resetForm();
  };

  const handleGoHome = () => {
    handleClose();
  };

  return (
    <StoryErrorBoundary
      onRetry={handleRetry} onGoHome={handleGoHome}
    >
      <Dialog open={isOpen} onOpenChange={(open) => {
        if (!open) handleClose();
      }}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden p-0">
          <DialogHeader className="p-4 border-b">
            <DialogTitle>Create Story</DialogTitle>
    </DialogHeader>
          <div className="grid grid-cols-1 md:grid-cols-2 h-[80vh]">
            {/* Preview Panel */}
            <Suspense fallback={<LoadingSpinner />}>
              <StoryPreview 
                formState={formState} className="w-full h-full"
              />
    </Suspense>
            {/* Editor Panel */}
            <div className="bg-white p-4 overflow-y-auto dark:bg-gray-800">
              <Tabs 
                value={formState.type} onValueChange={handleTypeChange}, className="w-full"
              >
                <TabsList className="grid w-full grid-cols-3 mb-4">
                  <TabsTrigger value="photo" className="flex items-center space-x-2">
                    <Camera className="w-4 h-4" />
                    <span>Photo</span>
    </TabsTrigger>
                  <TabsTrigger value="video" className="flex items-center space-x-2">
                    <Video className="w-4 h-4" />
                    <span>Video</span>
    </TabsTrigger>
                  <TabsTrigger value="text" className="flex items-center space-x-2">
                    <Type className="w-4 h-4" />
                    <span>Text</span>
    </TabsTrigger>
                </TabsList>

                {/* Photo Tab */}
                <TabsContent value="photo" className="space-y-4">
                  <Suspense fallback={<LoadingSpinner />}>
                    <MediaUpload
                      type="photo"
                      selectedImage={formState.selectedImage} videoUrl=""
                      isUploading={formState.isUploading} onImageSelect={mediaHandlers.handleImageSelect}, onVideoSelect={mediaHandlers.handleVideoSelect} onRemoveMedia={mediaHandlers.handleRemoveMedia}, onUploadStart={mediaHandlers.handleUploadStart} onUploadEnd={mediaHandlers.handleUploadEnd}
                    />
    </Suspense>
                  <div>
                    <label className="block text-sm font-medium mb-2 dark:text-gray-200">
                      Caption (Optional)
                    </label>
                    <Textarea
                      placeholder="Add a caption to your story..."
                      value={formState.content} onChange={(e) => handleContentChange(e.target.value)}, rows={3} className="dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                      maxLength={2000}
                    />
                    <div className="text-xs text-gray-500 mt-1">
                      {formState.content.length}/2000 characters
                    </div>
    </div>
                </TabsContent>

                {/* Video Tab */}
                <TabsContent value="video" className="space-y-4">
                  <Suspense fallback={<LoadingSpinner />}>
                    <MediaUpload
                      type="video"
                      selectedImage={formState.selectedImage} videoUrl={formState.videoUrl}, isUploading={formState.isUploading} onImageSelect={mediaHandlers.handleImageSelect}, onVideoSelect={mediaHandlers.handleVideoSelect} onRemoveMedia={mediaHandlers.handleRemoveMedia}, onUploadStart={mediaHandlers.handleUploadStart} onUploadEnd={mediaHandlers.handleUploadEnd}
                    />
    </Suspense>
                  <div>
                    <label className="block text-sm font-medium mb-2 dark:text-gray-200">
                      Caption (Optional)
                    </label>
                    <Textarea
                      placeholder="Add a caption to your story..."
                      value={formState.content} onChange={(e) => handleContentChange(e.target.value)}, rows={3} className="dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                      maxLength={2000}
                    />
                    <div className="text-xs text-gray-500 mt-1">
                      {formState.content.length}/2000 characters
                    </div>
    </div>
                </TabsContent>

                {/* Text Tab */}
                <TabsContent value="text" className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium mb-2 dark:text-gray-200">
                      Your Story Text
                    </label>
                    <Textarea
                      placeholder="What's on your mind?"
                      value={formState.content} onChange={(e) => handleContentChange(e.target.value)}, rows={5} className="mb-4 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                      maxLength={2000}
                    />
                    <div className="text-xs text-gray-500 mb-4">
                      {formState.content.length}/2000 characters
                    </div>
    </div>
                  <Suspense fallback={<LoadingSpinner />}>
                    <TextStyling
                      background={formState.background} textColor={formState.textColor}, fontSize={formState.fontSize} textAlignment={formState.textAlignment}, onBackgroundChange={textHandlers.handleBackgroundChange} onTextColorChange={textHandlers.handleTextColorChange}, onFontSizeChange={textHandlers.handleFontSizeChange} onTextAlignmentChange={textHandlers.handleTextAlignmentChange}
                    />
    </Suspense>
                </TabsContent>
    </Tabs>
              {/* Interactive Features */}
              <Suspense fallback={<LoadingSpinner />}>
                <InteractiveFeatures
                  formState={formState} onUpdateMusic={interactiveHandlers.handleUpdateMusic}, onUpdatePoll={interactiveHandlers.handleUpdatePoll} onUpdateCountdown={interactiveHandlers.handleUpdateCountdown}, onUpdateQuestion={interactiveHandlers.handleUpdateQuestion} onUpdateARFilters={interactiveHandlers.handleUpdateARFilters}, onUpdateStickers={interactiveHandlers.handleUpdateStickers} selectedMusicTrack={selectedMusicTrack}, onMusicTrackChange={setSelectedMusicTrack} className="mt-6"
                />
    </Suspense>
              {/* Privacy and Duration Settings */}
              <div className="space-y-4 mt-6 pt-4 border-t dark:border-gray-700">
                <div>
                  <label className="block text-sm font-medium mb-2 flex items-center dark:text-gray-200">
                    <Globe className="w-4 h-4 mr-2" />
                    Privacy
                  </label>
                  <Select 
                    value={formState.privacy} onValueChange={handlePrivacyChange}
                  >
                    <SelectTrigger className="dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                      <SelectValue placeholder="Who can see your story?" />
    </SelectTrigger>
                    <SelectContent>
                      {privacyOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          <span>{option.label}</span>
    </SelectItem>
                      ))}
                    </SelectContent>
    </Select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2 flex items-center dark:text-gray-200">
                    <Clock className="w-4 h-4 mr-2" />
                    Duration
                  </label>
                  <div className="grid grid-cols-4 gap-2 mb-2">
                    {(['24h', '12h', '6h', 'custom'] as DurationOption[]).map((duration) => (
                      <Button
                        key={duration} variant={formState.duration === duration ? 'default' : 'outline'}, size="sm"
                        onClick={() => handleDurationChange(duration)} className="dark:border-gray-600"
                      >
                        {duration === 'custom' ? 'Custom' : duration}
                      </Button>
                    ))}
                  </div>
                  
                  {formState.duration === 'custom' && (
                    <div className="mt-4">
                      <label className="block text-sm font-medium mb-2 dark:text-gray-200">
                        Custom Duration (hours)
                      </label>
                      <div className="flex items-center space-x-4">
                        <Slider
                          value={[formState.customHours]} onValueChange={(value) => handleCustomHoursChange(value[0])}, min={1} max={48}, step={1} className="flex-1"
                        />
                        <span className="font-medium dark:text-white">{formState.customHours}h</span>
    </div>
                    </div>
                  )}
                </div>
    </div>
              {/* Validation Error Display */}
              {validationError && (
                <div className="mt-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                  <p className="text-sm text-red-600 dark:text-red-400">
                    {validationError}
                  </p>
    </div>
              )}

              {/* Action Buttons */}
              <div className="flex justify-end space-x-3 mt-6 pt-4 border-t dark:border-gray-700">
                <Button 
                  variant="outline" 
                  onClick={handleClose} className="dark:border-gray-600 dark:text-gray-200"
                >
                  Cancel
                </Button>
                <Button 
                  onClick={handleCreateStory} disabled={formState.isUploading || !isValid}, className={!isValid ? 'opacity-50 cursor-not-allowed' : ''}
                >
                  Share to Story
                </Button>
    </div>
            </div>
    </div>
        </DialogContent>
    </Dialog>
    </StoryErrorBoundary>
  );
});

StoryCreatorOptimized.displayName = 'StoryCreatorOptimized';

export default StoryCreatorOptimized;
