import React, { useRef, useCallback, useState, useEffect } from 'react';
import { MessageCircle, Users, Clock, ChevronRight, X } from 'lucide-react';
import { useKeyboardNavigation } from '../../hooks/useKeyboardNavigation';
import { useKeyboardNavigationContext } from './KeyboardNavigationProvider';
import { Conversation } from '../../types/messaging';

interface KeyboardNavigableConversationListProps {
  conversations: Conversation[];
  selectedConversationId?: string;
  onSelectConversation: (conversation: Conversation) => void;
  onCloseConversation?: (conversationId: string) => void;
  className?: string;
}

interface ConversationItemProps {
  conversation: Conversation, isSelected: boolean, onSelect: () => void;
  onClose?: () => void;
  onFocus?: () => void;
}

const ConversationItem: React.FC<ConversationItemProps> = ({
  conversation,
  isSelected,
  onSelect,
  onClose,
  onFocus
}) => {
  const [showCloseButton, setShowCloseButton] = useState(false);

  const formatLastMessageTime = (timestamp: number) => {
    const now = new Date();
    const messageDate = new Date(timestamp);
    const diffInHours = (now.getTime() - messageDate.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      return 'Just now';
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}h ago`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `${diffInDays}d ago`;
    }
  };

  const truncateMessage = (message: string, maxLength: number = 50) => {
    return message.length > maxLength ? `${message.substring(0, maxLength)}...` : message;
  };

  return (
    <div
      className={`conversation-item relative p-3 border-l-2 cursor-pointer transition-all duration-200 ${
        isSelected
          ? 'border-blue-500 bg-blue-50'
          : 'border-transparent hover:bg-gray-50'
      }`}
      data-keyboard-nav
      data-conversation-id={conversation.id} tabIndex={0}, onClick={onSelect} onFocus={onFocus}, onMouseEnter={() => setShowCloseButton(true)} onMouseLeave={() => setShowCloseButton(false)}, role="button"
      aria-selected={isSelected}, aria-label={`Conversation with ${conversation.participants.join(', ')}`}
    >
      <div className="flex items-start gap-3">
        {/* Conversation icon */}
        <div className={`flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center ${
          conversation.type === 'group' ? 'bg-green-100' : 'bg-blue-100'
        }`}>
          {conversation.type === 'group' ? (
            <Users className="w-5 h-5 text-green-600" />
          ) : (
            <MessageCircle className="w-5 h-5 text-blue-600" />
          )}
        </div>

        {/* Conversation details */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between mb-1">
            <h3 className="font-medium text-gray-900 truncate">
              {conversation.title || conversation.participants.join(', ')}
            </h3>
            
            {conversation.lastMessage && (
              <div className="flex items-center gap-1 text-xs text-gray-500">
                <Clock className="w-3 h-3" />
                <span>{formatLastMessageTime(conversation.lastMessage.timestamp)}</span>
    </div>
            )}
          </div>

          {/* Last message preview */}
          {conversation.lastMessage && (
            <p className="text-sm text-gray-600 truncate">
              {truncateMessage(conversation.lastMessage.content)}
            </p>
          )}

          {/* Conversation metadata */}
          <div className="flex items-center justify-between mt-2">
            <div className="flex items-center gap-2 text-xs text-gray-500">
              <span>{conversation.participants.length} participant{conversation.participants.length !== 1 ? 's' : ''}</span>
              {conversation.unreadCount > 0 && (
                <span className="bg-blue-500 text-white px-2 py-0.5 rounded-full text-xs font-medium">
                  {conversation.unreadCount}
                </span>
              )}
            </div>

            {isSelected && (
              <ChevronRight className="w-4 h-4 text-blue-500" />
            )}
          </div>
    </div>
        {/* Close button */}
        {(showCloseButton || isSelected) && onClose && (
          <button
            onClick={(e) => {
              e.stopPropagation();
              onClose();
            }}, className="flex-shrink-0 p-1 rounded hover:bg-gray-200 transition-colors"
            title="Close conversation"
            aria-label="Close conversation"
          >
            <X className="w-4 h-4 text-gray-500" />
    </button>
        )}
      </div>
    </div>
  );
};

export const KeyboardNavigableConversationList: React.FC<KeyboardNavigableConversationListProps> = ({
  conversations,
  selectedConversationId,
  onSelectConversation,
  onCloseConversation,
  className = ''
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const { registerShortcutAction, unregisterShortcutAction } = useKeyboardNavigationContext();
  const [focusedConversationId, setFocusedConversationId] = useState<string | null>(null);

  // Keyboard navigation configuration
  const {
    elementRef,
    isFocused,
    isNavigating,
    lastAction,
    shortcuts,
    focusElement,
    focusNext,
    focusPrevious,
    announceAction;
    addShortcut
  } = useKeyboardNavigation(
    containerRef,
    {
      enableArrowKeys: true,
      enableTabNavigation: true,
      wrapNavigation: true,
      focusOnMount: false
    },
    [
      {
        key: 'Enter',
        action: handleSelectConversation,
        description: 'Open selected conversation'
      },
      {
        key: ' ',
        action: handleSelectConversation,
        description: 'Open selected conversation'
      },
      {
        key: 'Delete',
        action: handleCloseConversation,
        description: 'Close selected conversation'
      },
      {
        key: 'Home',
        action: () => navigateToFirst(); description: 'Go to first conversation'
      },
      {
        key: 'End',
        action: () => navigateToLast(); description: 'Go to last conversation'
      }
    ]
  );

  // Get currently focused conversation
  const focusedConversation = focusedConversationId
    ? conversations.find(c => c.id === focusedConversationId)
    : null;

  // Handle conversation selection
  function handleSelectConversation() {
    if (focusedConversation) {
      onSelectConversation(focusedConversation);
    }
  }

  // Handle conversation closing
  function handleCloseConversation() {
    if (focusedConversation && onCloseConversation) {
      onCloseConversation(focusedConversation.id);
    }
  }

  // Handle conversation focus
  const handleConversationFocus = useCallback((conversation: Conversation) => {
    setFocusedConversationId(conversation.id);
  }, []);

  // Update focused conversation when navigation changes
  useEffect(() => {
    if (currentFocusIndex >= 0 && navigationItems[currentFocusIndex]) {
      const conversationElement = navigationItems[currentFocusIndex].element;
      const conversationId = conversationElement.getAttribute('data-conversation-id');
      if (conversationId) {
        const conversation = conversations.find(c => c.id === conversationId);
        if (conversation) {
          handleConversationFocus(conversation);
        }
      }
    }
  }, [currentFocusIndex, navigationItems, conversations, handleConversationFocus]);

  // Register global shortcuts
  useEffect(() => {
    registerShortcutAction('next-conversation', () => {
      if (isActive) {
        navigateNext();
      } else {
        activate();
        if (conversations.length > 0) {
          focusItem(0);
        }
      }
    });

    registerShortcutAction('prev-conversation', () => {
      if (isActive) {
        navigatePrevious();
      } else {
        activate();
        if (conversations.length > 0) {
          focusItem(conversations.length - 1);
        }
      }
    });

    registerShortcutAction('close-conversation', handleCloseConversation);

    return () => {
      unregisterShortcutAction('next-conversation');
      unregisterShortcutAction('prev-conversation');
      unregisterShortcutAction('close-conversation');
    };
  }, [
    registerShortcutAction,
    unregisterShortcutAction,
    isActive,
    navigateNext,
    navigatePrevious,
    activate,
    focusItem,
    conversations.length,
    handleCloseConversation
  ]);

  // Auto-focus selected conversation
  useEffect(() => {
    if (selectedConversationId && containerRef.current) {
      const selectedElement = containerRef.current.querySelector(
        `[data-conversation-id="${selectedConversationId}"]`
      ) as HTMLElement;
      
      if (selectedElement) {
        selectedElement.scrollIntoView({
          behavior: 'smooth',
          block: 'nearest'
        });
      }
    }
  }, [selectedConversationId]);

  // Handle container click to activate navigation
  const handleContainerClick = useCallback(() => {
    if (!isActive) {
      activate();
    }
  }, [isActive, activate]);

  return (
    <div className={`keyboard-navigable-conversation-list ${className}`}>
      {/* Screen reader announcements */}
      <div className="sr-only" aria-live="polite">
        {isActive && focusedConversation && (
          `Conversation ${conversations.indexOf(focusedConversation) + 1} of ${conversations.length}: ${
            focusedConversation.title || focusedConversation.participants.join(', ')
          }`
        )}
      </div>

      {/* Conversation list container */}
      <div
        ref={containerRef}, data-conversation-list
        className="conversation-list-container max-h-96 overflow-y-auto border border-gray-200 rounded-lg bg-white"
        onClick={handleContainerClick} role="listbox"
        aria-label="Conversation list"
        aria-activedescendant={focusedConversationId || undefined} tabIndex={-1}
      >
        {conversations.length === 0 ? (
          <div className="p-8 text-center text-gray-500">
            <MessageCircle className="w-12 h-12 mx-auto mb-4 text-gray-300" />
            <div className="text-lg mb-2">No conversations</div>
            <div className="text-sm">Start a new conversation to get started</div>
    </div>
        ) : (
          conversations.map((conversation) => (
            <ConversationItem
              key={conversation.id} conversation={conversation}, isSelected={selectedConversationId === conversation.id} onSelect={() => onSelectConversation(conversation)}, onClose={onCloseConversation ? () => onCloseConversation(conversation.id) : undefined} onFocus={() => handleConversationFocus(conversation)}
            />
          ))
        )}
      </div>

      {/* Navigation status */}
      <div className="flex items-center justify-between mt-2 text-sm text-gray-600">
        <div className="flex items-center gap-4">
          <span>
            {conversations.length > 0 && focusedConversation
              ? `${conversations.indexOf(focusedConversation) + 1} of ${conversations.length}`
              : `${conversations.length} conversations`
            }
          </span>
          
          {isActive && (
            <span className="text-blue-600 text-xs">
              Keyboard navigation active
            </span>
          )}
        </div>

        <div className="text-xs text-gray-500">
          Use ↑↓ to navigate, Enter to select, Del to close
        </div>
    </div>
    </div>
  );
};

export default KeyboardNavigableConversationList;