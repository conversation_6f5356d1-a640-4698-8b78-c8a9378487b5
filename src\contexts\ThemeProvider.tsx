import React, { memo } from 'react';
import { storage } from '@/lib/storage';
import { STORAGE_KEYS } from '@/lib/constants';
import { Theme } from './ThemeContext';
import { ThemeContext } from './ThemeContext';

interface ThemeProviderProps {
  children: React.ReactNode;
}

// Simple theme provider without hooks to prevent React dispatcher corruption
export const ThemeProvider: React.FC<ThemeProviderProps> = memo(({ children }) => {
  // Get initial theme without hooks
  const getInitialTheme = (): Theme => {
    try {
      // Check for saved theme preference
      const savedTheme = storage.get<Theme>(STORAGE_KEYS.THEME);
      if (savedTheme) return savedTheme;

      // Check for system preference
      if (
        typeof window !== 'undefined' &&
        window.matchMedia &&
        window.matchMedia('(prefers-color-scheme: dark)').matches
      ) {
        return 'dark';
      }

      return 'light';
    } catch {
      return 'light';
    }
  };

  // Apply theme without hooks
  const applyTheme = (theme: Theme) => {
    if (typeof window === 'undefined') return;

    try {
      // Update localStorage
      storage.set(STORAGE_KEYS.THEME, theme);

      // Apply theme to document
      const root = window.document.documentElement;
      root.classList.remove('light', 'dark');

      if (theme === 'system') {
        const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
        root.classList.add(systemTheme);

        // Update meta theme-color
        const metaThemeColor = document.querySelector('meta[name="theme-color"]');
        if (metaThemeColor) {
          metaThemeColor.setAttribute('content', systemTheme === 'dark' ? '#1f2937' : '#ffffff');
        }
      } else {
        root.classList.add(theme);

        // Update meta theme-color
        const metaThemeColor = document.querySelector('meta[name="theme-color"]');
        if (metaThemeColor) {
          metaThemeColor.setAttribute('content', theme === 'dark' ? '#1f2937' : '#ffffff');
        }
      }
    } catch (error) {
      console.warn('Theme application failed:', error);
    }
  };

  const currentTheme = getInitialTheme();

  // Apply initial theme
  applyTheme(currentTheme);

  // Simple theme setter that doesn't require state management
  const setTheme = (newTheme: Theme) => {
    applyTheme(newTheme);
    // Force re-render by updating a class on body (simple state alternative)
    if (typeof window !== 'undefined') {
      document.body.setAttribute('data-theme', newTheme);
    }
  };

  const contextValue = {
    theme: currentTheme,
    setTheme,
  };

  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
});

ThemeProvider.displayName = 'ThemeProvider';

export default ThemeProvider;
