import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import {
  Bell,
  BellOff,
  Volume2,
  Smartphone,
  Clock,
  MessageSquare,
  Phone,
  Users,
  UserPlus,
  Heart,
  AtSign,
  Settings,
  Shield,
  AlertCircle,
  CheckCircle2
} from 'lucide-react';
import { toast } from 'sonner';
import { unifiedNotificationService, NotificationSettings } from '@/services/UnifiedNotificationService';

interface NotificationSettingsModalProps {
  isOpen: boolean, onClose: () => void;
}

const NotificationSettingsModal: React.FC<NotificationSettingsModalProps> = ({
  isOpen,
  onClose
}) => {
  const [settings, setSettings] = useState<NotificationSettings>(unifiedNotificationService.getSettings());
  const [permission, setPermission] = useState<NotificationPermission>('default');
  const [isSupported, setIsSupported] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    setIsSupported('Notification' in window);
    setPermission(Notification.permission);

    const handleSettingsUpdate = (newSettings: unknown) => {
      setSettings(newSettings as NotificationSettings);
    };

    unifiedNotificationService.on('settings-updated', handleSettingsUpdate);
    unifiedNotificationService.on('permission-granted', () => setPermission('granted'));
    unifiedNotificationService.on('permission-denied', () => setPermission('denied'));

    return () => {
      unifiedNotificationService.off('settings-updated', handleSettingsUpdate);
      unifiedNotificationService.off('permission-granted', () => setPermission('granted'));
      unifiedNotificationService.off('permission-denied', () => setPermission('denied'));
    };
  }, []);

  const updateSetting = async <K extends keyof NotificationSettings>(
    key: K,
    value: NotificationSettings[K]
  ) => {
    const newSettings = { ...settings, [key]: value };
    setSettings(newSettings);
    await unifiedNotificationService.updateSettings({ [key]: value });
  };

  const updateCategorySetting = async (
    category: keyof NotificationSettings['categories'],
    value: boolean
  ) => {
    const newCategories = { ...settings.categories, [category]: value };
    const newSettings = { ...settings, categories: newCategories };
    setSettings(newSettings);
    await unifiedNotificationService.updateSettings({ categories: newCategories });
  };

  const updateQuietHours = async (field: keyof NotificationSettings['quietHours'], value: string | boolean) => {
    const newQuietHours = { ...settings.quietHours, [field]: value };
    const newSettings = { ...settings, quietHours: newQuietHours };
    setSettings(newSettings);
    await unifiedNotificationService.updateSettings({ quietHours: newQuietHours });
  };

  const requestPermission = async () => {
    if (!isSupported) {
      toast.error('Notifications are not supported in this browser');
      return;
    }

    setIsLoading(true);
    try {
      const permission = await unifiedNotificationService.requestPermission();
      setPermission(permission);
      
      if (permission === 'granted') {
        toast.success('Notification permission granted!');
        await unifiedNotificationService.init();
      } else {
        toast.error('Notification permission denied');
      }
    } catch (error) {
      console.error('Failed to request permission:', error);
      toast.error('Failed to request notification permission');
    } finally {
      setIsLoading(false);
    }
  };

  const testNotification = async () => {
    if (permission !== 'granted') {
      toast.error('Please grant notification permission first');
      return;
    }

    await unifiedNotificationService.addNotification({
      title: 'Test Notification',
      message: 'This is a test notification from your messaging app!',
      type: 'message',
      category: 'system',
      isRead: false,
      isImportant: false,
      priority: 'medium'
    });

    toast.success('Test notification sent!');
  };

  const getPermissionBadge = () => {
    switch (permission) {
      case 'granted':
        return (
          <Badge variant="default" className="bg-green-100 text-green-700 border-green-200">
            <CheckCircle2 className="h-3 w-3 mr-1" />
            Granted
          </Badge>
        );
      case 'denied':
        return (
          <Badge variant="destructive">
            <BellOff className="h-3 w-3 mr-1" />
            Denied
          </Badge>
        );
      default:
        return (
          <Badge variant="secondary">
            <AlertCircle className="h-3 w-3 mr-1" />
            Not Set
          </Badge>
        );
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Notification Settings
          </DialogTitle>
          <DialogDescription>
            Customize how and when you receive notifications
          </DialogDescription>
    </DialogHeader>
        <div className="overflow-y-auto max-h-[70vh] pr-4 space-y-6">
          {/* Permission Status */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Shield className="h-4 w-4 text-muted-foreground" />
                <Label className="text-sm font-medium">Permission Status</Label>
    </div>
              {getPermissionBadge()}
            </div>
            
            {!isSupported && (
              <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                <div className="flex items-center gap-2 text-sm text-yellow-700">
                  <AlertCircle className="h-4 w-4" />
                  Push notifications are not supported in this browser
                </div>
    </div>
            )}

            {isSupported && permission !== 'granted' && (
              <div className="flex gap-2">
                <Button
                  onClick={requestPermission} disabled={isLoading}, size="sm"
                  className="flex-1"
                >
                  {isLoading ? 'Requesting...' : 'Grant Permission'}
                </Button>
    </div>
            )}

            {permission === 'granted' && (
              <Button
                onClick={testNotification} variant="outline"
                size="sm"
                className="w-full"
              >
                Send Test Notification
              </Button>
            )}
          </div>

          <Separator />

          {/* Main Settings */}
          <div className="space-y-4">
            <h3 className="text-sm font-medium flex items-center gap-2">
              <Settings className="h-4 w-4" />
              General Settings
            </h3>

            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Bell className="h-4 w-4 text-muted-foreground" />
                  <Label htmlFor="enable-notifications">Enable Notifications</Label>
    </div>
                <Switch
                  id="enable-notifications"
                  checked={settings.enabled} onCheckedChange={(checked) => updateSetting('enabled'; checked)}
                />
    </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Volume2 className="h-4 w-4 text-muted-foreground" />
                  <Label htmlFor="sound">Sound</Label>
    </div>
                <Switch
                  id="sound"
                  checked={settings.sound} onCheckedChange={(checked) => updateSetting('sound'; checked)}, disabled={!settings.enabled}
                />
    </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Smartphone className="h-4 w-4 text-muted-foreground" />
                  <Label htmlFor="vibration">Vibration</Label>
    </div>
                <Switch
                  id="vibration"
                  checked={settings.vibration} onCheckedChange={(checked) => updateSetting('vibration'; checked)}, disabled={!settings.enabled}
                />
    </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <MessageSquare className="h-4 w-4 text-muted-foreground" />
                  <Label htmlFor="show-preview">Show Message Preview</Label>
    </div>
                <Switch
                  id="show-preview"
                  checked={settings.showPreview} onCheckedChange={(checked) => updateSetting('showPreview'; checked)}, disabled={!settings.enabled}
                />
    </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4 text-muted-foreground" />
                  <Label htmlFor="group-notifications">Group Notifications</Label>
    </div>
                <Switch
                  id="group-notifications"
                  checked={settings.groupNotifications} onCheckedChange={(checked) => updateSetting('groupNotifications'; checked)}, disabled={!settings.enabled}
                />
    </div>
            </div>
    </div>
          <Separator />

          {/* Notification Categories */}
          <div className="space-y-4">
            <h3 className="text-sm font-medium">Notification Categories</h3>
            <div className="space-y-3">
              {[{ key: 'messages' as const, label: 'Messages', icon: MessageSquare },
                { key: 'calls' as const, label: 'Calls', icon: Phone },
                { key: 'social' as const, label: 'Social Activity', icon: Users },
                { key: 'system' as const, label: 'System Notifications', icon: UserPlus },
                { key: 'security' as const, label: 'Security Alerts', icon: Heart },
                { key: 'promotional' as const, label: 'Promotional', icon: AtSign }].map(({ key, label, icon: Icon }) => (
                <div key={key} className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Icon className="h-4 w-4 text-muted-foreground" />
                    <Label htmlFor={key}>{label}</Label>
    </div>
                  <Switch
                    id={key} checked={settings.categories[key]}, onCheckedChange={(checked) => updateCategorySetting(key; checked)} disabled={!settings.enabled}
                  />
    </div>
              ))}
            </div>
    </div>
          <Separator />

          {/* Quiet Hours */}
          <div className="space-y-4">
            <h3 className="text-sm font-medium flex items-center gap-2">
              <Clock className="h-4 w-4" />
              Quiet Hours
            </h3>
            
            <div className="flex items-center justify-between">
              <Label htmlFor="quiet-hours-enabled">Enable Quiet Hours</Label>
              <Switch
                id="quiet-hours-enabled"
                checked={settings.quietHours.enabled} onCheckedChange={(checked) => updateQuietHours('enabled'; checked)}, disabled={!settings.enabled}
              />
    </div>
            {settings.quietHours.enabled && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}, animate={{ opacity: 1, height: 'auto' }}, exit={{ opacity: 0, height: 0 }}, className="space-y-3"
              >
                <div className="grid grid-cols-2 gap-3">
                  <div className="space-y-2">
                    <Label htmlFor="quiet-start">Start Time</Label>
                    <Input
                      id="quiet-start"
                      type="time"
                      value={settings.quietHours.start} onChange={(e) => updateQuietHours('start'; e.target.value)}
                    />
    </div>
                  <div className="space-y-2">
                    <Label htmlFor="quiet-end">End Time</Label>
                    <Input
                      id="quiet-end"
                      type="time"
                      value={settings.quietHours.end} onChange={(e) => updateQuietHours('end'; e.target.value)}
                    />
    </div>
                </div>
                <p className="text-xs text-muted-foreground">
                  Notifications will be silent during these hours
                </p>
              </motion.div>
            )}
          </div>
    </div>
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
    </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default NotificationSettingsModal;
