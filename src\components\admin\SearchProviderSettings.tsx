import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { 
  Search, 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  Key,
  ExternalLink,
  Loader2
} from 'lucide-react';
import { unifiedSearchService, SearchProvider } from '@/services/youtube/unifiedSearchService';
import { toast } from 'sonner';

const SearchProviderSettings: React.FC = () => {
  const [currentProvider, setCurrentProvider] = useState<SearchProvider>('youtube-data-v3');
  const [isTestingProvider, setIsTestingProvider] = useState(false);
  const [testResults, setTestResults] = useState<Record<SearchProvider, boolean | null>>({
    'youtube-data-v3': null,
    'google-custom-search': null,
    'mock': null
  });

  // Load current provider on mount
  useEffect(() => {
    const current = unifiedSearchService.getCurrentProvider();
    setCurrentProvider(current);
  }, []);

  // Get provider information
  const providers = unifiedSearchService.getAvailableProviders();
  const currentStatus = unifiedSearchService.getProviderStatus();

  // Handle provider change
  const handleProviderChange = (value: string) => {
    const provider = value as SearchProvider;
    try {
      if (!unifiedSearchService.isProviderAvailable(provider)) {
        toast.error(`${provider} is not available. Please check your API configuration.`);
        return;
      }

      unifiedSearchService.setProvider(provider);
      setCurrentProvider(provider);
      toast.success(`Search provider changed to ${provider}`);
    } catch (error) {
      toast.error(`Failed to change provider: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  // Test a search provider
  const testProvider = async (provider: SearchProvider) => {
    setIsTestingProvider(true);
    setTestResults(prev => ({ ...prev, [provider]: null }));

    try {
      // Temporarily switch to test provider
      const originalProvider = unifiedSearchService.getCurrentProvider();
      
      if (unifiedSearchService.isProviderAvailable(provider)) {
        unifiedSearchService.setProvider(provider);
        
        // Perform a test search
        const testResult = await unifiedSearchService.search({
          q: 'test search',
          maxResults: 1
        });

        const success = testResult.videos.length > 0 || testResult.totalResults > 0;
        setTestResults(prev => ({ ...prev, [provider]: success }));
        
        if (success) {
          toast.success(`${provider} test successful`);
        } else {
          toast.warning(`${provider} test returned no results`);
        }

        // Restore original provider
        unifiedSearchService.setProvider(originalProvider);
      } else {
        setTestResults(prev => ({ ...prev, [provider]: false }));
        toast.error(`${provider} is not configured`);
      }
    } catch (error) {
      setTestResults(prev => ({ ...prev, [provider]: false }));
      toast.error(`${provider} test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsTestingProvider(false);
    }
  };

  // Get provider display information
  const getProviderInfo = (provider: SearchProvider) => {
    switch (provider) {
      case 'youtube-data-v3':
        return {
          name: 'YouTube Data API v3',
          description: 'Official YouTube API with full access to video metadata, statistics, and channel information',
          features: ['Video details', 'Channel info', 'Playlists', 'Statistics', 'Comments'],
          quotaInfo: '10,000 units/day (free tier)',
          setupUrl: 'https://developers.google.com/youtube/v3/getting-started',
          requiredEnvVars: ['VITE_YOUTUBE_API_KEY']
        };
      case 'google-custom-search':
        return {
          name: 'Google Custom Search JSON API',
          description: 'Google Custom Search engine configured to search YouTube videos',
          features: ['Video search', 'Basic metadata', 'Thumbnail images'],
          quotaInfo: '100 searches/day (free tier)',
          setupUrl: 'https://developers.google.com/custom-search/v1/overview',
          requiredEnvVars: ['VITE_GOOGLE_SEARCH_API_KEY', 'VITE_GOOGLE_SEARCH_ENGINE_ID']
        };
      default:
        return {
          name: 'Unknown Provider',
          description: 'Unknown search provider',
          features: [],
          quotaInfo: 'Unknown',
          setupUrl: '',
          requiredEnvVars: []
        };
    }
  };

  // Check if environment variables are configured
  const checkEnvVars = (provider: SearchProvider) => {
    const info = getProviderInfo(provider);
    return info.requiredEnvVars.every(envVar => {
      const value = process.env[envVar];
      return value && value !== `your_${envVar.toLowerCase().replace('vite_', '').replace('', '')}_here`;
    });
  };

  return (
    <div className="space-y-6">
      {/* Current Status */}
      <Card>
        <CardHeader>
          <div className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            <CardTitle>Current Search Provider</CardTitle>
    </div>
          <CardDescription>
            Currently using {currentStatus.name} for YouTube video search
          </CardDescription>
    </CardHeader>
        <CardContent>
          <div className="flex items-center gap-2">
            <Badge variant={currentStatus.available ? "default" : "destructive"}>
              {currentStatus.provider}
            </Badge>
            {currentStatus.available ? (
              <CheckCircle className="h-4 w-4 text-green-500" />
            ) : (
              <XCircle className="h-4 w-4 text-red-500" />
            )}
            <span className="text-sm text-gray-600">
              {currentStatus.available ? 'Available' : 'Not Available'}
            </span>
    </div>
        </CardContent>
    </Card>
      {/* Provider Selection */}
      <Card>
        <CardHeader>
          <CardTitle>Select Search Provider</CardTitle>
          <CardDescription>
            Choose which API to use for YouTube video search functionality
          </CardDescription>
    </CardHeader>
        <CardContent>
          <RadioGroup value={currentProvider} onValueChange={handleProviderChange}>
            <div className="space-y-6">
              {providers.map((provider) => {
                const info = getProviderInfo(provider.provider);
                const isConfigured = checkEnvVars(provider.provider);
                const testResult = testResults[provider.provider];

                return (
                  <div key={provider.provider} className="space-y-3">
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem 
                        value={provider.provider} id={provider.provider}, disabled={!provider.available}
                      />
                      <Label htmlFor={provider.provider} className="flex items-center gap-2">
                        <span className="font-medium">{info.name}</span>
                        {provider.available ? (
                          <CheckCircle className="h-4 w-4 text-green-500" />
                        ) : (
                          <XCircle className="h-4 w-4 text-red-500" />
                        )}
                      </Label>
    </div>
                    <div className="ml-6 space-y-2">
                      <p className="text-sm text-gray-600">{info.description}</p>
                      
                      {/* Features */}
                      <div className="flex flex-wrap gap-1">
                        {info.features.map((feature) => (
                          <Badge key={feature} variant="secondary" className="text-xs">
                            {feature}
                          </Badge>
                        ))}
                      </div>

                      {/* Configuration Status */}
                      <div className="flex items-center gap-2 text-sm">
                        <Key className="h-4 w-4" />
                        <span>Configuration:</span>
                        {isConfigured ? (
                          <Badge variant="default" className="text-xs">Configured</Badge>
                        ) : (
                          <Badge variant="destructive" className="text-xs">Not Configured</Badge>
                        )}
                      </div>

                      {/* Quota Info */}
                      <div className="text-sm text-gray-500">
                        <span className="font-medium">Quota:</span> {info.quotaInfo}
                      </div>

                      {/* Required Environment Variables */}
                      {!isConfigured && (
                        <Alert>
                          <AlertTriangle className="h-4 w-4" />
                          <AlertDescription>
                            <div className="space-y-1">
                              <p>Missing required environment variables:</p>
                              <ul className="list-disc list-inside text-sm">
                                {info.requiredEnvVars.map(envVar => (
                                  <li key={envVar}>{envVar}</li>
                                ))}
                              </ul>
    </div>
                          </AlertDescription>
    </Alert>
                      )}

                      {/* Test and Setup Actions */}
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => testProvider(provider.provider)} disabled={!provider.available || isTestingProvider}
                        >
                          {isTestingProvider ? (
                            <Loader2 className="h-4 w-4 animate-spin mr-2" />
                          ) : (
                            <Search className="h-4 w-4 mr-2" />
                          )}
                          Test
                        </Button>
                        
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => window.open(info.setupUrl, '_blank')}
                        >
                          <ExternalLink className="h-4 w-4 mr-2" />
                          Setup Guide
                        </Button>
    </div>
                      {/* Test Results */}
                      {testResult !== null && (
                        <div className="flex items-center gap-2 text-sm">
                          {testResult ? (
                            <>
                              <CheckCircle className="h-4 w-4 text-green-500" />
                              <span className="text-green-600">Test passed</span>
                            </>
                          ) : (
                            <>
                              <XCircle className="h-4 w-4 text-red-500" />
                              <span className="text-red-600">Test failed</span>
                            </>
                          )}
                        </div>
                      )}
                    </div>
    </div>
                );
              })}
            </div>
    </RadioGroup>
        </CardContent>
    </Card>
      {/* Setup Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>Setup Instructions</CardTitle>
          <CardDescription>
            How to configure your search providers
          </CardDescription>
    </CardHeader>
        <CardContent>
          <div className="space-y-4 text-sm">
            <div>
              <h4 className="font-medium mb-2">YouTube Data API v3:</h4>
              <ol className="list-decimal list-inside space-y-1 text-gray-600">
                <li>Go to <a href="https://console.cloud.google.com/" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">Google Cloud Console</a></li>
                <li>Create a new project or select an existing one</li>
                <li>Enable the YouTube Data API v3</li>
                <li>Create credentials (API Key)</li>
                <li>Add <code className="bg-gray-100 px-1 rounded">VITE_YOUTUBE_API_KEY=your_api_key</code> to your .env.local file</li>
    </ol>
            </div>

            <div>
              <h4 className="font-medium mb-2">Google Custom Search JSON API:</h4>
              <ol className="list-decimal list-inside space-y-1 text-gray-600">
                <li>Go to <a href="https://console.cloud.google.com/" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">Google Cloud Console</a></li>
                <li>Enable the Custom Search JSON API</li>
                <li>Create credentials (API Key)</li>
                <li>Go to <a href="https://cse.google.com/cse/" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">Google Custom Search</a></li>
                <li>Create a new search engine with sites to search: <code className="bg-gray-100 px-1 rounded">youtube.com</code></li>
                <li>Add both keys to your .env.local file:
                  <ul className="list-disc list-inside ml-4 mt-1">
                    <li><code className="bg-gray-100 px-1 rounded">VITE_GOOGLE_SEARCH_API_KEY=your_api_key</code></li>
                    <li><code className="bg-gray-100 px-1 rounded">VITE_GOOGLE_SEARCH_ENGINE_ID=your_search_engine_id</code></li>
    </ul>
                </li>
    </ol>
            </div>
    </div>
        </CardContent>
    </Card>
    </div>
  );
};

export default SearchProviderSettings;