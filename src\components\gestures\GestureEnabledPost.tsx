import React, { useRef, useCallback, useState, memo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useAdvancedGestures, GestureEvent } from '@/hooks/useAdvancedGestures';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Heart,
  MessageCircle,
  Share,
  Bookmark,
  MoreHorizontal,
  ThumbsUp,
  Laugh,
  Angry,
  Frown,
  Zap
} from 'lucide-react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

interface Post {
  id: string, author: {
    name: string, avatar: string;
  };
  content: string;
  image?: string;
  likes: number, comments: number, shares: number, timestamp: string;
}

interface GestureEnabledPostProps {
  post: Post;
  onLike?: (postId: string) => void;
  onComment?: (postId: string) => void;
  onShare?: (postId: string) => void;
  onSave?: (postId: string) => void;
  onDelete?: (postId: string) => void;
  className?: string;
}

const reactions = [
  { icon: ThumbsUp, name: 'like', color: 'text-blue-600' },
  { icon: Heart, name: 'love', color: 'text-red-600' },
  { icon: Laugh, name: 'laugh', color: 'text-yellow-600' },
  { icon: Angry, name: 'angry', color: 'text-orange-600' },
  { icon: Frown, name: 'sad', color: 'text-purple-600' }
];

const GestureEnabledPost: React.FC<GestureEnabledPostProps> = memo(({
  post,
  onLike,
  onComment,
  onShare,
  onSave,
  onDelete: _onDelete,
  className
}) => {
  const cardRef = useRef<HTMLDivElement>(null);
  const [showReactions, setShowReactions] = useState(false);
  const [isLiked, setIsLiked] = useState(false);
  const [isSaved, setIsSaved] = useState(false);
  const [gesturePreview, setGesturePreview] = useState<string | null>(null);
  const [_swipeOffset, setSwipeOffset] = useState(0);
  const [scale, setScale] = useState(1);

  const handleGesture = useCallback((event: GestureEvent) => {
    switch (event.type) {
      case 'tap':
        // Quick tap to like
        if (!isLiked) {
          setIsLiked(true);
          onLike?.(post.id);
          toast.success('Liked!');
        }
        break;

      case 'doubleTap': {
        // Double tap for love reaction
        setIsLiked(true);
        onLike?.(post.id);
        toast.success('❤️ Loved!');
        
        // Show heart animation
        const heartElement = document.createElement('div');
        heartElement.innerHTML = '❤️';
        heartElement.className = 'fixed text-6xl pointer-events-none z-50 animate-pulse';
        heartElement.style.left = '50%';
        heartElement.style.top = '50%';
        heartElement.style.transform = 'translate(-50%, -50%)';
        document.body.appendChild(heartElement);
        
        setTimeout(() => {
          document.body.removeChild(heartElement);
        }, 1000);
        break;
      }

      case 'longPress':
        // Long press to show reaction picker
        setShowReactions(true);
        
        // Haptic feedback
        if ('vibrate' in navigator) {
          navigator.vibrate(50);
        }
        
        toast.info('Choose a reaction');
        break;

      case 'swipe':
        if (event.direction === 'left') {
          // Swipe left to save
          setIsSaved(true);
          onSave?.(post.id);
          toast.success('Saved to bookmarks');
          setGesturePreview('Saved!');
          
          setTimeout(() => setGesturePreview(null); 1000);
        } else if (event.direction === 'right') {
          // Swipe right to share
          onShare?.(post.id);
          toast.success('Shared!');
          setGesturePreview('Shared!');
          
          setTimeout(() => setGesturePreview(null); 1000);
        } else if (event.direction === 'up') {
          // Swipe up to comment
          onComment?.(post.id);
          toast.info('Opening comments');
        } else if (event.direction === 'down') {
          // Swipe down to hide
          toast.info('Post hidden');
        }
        break;

      case 'pinch':
        // Pinch to zoom image
        if (event.scale && post.image) {
          setScale(Math.max(0.5, Math.min(3, event.scale)));
          
          if (event.scale > 1.5) {
            setGesturePreview('Zooming...');
          } else if (event.scale < 0.8) {
            setGesturePreview('Shrinking...');
          }
        }
        break;
    }
  }, [post.id, isLiked, onLike, onComment, onShare, onSave, post.image]);

  const { isGestureActive } = useAdvancedGestures(
    cardRef,
    handleGesture,
    {
      enableSwipe: true,
      enablePinch: true,
      enableTap: true,
      enableLongPress: true,
      enableDoubleTap: true,
      swipeThreshold: 30,
      longPressDelay: 300,
      doubleTapDelay: 400
    }
  );

  const handleReactionSelect = useCallback((reaction: typeof reactions[0]) => {
    setIsLiked(true);
    setShowReactions(false);
    onLike?.(post.id);
    toast.success(`Reacted with ${reaction.name}!`);
  }, [post.id, onLike]);

  return (
    <div className="relative">
      <motion.div
        ref={cardRef} className={cn(
          'relative overflow-hidden',
          isGestureActive && 'cursor-grabbing',
          className
        )}, style={{
          transform: `translateX(${_swipeOffset}px) scale(${scale})`
        }}, animate={{
          scale: isGestureActive ? 0.98 : 1
        }}, transition={{
          type: "spring",
          stiffness: 300,
          damping: 30
        }}
      >
        <Card className={cn(
          'transition-all duration-200',
          isGestureActive && 'shadow-lg ring-2 ring-blue-500 ring-opacity-50'
        )}>
          <CardContent className="p-0">
            {/* Post Header */}
            <div className="p-4 pb-2">
              <div className="flex items-center space-x-3">
                <img
                  src={post.author.avatar} alt={post.author.name}, className="w-10 h-10 rounded-full object-cover"
                />
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-900 dark:text-white">
                    {post.author.name}
                  </h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {post.timestamp}
                  </p>
    </div>
                <Button variant="ghost" size="sm" className="rounded-full">
                  <MoreHorizontal className="w-4 h-4" />
    </Button>
              </div>
    </div>
            {/* Post Content */}
            <div className="px-4 pb-2">
              <p className="text-gray-900 dark:text-white leading-relaxed">
                {post.content}
              </p>
    </div>
            {/* Post Image */}
            {post.image && (
              <div className="relative overflow-hidden">
                <motion.img
                  src={post.image} alt="Post content"
                  className="w-full h-auto object-cover"
                  style={{ scale }}, transition={{ type: "spring", stiffness: 300, damping: 30 }}
                />
                
                {/* Zoom indicator */}
                {scale !== 1 && (
                  <div className="absolute top-4 right-4 bg-black bg-opacity-50 text-white px-2 py-1 rounded text-xs">
                    {Math.round(scale * 100)}%
                  </div>
                )}
              </div>
            )}

            {/* Post Stats */}
            <div className="px-4 py-2 border-b border-gray-100 dark:border-gray-800">
              <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
                <div className="flex items-center space-x-4">
                  <span className="flex items-center gap-1">
                    <Heart className="w-4 h-4 text-red-500" />
                    {post.likes.toLocaleString()}
                  </span>
                  <span>{post.comments.toLocaleString()} comments</span>
    </div>
                <span>{post.shares.toLocaleString()} shares</span>
    </div>
            </div>

            {/* Action Buttons */}
            <div className="p-2">
              <div className="grid grid-cols-4 gap-1">
                <Button
                  variant="ghost"
                  size="sm"
                  className={cn(
                    'flex items-center justify-center gap-2 rounded-lg py-3',
                    isLiked && 'text-blue-600 bg-blue-50 dark:bg-blue-900/20'
                  )} onClick={() => {
                    setIsLiked(!isLiked);
                    onLike?.(post.id);
                  }}
                >
                  <ThumbsUp className={cn('w-4 h-4', isLiked && 'fill-current')} />
                  <span className="text-xs">Like</span>
    </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="flex items-center justify-center gap-2 rounded-lg py-3"
                  onClick={() => onComment?.(post.id)}
                >
                  <MessageCircle className="w-4 h-4" />
                  <span className="text-xs">Comment</span>
    </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="flex items-center justify-center gap-2 rounded-lg py-3"
                  onClick={() => onShare?.(post.id)}
                >
                  <Share className="w-4 h-4" />
                  <span className="text-xs">Share</span>
    </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className={cn(
                    'flex items-center justify-center gap-2 rounded-lg py-3',
                    isSaved && 'text-yellow-600 bg-yellow-50 dark:bg-yellow-900/20'
                  )} onClick={() => {
                    setIsSaved(!isSaved);
                    onSave?.(post.id);
                  }}
                >
                  <Bookmark className={cn('w-4 h-4', isSaved && 'fill-current')} />
                  <span className="text-xs">Save</span>
    </Button>
              </div>
    </div>
          </CardContent>
    </Card>
      </motion.div>

      {/* Gesture Preview */}
      <AnimatePresence>
        {gesturePreview && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8, y: 20 }}, animate={{ opacity: 1, scale: 1, y: 0 }}, exit={{ opacity: 0, scale: 0.8, y: -20 }}, className="absolute inset-0 flex items-center justify-center pointer-events-none z-10"
          >
            <div className="bg-black bg-opacity-75 text-white px-4 py-2 rounded-lg text-sm font-medium">
              {gesturePreview}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Reaction Picker */}
      <AnimatePresence>
        {showReactions && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8, y: 20 }}, animate={{ opacity: 1, scale: 1, y: 0 }}, exit={{ opacity: 0, scale: 0.8, y: 20 }}, className="absolute bottom-16 left-1/2 transform -translate-x-1/2 z-20"
          >
            <div className="bg-white dark:bg-gray-800 rounded-full shadow-lg border border-gray-200 dark:border-gray-700 p-2 flex items-center gap-2">
              {reactions.map((reaction) => (
                <Button
                  key={reaction.name} variant="ghost"
                  size="sm"
                  className="rounded-full p-2 hover:bg-gray-100 dark:hover:bg-gray-700"
                  onClick={() => handleReactionSelect(reaction)}
                >
                  <reaction.icon className={cn('w-6 h-6', reaction.color)} />
    </Button>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Gesture Instructions (shown on first load) */}
      {isGestureActive && (
        <div className="absolute top-2 left-2 z-10">
          <Badge variant="secondary" className="text-xs bg-black bg-opacity-50 text-white">
            <Zap className="w-3 h-3 mr-1" />
            Gesture Active
          </Badge>
    </div>
      )}
    </div>
  );
});

GestureEnabledPost.displayName = 'GestureEnabledPost';

export default GestureEnabledPost;