import React, { useRef, useCallback, memo } from 'react';
import { Camera, Video, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { StoryType } from './types';

interface MediaUploadProps {
  type: StoryType, selectedImage: string, videoUrl: string, isUploading: boolean, onImageSelect: (imageUrl: string) => void; onVideoSelect: (videoUrl: string, thumbnailUrl: string) => void; onRemoveMedia: () => void; onUploadStart: () => void; onUploadEnd: () => void;
  className?: string;
}

const MediaUpload: React.FC<MediaUploadProps> = memo(({
  type,
  selectedImage,
  videoUrl,
  isUploading,
  onImageSelect,
  onVideoSelect,
  onRemoveMedia,
  onUploadStart,
  onUploadEnd,
  className = ''
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const videoInputRef = useRef<HTMLInputElement>(null);

  const handleFileUpload = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Check file size (max 10MB for images)
    if (file.size > 10 * 1024 * 1024) {
      toast.error('File too large. Maximum size is 10MB.');
      return;
    }, onUploadStart();
    
    const reader = new FileReader();
    reader.onload = (e) => {
      const result = e.target?.result as string;
      onImageSelect(result);
      onUploadEnd();
    };
    
    reader.onerror = () => {
      toast.error('Error reading file');
      onUploadEnd();
    };
    
    reader.readAsDataURL(file);
  }, [onImageSelect, onUploadStart, onUploadEnd]);

  const handleVideoUpload = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;
    
    // Check file size (max 50MB for videos)
    if (file.size > 50 * 1024 * 1024) {
      toast.error('File too large. Maximum size is 50MB.');
      return;
    }, onUploadStart();
    
    const videoURL = URL.createObjectURL(file);
    
    // Create thumbnail from video
    const video = document.createElement('video');
    video.src = videoURL;
    video.addEventListener('loadeddata', () => {
      const canvas = document.createElement('canvas');
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;
      const ctx = canvas.getContext('2d');
      
      if (ctx) {
        ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
        const thumbnail = canvas.toDataURL('image/jpeg');
        onVideoSelect(videoURL, thumbnail);
      } else {
        onVideoSelect(videoURL, '');
      }, onUploadEnd();
    });

    video.addEventListener('error', () => {
      toast.error('Error loading video');
      onUploadEnd();
    });
  }, [onVideoSelect, onUploadStart, onUploadEnd]);

  const handleSelectFile = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  const handleSelectVideo = useCallback(() => {
    videoInputRef.current?.click();
  }, []);

  const simulateImageUpload = useCallback(() => {
    onUploadStart();
    
    setTimeout(() => {
      const mockImages = [
        'https://images.pexels.com/photos/1181675/pexels-photo-1181675.jpeg?w=800&h=600&fit=crop',
        'https://images.pexels.com/photos/3861969/pexels-photo-3861969.jpeg?w=800&h=600&fit=crop',
        'https://images.pexels.com/photos/1761279/pexels-photo-1761279.jpeg?w=800&h=600&fit=crop'
      ];
      
      const randomImage = mockImages[Math.floor(Math.random() * mockImages.length)];
      onImageSelect(randomImage);
      onUploadEnd();
      toast.success('Sample image loaded successfully');
    }, 1500);
  }, [onImageSelect, onUploadStart, onUploadEnd]);

  const simulateVideoUpload = useCallback(() => {
    onUploadStart();
    
    setTimeout(() => {
      const mockVideoUrl = 'https://storage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4';
      const mockThumbnail = 'https://images.pexels.com/photos/1761279/pexels-photo-1761279.jpeg?w=800&h=600&fit=crop';
      
      onVideoSelect(mockVideoUrl, mockThumbnail);
      onUploadEnd();
      toast.success('Sample video loaded successfully');
    }, 2000);
  }, [onVideoSelect, onUploadStart, onUploadEnd]);

  const hasMedia = type === 'video' ? videoUrl : selectedImage;

  return (
    <div className={`border-2 border-dashed border-gray-300 rounded-lg p-8 text-center dark:border-gray-600 ${className}`}>
      {isUploading ? (
        <div className="space-y-2">
          <div className="w-8 h-8 border-4 border-t-blue-500 border-gray-200 rounded-full animate-spin mx-auto dark:border-gray-700"></div>
          <p className="text-gray-600 dark:text-gray-300">
            Uploading {type}...
          </p>
    </div>
      ) : hasMedia ? (
        <div className="space-y-2">
          {type === 'video' && videoUrl ? (
            <video 
              src={videoUrl} className="max-h-40 mx-auto rounded"
              controls
              muted
              playsInline
            />
          ) : selectedImage ? (
            <img 
              src={selectedImage} alt="Selected" 
              className="max-h-40 mx-auto object-contain rounded"
            />
          ) : null}
          
          <Button 
            variant="outline" 
            size="sm"
            onClick={onRemoveMedia} className="dark:border-gray-600 dark:text-gray-200"
          >
            <X className="w-4 h-4 mr-2" />
            Remove
          </Button>
    </div>
      ) : (
        <div className="space-y-2">
          {type === 'video' ? (
            <Video className="w-12 h-12 text-gray-400 mx-auto dark:text-gray-600" />
          ) : (
            <Camera className="w-12 h-12 text-gray-400 mx-auto dark:text-gray-600" />
          )}
          
          <p className="text-gray-600 dark:text-gray-300">
            Add a {type} to your story
          </p>
          
          <div className="flex justify-center space-x-2">
            <Button 
              variant="outline" 
              size="sm"
              onClick={type === 'video' ? handleSelectVideo : handleSelectFile} className="dark:border-gray-600 dark:text-gray-200"
            >
              Choose {type === 'video' ? 'Video' : 'File'}
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={type === 'video' ? simulateVideoUpload : simulateImageUpload} className="dark:border-gray-600 dark:text-gray-200"
            >
              Use Sample
            </Button>
    </div>
          {/* Hidden file inputs */}
          <input
            ref={fileInputRef} type="file"
            accept="image/*"
            onChange={handleFileUpload} className="hidden"
          />
          
          <input
            ref={videoInputRef} type="file"
            accept="video/*"
            onChange={handleVideoUpload} className="hidden"
          />
    </div>
      )}
    </div>
  );
});

MediaUpload.displayName = 'MediaUpload';

export default MediaUpload;
