import React, { useState, use<PERSON><PERSON>back, useMemo } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import {
  Settings,
  Users,
  UserPlus,
  Crown,
  Shield,
  User,
  MoreVertical,
  UserMinus,
  Copy,
  Image,
  FileText,
  Search,
  AlertTriangle
} from 'lucide-react';
import { toast } from 'sonner';
import { useGroupManagement } from '@/hooks/useGroupManagement';
import { Conversation } from '@/types/enhanced-messaging';
import { formatDistanceToNow } from 'date-fns';

interface GroupManagementPanelProps {
  conversation: Conversation, currentUserId: string, isOpen: boolean, onOpenChange: (open: boolean) => void;
}

const GroupManagementPanel: React.FC<GroupManagementPanelProps> = ({
  conversation,
  currentUserId,
  isOpen,
  onOpenChange
}) => {
  const {
    removeParticipant,
    updateGroupInfo;
    updateParticipantRole
  } = useGroupManagement(currentUserId);

  // State management
  const [activeTab, setActiveTab] = useState<'info' | 'members' | 'settings' | 'media'>('info');
  const [isEditing, setIsEditing] = useState(false);
  const [editedName, setEditedName] = useState(conversation.name || '');
  const [editedDescription, setEditedDescription] = useState(conversation.description || '');
  const [memberSearch, setMemberSearch] = useState('');
  const [memberFilter, setMemberFilter] = useState<'all' | 'admin' | 'moderator' | 'member'>('all');
  const [showRemoveDialog, setShowRemoveDialog] = useState<string | null>(null);

  // Current user's role and permissions
  const currentUserParticipant = conversation.participants?.find(p => p.id === currentUserId);
  const isAdmin = currentUserParticipant?.role === 'admin';
  const isModerator = currentUserParticipant?.role === 'moderator' || isAdmin;

  // Memoize participants to avoid re-filtering on every render
  const participants = useMemo(() => conversation.participants || [], [conversation.participants]);
  
  // Memoize search term to avoid re-filtering on every keystroke
  const searchTerm = useMemo(() => memberSearch.toLowerCase(), [memberSearch]);
  
  // Filter and search members with optimized filtering
  const filteredMembers = useMemo(() => {
    if (participants.length === 0) return [];
    
    // Early return for no filters
    if (memberFilter === 'all' && !searchTerm) {
      return participants;
    }
    
    return participants.filter(p => {
      // Role filter
      if (memberFilter !== 'all' && p.role !== memberFilter) {
        return false;
      }
      
      // Search filter
      if (searchTerm) {
        const name = p.name.toLowerCase();
        const username = p.username?.toLowerCase() || '';
        if (!name.includes(searchTerm) && !username.includes(searchTerm)) {
          return false;
        }
      }
      
      return true;
    });
  }, [participants, memberFilter, searchTerm]);

  const handleSaveInfo = useCallback(async () => {
    try {
      await updateGroupInfo(conversation.id, {
        name: editedName,
        description: editedDescription
      });
      
      setIsEditing(false);
      toast.success('Group information updated successfully');
    } catch (error) {
      console.error('Failed to update group info:', error);
      toast.error('Failed to update group information');
    }
  }, [conversation.id, editedName, editedDescription, updateGroupInfo]);

  const handleRemoveMember = useCallback(async (memberId: string) => {
    try {
      await removeParticipant(conversation.id, memberId, 'Removed by admin');
      setShowRemoveDialog(null);
      toast.success('Member removed successfully');
    } catch (error) {
      console.error('Failed to remove member:', error);
      toast.error('Failed to remove member');
    }
  }, [conversation.id, removeParticipant]);

  const handleRoleChange = useCallback(async (memberId: string, newRole: 'admin' | 'moderator' | 'member') => {
    try {
      await updateParticipantRole(conversation.id, memberId, newRole);
      toast.success(`Role updated to ${newRole}`);
    } catch (error) {
      console.error('Failed to update role:', error);
      toast.error('Failed to update member role');
    }
  }, [conversation.id, updateParticipantRole]);

  const copyInviteLink = useCallback(() => {
    if (conversation.inviteLink) {
      navigator.clipboard.writeText(conversation.inviteLink);
      toast.success('Invite link copied to clipboard');
    }
  }, [conversation.inviteLink]);

  // Memoize role utilities
  const getRoleColor = useCallback((role: string) => {
    switch (role) {
      case 'admin': return 'text-red-600 bg-red-50 border-red-200';
      case 'moderator': return 'text-blue-600 bg-blue-50 border-blue-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  }, []);

  const getRoleIcon = useCallback((role: string) => {
    switch (role) {
      case 'admin': return Crown;
      case 'moderator': return Shield;
      default: return User;
    }
  }, []);

  // Memoize tabs to prevent recreation on every render
  const tabs = useMemo(() => [{ id: 'info' as const, label: 'Group Info', icon: FileText },
    { id: 'members' as const, label: 'Members', icon: Users },
    { id: 'settings' as const, label: 'Settings', icon: Settings },
    { id: 'media' as const, label: 'Media', icon: Image }], []);

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="w-full sm:max-w-2xl max-h-[90vh] p-0">
        <div className="flex flex-col h-full max-h-[80vh]">
          {/* Header */}
          <DialogHeader className="p-6 border-b">
            <div className="flex items-center gap-3">
              <Avatar className="h-12 w-12">
                <AvatarImage src={conversation.groupImage} />
                <AvatarFallback>
                  <Users className="h-6 w-6" />
    </AvatarFallback>
              </Avatar>
              <div className="flex-1">
                <DialogTitle className="text-left">{conversation.name}</DialogTitle>
                <DialogDescription className="text-left">
                  {conversation.participants?.length} members
                </DialogDescription>
    </div>
            </div>
    </DialogHeader>
          {/* Tabs */}
          <div className="flex border-b">
            {tabs.map((tab) => (
              <button
                key={tab.id} onClick={() => setActiveTab(tab.id)}, className={`flex-1 flex items-center justify-center gap-2 py-3 px-2 text-sm font-medium transition-colors ${
                  activeTab === tab.id
                    ? 'text-primary border-b-2 border-primary bg-primary/5'
                    : 'text-muted-foreground hover:text-foreground hover:bg-muted/50'
                }`}
              >
                <tab.icon className="h-4 w-4" />
                <span className="hidden sm:inline">{tab.label}</span>
    </button>
            ))}
          </div>

          {/* Content */}
          <ScrollArea className="flex-1">
            <div className="p-6">
              {activeTab === 'info' && (
                <div className="space-y-6">
                  {/* Group Information */}
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-semibold">Group Information</h3>
                      {isAdmin && (
                        <Button
                          onClick={() => isEditing ? handleSaveInfo() : setIsEditing(true)} variant="outline"
                          size="sm"
                        >
                          {isEditing ? 'Save' : 'Edit'}
                        </Button>
                      )}
                    </div>

                    <div className="space-y-4">
                      <div>
                        <Label htmlFor="groupName">Group Name</Label>
                        {isEditing ? (
                          <Input
                            id="groupName"
                            value={editedName} onChange={(e) => setEditedName(e.target.value)}, className="mt-1"
                          />
                        ) : (
                          <p className="mt-1 text-sm text-muted-foreground">{conversation.name}</p>
                        )}
                      </div>

                      <div>
                        <Label htmlFor="groupDescription">Description</Label>
                        {isEditing ? (
                          <Textarea
                            id="groupDescription"
                            value={editedDescription} onChange={(e) => setEditedDescription(e.target.value)}, className="mt-1"
                            rows={3}
                          />
                        ) : (
                          <p className="mt-1 text-sm text-muted-foreground">
                            {conversation.description || 'No description'}
                          </p>
                        )}
                      </div>
    </div>
                  </div>

                  <Separator />

                  {/* Group Stats */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold">Group Details</h3>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-1">
                        <p className="text-sm font-medium">Type</p>
                        <Badge variant="outline" className="capitalize">
                          {conversation.groupType || 'private'}
                        </Badge>
    </div>
                      <div className="space-y-1">
                        <p className="text-sm font-medium">Created</p>
                        <p className="text-sm text-muted-foreground">
                          {conversation.createdAt 
                            ? formatDistanceToNow(conversation.createdAt, { addSuffix: true })
                            : 'Unknown'
                          }
                        </p>
    </div>
                      <div className="space-y-1">
                        <p className="text-sm font-medium">Members</p>
                        <p className="text-sm text-muted-foreground">
                          {conversation.participants?.length || 0}
                          {conversation.maxParticipants && ` / ${conversation.maxParticipants}`}
                        </p>
    </div>
                      <div className="space-y-1">
                        <p className="text-sm font-medium">Admins</p>
                        <p className="text-sm text-muted-foreground">
                          {conversation.participants?.filter(p => p.role === 'admin').length || 0}
                        </p>
    </div>
                    </div>
    </div>
                  {/* Invite Link */}
                  {isAdmin && conversation.inviteLink && (
                    <>
                      <Separator />
                      <div className="space-y-4">
                        <h3 className="text-lg font-semibold">Invite Link</h3>
                        <div className="flex items-center gap-2">
                          <Input
                            value={conversation.inviteLink}, readOnly
                            className="font-mono text-xs"
                          />
                          <Button onClick={copyInviteLink} variant="outline" size="sm">
                            <Copy className="h-4 w-4" />
    </Button>
                        </div>
                        {conversation.inviteLinkExpiresAt && (
                          <p className="text-xs text-muted-foreground">
                            Expires {formatDistanceToNow(conversation.inviteLinkExpiresAt, { addSuffix: true })}
                          </p>
                        )}
                      </div>
                    </>
                  )}

                  {/* Group Rules */}
                  {conversation.groupRules && conversation.groupRules.length > 0 && (
                    <>
                      <Separator />
                      <div className="space-y-4">
                        <h3 className="text-lg font-semibold">Group Rules</h3>
                        <div className="space-y-2">
                          {conversation.groupRules.map((rule, index) => (
                            <div key={index} className="flex items-start gap-2">
                              <Badge variant="outline" className="mt-0.5 text-xs">
                                {index + 1}
                              </Badge>
                              <p className="text-sm text-muted-foreground">{rule}</p>
    </div>
                          ))}
                        </div>
    </div>
                    </>
                  )}
                </div>
              )}

              {activeTab === 'members' && (
                <div className="space-y-4">
                  {/* Members Header */}
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold">
                      Members ({conversation.participants?.length || 0})
                    </h3>
                    {isModerator && (
                      <Button onClick={() => toast.info('Add members feature coming soon')} size="sm">
                        <UserPlus className="h-4 w-4 mr-2" />
                        Add
                      </Button>
                    )}
                  </div>

                  {/* Search and Filter */}
                  <div className="space-y-3">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      <Input
                        value={memberSearch} onChange={(e) => setMemberSearch(e.target.value)}, placeholder="Search members..."
                        className="pl-10"
                      />
    </div>
                    <Select value={memberFilter} onValueChange={(value: 'all' | 'admin' | 'moderator' | 'member') => setMemberFilter(value)}>
                      <SelectTrigger>
                        <SelectValue />
    </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Members</SelectItem>
                        <SelectItem value="admin">Admins</SelectItem>
                        <SelectItem value="moderator">Moderators</SelectItem>
                        <SelectItem value="member">Members</SelectItem>
    </SelectContent>
                    </Select>
    </div>
                  {/* Members List */}
                  <div className="space-y-2">
                    {filteredMembers.map((member) => {
                      const RoleIcon = getRoleIcon(member.role);
                      const canManage = isAdmin || (isModerator && member.role === 'member');
                      const roleColor = getRoleColor(member.role);
                      const joinedText = member.joinedAt ? formatDistanceToNow(member.joinedAt, { addSuffix: true }) : null;
                      
                      return (
                        <div
                          key={member.id} className="flex items-center gap-3 p-3 rounded-lg border bg-card hover:bg-accent/50 transition-colors"
                        >
                          <Avatar className="h-10 w-10">
                            <AvatarImage src={member.avatar} />
                            <AvatarFallback>{member.name[0]}</AvatarFallback>
    </Avatar>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2">
                              <p className="font-medium text-sm truncate">{member.name}</p>
                              {member.isOnline && (
                                <div className="h-2 w-2 rounded-full bg-green-500" />
                              )}
                            </div>
                            {member.username && (
                              <p className="text-xs text-muted-foreground">@{member.username}</p>
                            )}
                            <div className="flex items-center gap-2 mt-1">
                              <Badge variant="outline" className={`text-xs ${roleColor}`}>
                                <RoleIcon className="h-3 w-3 mr-1" />
                                {member.role}
                              </Badge>
                              {joinedText && (
                                <span className="text-xs text-muted-foreground">
                                  Joined {joinedText}
                                </span>
                              )}
                            </div>
    </div>
                          {canManage && member.id !== currentUserId && (
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm">
                                  <MoreVertical className="h-4 w-4" />
    </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>Manage Member</DropdownMenuLabel>
                                <DropdownMenuSeparator />
                                
                                {isAdmin && (
                                  <>
                                    <DropdownMenuItem onClick={() => handleRoleChange(member.id, 'admin')}>
                                      <Crown className="h-4 w-4 mr-2" />
                                      Make Admin
                                    </DropdownMenuItem>
                                    <DropdownMenuItem onClick={() => handleRoleChange(member.id, 'moderator')}>
                                      <Shield className="h-4 w-4 mr-2" />
                                      Make Moderator
                                    </DropdownMenuItem>
                                  </>
                                )}
                                
                                {member.role !== 'member' && (
                                  <DropdownMenuItem onClick={() => handleRoleChange(member.id, 'member')}>
                                    <User className="h-4 w-4 mr-2" />
                                    Make Member
                                  </DropdownMenuItem>
                                )}
                                
                                <DropdownMenuSeparator />
                                <DropdownMenuItem 
                                  onClick={() => setShowRemoveDialog(member.id)} className="text-destructive"
                                >
                                  <UserMinus className="h-4 w-4 mr-2" />
                                  Remove from Group
                                </DropdownMenuItem>
    </DropdownMenuContent>
                            </DropdownMenu>
                          )}
                        </div>
                      );
                    })}
                  </div>

                  {filteredMembers.length === 0 && (
                    <div className="text-center py-8 text-muted-foreground">
                      {memberSearch || memberFilter !== 'all' 
                        ? 'No members found matching your filters'
                        : 'No members in this group'
                      }
                    </div>
                  )}
                </div>
              )}

              {activeTab === 'settings' && (
                <div className="space-y-6">
                  <h3 className="text-lg font-semibold">Group Settings</h3>
                  
                  {/* Notification Settings */}
                  <div className="space-y-4">
                    <h4 className="font-medium">Notifications</h4>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <div>
                          <Label>Mute Group</Label>
                          <p className="text-sm text-muted-foreground">
                            Stop receiving notifications from this group
                          </p>
    </div>
                        <Switch checked={conversation.isMuted} />
    </div>
                    </div>
    </div>
                  <Separator />

                  {/* Privacy Settings */}
                  {isAdmin && (
                    <div className="space-y-4">
                      <h4 className="font-medium">Privacy & Security</h4>
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <div>
                            <Label>Archive Group</Label>
                            <p className="text-sm text-muted-foreground">
                              Archive this group conversation
                            </p>
    </div>
                          <Switch checked={conversation.isArchived} />
    </div>
                      </div>
    </div>
                  )}
                </div>
              )}

              {activeTab === 'media' && (
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Media & Files</h3>
                  <div className="text-center py-8 text-muted-foreground">
                    <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>Media and files shared in this group will appear here</p>
    </div>
                </div>
              )}
            </div>
    </ScrollArea>
        </div>

        {/* Remove Member Dialog */}
        <Dialog open={!!showRemoveDialog} onOpenChange={() => setShowRemoveDialog(null)}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5 text-destructive" />
                Remove Member
              </DialogTitle>
              <DialogDescription>
                Are you sure you want to remove this member from the group? This action cannot be undone.
              </DialogDescription>
    </DialogHeader>
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowRemoveDialog(null)}>
                Cancel
              </Button>
              <Button 
                variant="destructive"
                onClick={() => showRemoveDialog && handleRemoveMember(showRemoveDialog)}
              >
                Remove Member
              </Button>
    </DialogFooter>
          </DialogContent>
    </Dialog>
      </DialogContent>
    </Dialog>
  );
};

export default GroupManagementPanel;
