import React, { memo, useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { But<PERSON> } from '@/components/ui/button';
import { 
  TrendingUp, 
  Activity, 
  Zap, 
  Clock,
  CheckCircle,
  AlertTriangle,
  Info,
  BarChart3,
  Monitor,
  Cpu,
  HardDrive,
  Wifi
} from 'lucide-react';
import { motion } from 'framer-motion';
import { useOptimizationSuite } from '@/hooks/useOptimizationSuite';

const OptimizationOverview: React.FC = memo(() => {
  const { 
    metrics, 
    performanceStatus, 
    memoryStatus, 
    isMonitoring;
    refresh 
  } = useOptimizationSuite();

  const [recommendations, setRecommendations] = useState<string[]>([]);

  useEffect(() => {
    // Generate recommendations based on current metrics
    const newRecommendations: string[] = [];
    
    if (metrics) {
      if (metrics.performance.score < 90) {
        newRecommendations.push('Consider optimizing Core Web Vitals for better performance');
      }
      if (metrics.memory.used > metrics.memory.total * 0.7) {
        newRecommendations.push('Memory usage is high - consider optimizing component renders');
      }
      if (metrics.bundle.size > 2.5) {
        newRecommendations.push('Bundle size is large - implement code splitting');
      }
      if (metrics.performance.lcp > 2.5) {
        newRecommendations.push('LCP is slow - optimize largest contentful paint');
      }
      if (metrics.performance.cls > 0.1) {
        newRecommendations.push('CLS is high - reduce layout shifts');
      }
    }

    if (newRecommendations.length === 0) {
      newRecommendations.push('Performance is excellent! Keep monitoring for consistency.');
    }

    setRecommendations(newRecommendations);
  }, [metrics]);

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBackground = (score: number) => {
    if (score >= 90) return 'from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20';
    if (score >= 70) return 'from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20';
    return 'from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20';
  };

  const formatBytes = (bytes: number) => {
    return `${bytes.toFixed(1)}MB`;
  };

  const formatTime = (seconds: number) => {
    return `${seconds.toFixed(2)}s`;
  };

  const formatMilliseconds = (ms: number) => {
    return `${ms.toFixed(0)}ms`;
  };

  if (!metrics) {
    return (
      <div className="space-y-6">
        <Card>
          <CardContent className="p-8 text-center">
            <Activity className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Loading Performance Data</h3>
            <p className="text-gray-600 dark:text-gray-400">
              Collecting performance metrics...
            </p>
    </CardContent>
        </Card>
    </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Performance Score Overview */}
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}, animate={{ opacity: 1, scale: 1 }}, transition={{ duration: 0.3 }}
      >
        <Card className={`bg-gradient-to-r ${getScoreBackground(metrics.performance.score)}`}>
          <CardContent className="p-8">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-2xl font-bold mb-2">Performance Overview</h2>
                <div className="flex items-baseline gap-3">
                  <span className={`text-5xl font-bold ${getScoreColor(metrics.performance.score)}`}>
                    {metrics.performance.score}
                  </span>
                  <span className="text-2xl text-gray-500">/100</span>
    </div>
                <Badge 
                  variant="outline" 
                  className={`mt-2 ${getScoreColor(metrics.performance.score)}, border-current`}
                >
                  {performanceStatus.status.toUpperCase()}
                </Badge>
    </div>
              <div className="text-right">
                <div className="flex items-center gap-2 mb-4">
                  <div className={`w-3 h-3 rounded-full ${isMonitoring ? 'bg-green-500 animate-pulse' : 'bg-gray-400'}`} />
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {isMonitoring ? 'Monitoring Active' : 'Monitoring Paused'}
                  </span>
    </div>
                <Button onClick={refresh} variant="outline" size="sm">
                  <Activity className="w-4 h-4 mr-2" />
                  Refresh
                </Button>
    </div>
            </div>
            
            <Progress value={metrics.performance.score} className="mt-6 h-3" />
    </CardContent>
        </Card>
      </motion.div>

      {/* Core Web Vitals */}
      <div>
        <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
          <Zap className="w-5 h-5 text-yellow-500" />
          Core Web Vitals
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {[
            { 
              name: 'LCP', 
              value: metrics.performance.lcp, 
              unit: 's', 
              threshold: 2.5, 
              format: formatTime,
              description: 'Largest Contentful Paint'
            },
            { 
              name: 'FID', 
              value: metrics.performance.fid, 
              unit: 'ms', 
              threshold: 100, 
              format: formatMilliseconds,
              description: 'First Input Delay'
            },
            { 
              name: 'CLS', 
              value: metrics.performance.cls, 
              unit: '', 
              threshold: 0.1, 
              format: (v: number) => v.toFixed(3); description: 'Cumulative Layout Shift'
            },
            { 
              name: 'FCP', 
              value: metrics.performance.fcp, 
              unit: 's', 
              threshold: 1.8, 
              format: formatTime,
              description: 'First Contentful Paint'
            }
          ].map((vital, index) => (
            <motion.div
              key={vital.name} initial={{ opacity: 0, y: 20 }}, animate={{ opacity: 1, y: 0 }}, transition={{ delay: index * 0.1 }}
            >
              <Card className="hover:shadow-lg transition-shadow duration-200">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-semibold">{vital.name}</h4>
                    {vital.value <= vital.threshold ? (
                      <CheckCircle className="w-4 h-4 text-green-500" />
                    ) : (
                      <AlertTriangle className="w-4 h-4 text-red-500" />
                    )}
                  </div>
                  <div className="text-2xl font-bold mb-1">
                    {vital.format(vital.value)}{vital.unit}
                  </div>
                  <div className="text-xs text-gray-500 mb-2">
                    {vital.description}
                  </div>
                  <Progress 
                    value={Math.min((vital.threshold / vital.value) * 100, 100)} className="h-2"
                  />
                  <div className="text-xs text-gray-500 mt-1">
                    Target: ≤{vital.threshold}{vital.unit}
                  </div>
    </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
    </div>
      {/* System Resources */}
      <div>
        <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
          <Monitor className="w-5 h-5 text-blue-500" />
          System Resources
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Memory Usage */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg flex items-center gap-2">
                <HardDrive className="w-5 h-5 text-orange-500" />
                Memory Usage
              </CardTitle>
    </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm">Used</span>
                  <span className="font-semibold">{formatBytes(metrics.memory.used)}</span>
    </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Total</span>
                  <span className="font-semibold">{formatBytes(metrics.memory.total)}</span>
    </div>
                <Progress 
                  value={(metrics.memory.used / metrics.memory.total) * 100} className="h-2"
                />
                <Badge 
                  variant="outline" 
                  className={`${memoryStatus.color === 'green' ? 'text-green-600 border-green-200' : 
                             memoryStatus.color === 'yellow' ? 'text-yellow-600 border-yellow-200' : 
                             'text-red-600 border-red-200'}`}
                >
                  {memoryStatus.status}
                </Badge>
    </div>
            </CardContent>
    </Card>
          {/* Bundle Size */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg flex items-center gap-2">
                <Cpu className="w-5 h-5 text-purple-500" />
                Bundle Analysis
              </CardTitle>
    </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm">Size</span>
                  <span className="font-semibold">{formatBytes(metrics.bundle.size)}</span>
    </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Chunks</span>
                  <span className="font-semibold">{metrics.bundle.chunks}</span>
    </div>
                <Progress 
                  value={Math.min((metrics.bundle.size / 5) * 100, 100)} className="h-2"
                />
                <Badge 
                  variant="outline" 
                  className={metrics.bundle.size <= 3 ? 'text-green-600 border-green-200' : 'text-yellow-600 border-yellow-200'}
                >
                  {metrics.bundle.size <= 3 ? 'Optimized' : 'Large'}
                </Badge>
    </div>
            </CardContent>
    </Card>
          {/* Network Info */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg flex items-center gap-2">
                <Wifi className="w-5 h-5 text-green-500" />
                Network Status
              </CardTitle>
    </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm">Type</span>
                  <span className="font-semibold">{metrics.network.effectiveType}</span>
    </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Speed</span>
                  <span className="font-semibold">{metrics.network.downlink}Mbps</span>
    </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">RTT</span>
                  <span className="font-semibold">{metrics.network.rtt}ms</span>
    </div>
                <Badge variant="outline" className="text-green-600 border-green-200">
                  Connected
                </Badge>
    </div>
            </CardContent>
    </Card>
        </div>
    </div>
      {/* Recommendations */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="w-5 h-5 text-green-500" />
            Performance Recommendations
          </CardTitle>
    </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {recommendations.map((recommendation, index) => (
              <motion.div
                key={index} initial={{ opacity: 0, x: -20 }}, animate={{ opacity: 1, x: 0 }}, transition={{ delay: index * 0.1 }}, className="flex items-start gap-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg"
              >
                <Info className="w-4 h-4 text-blue-500 mt-0.5 flex-shrink-0" />
                <span className="text-sm text-blue-800 dark:text-blue-200">
                  {recommendation}
                </span>
              </motion.div>
            ))}
          </div>
    </CardContent>
      </Card>

      {/* Quick Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <BarChart3 className="w-8 h-8 text-blue-500 mx-auto mb-2" />
            <div className="text-2xl font-bold">{metrics.performance.score}</div>
            <div className="text-xs text-gray-500">Performance Score</div>
    </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <Clock className="w-8 h-8 text-green-500 mx-auto mb-2" />
            <div className="text-2xl font-bold">{formatTime(metrics.performance.lcp)}</div>
            <div className="text-xs text-gray-500">Load Time</div>
    </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <HardDrive className="w-8 h-8 text-orange-500 mx-auto mb-2" />
            <div className="text-2xl font-bold">{formatBytes(metrics.memory.used)}</div>
            <div className="text-xs text-gray-500">Memory Used</div>
    </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <Cpu className="w-8 h-8 text-purple-500 mx-auto mb-2" />
            <div className="text-2xl font-bold">{formatBytes(metrics.bundle.size)}</div>
            <div className="text-xs text-gray-500">Bundle Size</div>
    </CardContent>
        </Card>
    </div>
    </div>
  );
});

OptimizationOverview.displayName = 'OptimizationOverview';

export default OptimizationOverview;