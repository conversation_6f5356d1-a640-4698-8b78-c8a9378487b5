import React from 'react';
// import { QueryClient, QueryClientProvider } from '@tanstack/react-query'; // Temporarily disabled due to React context corruption
import SimpleQueryProvider from '@/components/SimpleQueryProvider';
// import { APP_CONFIG } from '@/lib/constants'; // Temporarily disabled
// import { handleError } from '@/lib/utils'; // Temporarily disabled

/*
// Temporarily disabled QueryClient configuration due to React context corruption
// TODO: Restore full React Query functionality once React context issues are resolved
*/

interface QueryProviderProps {
  children: React.ReactNode;
}

export const QueryProvider: React.FC<QueryProviderProps> = ({ children }) => {
  return (
    <SimpleQueryProvider>
      {children}
    </SimpleQueryProvider>
  );
};

export default QueryProvider;