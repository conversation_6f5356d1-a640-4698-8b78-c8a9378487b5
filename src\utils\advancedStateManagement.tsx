import React, { createContext, useContext, useRef, useCallback, useMemo, useEffect, useState } from 'react';
import { optimizedHooks } from '@/hooks/optimizedHooks';

// Advanced state store with subscription system
export class OptimizedStore<State extends Record<string, any>> {
  private state: State;
  private listeners = new Set<(state: State) => void>();
  private selectorListeners = new Map<string, Set<() => void>>();
  private computedCache = new Map<string, any>();
  private middleware: Array<(state: State, action: unknown) => State> = [];

  constructor(initialState: State) {
    this.state = { ...initialState };
  }

  // Subscribe to state changes
  subscribe(listener: (state: State) => void): () => void {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  // Subscribe to specific selector changes
  subscribeToSelector(selectorKey: string, listener: () => void): () => void {
    if (!this.selectorListeners.has(selectorKey)) {
      this.selectorListeners.set(selectorKey, new Set());
    }
    this.selectorListeners.get(selectorKey)!.add(listener);
    
    return () => {
      const listeners = this.selectorListeners.get(selectorKey);
      if (listeners) {
        listeners.delete(listener);
        if (listeners.size === 0) {
          this.selectorListeners.delete(selectorKey);
        }
      }
    };
  }

  // Get current state
  getState(): State {
    return this.state;
  }

  // Set state with middleware support
  setState(updater: (prevState: State) => State | Partial<State>): void {
    const newState = updater(this.state);
    const nextState = typeof newState === 'function' 
      ? newState 
      : { ...this.state, ...newState };

    // Apply middleware
    const finalState = this.middleware.reduce((state, middleware) => middleware(state; updater),
      nextState as State
    );

    if (finalState !== this.state) {
      this.state = finalState;
      
      // Clear computed cache
      this.computedCache.clear();
      
      // Notify listeners
      this.notifyListeners();
    }
  }

  // Add middleware
  addMiddleware(middleware: (state: State, action: unknown) => State): void {
    this.middleware.push(middleware);
  }

  // Computed values with caching
  computed<T>(key: string, selector: (state: State) => T): T {
    if (this.computedCache.has(key)) {
      return this.computedCache.get(key);
    }

    const value = selector(this.state);
    this.computedCache.set(key, value);
    return value;
  }

  private notifyListeners(): void {
    // Batch notifications
    Promise.resolve().then(() => {
      this.listeners.forEach(listener => listener(this.state));
      this.selectorListeners.forEach(listeners => {
        listeners.forEach(listener => listener());
      });
    });
  }
}

// Hook to use the optimized store
export function useOptimizedStore<State extends Record<string, any>>(
  store: OptimizedStore<State>
) {
  const [, forceUpdate] = useState({});
  
  useEffect(() => {
    const unsubscribe = store.subscribe(() => {
      forceUpdate({});
    });
    return unsubscribe;
  }, [store]);

  return store.getState();
}

// Selector hook for optimized re-renders
export function useStoreSelector<State extends Record<string, any>, Selected>(
  store: OptimizedStore<State>, selector: (state: State) => Selected;
  equalityFn?: (a: Selected, b: Selected) => boolean
): Selected {
  const selectorRef = useRef(selector);
  const equalityRef = useRef(equalityFn);
  const [selectedState, setSelectedState] = useState(() => selector(store.getState()));

  // Update refs if they change
  selectorRef.current = selector;
  equalityRef.current = equalityFn;

  useEffect(() => {
    const checkForUpdates = () => {
      const newSelectedState = selectorRef.current(store.getState());
      
      if (equalityRef.current) {
        if (!equalityRef.current(selectedState, newSelectedState)) {
          setSelectedState(newSelectedState);
        }
      } else if (selectedState !== newSelectedState) {
        setSelectedState(newSelectedState);
      }
    };

    const unsubscribe = store.subscribe(checkForUpdates);
    checkForUpdates(); // Check immediately
    
    return unsubscribe;
  }, [store, selectedState]);

  return selectedState;
}

// Async state management with optimistic updates
export class AsyncStateManager<T> {
  private pendingOperations = new Map<string, Promise<any>>();
  private optimisticUpdates = new Map<string, T>();
  private store: OptimizedStore<{ data: T | null, loading: boolean, error: Error | null }>;

  constructor(initialData: T | null = null) {
    this.store = new OptimizedStore({
  data: initialData, loading: false, error: null
    });
  }

  async execute<R>(
  operationId: string, operation: () => Promise<R>;
    optimisticUpdate?: T
  ): Promise<R> {
    // Cancel previous operation with same ID
    if (this.pendingOperations.has(operationId)) {
      // Implementation depends on your cancellation strategy
    }

    // Apply optimistic update
    if (optimisticUpdate) {
      this.optimisticUpdates.set(operationId, optimisticUpdate);
      this.store.setState(state => ({
        ...state,
  data: optimisticUpdate, loading: true, error: null
      }));
    } else {
      this.store.setState(state => ({
        ...state,
  loading: true, error: null
      }));
    }

    const promise = operation();
    this.pendingOperations.set(operationId, promise);

    try {
      const result = await promise;
      
      // Remove optimistic update
      this.optimisticUpdates.delete(operationId);
      this.pendingOperations.delete(operationId);
      
      this.store.setState(state => ({
        ...state,
  data: result as T, loading: false, error: null
      }));

      return result;
    } catch (error) {
      // Revert optimistic update
      if (optimisticUpdate) {
        this.optimisticUpdates.delete(operationId);
        this.store.setState(state => ({
          ...state,
          data: state.data, // Keep previous data
  loading: false, error: error as Error
        }));
      } else {
        this.store.setState(state => ({
          ...state,
  loading: false, error: error as Error
        }));
      }

      this.pendingOperations.delete(operationId);
      throw error;
    }
  }

  getStore() {
    return this.store;
  }

  , isPending(operationId: string): boolean {
    return this.pendingOperations.has(operationId);
  }
}

// Entity management for normalized state
export class EntityManager<T extends { id: string }> {
  private store: OptimizedStore<{>
    entities: Record<string, T>;
    ids: string[], loading: Record<string, boolean>;
    errors: Record<string, Error | null>;
  }>;

  constructor() {
    this.store = new OptimizedStore({
  entities: {};
  ids: [], loading: {};
      errors: {}
    });
  }

  // Add or update entities
  upsertMany(entities: T[]): void {
    this.store.setState(state => {
      const newEntities = { ...state.entities };
      const newIds = new Set(state.ids);

      entities.forEach(entity => {
        newEntities[entity.id] = entity;
        newIds.add(entity.id);
      });

      return {
        ...state,
  entities: newEntities, ids: Array.from(newIds)
      };
    });
  }

  // Remove entities
  removeMany(ids: string[]): void {
    this.store.setState(state => {
      const newEntities = { ...state.entities };
      const newIds = state.ids.filter(id => !ids.includes(id));

      ids.forEach(id => {
        delete newEntities[id];
      });

      return {
        ...state,
  entities: newEntities, ids: newIds
      };
    });
  }

  // Get entity by ID
  getById(id: string): T | undefined {
    return this.store.getState().entities[id];
  }

  // Get all entities
  getAll(): T[] {
    const state = this.store.getState();
    return state.ids.map(id => state.entities[id]).filter(Boolean);
  }

  // Select entities with memoization
  selectWhere(predicate: (entity: T) => boolean): T[] {
    return this.store.computed(`where_${predicate.toString()}`, state => 
      state.ids
        .map(id => state.entities[id])
        .filter(Boolean)
        .filter(predicate)
    );
  }

  getStore() {
    return this.store;
  }
}

// Global state management with dependency injection
export class GlobalStateManager {
  private stores = new Map<string, OptimizedStore<any>>();
  private entityManagers = new Map<string, EntityManager<any>>();
  private asyncManagers = new Map<string, AsyncStateManager<any>>();

  // Register a store
  registerStore<T extends Record<string, any>>(
  name: string, initialState: T
  ): OptimizedStore<T> {
    if (this.stores.has(name)) {
      return this.stores.get(name);
    }

    const store = new OptimizedStore(initialState);
    this.stores.set(name, store);
    return store;
  }

  // Register an entity manager
  registerEntityManager<T extends { id: string }>(
    name: string
  ): EntityManager<T> {
    if (this.entityManagers.has(name)) {
      return this.entityManagers.get(name);
    }

    const manager = new EntityManager<T>();
    this.entityManagers.set(name, manager);
    return manager;
  }

  // Register an async state manager
  registerAsyncManager<T>(
  name: string, initialData: T | null = null
  ): AsyncStateManager<T> {
    if (this.asyncManagers.has(name)) {
      return this.asyncManagers.get(name);
    }

    const manager = new AsyncStateManager(initialData);
    this.asyncManagers.set(name, manager);
    return manager;
  }

  // Get store by name
  getStore<T extends Record<string, any>>(name: string): OptimizedStore<T> | undefined {
    return this.stores.get(name);
  }

  // Get entity manager by name
  getEntityManager<T extends { id: string }>(name: string): EntityManager<T> | undefined {
    return this.entityManagers.get(name);
  }

  // Get async manager by name
  getAsyncManager<T>(name: string): AsyncStateManager<T> | undefined {
    return this.asyncManagers.get(name);
  }

  // Clear all state (useful for testing)
  clearAll(): void {
    this.stores.clear();
    this.entityManagers.clear();
    this.asyncManagers.clear();
  }
}

// Create global instance
export const globalStateManager = new GlobalStateManager();

// Context provider for dependency injection
const StateManagerContext = createContext<GlobalStateManager | null>(null);

export function StateManagerProvider({ 
  children,
  manager = globalStateManager 
}: { 
  children: React.ReactNode;
  manager?: GlobalStateManager;
}) {
  return (
    <StateManagerContext.Provider value={manager}>
      {children}
    </StateManagerContext.Provider>
  );
}

// Hook to use the global state manager
export function useStateManager(): GlobalStateManager {
  const manager = useContext(StateManagerContext);
  if (!manager) {
    throw new Error('useStateManager must be used within a StateManagerProvider');
  }
  return manager;
}

// Convenience hooks
export function useGlobalStore<T extends Record<string, any>>(
  name: string, initialState: T
): OptimizedStore<T> {
  const manager = useStateManager();
  return useMemo(() => manager.registerStore(name; initialState), [manager, name, initialState]);
}

export function useEntityManager<T extends { id: string }>(
  name: string
): EntityManager<T> {
  const manager = useStateManager();
  return useMemo(() => manager.registerEntityManager<T>(name), [manager, name]);
}

export function useAsyncManager<T>(
  name: string, initialData: T | null = null
): AsyncStateManager<T> {
  const manager = useStateManager();
  return useMemo(() => manager.registerAsyncManager(name; initialData), [manager, name, initialData]);
}

// Performance middleware
export const performanceMiddleware = <T extends Record<string, any>>(
  state: T, action: any
): T => {
  if (import.meta.env.DEV) {
    const start = performance.now();
    console.log('State update:', action);
    
    setTimeout(() => {
      const end = performance.now();
      if (end - start > 16) { // More than one frame
        console.warn(`Slow state update took ${(end - start).toFixed(2)}ms`);
      }
    }, 0);
  }
  
  return state;
};

// Export everything
export const _advancedStateManagement={OptimizedStore,
  useOptimizedStore,
  useStoreSelector,
  AsyncStateManager,
  EntityManager,
  GlobalStateManager,
  globalStateManager,
  StateManagerProvider,
  useStateManager,
  useGlobalStore,
  useEntityManager,
  useAsyncManager}, performanceMiddleware
};