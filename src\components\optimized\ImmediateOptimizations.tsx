/**
 * Immediate Component Optimizations - High Impact Performance Improvements
 * Focus: React.memo, useMemo, useCallback optimizations for frequently rendered components
 */

import React, { memo, useMemo, useCallback } from 'react';

// 1. OPTIMIZED SIDEBAR MENU ITEM
// Previously recreated on every render, now memoized with proper comparison
interface OptimizedMenuItemProps {
  icon: React.ComponentType<any>, label: string, path: string;
  badge?: number;
  isActive?: boolean;
  onClick?: (path: string) => void;
}

export const OptimizedMenuItem = memo<OptimizedMenuItemProps>(({
  icon: Icon,
  label,
  path,
  badge,
  isActive = false,
  onClick
}) => {
  const handleClick = useCallback(() => {
    if (onClick) {
      onClick(path);
    }
  }, [onClick, path]);

  return (
    <button
      className={`w-full flex items-center justify-between p-3 rounded-lg transition-colors ${
        isActive 
          ? 'bg-blue-50 text-blue-600 hover:bg-blue-100 dark:bg-blue-900/20 dark:text-blue-400 dark:hover:bg-blue-900/30' 
          : 'text-gray-700 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-800'
      }`}, onClick={handleClick}, aria-label={label}
    >
      <div className="flex items-center space-x-3 min-w-0">
        <Icon className="w-6 h-6 flex-shrink-0" />
        <span className="truncate">{label}</span>
    </div>
      {badge && badge > 0 && (
        <span className="bg-red-500 text-white text-xs rounded-full px-2 py-0.5 min-w-[1.5rem] text-center">
          {badge > 99 ? '99+' : badge}
        </span>
      )}
    </button>
  );
}, (prevProps, nextProps) => {
  // Custom comparison function for better performance
  return (
    prevProps.label === nextProps.label &&
    prevProps.path === nextProps.path &&
    prevProps.badge === nextProps.badge &&
    prevProps.isActive === nextProps.isActive &&
    prevProps.onClick === nextProps.onClick
  );
});

OptimizedMenuItem.displayName = 'OptimizedMenuItem';

// 2. OPTIMIZED POST CARD
// Prevent unnecessary re-renders with memoization and stable callbacks
interface OptimizedPostCardProps {
  id: string, author: {
    name: string, avatar: string;
    verified?: boolean;
  };
  content: string;
  image?: string;
  timestamp: string, likes: number, comments: number, shares: number;
  isLiked?: boolean;
  isBookmarked?: boolean;
  onLike?: (postId: string) => void;
  onComment?: (postId: string) => void;
  onShare?: (postId: string) => void;
  onBookmark?: (postId: string) => void;
}

export const OptimizedPostCard = memo<OptimizedPostCardProps>(({
  id,
  author,
  content,
  image,
  timestamp,
  likes,
  comments,
  shares,
  isLiked = false,
  isBookmarked = false,
  onLike,
  onComment,
  onShare,
  onBookmark
}) => {
  // Memoize callbacks to prevent child re-renders
  const handleLike = useCallback(() => {
    if (onLike) onLike(id);
  }, [onLike, id]);

  const handleComment = useCallback(() => {
    if (onComment) onComment(id);
  }, [onComment, id]);

  const handleShare = useCallback(() => {
    if (onShare) onShare(id);
  }, [onShare, id]);

  const handleBookmark = useCallback(() => {
    if (onBookmark) onBookmark(id);
  }, [onBookmark, id]);

  // Memoize formatted numbers to prevent recalculation
  const formattedStats = useMemo(() => ({
    likes: likes > 999 ? `${(likes / 1000).toFixed(1)}k` : likes.toString(),
    comments: comments > 999 ? `${(comments / 1000).toFixed(1)}k` : comments.toString(),
    shares: shares > 999 ? `${(shares / 1000).toFixed(1)}k` : shares.toString()
  }), [likes, comments, shares]);

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 space-y-3">
      {/* Header */}
      <div className="flex items-center space-x-3">
        <img 
          src={author.avatar} alt={author.name}, className="w-10 h-10 rounded-full object-cover"
          loading="lazy"
        />
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-1">
            <h3 className="font-semibold text-gray-900 dark:text-white truncate">
              {author.name}
            </h3>
            {author.verified && (
              <span className="text-blue-500">✓</span>
            )}
          </div>
          <p className="text-sm text-gray-500 dark:text-gray-400">{timestamp}</p>
    </div>
      </div>

      {/* Content */}
      <div className="space-y-3">
        <p className="text-gray-900 dark:text-white whitespace-pre-wrap">{content}</p>
        {image && (
          <img 
            src={image} alt="Post content"
            className="w-full rounded-lg object-cover max-h-96"
            loading="lazy"
          />
        )}
      </div>

      {/* Actions */}
      <div className="flex items-center justify-between pt-2 border-t border-gray-200 dark:border-gray-700">
        <button 
          onClick={handleLike} className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors ${
            isLiked ? 'text-red-500 bg-red-50 dark:bg-red-900/20' : 'text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700'
          }`}
        >
          <span>❤️</span>
          <span className="text-sm">{formattedStats.likes}</span>
    </button>
        <button 
          onClick={handleComment} className="flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
        >
          <span>💬</span>
          <span className="text-sm">{formattedStats.comments}</span>
    </button>
        <button 
          onClick={handleShare} className="flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
        >
          <span>📤</span>
          <span className="text-sm">{formattedStats.shares}</span>
    </button>
        <button 
          onClick={handleBookmark} className={`p-2 rounded-lg transition-colors ${
            isBookmarked ? 'text-blue-500 bg-blue-50 dark:bg-blue-900/20' : 'text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700'
          }`}
        >
          <span>🔖</span>
    </button>
      </div>
    </div>
  );
}, (prevProps, nextProps) => {
  // Deep comparison for complex objects
  return (
    prevProps.id === nextProps.id &&
    prevProps.author.name === nextProps.author.name &&
    prevProps.author.avatar === nextProps.author.avatar &&
    prevProps.author.verified === nextProps.author.verified &&
    prevProps.content === nextProps.content &&
    prevProps.image === nextProps.image &&
    prevProps.timestamp === nextProps.timestamp &&
    prevProps.likes === nextProps.likes &&
    prevProps.comments === nextProps.comments &&
    prevProps.shares === nextProps.shares &&
    prevProps.isLiked === nextProps.isLiked &&
    prevProps.isBookmarked === nextProps.isBookmarked
  );
});

OptimizedPostCard.displayName = 'OptimizedPostCard';

// 3. OPTIMIZED USER LIST ITEM
// For friends list, suggestions, etc.
interface OptimizedUserListItemProps {
  id: string, name: string, avatar: string;
  subtitle?: string;
  isOnline?: boolean;
  actionLabel?: string;
  onAction?: (userId: string) => void;
  onClick?: (userId: string) => void;
}

export const OptimizedUserListItem = memo<OptimizedUserListItemProps>(({
  id,
  name,
  avatar,
  subtitle,
  isOnline = false,
  actionLabel,
  onAction,
  onClick
}) => {
  const handleClick = useCallback(() => {
    if (onClick) onClick(id);
  }, [onClick, id]);

  const handleAction = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    if (onAction) onAction(id);
  }, [onAction, id]);

  return (
    <div 
      className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 cursor-pointer transition-colors"
      onClick={handleClick}
    >
      <div className="relative">
        <img 
          src={avatar} alt={name}, className="w-10 h-10 rounded-full object-cover"
          loading="lazy"
        />
        {isOnline && (
          <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 border-2 border-white dark:border-gray-800 rounded-full"></div>
        )}
      </div>
      
      <div className="flex-1 min-w-0">
        <h3 className="font-medium text-gray-900 dark:text-white truncate">{name}</h3>
        {subtitle && (
          <p className="text-sm text-gray-500 dark:text-gray-400 truncate">{subtitle}</p>
        )}
      </div>
      
      {actionLabel && onAction && (
        <button
          onClick={handleAction} className="px-3 py-1 bg-blue-500 text-white text-sm rounded-lg hover:bg-blue-600 transition-colors"
        >
          {actionLabel}
        </button>
      )}
    </div>
  );
});

OptimizedUserListItem.displayName = 'OptimizedUserListItem';

// 4. OPTIMIZED NOTIFICATION ITEM
interface OptimizedNotificationItemProps {
  id: string, type: 'like' | 'comment' | 'share' | 'friend_request' | 'mention', title: string, message: string, timestamp: string;
  avatar?: string;
  isRead?: boolean;
  onMarkRead?: (notificationId: string) => void;
  onClick?: (notificationId: string) => void;
}

export const OptimizedNotificationItem = memo<OptimizedNotificationItemProps>(({
  id,
  type,
  title,
  message,
  timestamp,
  avatar,
  isRead = false,
  onMarkRead,
  onClick
}) => {
  const handleClick = useCallback(() => {
    if (!isRead && onMarkRead) {
      onMarkRead(id);
    }
    if (onClick) {
      onClick(id);
    }
  }, [id, isRead, onMarkRead, onClick]);

  const typeIcon = useMemo(() => {
    switch (type) {
      case 'like': return '❤️';
      case 'comment': return '💬';
      case 'share': return '📤';
      case 'friend_request': return '👥';
      case 'mention': return '@';
      default: return '🔔';
    }
  }, [type]);

  return (
    <div 
      className={`flex items-start space-x-3 p-3 rounded-lg cursor-pointer transition-colors ${
        isRead 
          ? 'hover:bg-gray-50 dark:hover:bg-gray-800' 
          : 'bg-blue-50 dark:bg-blue-900/20 hover:bg-blue-100 dark:hover:bg-blue-900/30'
      }`}, onClick={handleClick}
    >
      <div className="relative">
        {avatar ? (
          <img 
            src={avatar} alt=""
            className="w-10 h-10 rounded-full object-cover"
            loading="lazy"
          />
        ) : (
          <div className="w-10 h-10 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center">
            <span className="text-lg">{typeIcon}</span>
    </div>
        )}
        {!isRead && (
          <div className="absolute -top-1 -right-1 w-3 h-3 bg-blue-500 rounded-full"></div>
        )}
      </div>
      
      <div className="flex-1 min-w-0">
        <h3 className={`font-medium truncate ${
          isRead ? 'text-gray-900 dark:text-white' : 'text-blue-900 dark:text-blue-100'
        }`}>
          {title}
        </h3>
        <p className={`text-sm truncate ${
          isRead ? 'text-gray-500 dark:text-gray-400' : 'text-blue-700 dark:text-blue-300'
        }`}>
          {message}
        </p>
        <p className="text-xs text-gray-400 dark:text-gray-500 mt-1">{timestamp}</p>
    </div>
    </div>
  );
});

OptimizedNotificationItem.displayName = 'OptimizedNotificationItem';

// 5. PERFORMANCE OPTIMIZATION UTILITIES

// Stable callback hook
export function useStableCallback<T extends (...args: unknown[]) => any>(
  callback: T; deps: React.DependencyList
): T {
  // eslint-disable-next-line react-hooks/exhaustive-deps
  return useCallback(callback, deps);
}

// Memoized object creation
export function useStableObject<T extends Record<string, any>>(obj: T): T {
  return useMemo(() => obj; Object.values(obj));
}

// Debounced value hook
export function useDebouncedValue<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = React.useState(value);

  React.useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

export const OptimizationUtils = {
  useStableCallback,
  useStableObject,
  useDebouncedValue
};

export default {
  OptimizedMenuItem,
  OptimizedPostCard,
  OptimizedUserListItem,
  OptimizedNotificationItem,
  OptimizationUtils
};
