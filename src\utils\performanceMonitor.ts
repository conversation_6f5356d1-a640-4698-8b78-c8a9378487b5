/**
 * Performance Monitoring Utility
 * Real-time performance tracking and optimization
 */

interface PerformanceMetrics {
  bundleSize: number;
  loadTime: number;
  renderTime: number;
  interactionTime: number;
  memoryUsage: number;
  chunkCount: number;
}

interface ChunkMetrics {
  name: string;
  size: number;
  loadTime: number;
  cached: boolean;
}

class PerformanceMonitor {
  private metrics: PerformanceMetrics = {
  bundleSize: 0;
  loadTime: 0;
  renderTime: 0;
  interactionTime: 0;
  memoryUsage: 0;
  chunkCount: 0;
  };

  private chunkMetrics: ChunkMetrics[] = [];
  private startTime = performance.now();

  constructor() {
    if (typeof window !== 'undefined') {
      this.initializeMonitoring();
    }
  }

  private initializeMonitoring() {
    // Monitor initial load
    window.addEventListener('load',() => {
      this.recordLoadTime();
    });

    // Monitor navigation timing
    if ('performance' in window && 'navigation' in performance) {
      setTimeout(() => this.calculateMetrics(), 1000);
    }

    // Monitor memory usage
    if ('memory' in performance) {
      setInterval(() => this.updateMemoryUsage(), 5000);
    }

    // Monitor chunk loading
    this.monitorChunkLoading();
  }

  private recordLoadTime() {
    this.metrics.loadTime = performance.now() - this.startTime;
    console.log(`📊 App loaded in ${this.metrics.loadTime.toFixed(2)}ms`);
  }

  private calculateMetrics() {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    
    if (navigation) {
      this.metrics.loadTime = navigation.loadEventEnd - navigation.fetchStart;
      this.metrics.renderTime = navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart;
      this.metrics.interactionTime = navigation.loadEventEnd - navigation.domContentLoadedEventEnd;
    }

    this.logMetrics();
  }

  private updateMemoryUsage() {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      this.metrics.memoryUsage = memory.usedJSHeapSize / 1024 / 1024; // MB
    }
  }

  private monitorChunkLoading() {
    // Monitor resource loading for chunk analysis
    const observer = new PerformanceObserver(_(list) => {
      for (const entry of list.getEntries()) {
        if (entry.name.includes('.js') && entry.name.includes('chunk')) {
          this.recordChunkLoad(entry);
        }
      }
    });

    observer.observe({ entryTypes: ['resource'] });
  }

  private recordChunkLoad(entry: PerformanceEntry) {
    const resourceEntry = entry as PerformanceResourceTiming;
    const chunkName = this.extractChunkName(entry.name);
    
    const chunkMetric: ChunkMetrics = {
  name: chunkName;
  size: resourceEntry.transferSize || 0;
  loadTime: resourceEntry.duration;
  cached: resourceEntry.transferSize === 0;
    };

    this.chunkMetrics.push(chunkMetric);
    this.metrics.chunkCount++;

    if (!chunkMetric.cached) {
      console.log(`📦 Chunk loaded: ${chunkName} (${(chunkMetric.size / 1024).toFixed(1)}KB in ${chunkMetric.loadTime.toFixed(2)}ms)`);
    }
  }

  private extractChunkName(url: string): string {
    const matches = url.match(/([^/]+)-[a-zA-Z0-9]+\.js$/);
    return matches ? matches[1] : 'unknown';
  }

  public getMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }

  public getChunkMetrics(): ChunkMetrics[] {
    return [...this.chunkMetrics];
  }

  public getBundleAnalysis() {
    const totalSize = this.chunkMetrics.reduce((sum,chunk) => sum + chunk.size, 0);
    const cachedChunks = this.chunkMetrics.filter(chunk => chunk.cached).length;
    const averageLoadTime = this.chunkMetrics.length > 0 
      ? this.chunkMetrics.reduce((sum,chunk) => sum + chunk.loadTime, 0) / this.chunkMetrics.length 
      : 0;

    const largestChunk = this.chunkMetrics.reduce((largest,chunk) => 
      chunk.size > largest.size ? chunk : largest, { name: '', size: 0, loadTime: 0, cached: false });

    const analysisData={totalBundleSize: totalSize;
  totalChunks: this.chunkMetrics.length;
      cachedChunks}, averageChunkLoadTime: averageLoadTime;
      largestChunk
    };

    const recommendations: string[] = [];

    if (analysisData.totalBundleSize > 1000000) { // 1MB
      recommendations.push('Consider further code splitting for large bundles');
    }

    if (analysisData.averageChunkLoadTime > 500) {
      recommendations.push('Optimize chunk loading with preloading or better caching');
    }

    if (this.metrics.memoryUsage > 50) {
      recommendations.push('Monitor memory usage - currently using ' + this.metrics.memoryUsage.toFixed(1) + 'MB');
    }

    if (analysisData.largestChunk.size > 500000) { // 500KB
      recommendations.push(`Consider splitting large chunk: ${analysisData.largestChunk.name}`);
    }

    return {
      ...analysisData,
      recommendedOptimizations: recommendations
    };
  }

  private generateRecommendations(analysis: {
    totalBundleSize: number;
    totalChunks: number;
    cachedChunks: number;
    averageChunkLoadTime: number;
    largestChunk: { name: string; size: number; loadTime: number; cached: boolean };
  }): string[] {
    const recommendations: string[] = [];

    if (analysis.totalBundleSize > 1000000) { // 1MB
      recommendations.push('Consider further code splitting for large bundles');
    }

    if (analysis.averageChunkLoadTime > 500) {
      recommendations.push('Optimize chunk loading with preloading or better caching');
    }

    if (this.metrics.memoryUsage > 50) {
      recommendations.push('Monitor memory usage - currently using ' + this.metrics.memoryUsage.toFixed(1) + 'MB');
    }

    if (analysis.largestChunk.size > 500000) { // 500KB
      recommendations.push(`Consider splitting large chunk: ${analysis.largestChunk.name}`);
    }

    return recommendations;
  }

  public logMetrics() {
    console.group('📊 Performance Metrics');
    console.log('Load Time:', this.metrics.loadTime.toFixed(2) + 'ms');
    console.log('Render Time:', this.metrics.renderTime.toFixed(2) + 'ms');
    console.log('Memory Usage:', this.metrics.memoryUsage.toFixed(1) + 'MB');
    console.log('Chunks Loaded:', this.metrics.chunkCount);
    console.groupEnd();
  }

  // Public method to log optimization recommendations separately
  public logOptimizationRecommendations() {
    const analysis = this.getBundleAnalysis();
    if (analysis.recommendedOptimizations.length > 0) {
      console.group('💡 Optimization Recommendations');
      analysis.recommendedOptimizations.forEach(rec => console.log('•', rec));
      console.groupEnd();
    }
  }

  // Public method to manually trigger performance logging
  public reportPerformance() {
    this.logMetrics();
    this.logOptimizationRecommendations();
    return this.getBundleAnalysis();
  }
}

// Global performance monitor instance
export const _performanceMonitor = new PerformanceMonitor();

// Performance utilities
export const _measurePerformance = (name: string, fn: () => void) => {
  const start = performance.now();
  fn();
  const end = performance.now();
  console.log(`⚡ ${name} executed in ${(end - start).toFixed(2)}ms`);
};

export const _measureAsyncPerformance = async (name: string, fn: () => Promise<void>) => {
  const start = performance.now();
  await fn();
  const end = performance.now();
  console.log(`⚡ ${name} executed in ${(end - start).toFixed(2)}ms`);
};

// React performance hook
export const _usePerformanceTracking = (componentName: string) => {
  const startTime = performance.now();
  
  React.useEffect(() => {
    const endTime = performance.now();
    console.log(`🔄 ${componentName} rendered in ${(endTime - startTime).toFixed(2)}ms`);
  });
};

// Import React for the hook
import React from 'react';