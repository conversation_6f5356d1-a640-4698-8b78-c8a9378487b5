import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';

// Enhanced code splitting for App component with preloading
const App = React.lazy(() => {
  return Promise.all([import('./App'),
    // Preload critical components
    import('./components/layout/AppLayout'),
    import('./components/providers')
  ]).then(([appModule]) => appModule);
});

// Initialize WebSocket fallback system for development
if (import.meta.env.DEV) {
  import('./utils/websocketFallback').then(({ webSocketFallback }) => {
    webSocketFallback.init();
  });
}

// Enhanced loading fallback for main app
const MainAppFallback = () => (
  <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
    <div className="text-center space-y-4">
      <div className="w-16 h-16 border-4 border-t-blue-500 border-blue-200 rounded-full animate-spin mx-auto"></div>
      <div className="text-lg font-medium text-gray-700 dark:text-gray-300">
        Loading Facebook Clone...
      </div>
    </div>
  </div>
);

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <React.Suspense fallback={<MainAppFallback />}>
      <App />
    </React.Suspense>
  </React.StrictMode>
);
