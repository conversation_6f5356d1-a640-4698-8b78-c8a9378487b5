import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import AdvancedMessagingContainer from './MessagingContainer';
import MessagingMigrationService from '../../utils/messagingMigration';

interface MessagingMigrationWrapperProps {
  forceAdvanced?: boolean;
  enableFeatureToggle?: boolean;
}

const MessagingMigrationWrapper: React.FC<MessagingMigrationWrapperProps> = ({
  forceAdvanced = false,
  enableFeatureToggle = true
}) => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [useAdvanced, setUseAdvanced] = useState(() => {
    // Check URL parameter first
    const urlParam = searchParams.get('advanced');
    if (urlParam !== null) {
      return urlParam === 'true';
    }

    // Check localStorage preference
    const saved = localStorage.getItem('messaging_use_advanced');
    if (saved !== null) {
      return saved === 'true';
    }

    // Check if migration is completed
    const migrationService = MessagingMigrationService.getInstance();
    const migrationStatus = migrationService.getMigrationStatus();
    return (migrationStatus?.completed ?? false) || forceAdvanced;
  });

  const [showToggle, setShowToggle] = useState(enableFeatureToggle);
  const [migrationService] = useState(() => MessagingMigrationService.getInstance());

  // Update localStorage when preference changes
  useEffect(() => {
    localStorage.setItem('messaging_use_advanced', String(useAdvanced));
  }, [useAdvanced]);

  // Update URL parameter when preference changes
  useEffect(() => {
    if (enableFeatureToggle) {
      const newParams = new URLSearchParams(searchParams);
      if (useAdvanced) {
        newParams.set('advanced', 'true');
      } else {
        newParams.delete('advanced');
      }
      setSearchParams(newParams, { replace: true });
    }
  }, [useAdvanced, enableFeatureToggle, searchParams, setSearchParams]);

  // Hide toggle if migration is not completed and not forced
  useEffect(() => {
    const migrationStatus = migrationService.getMigrationStatus();
    if (!forceAdvanced && !(migrationStatus?.completed ?? false)) {
      setShowToggle(false);
    }
  }, [forceAdvanced, migrationService]);

  const handleToggleAdvanced = () => {
    setUseAdvanced(prev => !prev);
    
    // Show feedback
    if (window.toast) {
      window.toast.success(
        useAdvanced 
          ? 'Switched to classic messaging' 
          : 'Switched to advanced messaging'
      );
    }
  };

  const handleResetMigration = () => {
    if (window.confirm('This will reset your messaging system. Are you sure?')) {
      localStorage.removeItem('messaging_migration_completed');
      localStorage.removeItem('messaging_migration_date');
      localStorage.removeItem('messaging_use_advanced');
      
      if (window.toast) {
        window.toast.success('Migration reset. Please refresh the page.');
      }
      
      // Refresh after a short delay
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    }
  };

  const migrationStatus = migrationService.getMigrationStatus();

  return (
    <div className="relative w-full">
      {/* Feature toggle */}
      {showToggle && (
        <div className="absolute top-4 right-4 z-50 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-3">
          <div className="flex items-center gap-3">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Messaging Mode:
            </span>
            <button
              onClick={handleToggleAdvanced} className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                useAdvanced 
                  ? 'bg-blue-600' 
                  : 'bg-gray-200 dark:bg-gray-600'
              }`}
              aria-label={`Switch to ${useAdvanced ? 'classic' : 'advanced'}, messaging`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  useAdvanced ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
    </button>
            <span className="text-sm text-gray-600 dark:text-gray-400">
              {useAdvanced ? 'Advanced' : 'Classic'}
            </span>
    </div>
          {/* Migration info */}
          <div className="mt-2 pt-2 border-t border-gray-200 dark:border-gray-600">
            <div className="text-xs text-gray-500 dark:text-gray-400">
              Migration: {migrationStatus?.completed ? '✅ Complete' : '⏳ Pending'}
              {migrationStatus?.timestamp && (
                <div>Date: {new Date(migrationStatus.timestamp).toLocaleDateString()}</div>
              )}
            </div>
            
            {/* Reset button for development */}
            {process.env.NODE_ENV === 'development' && (
              <button
                onClick={handleResetMigration} className="mt-1 text-xs text-red-500 hover:text-red-700 underline"
              >
                Reset Migration
              </button>
            )}
          </div>
    </div>
      )}

      {/* Feature comparison banner */}
      {showToggle && !useAdvanced && (
        <div className="bg-blue-50 dark:bg-blue-900/20 border-b border-blue-200 dark:border-blue-800 p-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="text-blue-600 dark:text-blue-400 text-sm font-medium">
                💡 Try Advanced Messaging
              </div>
              <div className="text-blue-600 dark:text-blue-400 text-sm">
                Get reactions, threads, keyboard navigation, and more!
              </div>
    </div>
            <button
              onClick={() => setUseAdvanced(true)} className="text-blue-600 dark:text-blue-400 text-sm font-medium hover:underline"
            >
              Switch Now →
            </button>
    </div>
        </div>
      )}

      {/* Render appropriate messaging component */}
      <AdvancedMessagingContainer
        enableAdvancedFeatures={useAdvanced || forceAdvanced} enableKeyboardNavigation={useAdvanced || forceAdvanced}, enableNotifications={true} migrationMode="auto"
      />

      {/* Feature showcase overlay */}
      {useAdvanced && showToggle && (
        <div className="absolute bottom-4 left-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-3 max-w-sm">
          <div className="text-green-800 dark:text-green-200 text-sm font-medium mb-1">
            🎉 Advanced Features Active
          </div>
          <div className="text-green-700 dark:text-green-300 text-xs space-y-1">
            <div>• Message reactions and threads</div>
            <div>• Keyboard navigation (F1 for help)</div>
            <div>• Enhanced notifications</div>
            <div>• Improved accessibility</div>
    </div>
        </div>
      )}
    </div>
  );
};

export default MessagingMigrationWrapper;