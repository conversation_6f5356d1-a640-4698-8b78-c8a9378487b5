import React, { 
  useState, 
  useEffect, 
  useRef, 
  useCallback, 
  useMemo, 
  memo
} from 'react';
import { useInView } from 'react-intersection-observer';
import { ErrorBoundary } from 'react-error-boundary';
import { useRealTimeFeed } from '@/hooks/useRealTimeFeed';
import { useFeedPersonalization } from '@/hooks/useFeedPersonalization';
import { useImmediateOptimization } from '@/hooks/useImmediateOptimizations';
import { BasePost } from '@/types/shared';
import { cn } from '@/lib/utils';

// Import refactored components
import FeedFilters, { type FeedFilters as FeedFiltersType, DEFAULT_FILTERS } from './FeedFilters';
import { FeedErrorFallback } from './FeedErrorBoundary';
import FeedHeader from './FeedHeader';
import VirtualizedFeedList from './VirtualizedFeedList';

// Component props interface
interface VirtualizedNewsFeedProps {
  posts: BasePost[], isLoading: boolean;
  isLoadingMore?: boolean;
  hasNextPage?: boolean;
  onLoadMore?: () => void;
  onRefresh: () => void;
  onPostInteraction?: (postId: string, action: string, data?: unknown) => void;
  filter?: 'all' | 'friends' | 'pages' | 'groups';
  sortBy?: 'recent' | 'relevant' | 'top';
  onFilterChange?: (filter: 'all' | 'friends' | 'pages' | 'groups') => void;
  onSortChange?: (sort: 'recent' | 'relevant' | 'top') => void;
  showFilters?: boolean;
  className?: string;
  enableVirtualization?: boolean;
  itemHeight?: number;
  containerHeight?: string;
}

// Constants
const DEFAULT_ITEM_HEIGHT = 400;
const CONTAINER_HEIGHT = '80vh';

const VirtualizedNewsFeed: React.FC<VirtualizedNewsFeedProps> = memo(({
  posts,
  isLoading,
  isLoadingMore = false,
  hasNextPage = false,
  onLoadMore,
  onRefresh,
  onPostInteraction,
  filter = 'all',
  sortBy = 'recent',
  onFilterChange,
  onSortChange,
  showFilters = true,
  className = '',
  enableVirtualization = true,
  itemHeight = DEFAULT_ITEM_HEIGHT,
  containerHeight = CONTAINER_HEIGHT
}) => {
  // State management
  const [showScrollToTop, setShowScrollToTop] = useState(false);
  const [newPostsCount, setNewPostsCount] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);

  // Hooks for enhanced functionality
  const { createOptimizedCallback, getPerformanceMetrics } = useImmediateOptimization('VirtualizedNewsFeed');
  const { personalizedPosts } = useFeedPersonalization(posts, true);
  const { newPosts: realTimePosts, newPostsCount: newPostsAvailable } = useRealTimeFeed({ posts: personalizedPosts });

  // Intersection observer for scroll to top button
  const { ref: topRef, inView: isTopInView } = useInView({
    threshold: 0.1,
    rootMargin: '100px'
  });

  // Update scroll to top visibility
  useEffect(() => {
    setShowScrollToTop(!isTopInView);
  }, [isTopInView]);

  // Update new posts count
  useEffect(() => {
    if (newPostsAvailable) {
      setNewPostsCount(prev => prev + 1);
    }
  }, [newPostsAvailable]);

  // Scroll to top handler
  const handleScrollToTop = useCallback(() => {
    containerRef.current?.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
    setNewPostsCount(0);
  }, []);

  // Enhanced refresh handler
  const handleRefresh = useCallback(() => {
    setNewPostsCount(0);
    onRefresh();
  }, [onRefresh]);

  // Memoized filter handlers
  const handleFilterChange = useCallback((newFilter: 'all' | 'friends' | 'pages' | 'groups') => {
    onFilterChange?.(newFilter);
  }, [onFilterChange]);

  const handleSortChange = useCallback((newSort: 'recent' | 'relevant' | 'top') => {
    onSortChange?.(newSort);
  }, [onSortChange]);

  // Memoized final posts list
  const finalPosts = useMemo(() => {
    return realTimePosts.length > 0 ? realTimePosts : posts;
  }, [realTimePosts, posts]);

  return (
    <ErrorBoundary
      FallbackComponent={FeedErrorFallback} onReset={handleRefresh}
    >
      <div 
        ref={containerRef} className={cn(
          "w-full max-w-2xl mx-auto space-y-6 relative",
          className
        )}
      >
        {/* Top reference for intersection observer */}
        <div ref={topRef} className="absolute top-0 left-0 w-1 h-1" />

        {/* Feed Header */}
        <FeedHeader
          isLoading={isLoading} onRefresh={handleRefresh}, showScrollToTop={showScrollToTop} onScrollToTop={handleScrollToTop}, newPostsCount={newPostsCount}
        />

        {/* Feed Filters */}
        {showFilters && onFilterChange && onSortChange && (
          <FeedFilters
            filter={filter} sortBy={sortBy}, onFilterChange={handleFilterChange} onSortChange={handleSortChange}, showFilters={showFilters}
          />
        )}

        {/* Virtualized Feed List */}
        <VirtualizedFeedList
          posts={finalPosts} isLoading={isLoading}, isLoadingMore={isLoadingMore} hasNextPage={hasNextPage}, onLoadMore={onLoadMore} onPostInteraction={onPostInteraction}, enableVirtualization={enableVirtualization} itemHeight={itemHeight}, containerHeight={containerHeight}
        />

        {/* Loading state for optimization */}
        {isOptimizing && (
          <div className="text-center py-4">
            <div className="text-sm text-gray-500 dark:text-gray-400">
              Optimizing feed performance...
            </div>
    </div>
        )}
      </div>
    </ErrorBoundary>
  );
});

VirtualizedNewsFeed.displayName = 'VirtualizedNewsFeed';

export default VirtualizedNewsFeed;
