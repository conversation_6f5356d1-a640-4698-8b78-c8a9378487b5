{"timestamp": "2025-08-01T08:35:29.454Z", "totalFilesProcessed": 152, "totalFixesApplied": 152, "fixedFiles": ["src\\actions\\buttonActions.ts", "src\\components\\admin\\ErrorsTab.tsx", "src\\components\\advanced\\AdvancedVirtualizedFeed.tsx", "src\\components\\advanced\\FuzzySearchDemo.tsx", "src\\components\\AdvancedFileUpload.tsx", "src\\components\\AdvancedReactions.tsx", "src\\components\\AdvancedSearchComponent.tsx", "src\\components\\EnhancedVideoCall.tsx", "src\\components\\errorBoundaries\\EnhancedErrorBoundary.tsx", "src\\components\\EventCalendar.tsx", "src\\components\\feed\\VirtualizedNewsFeed.tsx", "src\\components\\FileAttachment.tsx", "src\\components\\FriendRequestsPanel.tsx", "src\\components\\GroupManagementPanel.tsx", "src\\components\\messages\\ConversationItem.tsx", "src\\components\\messages\\ConversationPane.tsx", "src\\components\\messages\\ConversationSidebar.tsx", "src\\components\\messages\\MessageInput.tsx", "src\\components\\messages\\MessageList.tsx", "src\\components\\messages\\MessageRenderer.tsx", "src\\components\\messaging\\AccessibleMessageInput.tsx", "src\\components\\messaging\\AdvancedMessageThread.tsx", "src\\components\\messaging\\config\\bundleOptimization.ts", "src\\components\\messaging\\hooks\\useResilientMessaging.ts", "src\\components\\messaging\\KeyboardNavigableConversationList.tsx", "src\\components\\messaging\\KeyboardNavigableMessage.tsx", "src\\components\\messaging\\MessagingContainer.tsx", "src\\components\\messaging\\MessagingNotificationIntegration.tsx", "src\\components\\messaging\\OptimizedMessageBubble.tsx", "src\\components\\messaging\\performance\\benchmarkUtils.ts", "src\\components\\messaging\\PrivacyAwareMessage.tsx", "src\\components\\messaging\\recovery\\ErrorRecoveryManager.tsx", "src\\components\\messaging\\SecureFileUpload.tsx", "src\\components\\messaging\\SecureMessageInput.tsx", "src\\components\\messaging\\VirtualizedMessageList.tsx", "src\\components\\modals\\CreateReelModal.tsx", "src\\components\\notifications\\EnhancedNotificationsCenter.tsx", "src\\components\\notifications\\NotificationsCenter.tsx", "src\\components\\optimization\\ComponentOptimizer.tsx", "src\\components\\OptimizedDemoTab.tsx", "src\\components\\OptimizedMessagingInterface.tsx", "src\\components\\OptimizedNotificationCenter.tsx", "src\\components\\PageHeader.tsx", "src\\components\\PeopleYouMayKnow.tsx", "src\\components\\performance\\OptimizedComponents.tsx", "src\\components\\PhotoAlbumManager.tsx", "src\\components\\PlaylistManager.tsx", "src\\components\\posts\\CreatePostNew.tsx", "src\\components\\posts\\EnhancedCreatePost.tsx", "src\\components\\posts\\EnhancedPostCard.tsx", "src\\components\\posts\\PostActions.tsx", "src\\components\\posts\\PostCard.tsx", "src\\components\\posts\\PostComments.tsx", "src\\components\\posts\\PostHeader.tsx", "src\\components\\pwa\\PWAManager.tsx", "src\\components\\ReactionsSystem.tsx", "src\\components\\search\\EnhancedSearchInterface.tsx", "src\\components\\shared\\CommonComponents.tsx", "src\\components\\shared\\FormComponents.tsx", "src\\components\\shared\\MediaComponents.tsx", "src\\components\\shared\\UnifiedSearch.tsx", "src\\components\\shared\\widgets\\TasksContent.tsx", "src\\components\\story\\MediaUpload.tsx", "src\\components\\StoryViewer.tsx", "src\\components\\ui\\UnifiedImage.tsx", "src\\components\\VirtualizedNewsFeedRefactored.tsx", "src\\components\\youtube\\AddToPlaylistButton.tsx", "src\\components\\youtube\\channel\\SubscriptionsList.tsx", "src\\components\\youtube\\community\\CommunityPosts.tsx", "src\\components\\youtube\\notifications\\NotificationCenter.tsx", "src\\components\\youtube\\playlist\\PlaylistCard.tsx", "src\\components\\youtube\\playlist\\PlaylistGrid.tsx", "src\\components\\youtube\\shared\\TrendingSection.tsx", "src\\components\\youtube\\video\\VideoRecommendations.tsx", "src\\components\\youtube\\video\\VirtualizedVideoGrid.tsx", "src\\contexts\\PlaylistContext.tsx", "src\\hooks\\optimizedHooks.ts", "src\\hooks\\useAdvancedPerformanceMonitor.ts", "src\\hooks\\useButtonActions.ts", "src\\hooks\\useDemoKeyboardNavigation.tsx", "src\\hooks\\useFileUpload.ts", "src\\hooks\\useMessaging.ts", "src\\hooks\\useOptimizedPostsAPI.ts", "src\\hooks\\useOptimizedRealTime.ts", "src\\hooks\\usePostFilters.ts", "src\\hooks\\useRealTimeCollaboration.ts", "src\\hooks\\useRealTimeFeed.ts", "src\\hooks\\useRealTimeNotifications.ts", "src\\hooks\\useVoicePlayback.ts", "src\\hooks\\youtube\\useChannelData.ts", "src\\hooks\\youtube\\useVideoSearch.ts", "src\\lib\\apollo-client-dev.ts", "src\\pages\\Friends.tsx", "src\\pages\\Home.tsx", "src\\pages\\LiveStream.tsx", "src\\pages\\Profile.tsx", "src\\pages\\Reels.tsx", "src\\pages\\SearchResults.tsx", "src\\pages\\Weather.tsx", "src\\pages\\youtube\\LibraryPage.tsx", "src\\pages\\youtube\\PlaylistPage.tsx", "src\\pages\\youtube\\SubscriptionsPage.tsx", "src\\services\\AdvancedSearchService.ts", "src\\services\\AIFeedEngine.ts", "src\\services\\AssetPreloadService.ts", "src\\services\\EnhancedMessagingService.ts", "src\\services\\errorLoggingService.ts", "src\\services\\FeedPersonalizationService.ts", "src\\services\\FileUploadService.ts", "src\\services\\GroupManagementService.ts", "src\\services\\ImageOptimizationService.ts", "src\\services\\MessageFileService.ts", "src\\services\\messaging\\ConnectionManager.ts", "src\\services\\messaging\\ErrorRecoveryService.ts", "src\\services\\messaging\\MessageSearchService.ts", "src\\services\\messaging\\MessagingIntegrationService.ts", "src\\services\\messaging\\MessagingNotificationService.ts", "src\\services\\messaging\\PresenceService.ts", "src\\services\\messaging\\PrivacyService.ts", "src\\services\\messaging\\RealTimeMessagingService.ts", "src\\services\\messaging\\__tests__\\MessageSearchService.test.ts", "src\\services\\messaging\\__tests__\\TypingManager.test.ts", "src\\services\\NewsFeedAlgorithm.ts", "src\\services\\OptimizedMessagingService.ts", "src\\services\\OptimizedNotificationManager.ts", "src\\services\\RealTimeCollaborationService.ts", "src\\services\\RealTimeFeedService.ts", "src\\services\\RealWebSocketService.ts", "src\\services\\SearchDiscoveryService.ts", "src\\services\\UnifiedMessagingService.ts", "src\\services\\UnifiedPrivacyService.ts", "src\\services\\VoiceMessageService.ts", "src\\services\\WebSocketManager.ts", "src\\services\\youtube\\googleCustomSearchService.ts", "src\\services\\youtube\\integratedYouTubeService.ts", "src\\services\\youtube\\playlistService.ts", "src\\services\\youtube\\realYouTubeService.ts", "src\\services\\youtube\\studioService.ts", "src\\services\\youtube\\youtubeApiService.ts", "src\\services\\youtube\\youTubePlayerService.ts", "src\\utils\\advancedPerformance.ts", "src\\utils\\codeSplitting.ts", "src\\utils\\codeSplittingStrategy.tsx", "src\\utils\\errorHandler.ts", "src\\utils\\marketplace.ts", "src\\utils\\messagingMemoization.tsx", "src\\utils\\messagingMigration.ts", "src\\utils\\productionMonitoring.ts", "src\\utils\\resourcePreloader.ts", "src\\utils\\virtualizedList.ts", "src\\utils\\websocketFallback.ts", "src\\utils\\youtube.ts"], "recommendations": ["Run npm run type-check to verify TypeScript compilation", "Run npm run lint to check for remaining ESLint issues", "Test the application to ensure functionality is preserved", "Consider adding stricter type definitions where appropriate"]}