import React, { lazy, ComponentType } from 'react';

/**
 * Configuration options for robust lazy loading
 */
export interface LazyLoadOptions {
  /** Maximum number of retry attempts (default: 3) */
  maxRetries?: number;
  /** Initial delay between retries in milliseconds (default: 1000) */
  retryDelay?: number;
  /** Multiplier for exponential backoff (default: 2) */
  backoffMultiplier?: number;
  /** Maximum delay between retries in milliseconds (default: 10000) */
  maxDelay?: number;
  /** Custom error handler */
  onError?: (error: Error, retryCount: number) => void;
  /** Custom retry condition */
  retryCondition?: (error: Error) => boolean;
}

/**
 * Error types for import failures
 */
export enum ImportErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  MODULE_NOT_FOUND = 'MODULE_NOT_FOUND',
  DEPENDENCY_ERROR = 'DEPENDENCY_ERROR',
  RUNTIME_ERROR = 'RUNTIME_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

/**
 * Enhanced error class for import failures
 */
export class ImportError extends Error {
  public readonly type: ImportErrorType;
  public readonly originalError: Error;
  public readonly retryCount: number;
  public readonly timestamp: Date;

  constructor(
  message: string, type: ImportErrorType, originalError: Error, retryCount: number = 0
  ) {
    super(message);
    this.name = 'ImportError';
    this.type = type;
    this.originalError = originalError;
    this.retryCount = retryCount;
    this.timestamp = new Date();
  }
}

/**
 * Classifies import errors based on error message and type
 */
function classifyImportError(error: Error): ImportErrorType {
  const message = error.message.toLowerCase();
  
  if (message.includes('failed to fetch') || 
      message.includes('network error') || 
      message.includes('fetch error')) {
    return ImportErrorType.NETWORK_ERROR;
  }
  
  if (message.includes('module not found') || 
      message.includes('cannot resolve') ||
      message.includes('404')) {
    return ImportErrorType.MODULE_NOT_FOUND;
  }
  
  if (message.includes('dependency') || 
      message.includes('import') ||
      message.includes('export')) {
    return ImportErrorType.DEPENDENCY_ERROR;
  }
  
  if (message.includes('runtime') || 
      message.includes('execution')) {
    return ImportErrorType.RUNTIME_ERROR;
  }
  
  return ImportErrorType.UNKNOWN_ERROR;
}

/**
 * Determines if an error should trigger a retry
 */
function shouldRetry(error: Error, retryCount: number, maxRetries: number): boolean {
  if (retryCount >= maxRetries) {
    return false;
  }
  
  const errorType = classifyImportError(error);
  
  // Retry network errors and unknown errors, but not module resolution errors
  return errorType === ImportErrorType.NETWORK_ERROR || 
         errorType === ImportErrorType.UNKNOWN_ERROR;
}

/**
 * Calculates delay for exponential backoff
 */
function calculateDelay(
  retryCount: number, baseDelay: number, backoffMultiplier: number, maxDelay: number
): number {
  const delay = baseDelay * Math.pow(backoffMultiplier, retryCount);
  return Math.min(delay, maxDelay);
}

/**
 * Sleeps for the specified number of milliseconds
 */
function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Enhanced lazy loading function with retry logic and error handling
 */
export function createRobustLazy<T extends ComponentType<unknown>>(
  importFunc: () => Promise<{ default: T }>;
  options: LazyLoadOptions = {}
): React.LazyExoticComponent<T> {
  const {
    maxRetries = 3;
    retryDelay = 1000,
    backoffMultiplier = 2,
    maxDelay = 10000,
    onError,
    retryCondition = shouldRetry
  } = options;

  /**
   * Wrapper function that implements retry logic
   */
  const robustImportFunc = async (): Promise<{ default: T }> => {
    let lastError: Error | null = null;
    let retryCount = 0;

    while (retryCount <= maxRetries) {
      try {
        // Attempt to import the module
        const module = await importFunc();
        
        // Validate that the module has a default export
        if (!module || !module.default) {
          throw new Error('Module does not have a default export');
        }
        
        // Success - return the module
        return module;
        
      } catch (error) {
        const importError = error instanceof Error ? error : new Error(String(error));
        lastError = importError;
        
        // Log the error with context
        console.warn(`Import attempt ${retryCount + 1} failed:`, {
  error: importError.message;
          retryCount,
          maxRetries,
          timestamp: new Date().toISOString()
        });
        
        // Call custom error handler if provided
        if (onError) {
          try {
            onError(importError, retryCount);
          } catch (handlerError) {
            console.error('Error in custom error handler:', handlerError);
          }
        }
        
        // Check if we should retry
        if (!retryCondition(importError, retryCount, maxRetries)) {
          break;
        }
        
        // If this isn't the last attempt, wait before retrying
        if (retryCount < maxRetries) {
          const delay = calculateDelay(retryCount, retryDelay, backoffMultiplier, maxDelay);
          console.log(`Retrying import in ${delay}ms...`);
          await sleep(delay);
        }
        
        retryCount++;
      }
    }
    
    // All retries failed - throw enhanced error
    const errorType = lastError ? classifyImportError(lastError) : ImportErrorType.UNKNOWN_ERROR;
    const enhancedError = new ImportError(
      `Failed to import module after ${retryCount} attempts: ${lastError?.message || 'Unknown error'}`,
      errorType,
      lastError || new Error('Unknown error'),
      retryCount
    );
    
    throw enhancedError;
  };

  // Create and return the lazy component
  return lazy(robustImportFunc);
}

/**
 * Default configuration for common use cases
 */
export const LazyLoadPresets = {
  /** Fast retry for critical components */
  CRITICAL: { maxRetries: 5, retryDelay: 500, backoffMultiplier: 1.5 }
    maxDelay: 5000
  } as LazyLoadOptions;
  /** Standard retry for regular components */
  STANDARD: { maxRetries: 3, retryDelay: 1000, backoffMultiplier: 2 }
    maxDelay: 10000
  } as LazyLoadOptions;
  /** Conservative retry for non-critical components */
  CONSERVATIVE: { maxRetries: 2, retryDelay: 2000, backoffMultiplier: 3 }
    maxDelay: 15000
  } as LazyLoadOptions
};

/**
 * Convenience function for creating lazy components with standard retry logic
 */
export function createStandardLazy<T extends ComponentType<unknown>>(
  importFunc: () => Promise<{ default: T }>
): React.LazyExoticComponent<T> {
  return createRobustLazy(importFunc, LazyLoadPresets.STANDARD);
}

/**
 * Convenience function for creating lazy components with critical retry logic
 */
export function createCriticalLazy<T extends ComponentType<unknown>>(
  importFunc: () => Promise<{ default: T }>
): React.LazyExoticComponent<T> {
  return createRobustLazy(importFunc, LazyLoadPresets.CRITICAL);
}