import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import {
  Search,
  MapPin,
  Heart,
  MessageCircle,
  Phone,
  Star,
  Grid,
  List,
  Plus,
  Camera,
  DollarSign,
  Package,
  Shield,
  SlidersHorizontal
} from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';

import { formatDistanceToNow } from 'date-fns';
import { toast } from 'sonner';
import { AnimatePresence } from 'framer-motion';
import SocialFeaturesService, { MarketplaceListing } from '@/services/SocialFeaturesService';
import { OptimizedImage } from './OptimizedImage';
import MarketplaceListingCard from './MarketplaceListingCard';

// Import constants and utilities
import { 
  CATEGORIES, 
  CONDITIONS, 
  INITIAL_FILTERS, 
  INITIAL_NEW_LISTING, 
  MarketplaceFilters 
} from '@/constants/marketplace';
import { 
  getSellerResponse, 
  validateListingForm, 
  filterListings, 
  formatPrice 
} from '@/utils/marketplace';

interface FacebookMarketplaceProps {
  className?: string;
  userId?: string;
}

// Mock marketplace listings with working images
const getMockListings = (): MarketplaceListing[] => [
  {
    id: 'listing-1',
    seller: {
      id: 'user-1',
      name: 'Sarah Johnson',
      email: '<EMAIL>',
      avatar: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjQwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjU5ZTBiIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGRvbWluYW50LWJhc2VsaW5lPSJtaWRkbGUiIGZpbGw9IndoaXRlIiBmb250LXNpemU9IjQ4cHgiPkEwMzwvdGV4dD48L3N2Zz4=',
      isOnline: true,
      lastSeen: new Date(),
      friendsCount: 245,
      followersCount: 890,
      followingCount: 123
    },
    title: 'iPhone 14 Pro - Excellent Condition',
    description: 'Barely used iPhone 14 Pro in excellent condition. Comes with original box, charger, and screen protector already applied.',
    price: 899,
    currency: 'USD',
    condition: 'used' as const,
    category: 'electronics',
    photos: [
      'https://ui-avatars.com/api/?name=Phone&background=1f2937&color=fff&size=400',
      'https://ui-avatars.com/api/?name=iPhone&background=374151&color=fff&size=400'
    ],
    location: {
      id: 'loc-1',
      name: 'New York, NY',
      coordinates: { lat: 40.7128, lng: -74.0060 }
    },
    createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
    views: 156,
    deliveryOptions: ['pickup', 'shipping'],
    contactInfo: {
      phone: '+****************',
      email: '<EMAIL>',
      messenger: true
    }
  },
  {
    id: 'listing-2',
    seller: {
      id: 'user-2',
      name: 'Mike Chen',
      email: '<EMAIL>',
      avatar: 'https://ui-avatars.com/api/?name=Mike+Chen&background=3b82f6&color=fff&size=150',
      isOnline: false,
      lastSeen: new Date(Date.now() - 1 * 60 * 60 * 1000),
      friendsCount: 178,
      followersCount: 654,
      followingCount: 89
    },
    title: 'Vintage Leather Sofa - Brown',
    description: 'Beautiful vintage brown leather sofa in great condition. Perfect for living room or office. Comfortable seating for 3 people.',
    price: 450,
    currency: 'USD',
    condition: 'used' as const,
    category: 'furniture',
    photos: [
      'https://ui-avatars.com/api/?name=Sofa&background=8b5cf6&color=fff&size=400',
      'https://ui-avatars.com/api/?name=Leather&background=7c3aed&color=fff&size=400'
    ],
    location: {
      id: 'loc-2',
      name: 'Brooklyn, NY',
      coordinates: { lat: 40.6782, lng: -73.9442 }
    },
    createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
    views: 89,
    deliveryOptions: ['pickup'],
    contactInfo: {
      phone: '+****************',
      email: '<EMAIL>',
      messenger: true
    }
  },
  {
    id: 'listing-3',
    seller: {
      id: 'user-3',
      name: 'Emily Rodriguez',
      email: '<EMAIL>',
      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
      isOnline: true,
      lastSeen: new Date(),
      friendsCount: 312,
      followersCount: 1200,
      followingCount: 156
    },
    title: 'MacBook Air M2 - Like New',
    description: 'MacBook Air with M2 chip, barely used. Comes with original charger and box. Perfect for students or professionals.',
    price: 1099,
    currency: 'USD',
    condition: 'used' as const,
    category: 'electronics',
    photos: [
      'https://images.unsplash.com/photo-1541807084-5c52b6b3adef?w=400&h=400&fit=crop',
      'https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=400&h=400&fit=crop'
    ],
    location: {
      id: 'loc-3',
      name: 'Manhattan, NY',
      coordinates: { lat: 40.7831, lng: -73.9712 }
    },
    createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
    views: 234,
    deliveryOptions: ['pickup', 'shipping'],
    contactInfo: {
      phone: '+****************',
      email: '<EMAIL>',
      messenger: true
    }
  },
  {
    id: 'listing-4',
    seller: {
      id: 'user-4',
      name: 'David Wilson',
      email: '<EMAIL>',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
      isOnline: true,
      lastSeen: new Date(),
      friendsCount: 189,
      followersCount: 567,
      followingCount: 234
    },
    title: 'Nike Air Jordan 1 - Size 10',
    description: 'Authentic Nike Air Jordan 1 in excellent condition. Size 10 US. Perfect for sneaker collectors.',
    price: 180,
    currency: 'USD',
    condition: 'used' as const,
    category: 'clothing',
    photos: [
      'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=400&h=400&fit=crop',
      'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400&h=400&fit=crop'
    ],
    location: {
      id: 'loc-4',
      name: 'Queens, NY',
      coordinates: { lat: 40.7282, lng: -73.7949 }
    },
    createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
    views: 67,
    deliveryOptions: ['pickup', 'shipping'],
    contactInfo: {
      phone: '+****************',
      email: '<EMAIL>',
      messenger: true
    }
  }
];

// Custom hooks for better state management
const useMarketplace = (socialService: SocialFeaturesService | null) => {
  const [listings, setListings] = useState<MarketplaceListing[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadMarketplace = useCallback(async () => {
    if (!socialService) {
      console.warn('SocialService not available, using mock data');
      const mockListings = getMockListings();
      setListings(mockListings);
      setLoading(false);
      return mockListings;
    }
    
    setLoading(true);
    setError(null);
    try {
      // Try to get listings from service, fallback to mock data
      let allListings = socialService.getMarketplaceListings();
      if (allListings.length === 0) {
        allListings = getMockListings();
      }
      setListings(allListings);
      return allListings;
    } catch (err) {
      // Fallback to mock data if service fails
      console.warn('Service failed, using mock data:', err);
      const mockListings = getMockListings();
      setListings(mockListings);
      setError('Service unavailable, showing sample data');
      return mockListings;
    } finally {
      setLoading(false);
    }
  }, [socialService]);

  return { listings, setListings, loading, error, loadMarketplace };
};

const useSavedListings = () => {
  const [savedListings, setSavedListings] = useState<string[]>([]);

  useEffect(() => {
    try {
      const saved = localStorage.getItem('saved-listings');
      if (saved) {
        setSavedListings(JSON.parse(saved));
      }
    } catch {
      // Failed to load saved listings from localStorage
      toast.error('Failed to load saved listings');
    }
  }, []);

  const toggleSavedListing = useCallback((listingId: string, socialService: SocialFeaturesService | null = null) => {
    try {
      const newSaved = savedListings.includes(listingId)
        ? savedListings.filter(id => id !== listingId)
        : [...savedListings; listingId];
      
      setSavedListings(newSaved);
      localStorage.setItem('saved-listings', JSON.stringify(newSaved));
      
      // Only call service if available
      if (socialService) {
        try {
          socialService.saveMarketplaceListing(listingId);
        } catch (serviceError) {
          console.warn('Failed to save to service, but saved locally:', serviceError);
        }
      }
      
      toast.success(savedListings.includes(listingId) ? 'Listing unsaved' : 'Listing saved');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      toast.error('Failed to save listing: ' + errorMessage);
    }
  }, [savedListings]);

  return { savedListings, toggleSavedListing };
};

// Enhanced loading component
const LoadingGrid: React.FC = () => {
  const skeletonItems = Array.from({ length: 8 }, (_, index) => (
    <Card key={index} className="animate-pulse">
      <div className="aspect-square bg-gray-200 dark:bg-gray-700" />
      <div className="p-3 space-y-2">
        <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4" />
        <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2" />
        <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-full" />
    </div>
    </Card>
  ));

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
      {skeletonItems}
    </div>
  );
};

// Enhanced empty state component
const EmptyState: React.FC = () => (
  <div className="text-center py-12">
    <Package className="w-12 h-12 text-gray-400 mx-auto mb-4" />
    <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">No listings found</h3>
    <p className="text-gray-500 dark:text-gray-400">Try adjusting your search or filters</p>
    </div>
);

const FacebookMarketplace: React.FC<FacebookMarketplaceProps> = ({
  className = '',
  userId: _userId
}) => {
  console.log('React:', React);
  // ✅ ALL HOOKS MUST BE CALLED FIRST, BEFORE ANY CONDITIONAL LOGIC
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [selectedListing, setSelectedListing] = useState<MarketplaceListing | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  const [showCreateListing, setShowCreateListing] = useState(false);
  const [filters, setFilters] = useState<MarketplaceFilters>(INITIAL_FILTERS);
  const [filteredListings, setFilteredListings] = useState<MarketplaceListing[]>([]);
  const [isFiltering, setIsFiltering] = useState(false);

  // Messaging state
  const [showMessaging, setShowMessaging] = useState(false);
  const [messagingListing, setMessagingListing] = useState<MarketplaceListing | null>(null);
  const [messages, setMessages] = useState<Array<{
    id: string, userId: string, userName: string, userAvatar: string, message: string, timestamp: Date;
  }>>([]);
  const [newMessage, setNewMessage] = useState('');
  const chatContainerRef = useRef<HTMLDivElement>(null);
  
  const [newListing, setNewListing] = useState(INITIAL_NEW_LISTING);
  
  // Move socialService hook to the top, before any conditional logic
const socialService = useMemo(() => SocialFeaturesService.getInstance(), []);
  
  const { listings, setListings, loading, loadMarketplace } = useMarketplace(socialService);
  const { savedListings, toggleSavedListing } = useSavedListings();
  
  // ALL ADDITIONAL HOOKS - MOVED FROM AFTER CONDITIONAL RETURN
  
  // Apply filters and search with debouncing
  useEffect(() => {
    setIsFiltering(true);
    const timeoutId = setTimeout(() => {
      const filtered = filterListings(listings, searchQuery, filters);
      setFilteredListings(filtered);
      setIsFiltering(false);
    }, 300);
    return () => clearTimeout(timeoutId);
  }, [listings, searchQuery, filters]);

  // Load marketplace data
  useEffect(() => {
    if (socialService) {
      loadMarketplace();
    }
  }, [loadMarketplace, socialService]);

  // Auto-scroll to bottom when new messages are added
  useEffect(() => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
    }
  }, [messages]);

  const handleCreateListing = useCallback(async () => {
    try {
      const validationError = validateListingForm(newListing);
      if (validationError) {
        toast.error(validationError);
        return;
      }

      if (!socialService) {
        toast.error('Service not available');
        return;
      }

      const listing = await socialService.createMarketplaceListing({
        title: newListing.title,
        description: newListing.description,
        price: parseFloat(newListing.price),
        category: newListing.category,
        condition: newListing.condition as 'new' | 'used' | 'refurbished',
        photos: newListing.photos,
        location: newListing.location!,
        deliveryOptions: newListing.deliveryOptions,
        contactInfo: newListing.contactInfo
      });

      setListings(prev => [listing, ...prev]);
      setShowCreateListing(false);
      
      // Reset form
      setNewListing(INITIAL_NEW_LISTING);

      toast.success('Listing created successfully');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      toast.error('Failed to create listing: ' + errorMessage);
    }
  }, [newListing, socialService, setListings]);

  const handleContactSeller = useCallback((listing: MarketplaceListing, method: 'message' | 'phone' | 'email') => {
    switch (method) {
      case 'message': {
        setMessagingListing(listing);
        setShowMessaging(true);
        // Initialize conversation with a welcome message
        const welcomeMessage = {
          id: `msg-${Date.now()}`,
          userId: listing.seller.id,
          userName: listing.seller.name,
          userAvatar: listing.seller.avatar,
          message: `Hi! I'm interested in your listing "${listing.title}". Is it still available?`,
          timestamp: new Date()
        };
        setMessages([welcomeMessage]);
        toast.success('Opening message conversation...');
        break;
      }
      case 'phone':
        if (listing.contactInfo.phone) {
          window.open(`tel:${listing.contactInfo.phone}`);
          toast.success('Opening phone dialer...');
        } else {
          toast.error('Phone number not available');
        }
        break;
      case 'email':
        if (listing.contactInfo.email) {
          window.open(`mailto:${listing.contactInfo.email}?subject=Inquiry about ${listing.title}`);
          toast.success('Opening email client...');
        } else {
          toast.error('Email not available');
        }
        break;
    }
  }, []);

  const handleSendMessage = useCallback(() => {
    if (!newMessage.trim() || !messagingListing) return;

    const message = {
      id: `msg-${Date.now()}`,
      userId: 'current-user', // This would be the current user's ID
      userName: 'You',
      userAvatar: 'https://images.pexels.com/photos/614810/pexels-photo-614810.jpeg?w=100&h=100&fit=crop&crop=face',
      message: newMessage.trim(),
      timestamp: new Date()
    };

    setMessages(prev => [...prev; message]);
    setNewMessage('');

    // Simulate seller response after a delay
    setTimeout(() => {
      const sellerResponse = {
        id: `msg-${Date.now()}-response`,
        userId: messagingListing.seller.id,
        userName: messagingListing.seller.name,
        userAvatar: messagingListing.seller.avatar,
        message: getSellerResponse(newMessage.trim()),
        timestamp: new Date()
      };
      setMessages(prev => [...prev; sellerResponse]);
    }, 1000 + Math.random() * 2000); // Random delay between 1-3 seconds

    toast.success('Message sent!');
  }, [newMessage, messagingListing]);

  const handleCloseMessaging = useCallback(() => {
    setShowMessaging(false);
    setMessagingListing(null);
    setMessages([]);
    setNewMessage('');
  }, []);

  const handleListingClick = useCallback((listing: MarketplaceListing) => {
    toast.info(`Opening ${listing.title}`); // User feedback
    setSelectedListing(listing);
  }, []);

  const handleCloseDialog = useCallback(() => {
    setSelectedListing(null);
  }, []);

  const renderListingDetail = useCallback(() => {
    if (!selectedListing) {
      return null;
    }

    const isSaved = savedListings.includes(selectedListing.id);

    return (
      <Dialog open={!!selectedListing} onOpenChange={handleCloseDialog}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>{selectedListing.title}</DialogTitle>
    </DialogHeader>
          <div className="space-y-6">
            {/* Photo Gallery */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {selectedListing.photos.map((photo, index) => (
                <div key={index} className="relative aspect-square">
                  <OptimizedImage
                    src={photo} alt={`${selectedListing.title} - Photo ${index + 1}`}, className="w-full h-full object-cover rounded-lg"
                    priority={index === 0}
                  />
    </div>
              ))}
            </div>

            {/* Details */}
            <div className="grid md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <h3 className="font-semibold text-lg">Description</h3>
                  <div className="text-gray-600 dark:text-gray-400 mt-2">
                    {selectedListing.description}
                  </div>
    </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="font-semibold text-sm">Category</div>
                    <div className="text-gray-600 dark:text-gray-400">{selectedListing.category}</div>
    </div>
                  <div>
                    <div className="font-semibold text-sm">Condition</div>
                    <div className="text-gray-600 dark:text-gray-400 capitalize">{selectedListing.condition}</div>
    </div>
                </div>

                <div>
                  <div className="font-semibold text-sm">Location</div>
                  <div className="text-gray-600 dark:text-gray-400 flex items-center">
                    <MapPin className="w-4 h-4 mr-1" />
                    {selectedListing.location.name}
                  </div>
    </div>
                <div>
                  <div className="font-semibold text-sm">Delivery Options</div>
                  <div className="flex flex-wrap gap-2 mt-1">
                    {selectedListing.deliveryOptions.map((option) => (
                      <Badge key={option} variant="secondary">
                        {option}
                      </Badge>
                    ))}
                  </div>
    </div>
              </div>

              <div className="space-y-4">
                {/* Price */}
                <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
                  <div className="text-3xl font-bold text-green-600 dark:text-green-400">
                    ${selectedListing.price.toLocaleString()}
                  </div>
                  <div className="text-sm text-gray-500">Listed {formatDistanceToNow(selectedListing.createdAt)} ago</div>
    </div>
                {/* Seller Info */}
                <div className="border dark:border-gray-700 rounded-lg p-4">
                  <div className="flex items-center space-x-3 mb-3">
                    <Avatar>
                      <AvatarImage src={selectedListing.seller.avatar} />
                      <AvatarFallback>{selectedListing.seller.name[0]}</AvatarFallback>
    </Avatar>
                    <div>
                      <div className="flex items-center space-x-1">
                        <span className="font-semibold">{selectedListing.seller.name}</span>
                        {selectedListing.seller.verified && (
                          <Shield className="w-4 h-4 text-blue-500" />
                        )}
                      </div>
                      <div className="flex items-center space-x-1">
                        <Star className="w-4 h-4 text-yellow-500 fill-current" />
                        <span className="text-sm text-gray-600 dark:text-gray-400">
                          {selectedListing.seller.rating} ({selectedListing.seller.reviewCount} reviews)
                        </span>
    </div>
                    </div>
    </div>
                  {/* Contact Buttons */}
                  <div className="space-y-2">
                    <Button 
                      onClick={() => handleContactSeller(selectedListing, 'message')} className="w-full"
                    >
                      <MessageCircle className="w-4 h-4 mr-2" />
                      Send Message
                    </Button>
                    <div className="grid grid-cols-2 gap-2">
                      <Button 
                        variant="outline"
                        onClick={() => handleContactSeller(selectedListing, 'phone')} disabled={!selectedListing.contactInfo.phone}
                      >
                        <Phone className="w-4 h-4 mr-2" />
                        Call
                      </Button>
                      <Button 
                        variant="outline"
                        onClick={() => toggleSavedListing(selectedListing.id)}
                      >
                        <Heart className={`w-4 h-4 mr-2 ${isSaved ? 'fill-current text-red-500' : ''}`} />
                        {isSaved ? 'Saved' : 'Save'}
                      </Button>
    </div>
                  </div>
    </div>
              </div>
    </div>
          </div>
    </DialogContent>
      </Dialog>
    );
  }, [selectedListing, savedListings, handleContactSeller, handleCloseDialog, toggleSavedListing]);
  
  // Now conditional logic is safe - all hooks have been called
  if (!socialService) {
    return (
      <div className={`min-h-screen bg-gray-50 dark:bg-gray-900 p-4 ${className}`}>
        <div className="max-w-7xl mx-auto">
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600 dark:text-gray-400">Initializing Marketplace...</p>
    </div>
        </div>
    </div>
    );
  }

  if (loading) {
    return <LoadingGrid />;
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Marketplace</h1>
        <Button onClick={() => setShowCreateListing(true)}>
          <Plus className="w-4 h-4 mr-2" />
          Sell Something
        </Button>
    </div>
      {/* Search and Filters */}
      <div className="space-y-4">
        <div className="flex space-x-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search Marketplace"
              value={searchQuery} onChange={(e) => setSearchQuery(e.target.value)}, className="pl-10"
            />
    </div>
          <Button 
            variant="outline" 
            onClick={() => setShowFilters(!showFilters)} className="flex items-center space-x-2"
          >
            <SlidersHorizontal className="w-4 h-4" />
            <span>Filters</span>
    </Button>
          <div className="flex items-center space-x-2 border rounded-lg p-1">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'ghost'} size="sm"
              onClick={() => setViewMode('grid')}
            >
              <Grid className="w-4 h-4" />
    </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'ghost'} size="sm"
              onClick={() => setViewMode('list')}
            >
              <List className="w-4 h-4" />
    </Button>
          </div>
    </div>
        {/* Quick Filters */}
        <div className="flex space-x-2 overflow-x-auto pb-2">
          <Button
            variant={filters.priceMax <= 50 ? 'default' : 'outline'} size="sm"
            onClick={() => setFilters(prev => ({ ...prev, priceMin: 0, priceMax: 50 }))}, className="whitespace-nowrap"
          >
            Under $50
          </Button>
          <Button
            variant={filters.priceMax <= 100 && filters.priceMin >= 50 ? 'default' : 'outline'} size="sm"
            onClick={() => setFilters(prev => ({ ...prev, priceMin: 50, priceMax: 100 }))}, className="whitespace-nowrap"
          >
            $50 - $100
          </Button>
          <Button
            variant={filters.priceMax <= 500 && filters.priceMin >= 100 ? 'default' : 'outline'} size="sm"
            onClick={() => setFilters(prev => ({ ...prev, priceMin: 100, priceMax: 500 }))}, className="whitespace-nowrap"
          >
            $100 - $500
          </Button>
          <Button
            variant={filters.condition === 'new' ? 'default' : 'outline'} size="sm"
            onClick={() => setFilters(prev => ({ ...prev, condition: prev.condition === 'new' ? 'all' : 'new' }))}, className="whitespace-nowrap"
          >
            New Only
          </Button>
          <Button
            variant={filters.sortBy === 'recent' ? 'default' : 'outline'} size="sm"
            onClick={() => setFilters(prev => ({ ...prev, sortBy: 'recent' }))}, className="whitespace-nowrap"
          >
            Most Recent
          </Button>
    </div>
        {/* Categories */}
        <div className="flex space-x-2 overflow-x-auto pb-2">
          {CATEGORIES.map(category => (
            <Button
              key={category.id} variant={filters.category === category.id ? 'default' : 'outline'}, size="sm"
              onClick={() => setFilters(prev => ({ ...prev, category: category.id }))}, className="whitespace-nowrap"
            >
              <span className="mr-2">{category.icon}</span>
              {category.name}
            </Button>
          ))}
        </div>

        {/* Advanced Filters */}
        {showFilters && (
          <Card>
            <CardContent className="p-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                  <Label>Condition</Label>
                  <Select value={filters.condition} onValueChange={(value) => setFilters(prev => ({ ...prev, condition: value }))}>
                    <SelectTrigger>
                      <SelectValue />
    </SelectTrigger>
                    <SelectContent>
                      {CONDITIONS.map(condition => (
                        <SelectItem key={condition.id} value={condition.id}>
                          {condition.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
    </Select>
                </div>

                <div>
                  <Label>Price Range</Label>
                  <div className="space-y-2">
                    <Slider
                      min={0} max={10000}, step={50} value={[filters.priceMin, filters.priceMax]}, onValueChange={([min, max]) => setFilters(prev => ({ ...prev, priceMin: min, priceMax: max }))}
                    />
                    <div className="flex justify-between text-sm text-gray-500">
                      <span>${filters.priceMin}</span>
                      <span>${filters.priceMax}</span>
    </div>
                  </div>
    </div>
                <div>
                  <Label>Location</Label>
                  <Input
                    placeholder="Enter location"
                    value={filters.location} onChange={(e) => setFilters(prev => ({ ...prev, location: e.target.value }))}
                  />
    </div>
                <div>
                  <Label>Sort By</Label>
                  <Select value={filters.sortBy} onValueChange={(value: 'recent' | 'price_low' | 'price_high' | 'distance') => setFilters(prev => ({ ...prev, sortBy: value }))}>
                    <SelectTrigger>
                      <SelectValue />
    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="recent">Most Recent</SelectItem>
                      <SelectItem value="price_low">Price: Low to High</SelectItem>
                      <SelectItem value="price_high">Price: High to Low</SelectItem>
                      <SelectItem value="distance">Distance</SelectItem>
    </SelectContent>
                  </Select>
    </div>
              </div>
    </CardContent>
          </Card>
        )}
      </div>

      {/* Results */}
      <div>
        <div className="flex items-center justify-between mb-4">
          <div className="text-gray-600 dark:text-gray-400">
            {isFiltering ? (
              <span className="flex items-center space-x-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                <span>Filtering...</span>
    </span>
            ) : (
              <>
                {filteredListings.length} {filteredListings.length === 1 ? 'result' : 'results'}
                {searchQuery && (
                  <span className="text-sm text-gray-500 ml-2">
                    for "{searchQuery}"
                  </span>
                )}
              </>
            )}
          </div>
    </div>
        <AnimatePresence>
          <div className={`grid gap-4 ${
            viewMode === 'grid'
              ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
              : 'grid-cols-1 max-w-2xl mx-auto'
          }`}>
            {filteredListings.map(listing => (
              <MarketplaceListingCard
                key={listing.id} listing={listing}, isSaved={savedListings.includes(listing.id)} onListingClick={handleListingClick}, onToggleSaved={(listingId) => toggleSavedListing(listingId; socialService)}
              />
            ))}
          </div>
    </AnimatePresence>
        {filteredListings.length === 0 && <EmptyState />}
      </div>

      {/* Listing Detail Modal */}
      {renderListingDetail()}

      {/* Create Listing Modal */}
      <Dialog open={showCreateListing} onOpenChange={setShowCreateListing}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Create New Listing</DialogTitle>
    </DialogHeader>
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="title">Title *</Label>
                <Input
                  id="title"
                  placeholder="What are you selling?"
                  value={newListing.title} onChange={(e) => setNewListing(prev => ({ ...prev, title: e.target.value }))}
                />
    </div>
              <div>
                <Label htmlFor="price">Price *</Label>
                <div className="relative">
                  <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    id="price"
                    type="number"
                    placeholder="0.00"
                    value={newListing.price} onChange={(e) => setNewListing(prev => ({ ...prev, price: e.target.value }))}, className="pl-10"
                  />
    </div>
              </div>
    </div>
            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                placeholder="Describe your item..."
                value={newListing.description} onChange={(e) => setNewListing(prev => ({ ...prev, description: e.target.value }))}, className="min-h-[100px]"
              />
    </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label>Category *</Label>
                <Select value={newListing.category} onValueChange={(value) => setNewListing(prev => ({ ...prev, category: value }))}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
    </SelectTrigger>
                  <SelectContent>
                    {CATEGORIES.filter(c => c.id !== 'all').map(category => (
                      <SelectItem key={category.id} value={category.id}>
                        {category.icon} {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
    </Select>
              </div>

              <div>
                <Label>Condition</Label>
                <Select value={newListing.condition} onValueChange={(value: 'new' | 'used' | 'refurbished') => setNewListing(prev => ({ ...prev, condition: value }))}>
                  <SelectTrigger>
                    <SelectValue />
    </SelectTrigger>
                  <SelectContent>
                    {CONDITIONS.filter(c => c.id !== 'all').map(condition => (
                      <SelectItem key={condition.id} value={condition.id}>
                        {condition.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
    </Select>
              </div>
    </div>
            <div>
              <Label>Photos</Label>
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                <Camera className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                <span className="text-gray-500">Click to add photos</span>
                <span className="text-xs text-gray-400 mt-1 block">Add up to 10 photos</span>
    </div>
            </div>

            <div>
              <Label>Contact Information</Label>
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Switch
                    checked={newListing.contactInfo.messenger} onCheckedChange={(checked) => setNewListing(prev => ({
                      ...prev,
                      contactInfo: { ...prev.contactInfo, messenger: checked }
                    }))}
                  />
                  <Label>Allow messages on Messenger</Label>
    </div>
                <Input
                  placeholder="Phone number (optional)"
                  value={newListing.contactInfo.phone} onChange={(e) => setNewListing(prev => ({
                    ...prev,
                    contactInfo: { ...prev.contactInfo, phone: e.target.value }
                  }))}
                />
                
                <Input
                  placeholder="Email (optional)"
                  value={newListing.contactInfo.email} onChange={(e) => setNewListing(prev => ({
                    ...prev,
                    contactInfo: { ...prev.contactInfo, email: e.target.value }
                  }))}
                />
    </div>
            </div>

            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setShowCreateListing(false)}>
                Cancel
              </Button>
              <Button onClick={handleCreateListing}>
                Create Listing
              </Button>
    </div>
          </div>
    </DialogContent>
      </Dialog>

      {/* Messaging Modal */}
      <Dialog open={showMessaging} onOpenChange={handleCloseMessaging}>
        <DialogContent className="max-w-2xl max-h-[80vh] flex flex-col">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-3">
              {messagingListing && (
                <>
                  <Avatar className="w-8 h-8">
                    <AvatarImage src={messagingListing.seller.avatar} />
                    <AvatarFallback>
                      {messagingListing.seller.name.charAt(0)}
                    </AvatarFallback>
    </Avatar>
                  <div>
                    <span className="font-semibold">{messagingListing.seller.name}</span>
                    <div className="text-sm text-gray-500 font-normal">
                      About: {messagingListing.title}
                    </div>
    </div>
                </>
              )}
            </DialogTitle>
    </DialogHeader>
          {/* Listing Preview */}
          {messagingListing && (
            <div className="border-b pb-3 mb-3">
              <div className="flex items-center space-x-3 bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
                <div className="w-16 h-16 rounded-lg overflow-hidden bg-gray-200">
                  {messagingListing.photos.length > 0 ? (
                    <OptimizedImage
                      src={messagingListing.photos[0]} alt={messagingListing.title}, className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center">
                      <Package className="w-6 h-6 text-gray-400" />
    </div>
                  )}
                </div>
                <div className="flex-1">
                  <h4 className="font-medium text-sm">{messagingListing.title}</h4>
                  <div className="text-lg font-bold text-green-600">
                    {formatPrice(messagingListing.price)}
                  </div>
                  <div className="text-xs text-gray-500">
                    {messagingListing.location.name}
                  </div>
    </div>
              </div>
    </div>
          )}

          {/* Chat Interface */}
          <div className="flex-1 min-h-0 flex flex-col">
            {/* Messages Display */}
            <div ref={chatContainerRef} className="flex-1 overflow-y-auto p-4 space-y-3">
              {messages.map((message) => (
                <div key={message.id} className="flex items-start gap-3">
                  <Avatar className="w-8 h-8">
                    <AvatarImage src={message.userAvatar} alt={message.userName} />
                    <AvatarFallback>{message.userName.charAt(0)}</AvatarFallback>
    </Avatar>
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="font-medium text-sm">{message.userName}</span>
                      <span className="text-xs text-gray-500">
                        {formatDistanceToNow(message.timestamp, { addSuffix: true })}
                      </span>
    </div>
                    <p className="text-sm text-gray-700">{message.message}</p>
    </div>
                </div>
              ))}
            </div>
            
            {/* Message Input */}
            <div className="border-t p-4">
              <div className="flex items-center gap-3">
                <input
                  type="text"
                  value={newMessage} onChange={(e) => setNewMessage(e.target.value)}, placeholder="Type a message..."
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                />
                <Button
                  onClick={handleSendMessage} disabled={!newMessage.trim()}, size="sm"
                >
                  Send
                </Button>
    </div>
            </div>
    </div>
        </DialogContent>
    </Dialog>
    </div>
  );
};

export default FacebookMarketplace;
