import { toast } from 'sonner';

export type ErrorLevel = 'info' | 'warning' | 'error' | 'critical';
export type ErrorCategory = 'network' | 'validation' | 'auth' | 'api' | 'ui' | 'system' | 'media' | 'websocket' | 'storage';

export interface ErrorLog {
  id: string;
  level: ErrorLevel;
  category: ErrorCategory;
  message: string;
  details?: unknown;
  stack?: string;
  timestamp: Date;
  context?: string;
  userId?: string;
  metadata?: Record<string, unknown>;
}

interface ErrorLoggerConfig {
  enableConsoleLog: boolean;
  enableToast: boolean;
  enableRemoteLogging: boolean;
  toastDuration?: number;
  maxQueueSize?: number;
}

class ErrorLogger {
  private static instance: ErrorLogger;
  private config: ErrorLoggerConfig;
  private errorQueue: ErrorLog[] = [];
  private listeners: ((error: ErrorLog) => void)[] = [];

  private constructor() {
    this.config = {
      enableConsoleLog: import.meta.env.DEV,
      enableToast: true,
      enableRemoteLogging: import.meta.env.PROD,
      toastDuration: 5000,
      maxQueueSize: 100
    };
  }

  static getInstance(): ErrorLogger {
    if (!ErrorLogger.instance) {
      ErrorLogger.instance = new ErrorLogger();
    }
    return ErrorLogger.instance;
  }

  configure(config: Partial<ErrorLoggerConfig>): void {
    this.config = { ...this.config, ...config };
  }

  private generateErrorId(): string {
    return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private getErrorMessage(error: unknown): string {
    if (error instanceof Error) {
      return error.message;
    }
    if (typeof error === 'string') {
      return error;
    }
    return 'An unknown error occurred';
  }

  private getErrorStack(error: unknown): string | undefined {
    if (error instanceof Error) {
      return error.stack;
    }
    return undefined;
  }

  private getUserFriendlyMessage(category: ErrorCategory, message: string): string {
    const friendlyMessages: Record<ErrorCategory, (msg: string) => string> = {
      network: (_msg) => {
        if (msg.includes('fetch')) return 'Network connection error. Please check your internet connection.';
        if (msg.includes('timeout')) return 'Request timed out. Please try again.';
        return 'Network error occurred. Please try again later.';
      },
      validation: (_msg) => {
        if (msg.includes('email')) return 'Please enter a valid email address.';
        if (msg.includes('password')) return 'Password requirements not met.';
        if (msg.includes('required')) return 'Please fill in all required fields.';
        return 'Validation error. Please check your input.';
      },
      auth: (_msg) => {
        if (msg.includes('unauthorized')) return 'You need to log in to access this feature.';
        if (msg.includes('forbidden')) return 'You don\'t have permission to perform this action.';
        if (msg.includes('expired')) return 'Your session has expired. Please log in again.';
        return 'Authentication error. Please try logging in again.';
      },
      api: () => 'Server error. Please try again later.',
      ui: (_msg) => {
        if (msg.includes('React')) return 'Interface error. The page may need refreshing.';
        if (msg.includes('component')) return 'Display issue detected. Please refresh if needed.';
        return 'Interface error. Please refresh the page if issues persist.';
      },
      system: (_msg) => {
        if (msg.includes('ChunkLoadError') || msg.includes('Loading chunk')) {
          return 'App update detected. Please refresh the page.';
        }
        if (msg.includes('Network Error') || msg.includes('Failed to fetch')) {
          return 'Connection error. Please check your internet connection.';
        }
        return 'System error. Our team has been notified.';
      },
      media: (_msg) => {
        if (msg.includes('camera')) return 'Unable to access camera. Please check permissions.';
        if (msg.includes('microphone')) return 'Unable to access microphone. Please check permissions.';
        if (msg.includes('screen')) return 'Unable to share screen. Please try again.';
        return 'Media error. Please check your device settings.';
      },
      websocket: () => 'Connection error. Attempting to reconnect...',
      storage: (_msg) => {
        if (msg.includes('quota')) return 'Storage quota exceeded. Please clear some space.';
        return 'Storage error. Some features may not work properly.';
      }
    };

    return friendlyMessages[category]?.(message) || message;
  }

  private async sendToRemoteService(errorLog: ErrorLog): Promise<void> {
    if (!this.config.enableRemoteLogging) return;

    try {
      // In production, this would send to your error tracking service
      // Example: Sentry, LogRocket, etc.
      
      // For now, just store in localStorage as a demo
      const errors = JSON.parse(localStorage.getItem('error_logs') || '[]');
      errors.push({
        ...errorLog,
        timestamp: errorLog.timestamp.toISOString()
      });
      
      // Keep only last 50 errors
      if (errors.length > 50) {
        errors.splice(0, errors.length - 50);
      }
      
      localStorage.setItem('error_logs', JSON.stringify(errors));
    } catch (_err) {
      // Silently fail - we don't want error logging to cause more errors
      if (this.config.enableConsoleLog) {
        // Failed to send error to remote service - silently handled
      }
    }
  }

  private showToast(level: ErrorLevel, category: ErrorCategory, message: string): void {
    if (!this.config.enableToast) return;

    // In development, suppress generic system error messages entirely
    if (process.env.NODE_ENV === 'development') {
      if (message === 'System error. Our team has been notified.' || 
          message.includes('System error')) {
        return;
      }
    }

    // Even in production, only show generic system message for truly critical system errors
    if (message === 'System error. Our team has been notified.' && category === 'system') {
      // Only show if it's a critical level error
      if (level !== 'critical') {
        return;
      }
    }

    const toastOptions = {
      duration: this.config.toastDuration,
      dismissible: true
    };

    switch (level) {
      case 'info':
        toast.info(message, toastOptions);
        break;
      case 'warning':
        toast.warning(message, toastOptions);
        break;
      case 'error':
        toast.error(message, toastOptions);
        break;
      case 'critical':
        toast.error(message, {
          ...toastOptions,
          duration: Infinity,
          action: {
            label: 'Refresh',
            onClick: () => window.location.reload()
          }
        });
        break;
    }
  }

  private logToConsole(errorLog: ErrorLog): void {
    if (!this.config.enableConsoleLog) return;

    const style = {
      info: 'color: #3b82f6',
      warning: 'color: #f59e0b',
      error: 'color: #ef4444',
      critical: 'color: #dc2626; font-weight: bold'
    };

    console.group(`%c[${errorLog.level.toUpperCase()}] ${errorLog.category}`, style[errorLog.level]);
    console.log('Message:', errorLog.message);
    
    if (errorLog.context) {
      console.log('Context:', errorLog.context);
    }
    
    if (errorLog.details) {
      console.log('Details:', errorLog.details);
    }
    
    if (errorLog.metadata) {
      console.log('Metadata:', errorLog.metadata);
    }
    
    if (errorLog.stack) {
      console.log('Stack:', errorLog.stack);
    }
    
    console.log('Time:', errorLog.timestamp.toISOString());
    console.groupEnd();
  }

  private addToQueue(errorLog: ErrorLog): void {
    this.errorQueue.push(errorLog);
    
    if (this.errorQueue.length > (this.config.maxQueueSize || 100)) {
      this.errorQueue.shift();
    }
    
    // Notify listeners
    this.listeners.forEach(listener => listener(errorLog));
  }

  // Main logging method
  log(
    level: ErrorLevel,
    category: ErrorCategory,
    error: unknown,
    context?: string,
    metadata?: Record<string, unknown>
  ): void {
    const errorLog: ErrorLog = {
      id: this.generateErrorId(),
      level,
      category,
      message: this.getErrorMessage(error),
      details: error,
      stack: this.getErrorStack(error),
      timestamp: new Date(),
      context,
      metadata
    };

    // Add to queue
    this.addToQueue(errorLog);

    // Log to console in development
    this.logToConsole(errorLog);

    // Show user-friendly toast
    const userMessage = this.getUserFriendlyMessage(category, errorLog.message);
    this.showToast(level, category, userMessage);

    // Send to remote service
    this.sendToRemoteService(errorLog);
  }

  // Convenience methods
  info(category: ErrorCategory, message: string, context?: string, metadata?: Record<string, unknown>): void {
    this.log('info', category, message, context, metadata);
  }

  warning(category: ErrorCategory, error: unknown, context?: string, metadata?: Record<string, unknown>): void {
    this.log('warning', category, error, context, metadata);
  }

  error(category: ErrorCategory, error: unknown, context?: string, metadata?: Record<string, unknown>): void {
    // Add detailed logging for UI errors in development
    if (category === 'ui' && process.env.NODE_ENV === 'development') {
      console.error('UI Error Details:', {
        error,
        context,
        metadata,
        errorMessage: error instanceof Error ? error.message : String(error),
        errorStack: error instanceof Error ? error.stack : undefined
      });
    }
    this.log('error', category, error, context, metadata);
  }

  critical(category: ErrorCategory, error: unknown, context?: string, metadata?: Record<string, unknown>): void {
    this.log('critical', category, error, context, metadata);
  }

  // Get error history
  getErrorQueue(): ErrorLog[] {
    return [...this.errorQueue];
  }

  clearErrorQueue(): void {
    this.errorQueue = [];
  }

  // Subscribe to errors
  subscribe(listener: (error: ErrorLog) => void): () => void {
    this.listeners.push(listener);
    
    // Return unsubscribe function
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  // Get errors by criteria
  getErrors(filter?: {
    level?: ErrorLevel;
    category?: ErrorCategory;
    since?: Date;
  }): ErrorLog[] {
    let filtered = this.errorQueue;
    
    if (filter?.level) {
      filtered = filtered.filter(e => e.level === filter.level);
    }
    
    if (filter?.category) {
      filtered = filtered.filter(e => e.category === filter.category);
    }
    
    if (filter?.since) {
      filtered = filtered.filter(e => e.timestamp >= (filter.since || new Date(0)));
    }
    
    return filtered;
  }
}

// Export singleton instance
export const errorLogger = ErrorLogger.getInstance();

// Export helper functions for easy usage
export const _logInfo = (category: ErrorCategory, message: string, context?: string, metadata?: Record<string, unknown>) => errorLogger.info(category, message, context, metadata);

export const _logWarning = (category: ErrorCategory, error: unknown, context?: string, metadata?: Record<string, unknown>) => errorLogger.warning(category, error, context, metadata);

export const _logError = (category: ErrorCategory, error: unknown, context?: string, metadata?: Record<string, unknown>) => errorLogger.error(category, error, context, metadata);

export const _logCritical = (category: ErrorCategory, error: unknown, context?: string, metadata?: Record<string, unknown>) => errorLogger.critical(category, error, context, metadata);
