import React, { useState, memo, useCallback } from 'react';
import {
  Heart,
  MessageSquare,
  Share2,
  Users,
  Calendar,
  FileText,
  Eye,
  EyeOff,
  Shield,
  Info
} from 'lucide-react';
import { unifiedPrivacyService, ActivityPrivacySettings } from '@/services/UnifiedPrivacyService';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

interface ActivityPrivacyControlsProps {
  userId: string;
  onSettingsChange?: (settings: ActivityPrivacySettings) => void;
  className?: string;
}

type VisibilityOption = 'public' | 'friends' | 'only_me';

interface ActivityCategory {
  id: keyof ActivityPrivacySettings, label: string, description: string, icon: React.ComponentType<{ className?: string }>;
  color: string;
}

const ACTIVITY_CATEGORIES: ActivityCategory[] = [
  {
    id: 'likes',
    label: 'Likes',
    description: 'Control who can see what you like',
    icon: Heart,
    color: 'text-red-500'
  },
  {
    id: 'comments',
    label: 'Comments',
    description: 'Manage visibility of your comments',
    icon: MessageSquare,
    color: 'text-blue-500'
  },
  {
    id: 'shares',
    label: 'Shares',
    description: 'Control who sees what you share',
    icon: Share2,
    color: 'text-green-500'
  },
  {
    id: 'followedPages',
    label: 'Followed Pages',
    description: 'Manage visibility of pages you follow',
    icon: FileText,
    color: 'text-purple-500'
  },
  {
    id: 'friendsList',
    label: 'Friends List',
    description: 'Control who can see your friends',
    icon: Users,
    color: 'text-orange-500'
  },
  {
    id: 'groups',
    label: 'Groups',
    description: 'Manage group membership visibility',
    icon: Users,
    color: 'text-indigo-500'
  },
  {
    id: 'events',
    label: 'Events',
    description: 'Control event attendance visibility',
    icon: Calendar,
    color: 'text-teal-500'
  }
];

const VISIBILITY_OPTIONS = [
  { value: 'public', label: 'Public', icon: Eye, description: 'Anyone can see this' },
  { value: 'friends', label: 'Friends', icon: Users, description: 'Only your friends' },
  { value: 'only_me', label: 'Only Me', icon: EyeOff, description: 'Private to you' }
];

const ActivityPrivacyControls: React.FC<ActivityPrivacyControlsProps> = memo(({
  userId,
  onSettingsChange,
  className
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [activitySettings, setActivitySettings] = useState<ActivityPrivacySettings>(
    () => unifiedPrivacyService.getActivityPrivacySettings(userId)
  );
  const [hasChanges, setHasChanges] = useState(false);
  const [activeTab, setActiveTab] = useState<string>('likes');

  const handleSettingChange = useCallback(<T extends keyof ActivityPrivacySettings>(
    category: T,
    field: keyof ActivityPrivacySettings[T],
    value: ActivityPrivacySettings[T][keyof ActivityPrivacySettings[T]]
  ) => {
    setActivitySettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        [field]: value
      }
    }));
    setHasChanges(true);
  }, []);

  const handleSave = useCallback(() => {
    unifiedPrivacyService.updateActivityPrivacy(userId, activitySettings);
    onSettingsChange?.(activitySettings);
    setHasChanges(false);
    setIsOpen(false);
    toast.success('Activity privacy settings updated successfully');
  }, [userId, activitySettings, onSettingsChange]);

  const handleReset = useCallback(() => {
    const defaultSettings = unifiedPrivacyService.getActivityPrivacySettings(userId);
    setActivitySettings(defaultSettings);
    setHasChanges(false);
  }, [userId]);

  const getPrivacyScore = (): { score: number, level: string, color: string } => {
    let score = 0;
    let totalSettings = 0;

    Object.values(activitySettings).forEach((category: ActivityPrivacySettings[keyof ActivityPrivacySettings]) => {
      if (category.visibility) {
        totalSettings++;
        if (category.visibility === 'only_me') score += 2;
        else if (category.visibility === 'friends') score += 1;
      }
      if ('showOnProfile' in category && typeof category.showOnProfile === 'boolean') {
        totalSettings++;
        if (!category.showOnProfile) score += 1;
      }
      if ('showInActivity' in category && typeof category.showInActivity === 'boolean') {
        totalSettings++;
        if (!category.showInActivity) score += 1;
      }
    });

    const percentage = (score / (totalSettings * 2)) * 100;

    if (percentage >= 80) return { score: percentage, level: 'High', color: 'text-green-600' };
    if (percentage >= 50) return { score: percentage, level: 'Medium', color: 'text-yellow-600' };
    return { score: percentage, level: 'Low', color: 'text-red-600' };
  };

  const privacyScore = getPrivacyScore();

  return (
    <>
      <Button
        variant="outline"
        onClick={() => setIsOpen(true)} className={cn('gap-2', className)}
      >
        <Shield className="w-4 h-4" />
        Activity Privacy
      </Button>

      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Shield className="w-5 h-5 text-blue-600" />
              Activity Privacy Controls
            </DialogTitle>
            <DialogDescription>
              Control who can see your activities across the platform
            </DialogDescription>
    </DialogHeader>
          <div className="mt-4">
            {/* Privacy Score */}
            <Card className="mb-6">
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">Privacy Level</p>
                    <p className={cn('text-2xl font-bold', privacyScore.color)}>
                      {privacyScore.level}
                    </p>
    </div>
                  <div className="text-right">
                    <p className="text-sm text-muted-foreground">Score</p>
                    <p className="text-2xl font-bold">{Math.round(privacyScore.score)}%</p>
    </div>
                </div>
    </CardContent>
            </Card>

            {/* Activity Categories Tabs */}
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid grid-cols-4 lg:grid-cols-7 h-auto">
                {ACTIVITY_CATEGORIES.map((category) => {
                  const Icon = category.icon;
                  return (
                    <TabsTrigger
                      key={category.id}
                      value={category.id}
                      className="flex flex-col gap-1 h-auto py-2"
                    >
                      <Icon className={cn('w-4 h-4', category.color)} />
                      <span className="text-xs">{category.label}</span>
    </TabsTrigger>
                  );
                })}
              </TabsList>

              {ACTIVITY_CATEGORIES.map((category) => {
                const settings = activitySettings[category.id];
                const Icon = category.icon;

                return (
                  <TabsContent key={category.id} value={category.id} className="mt-6">
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <Icon className={cn('w-5 h-5', category.color)} />
                          {category.label}
                        </CardTitle>
                        <CardDescription>{category.description}</CardDescription>
    </CardHeader>
                      <CardContent className="space-y-6">
                        {/* Visibility Setting */}
                        <div className="space-y-2">
                          <Label>Who can see your {category.label.toLowerCase()}?</Label>
                          <Select
                            value={('visibility' in settings ? settings.visibility : 'friends') as VisibilityOption} onValueChange={(value: VisibilityOption) =>
                              handleSettingChange(category.id, 'visibility', value)
                            }
                          >
                            <SelectTrigger>
                              <SelectValue />
    </SelectTrigger>
                            <SelectContent>
                              {VISIBILITY_OPTIONS.map((option) => {
                                const OptionIcon = option.icon;
                                return (
                                  <SelectItem key={option.value} value={option.value}>
                                    <div className="flex items-center gap-2">
                                      <OptionIcon className="w-4 h-4" />
                                      <span>{option.label}</span>
                                      <span className="text-xs text-muted-foreground ml-auto">
                                        {option.description}
                                      </span>
    </div>
                                  </SelectItem>
                                );
                              })}
                            </SelectContent>
    </Select>
                        </div>

                        <Separator />

                        {/* Show on Profile */}
                        {'showOnProfile' in settings && (
                          <div className="flex items-center justify-between space-x-2">
                            <div className="flex-1">
                              <Label htmlFor={`${category.id}-profile`} className="cursor-pointer">
                                Show on Profile
                              </Label>
                              <p className="text-sm text-muted-foreground">
                                Display this activity on your profile page
                              </p>
    </div>
                            <Switch
                              id={`${category.id}-profile`}
                              checked={('showOnProfile' in settings ? settings.showOnProfile : false)}
                              onCheckedChange={(checked) =>
                                handleSettingChange(category.id, 'visibility', checked ? 'friends' : 'only_me')
                              }
                            />
    </div>
                        )}

                        {/* Show in Activity Feed */}
                        {'showInActivity' in settings && (
                          <div className="flex items-center justify-between space-x-2">
                            <div className="flex-1">
                              <Label htmlFor={`${category.id}-activity`} className="cursor-pointer">
                                Show in Activity Feed
                              </Label>
                              <p className="text-sm text-muted-foreground">
                                Include in your friends' activity feeds
                              </p>
    </div>
                            <Switch
                              id={`${category.id}-activity`}
                              checked={('showInActivity' in settings ? settings.showInActivity : false)}
                              onCheckedChange={(checked) =>
                                handleSettingChange(category.id, 'visibility', checked ? 'friends' : 'only_me')
                              }
                            />
    </div>
                        )}

                        {/* Special settings for friends list */}
                        {category.id === 'friendsList' && 'showCount' in settings && (
                          <>
                            <div className="flex items-center justify-between space-x-2">
                              <div className="flex-1">
                                <Label htmlFor="show-count" className="cursor-pointer">
                                  Show Friend Count
                                </Label>
                                <p className="text-sm text-muted-foreground">
                                  Display the total number of friends
                                </p>
    </div>
                              <Switch
                                id="show-count"
                                checked={('showCount' in settings ? settings.showCount : false)} onCheckedChange={(checked) =>
                                  handleSettingChange('friendsList', 'showCount', checked)
                                }
                              />
    </div>
                            <div className="flex items-center justify-between space-x-2">
                              <div className="flex-1">
                                <Label htmlFor="mutual-only" className="cursor-pointer">
                                  Show Mutual Friends Only
                                </Label>
                                <p className="text-sm text-muted-foreground">
                                  Only show friends you have in common with viewers
                                </p>
    </div>
                              <Switch
                                id="mutual-only"
                                checked={('showMutualOnly' in settings ? settings.showMutualOnly : false)} onCheckedChange={(checked) =>
                                  handleSettingChange('friendsList', 'showMutualOnly', checked)
                                }
                              />
    </div>
                          </>
                        )}

                        {/* Special settings for events */}
                        {category.id === 'events' && 'showPastEvents' in settings && (
                          <div className="flex items-center justify-between space-x-2">
                            <div className="flex-1">
                              <Label htmlFor="past-events" className="cursor-pointer">
                                Show Past Events
                              </Label>
                              <p className="text-sm text-muted-foreground">
                                Display events you've attended in the past
                              </p>
    </div>
                            <Switch
                              id="past-events"
                              checked={('showPastEvents' in settings ? settings.showPastEvents : false)} onCheckedChange={(checked) =>
                                handleSettingChange('events', 'showPastEvents', checked)
                              }
                            />
    </div>
                        )}

                        {/* Privacy Tips */}
                        <Alert>
                          <Info className="h-4 w-4" />
                          <AlertDescription>
                            {category.id === 'likes' && (
                              <span>
                                When set to "Only Me", your likes won't appear in friends' feeds
                                or on the pages you've liked.
                              </span>
                            )}
                            {category.id === 'comments' && (
                              <span>
                                This controls who can see your commenting activity in their feeds,
                                but comments on public posts may still be visible to anyone.
                              </span>
                            )}
                            {category.id === 'shares' && (
                              <span>
                                Shared posts will respect both your privacy settings and the
                                original post's privacy settings.
                              </span>
                            )}
                            {category.id === 'followedPages' && (
                              <span>
                                Pages you follow may still show you as a follower on their page,
                                regardless of this setting.
                              </span>
                            )}
                            {category.id === 'friendsList' && (
                              <span>
                                Your friends can always see if they're connected to you,
                                regardless of these settings.
                              </span>
                            )}
                            {category.id === 'groups' && (
                              <span>
                                Group members can always see you in the group, but this controls
                                who else can see your memberships.
                              </span>
                            )}
                            {category.id === 'events' && (
                              <span>
                                Event hosts and attendees can see you've joined, but this
                                controls who else can see your attendance.
                              </span>
                            )}
                          </AlertDescription>
    </Alert>
                      </CardContent>
    </Card>
                  </TabsContent>
                );
              })}
            </Tabs>
    </div>
          <DialogFooter className="mt-6">
            <div className="flex items-center justify-between w-full">
              <div>
                {hasChanges && (
                  <Badge variant="secondary">Unsaved changes</Badge>
                )}
              </div>
              <div className="flex gap-2">
                <Button variant="outline" onClick={handleReset} disabled={!hasChanges}>
                  Reset
                </Button>
                <Button variant="outline" onClick={() => setIsOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleSave} disabled={!hasChanges}>
                  Save Changes
                </Button>
    </div>
            </div>
    </DialogFooter>
        </DialogContent>
    </Dialog>
    </>
  );
});

ActivityPrivacyControls.displayName = 'ActivityPrivacyControls';

export default ActivityPrivacyControls;

