/**
 * Comprehensive Error Fixer - Phase 6
 * Fixes remaining TypeScript and JSX syntax errors
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class ComprehensiveErrorFixer {
  constructor() {
    this.srcDir = path.join(path.dirname(__dirname), 'src');
    this.fixedFiles = 0;
    this.fixedErrors = 0;
    this.logFile = path.join(path.dirname(__dirname), 'comprehensive-error-fixes.log');
  }

  log(message) {
    const timestamp = new Date().toISOString();
    const logEntry = `[${timestamp}] ${message}\n`;
    console.log(message);
    fs.appendFileSync(this.logFile, logEntry);
  }

  fixJSXPropCommas(content) {
    let fixes = 0;
    
    // Fix JSX prop syntax: prop={value}, nextProp -> prop={value} nextProp
    content = content.replace(/(\w+)=\{([^}]+)\},\s*(\w+)=/g, (match, prop1, value1, prop2) => {
      fixes++;
      return `${prop1}={${value1}} ${prop2}=`;
    });
    
    // Fix function parameter syntax: param: type; -> param: type,
    content = content.replace(/(\w+):\s*([^;,}]+);\s*(\w+):/g, (match, param1, type1, param2) => {
      fixes++;
      return `${param1}: ${type1}, ${param2}:`;
    });
    
    // Fix object property syntax: prop: value; -> prop: value,
    content = content.replace(/(\w+):\s*([^;,}]+);\s*(\w+):/g, (match, prop1, value1, prop2) => {
      fixes++;
      return `${prop1}: ${value1}, ${prop2}:`;
    });
    
    return { content, fixes };
  }

  fixTypeScriptSyntax(content) {
    let fixes = 0;
    
    // Fix function type definitions with semicolons instead of commas
    content = content.replace(/(\w+):\s*([^;,}]+);\s*(\w+):\s*([^;,}]+)\)/g, (match, param1, type1, param2, type2) => {
      fixes++;
      return `${param1}: ${type1}, ${param2}: ${type2})`;
    });
    
    // Fix interface/type definitions with semicolons
    content = content.replace(/(\w+):\s*([^;,}]+);\s*(\w+):\s*([^;,}]+)\s*}/g, (match, prop1, type1, prop2, type2) => {
      fixes++;
      return `${prop1}: ${type1}, ${prop2}: ${type2} }`;
    });
    
    // Fix object literal syntax issues
    content = content.replace(/(\w+):\s*([^;,}]+);\s*(\w+):\s*([^;,}]+)\s*}/g, (match, prop1, val1, prop2, val2) => {
      fixes++;
      return `${prop1}: ${val1}, ${prop2}: ${val2} }`;
    });
    
    return { content, fixes };
  }

  fixReactComponentSyntax(content) {
    let fixes = 0;
    
    // Fix React component prop spreading issues
    content = content.replace(/(\.\.\.\w+),\s*(\w+)=/g, (match, spread, prop) => {
      fixes++;
      return `${spread} ${prop}=`;
    });
    
    // Fix JSX closing tag issues
    content = content.replace(/\/>\s*,\s*(\w+)/g, (match, next) => {
      fixes++;
      return `/>\n        ${next}`;
    });
    
    // Fix array method chaining syntax
    content = content.replace(/\),\s*(\w+)\(/g, (match, method) => {
      fixes++;
      return `).${method}(`;
    });
    
    return { content, fixes };
  }

  fixFunctionCallSyntax(content) {
    let fixes = 0;
    
    // Fix function call syntax with misplaced commas
    content = content.replace(/(\w+)\(([^)]+)\),\s*(\w+)\(/g, (match, func1, args1, func2) => {
      fixes++;
      return `${func1}(${args1}).${func2}(`;
    });
    
    // Fix method chaining syntax
    content = content.replace(/\),\s*\.(\w+)\(/g, (match, method) => {
      fixes++;
      return `).${method}(`;
    });
    
    // Fix callback function syntax
    content = content.replace(/(\w+):\s*\(([^)]+)\)\s*=>\s*\{([^}]+)\},\s*(\w+):/g, (match, prop1, params, body, prop2) => {
      fixes++;
      return `${prop1}: (${params}) => {${body}}, ${prop2}:`;
    });
    
    return { content, fixes };
  }

  fixGenericSyntax(content) {
    let fixes = 0;
    
    // Fix generic type syntax issues
    content = content.replace(/<([^>]+)>\s*,\s*(\w+)=/g, (match, generic, prop) => {
      fixes++;
      return `<${generic}> ${prop}=`;
    });
    
    // Fix arrow function return type syntax
    content = content.replace(/=>\s*([^,;{}]+),\s*(\w+)/g, (match, returnType, next) => {
      fixes++;
      return `=> ${returnType}; ${next}`;
    });
    
    return { content, fixes };
  }

  processFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      let modifiedContent = content;
      let totalFixes = 0;

      // Apply all fix methods
      const fixMethods = [
        this.fixJSXPropCommas,
        this.fixTypeScriptSyntax,
        this.fixReactComponentSyntax,
        this.fixFunctionCallSyntax,
        this.fixGenericSyntax
      ];

      for (const fixMethod of fixMethods) {
        const result = fixMethod.call(this, modifiedContent);
        modifiedContent = result.content;
        totalFixes += result.fixes;
      }

      if (totalFixes > 0) {
        fs.writeFileSync(filePath, modifiedContent);
        this.fixedFiles++;
        this.fixedErrors += totalFixes;
        this.log(`Fixed ${totalFixes} errors in ${filePath}`);
      }

      return totalFixes;
    } catch (error) {
      this.log(`Error processing ${filePath}: ${error.message}`);
      return 0;
    }
  }

  processDirectory(dirPath) {
    const entries = fs.readdirSync(dirPath, { withFileTypes: true });

    for (const entry of entries) {
      const fullPath = path.join(dirPath, entry.name);

      if (entry.isDirectory() && !entry.name.startsWith('.') && entry.name !== 'node_modules') {
        this.processDirectory(fullPath);
      } else if (entry.isFile() && /\.(ts|tsx|js|jsx)$/.test(entry.name)) {
        this.processFile(fullPath);
      }
    }
  }

  async run() {
    this.log('Starting Comprehensive Error Fixing - Phase 6');
    this.log('='.repeat(50));

    // Clear log file
    fs.writeFileSync(this.logFile, '');

    // Process all files
    this.processDirectory(this.srcDir);

    this.log('='.repeat(50));
    this.log(`Comprehensive Error Fixing Complete!`);
    this.log(`Files processed: ${this.fixedFiles}`);
    this.log(`Total errors fixed: ${this.fixedErrors}`);

    // Run type check to see remaining errors
    try {
      this.log('\nRunning type check to verify fixes...');
      execSync('npm run type-check', { stdio: 'inherit' });
      this.log('✅ Type check passed!');
    } catch (error) {
      this.log('ℹ️ Some errors may still remain - checking specific files...');
    }
  }
}

// Run the fixer
const fixer = new ComprehensiveErrorFixer();
fixer.run().catch(console.error);
