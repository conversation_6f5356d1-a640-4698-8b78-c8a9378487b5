// Enhanced File Sharing Interface for Messaging
import React, { useState, useCallback, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Upload,
  X,
  File,
  Image,
  Video,
  Music,
  FileText,
  RotateCcw,
  Trash2,
  CheckCircle,
  AlertCircle,
  Clock,
  Paperclip
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Card, CardContent } from '@/components/ui/card';
import { useMessageFileService } from '@/hooks/useMessageFileService';
import { cn } from '@/lib/utils';

// Import proper types
import { FileUploadService } from '@/services/FileUploadService';
import { EnhancedMessagingService } from '@/services/EnhancedMessagingService';
import { CryptoService } from '@/services/CryptoService';
import { WebSocketManager } from '@/services/WebSocketManager';

interface FileUploadState {
  uploadId: string, file: File, progress: { percentage: number, speed: number, timeRemaining: number };
  status: 'pending' | 'uploading' | 'encrypting' | 'completed' | 'failed' | 'cancelled';
  previewUrl?: string;
  error?: { message: string };
}

interface MessageFileUploaderProps {
  conversationId: string;
  onFilesSent?: (files: File[]) => void;
  maxFiles?: number;
  accept?: string;
  className?: string;
  compact?: boolean;
  disabled?: boolean;
  services: {
    fileUploadService: FileUploadService, messagingService: EnhancedMessagingService, cryptoService: CryptoService, webSocketManager: WebSocketManager;
  };
}

const MessageFileUploader: React.FC<MessageFileUploaderProps> = ({
  conversationId,
  onFilesSent,
  maxFiles = 10,
  accept = "*/*",
  className,
  compact = false,
  disabled = false,
  services
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [dragActive, setDragActive] = useState(false);
  const [showUploadPanel, setShowUploadPanel] = useState(false);

  const fileService = useMessageFileService(
    services.fileUploadService,
    services.messagingService,
    services.cryptoService,
    services.webSocketManager,
    {
      maxFileSize: 100 * 1024 * 1024, // 100MB
      enableCompression: true,
      generateThumbnails: true
    }
  );

  // Handle file selection
  const handleFiles = useCallback(async (files: File[]) => {
    if (disabled || files.length === 0) return;
    
    const filesToUpload = files.slice(0, maxFiles);
    
    try {
      setShowUploadPanel(true);
      await fileService.sendFileMessage(conversationId, filesToUpload);
      onFilesSent?.(filesToUpload);
    } catch (error) {
      console.error('Failed to send files:', error);
    }
  }, [disabled, maxFiles, conversationId, fileService, onFilesSent]);

  // File input handlers
  const handleFileInputChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    if (files.length > 0) {
      handleFiles(files);
    }
    // Reset input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, [handleFiles]);

  const openFilePicker = useCallback(() => {
    if (!disabled && fileInputRef.current) {
      fileInputRef.current.click();
    }
  }, [disabled]);

  // Drag and drop handlers
  const handleDragEnter = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (!disabled) setDragActive(true);
  }, [disabled]);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
  }, []);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (disabled) return;
    
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFiles(files);
    }
  }, [disabled, handleFiles]);

  // File icon helper
  const getFileIcon = (file: File | string) => {
    const mimeType = typeof file === 'string' ? file : file.type;
    if (mimeType.startsWith('image/')) return <Image className="w-4 h-4" />;
    if (mimeType.startsWith('video/')) return <Video className="w-4 h-4" />;
    if (mimeType.startsWith('audio/')) return <Music className="w-4 h-4" />;
    if (mimeType.includes('pdf') || mimeType.includes('document') || mimeType.includes('text')) 
      return <FileText className="w-4 h-4" />;
    return <File className="w-4 h-4" />;
  };

  // Status icon helper
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'failed': return <AlertCircle className="w-4 h-4 text-red-500" />;
      case 'uploading': case 'encrypting': return <Clock className="w-4 h-4 text-blue-500 animate-spin" />;
      case 'cancelled': return <X className="w-4 h-4 text-gray-500" />;
      default: return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  // Format file size
  const formatFileSize = (bytes: number) => {
    const sizes = ['B', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 B';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${Math.round(bytes / Math.pow(1024, i) * 100) / 100} ${sizes[i]}`;
  };

  // Format time remaining
  const formatTimeRemaining = (seconds: number) => {
    if (seconds < 60) return `${Math.round(seconds)}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.round(seconds % 60);
    return `${minutes}m ${remainingSeconds}s`;
  };

  if (compact) {
    return (
      <div className={className}>
        <input
          ref={fileInputRef} type="file"
          accept={accept} multiple={maxFiles > 1}, onChange={handleFileInputChange} className="hidden"
          disabled={disabled}
        />
        
        <Button
          variant="ghost"
          size="sm"
          onClick={openFilePicker} disabled={disabled}, className="h-8 w-8 p-0"
          title="Attach files"
        >
          <Paperclip className="w-4 h-4" />
    </Button>
        {/* Upload Progress Panel */}
        <AnimatePresence>
          {showUploadPanel && fileService.activeUploads.length > 0 && (
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}, animate={{ opacity: 1, scale: 1 }}, exit={{ opacity: 0, scale: 0.95 }}, className="fixed bottom-4 right-4 w-80 bg-white dark:bg-gray-800 rounded-lg shadow-lg border z-50"
            >
              <div className="p-3 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between">
                <h3 className="font-medium text-sm">Uploading Files</h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowUploadPanel(false)} className="h-6 w-6 p-0"
                >
                  <X className="w-3 h-3" />
    </Button>
              </div>
              
              <ScrollArea className="max-h-48">
                <div className="p-2 space-y-2">
                  {fileService.activeUploads.map((upload) => (
                    <FileUploadItem
                      key={upload.uploadId} upload={upload}, onCancel={() => fileService.cancelUpload(upload.uploadId)} onRetry={() => fileService.retryUpload(upload.uploadId)}, getFileIcon={getFileIcon} getStatusIcon={getStatusIcon}, formatFileSize={formatFileSize} formatTimeRemaining={formatTimeRemaining}
                    />
                  ))}
                </div>
    </ScrollArea>
            </motion.div>
          )}
        </AnimatePresence>
    </div>
    );
  }

  return (
    <div className={cn("w-full space-y-4", className)}>
      {/* Drop Zone */}
      <Card
        className={cn(
          "relative border-2 border-dashed transition-all duration-200 cursor-pointer",
          dragActive
            ? "border-blue-500 bg-blue-50 dark:bg-blue-950/20"
            : "border-gray-300 dark:border-gray-700 hover:border-gray-400 dark:hover:border-gray-600",
          disabled && "opacity-50 cursor-not-allowed"
        )} onDragEnter={handleDragEnter}, onDragLeave={handleDragLeave} onDragOver={handleDragOver}, onDrop={handleDrop} onClick={openFilePicker}
      >
        <input
          ref={fileInputRef} type="file"
          accept={accept} multiple={maxFiles > 1}, onChange={handleFileInputChange} className="hidden"
          disabled={disabled}
        />

        <CardContent className="p-8">
          <div className="flex flex-col items-center justify-center text-center space-y-2">
            <motion.div
              animate={dragActive ? { scale: 1.1 } : { scale: 1 }}, transition={{ duration: 0.2 }}
            >
              <Upload className="w-8 h-8 text-gray-400" />
            </motion.div>
            <div className="space-y-1">
              <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
                {dragActive ? "Drop files here" : "Click to upload or drag and drop"}
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                {accept !== "*/*" ? `Accepts: ${accept}` : "All file types supported"}
                {maxFiles > 1 && ` • Max ${maxFiles} files`}
              </p>
    </div>
          </div>
    </CardContent>
      </Card>

      {/* Upload Queue */}
      <AnimatePresence>
        {fileService.activeUploads.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}, animate={{ opacity: 1, y: 0 }}, exit={{ opacity: 0, y: -10 }}
          >
            <Card>
              <CardContent className="p-4">
                <div className="space-y-4">
                  {/* Queue Header */}
                  <div className="flex items-center justify-between">
                    <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      Upload Queue ({fileService.activeUploads.length})
                    </h3>
                    
                    <div className="flex gap-2">
                      {fileService.hasErrors && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={fileService.clearErrors} className="text-xs"
                        >
                          Clear Errors
                        </Button>
                      )}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={fileService.clearCompleted} className="text-xs"
                      >
                        Clear Completed
                      </Button>
    </div>
                  </div>

                  {/* File List */}
                  <ScrollArea className="max-h-64 space-y-2">
                    <div className="space-y-2">
                      {fileService.activeUploads.map((upload) => (
                        <FileUploadItem
                          key={upload.uploadId} upload={upload}, onCancel={() => fileService.cancelUpload(upload.uploadId)} onRetry={() => fileService.retryUpload(upload.uploadId)}, getFileIcon={getFileIcon} getStatusIcon={getStatusIcon}, formatFileSize={formatFileSize} formatTimeRemaining={formatTimeRemaining}
                        />
                      ))}
                    </div>
    </ScrollArea>
                </div>
    </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

// File upload item component
interface FileUploadItemProps {
  upload: FileUploadState, onCancel: () => void; onRetry: () => void; getFileIcon: (file: File) => React.ReactNode; getStatusIcon: (status: string) => React.ReactNode; formatFileSize: (bytes: number) => string; formatTimeRemaining: (seconds: number) => string;
}

const FileUploadItem: React.FC<FileUploadItemProps> = ({
  upload,
  onCancel,
  onRetry,
  getFileIcon,
  getStatusIcon,
  formatFileSize,
  formatTimeRemaining
}) => {
  return (
    <motion.div
      layout
      initial={{ opacity: 0, y: 10 }}, animate={{ opacity: 1, y: 0 }}, exit={{ opacity: 0, x: -10 }}, className="flex items-center space-x-3 p-3 rounded-lg bg-gray-50 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-700"
    >
      {/* File Icon & Preview */}
      <div className="flex-shrink-0">
        {upload.previewUrl ? (
          <img
            src={upload.previewUrl} alt="Preview"
            className="w-10 h-10 object-cover rounded border"
          />
        ) : (
          <div className="w-10 h-10 bg-gray-200 dark:bg-gray-700 rounded border flex items-center justify-center">
            {getFileIcon(upload.file)}
          </div>
        )}
      </div>

      {/* File Info */}
      <div className="flex-1 min-w-0">
        <div className="flex items-center space-x-2">
          <p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
            {upload.file.name}
          </p>
          {getStatusIcon(upload.status)}
        </div>
        
        <div className="flex items-center justify-between mt-1">
          <p className="text-xs text-gray-500 dark:text-gray-400">
            {formatFileSize(upload.file.size)}
          </p>
          
          {upload.status === 'uploading' && (
            <div className="flex items-center space-x-2">
              <p className="text-xs text-gray-500 dark:text-gray-400">
                {Math.round(upload.progress.percentage)}%
              </p>
              {upload.progress.timeRemaining > 0 && (
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  {formatTimeRemaining(upload.progress.timeRemaining)} left
                </p>
              )}
            </div>
          )}
        </div>

        {/* Progress Bar */}
        {(upload.status === 'uploading' || upload.status === 'encrypting') && (
          <Progress value={upload.progress.percentage} className="mt-2 h-1" />
        )}

        {/* Error Message */}
        {upload.status === 'failed' && upload.error && (
          <p className="text-xs text-red-500 mt-1">
            {upload.error.message}
          </p>
        )}
      </div>

      {/* Actions */}
      <div className="flex items-center space-x-1">
        {upload.status === 'failed' && (
          <Button
            size="sm"
            variant="ghost"
            onClick={onRetry} className="h-8 w-8 p-0"
            title="Retry upload"
          >
            <RotateCcw className="w-3 h-3" />
    </Button>
        )}

        {(upload.status === 'uploading' || upload.status === 'pending' || upload.status === 'failed') && (
          <Button
            size="sm"
            variant="ghost"
            onClick={onCancel} className="h-8 w-8 p-0"
            title="Cancel upload"
          >
            <Trash2 className="w-3 h-3" />
    </Button>
        )}
      </div>
    </motion.div>
  );
};

export default MessageFileUploader;
