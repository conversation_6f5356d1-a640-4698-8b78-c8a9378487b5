/**
 * Immediately Optimized Sidebar Component
 * Applied performance optimizations for immediate improvement
 */

import React, { useState, useCallback, useMemo, memo } from 'react';
import { Home, Users, Bookmark, Clock, Calendar, Store, Video, MessageCircle, Flag, ChevronDown, UsersRound, TrendingUp, Gamepad2, Film, CloudSun, Heart, Briefcase, Building2, Settings, Sun, Moon, Plus, X, Radio, BarChart3, PlaySquare } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui';
import { useNavigate, useLocation } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { ROUTES, MOCK_IMAGES, getSafeImage } from '@/lib/constants';
import { useTheme } from '@/hooks/useTheme';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { WeatherContent } from './shared/widgets';
import WidgetFactory from './shared/WidgetFactory';
import { WidgetType } from '@/utils/widgetUtilities';
import { toast } from 'sonner';
import { MenuItem, Shortcut } from '@/types/interfaces';
import { useImmediateOptimization } from '@/hooks/useImmediateOptimizations';

// OPTIMIZATION 1: Static widgets - created once, never recreated
const STATIC_WIDGETS = {
  weather: <WeatherContent title="Weather" icon={CloudSun} />
        stocks: WidgetFactory.createWidget(WidgetType.STOCKS, { key: 'sidebar-stocks' }),
  reminders: WidgetFactory.createWidget(WidgetType.REMINDERS, { key: 'sidebar-reminders' }),
  tasks: WidgetFactory.createWidget(WidgetType.TASKS, { key: 'sidebar-tasks' }),
  marketplace: WidgetFactory.createWidget(WidgetType.MARKETPLACE, { key: 'sidebar-marketplace' })
};

// OPTIMIZATION 2: Static menu items - never change, prevent recreation
const STATIC_MENU_ITEMS: MenuItem[] = [{ id: 'home', icon: Home, path: ROUTES.HOME, label: 'Home' },
  { id: 'recent', icon: TrendingUp, path: ROUTES.RECENT, label: 'Most Recent' },
  { id: 'friends', icon: Users, path: ROUTES.FRIENDS, label: 'Friends', count: 3 },
  { id: 'messages', icon: MessageCircle, path: ROUTES.MESSAGES, label: 'Messenger', count: 2 },
  { id: 'watch', icon: Video, path: ROUTES.WATCH, label: 'Watch' },
  { id: 'youtube', icon: PlaySquare, path: ROUTES.YOUTUBE, label: 'YouTube', isNew: true },
  { id: 'reels', icon: Film, path: ROUTES.REELS, label: 'Reels', isNew: true },
  { id: 'marketplace', icon: Store, path: ROUTES.MARKETPLACE, label: 'Marketplace' },
  { id: 'groups', icon: UsersRound, path: ROUTES.GROUPS, label: 'Groups' },
  { id: 'gaming', icon: Gamepad2, path: ROUTES.GAMING, label: 'Gaming' },
  { id: 'live', icon: Radio, path: ROUTES.LIVE, label: 'Go Live', isNew: true },
  { id: 'saved', icon: Bookmark, path: ROUTES.SAVED, label: 'Saved' },
  { id: 'events', icon: Calendar, path: ROUTES.EVENTS, label: 'Events' },
  { id: 'memories', icon: Clock, path: ROUTES.MEMORIES, label: 'Memories' },
  { id: 'pages', icon: Flag, path: ROUTES.PAGES, label: 'Pages' },
  { id: 'weather', icon: CloudSun, path: ROUTES.WEATHER, label: 'Weather' },
  { id: 'dating', icon: Heart, path: ROUTES.DATING, label: 'Dating', isNew: true },
  { id: 'optimization', icon: BarChart3, path: ROUTES.OPTIMIZATION, label: 'Performance', isNew: true },
  { id: 'jobs', icon: Briefcase, path: ROUTES.JOBS, label: 'Jobs' },
  { id: 'business', icon: Building2, path: ROUTES.BUSINESS, label: 'Business Manager' },
  { id: 'settings', icon: Settings, path: ROUTES.SETTINGS, label: 'Settings' }];

// OPTIMIZATION 3: Deeply memoized menu item component
const OptimizedMenuItemComponent = memo<{ 
  item: MenuItem, isActive: boolean, onNavigate: (path: string) => void;
}>(({ item, isActive, onNavigate }) => {
  // Memoize the click handler to prevent recreation
  const handleClick = useCallback(() => {
    onNavigate(item.path);
  }, [onNavigate, item.path]);

  return (
    <Button
      variant="ghost"
      className={`w-full flex items-center justify-between p-3 rounded-lg text-left font-normal transition-colors ${
        isActive 
          ? 'bg-blue-50 text-blue-600 hover:bg-blue-100 dark:bg-blue-900/20 dark:text-blue-400 dark:hover:bg-blue-900/30' 
          : 'text-gray-700 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-800'
      }`}, onClick={handleClick}, aria-label={item.label}
    >
      <div className="flex items-center space-x-3 min-w-0">
        <item.icon className="w-6 h-6 flex-shrink-0" />
        <span className="truncate">{item.label}</span>
    </div>
      <div className="flex items-center space-x-2">
        {item.isNew && (
          <Badge variant="secondary" className="bg-blue-100 text-blue-800 text-xs px-1.5 py-0.5 dark:bg-blue-900 dark:text-blue-200">
            New
          </Badge>
        )}
        {item.count && item.count > 0 && (
          <Badge variant="destructive" className="text-xs px-1.5 py-0.5 min-w-[1.5rem] text-center">
            {item.count > 99 ? '99+' : item.count}
          </Badge>
        )}
      </div>
    </Button>
  );
}, (prevProps, nextProps) => {
  // OPTIMIZATION 4: Custom comparison function for better performance
  return (
    prevProps.item.id === nextProps.item.id &&
    prevProps.item.count === nextProps.item.count &&
    prevProps.item.isNew === nextProps.item.isNew &&
    prevProps.isActive === nextProps.isActive &&
    prevProps.onNavigate === nextProps.onNavigate
  );
});

OptimizedMenuItemComponent.displayName = 'OptimizedMenuItemComponent';

// OPTIMIZATION 5: Memoized shortcut component
const OptimizedShortcutItem = memo<{
  shortcut: Shortcut, onNavigate: (path: string) => void; onRemove: (id: string) => void;
}>(({ shortcut, onNavigate, onRemove }) => {
  const handleClick = useCallback(() => {
    onNavigate(shortcut.path);
  }, [onNavigate, shortcut.path]);

  const handleRemove = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    onRemove(shortcut.id);
  }, [onRemove, shortcut.id]);

  return (
    <div 
      className="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 cursor-pointer transition-colors group"
      onClick={handleClick}
    >
      <Avatar className="w-8 h-8">
        <AvatarImage src={shortcut.image} alt={shortcut.name} />
        <AvatarFallback>{shortcut.name.charAt(0)}</AvatarFallback>
    </Avatar>
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
          {shortcut.name}
        </p>
        <p className="text-xs text-gray-500 dark:text-gray-400">
          {shortcut.members} members
        </p>
    </div>
      <button
        onClick={handleRemove} className="opacity-0 group-hover:opacity-100 p-1 hover:bg-gray-200 dark:hover:bg-gray-700 rounded transition-all"
        aria-label={`Remove ${shortcut.name}`}
      >
        <X className="w-4 h-4" />
    </button>
    </div>
  );
}, (prevProps, nextProps) => {
  return (
    prevProps.shortcut.id === nextProps.shortcut.id &&
    prevProps.shortcut.name === nextProps.shortcut.name &&
    prevProps.shortcut.members === nextProps.shortcut.members &&
    prevProps.onNavigate === nextProps.onNavigate &&
    prevProps.onRemove === nextProps.onRemove
  );
});

OptimizedShortcutItem.displayName = 'OptimizedShortcutItem';

// OPTIMIZATION 6: Main Sidebar component with performance monitoring
interface OptimizedSidebarProps {
  isMobileOpen?: boolean;
  onMobileClose?: () => void;
}

const OptimizedSidebar = memo<OptimizedSidebarProps>(({ 
  isMobileOpen = false, 
  onMobileClose = () => {} 
}) => {
  // Performance monitoring
  const { 
    createOptimizedCallback, 
    createMemoizedObject;
    getPerformanceMetrics 
  } = useImmediateOptimization('OptimizedSidebar');

  // const isMobile = useIsMobile(); // TODO: Use for responsive behavior
  const [showMore, setShowMore] = useState(true);
  const [showWidgets, setShowWidgets] = useState(true);
  const [showShortcutDialog, setShowShortcutDialog] = useState(false);
  const [searchShortcut, setSearchShortcut] = useState('');
  const [touchStart, setTouchStart] = useState(0);
  const navigate = useNavigate();
  const location = useLocation();
  const { theme, setTheme } = useTheme();

  // OPTIMIZATION 7: Static initial shortcuts data
  const [shortcuts, setShortcuts] = useState<Shortcut[]>(() => [
    { 
      id: '1',
      name: 'React Developers', 
      image: MOCK_IMAGES.POSTS[0], 
      path: '/groups/react-developers',
      members: '12.5k'
    },
    { 
      id: '2',
      name: 'Web Design Community', 
      image: MOCK_IMAGES.POSTS[1], 
      path: '/groups/web-design',
      members: '8.2k'
    },
    { 
      id: '3',
      name: 'JavaScript Enthusiasts', 
      image: getSafeImage('POSTS', 2), 
      path: '/groups/javascript',
      members: '15.7k'
    },
  ]);

  // OPTIMIZATION 8: Memoized handlers with optimization hook
  const handleNavigation = createOptimizedCallback((...args: unknown[]) => {
    const path = args[0] as string;
    navigate(path);
    onMobileClose(); // Close mobile sidebar after navigation
  }, [navigate, onMobileClose]);

  const handleProfileClick = createOptimizedCallback(() => {
    navigate(ROUTES.PROFILE);
    onMobileClose(); // Close mobile sidebar after navigation
  }, [navigate, onMobileClose]);

  const handleRemoveShortcut = createOptimizedCallback((...args: unknown[]) => {
    const shortcutId = args[0] as string;
    setShortcuts(prev => prev.filter(s => s.id !== shortcutId));
    toast.success('Shortcut removed');
  }, []);

  const toggleTheme = createOptimizedCallback(() => {
    setTheme(theme === 'dark' ? 'light' : 'dark');
  }, [theme, setTheme]);

  const toggleShowMore = createOptimizedCallback(() => {
    setShowMore(prev => !prev);
  }, []);

  const toggleShowWidgets = createOptimizedCallback(() => {
    setShowWidgets(prev => !prev);
  }, []);

  // Touch handlers for swipe-to-close
  const handleTouchStart = createOptimizedCallback((...args: unknown[]) => {
    const e = args[0] as React.TouchEvent;
    setTouchStart(e.touches[0].clientX);
  }, []);

  const handleTouchEnd = createOptimizedCallback((...args: unknown[]) => {
    const e = args[0] as React.TouchEvent;
    if (!touchStart) return;
    
    const touchEnd = e.changedTouches[0].clientX;
    const swipeDistance = touchStart - touchEnd;
    
    // If swipe left more than 50px, close sidebar
    if (swipeDistance > 50) {
      onMobileClose();
    }
    
    setTouchStart(0);
  }, [touchStart, onMobileClose]);

  // OPTIMIZATION 9: Memoized computed values
  const visibleMenuItems = useMemo(() => {
    return showMore ? STATIC_MENU_ITEMS : STATIC_MENU_ITEMS.slice(0, 8);
  }, [showMore]);

  const filteredShortcuts = useMemo(() => {
    if (!searchShortcut) return shortcuts;
    return shortcuts.filter(shortcut => 
      shortcut.name.toLowerCase().includes(searchShortcut.toLowerCase())
    );
  }, [shortcuts, searchShortcut]);

  const currentPath = location.pathname;

  // OPTIMIZATION 10: Memoized user info object
  const userInfo = createMemoizedObject(() => ({
    name: 'Alex Smith',
    avatar: MOCK_IMAGES.AVATARS[0],
    verified: true
  }), []);

  // Log performance metrics in development
  if (process.env.NODE_ENV === 'development') {
    const metrics = getPerformanceMetrics();
    if (metrics.renderCount > 0 && metrics.renderCount % 5 === 0) {
      console.log('📊 OptimizedSidebar performance:', metrics);
    }
  }

  return (
    <>
      {/* Mobile overlay backdrop */}
      {isMobileOpen && (
        <div 
          className="fixed inset-0 bg-black/50 z-40 md:hidden"
          onClick={onMobileClose}
        />
      )}
      
      {/* Sidebar */}
      <aside 
        className={`
          fixed left-0 top-16 h-[calc(100vh-4rem)] w-80 
          bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-800 
          overflow-hidden transition-transform duration-300 ease-in-out z-50
          ${isMobileOpen ? 'translate-x-0' : '-translate-x-full'}, md:translate-x-0 md:flex flex-col
        `}, onTouchStart={handleTouchStart} onTouchEnd={handleTouchEnd}
      >
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {/* Profile Section - Memoized */}
        <div 
          className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 cursor-pointer transition-colors"
          onClick={handleProfileClick}
        >
          <Avatar className="w-10 h-10">
            <AvatarImage src={userInfo.avatar} alt={userInfo.name} />
            <AvatarFallback>AS</AvatarFallback>
    </Avatar>
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-1">
              <h2 className="font-semibold text-gray-900 dark:text-white truncate">
                {userInfo.name}
              </h2>
              {userInfo.verified && (
                <Badge variant="secondary" className="text-blue-600 dark:text-blue-400">✓</Badge>
              )}
            </div>
            <p className="text-sm text-gray-500 dark:text-gray-400">View your profile</p>
    </div>
        </div>

        {/* Menu Items - Optimized */}
        <nav className="space-y-1">
          {visibleMenuItems.map((item) => (
            <OptimizedMenuItemComponent
              key={item.id} item={item}, isActive={currentPath === item.path} onNavigate={handleNavigation}
            />
          ))}
        </nav>

        {/* Show More/Less Toggle */}
        <Button
          variant="ghost"
          className="w-full flex items-center justify-center p-3 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800"
          onClick={toggleShowMore}
        >
          <ChevronDown className={`w-5 h-5 transition-transform ${showMore ? 'rotate-180' : ''}`} />
          <span className="ml-2">{showMore ? 'Show Less' : 'Show More'}</span>
    </Button>
        {/* Shortcuts Section - Optimized */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200">
              Your Shortcuts
            </h3>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowShortcutDialog(true)} className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            >
              <Plus className="w-4 h-4" />
    </Button>
          </div>
          
          <div className="space-y-1">
            {filteredShortcuts.map((shortcut) => (
              <OptimizedShortcutItem
                key={shortcut.id} shortcut={shortcut}, onNavigate={handleNavigation} onRemove={handleRemoveShortcut}
              />
            ))}
          </div>
    </div>
        {/* Widgets Section - Static widgets */}
        {showWidgets && (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200">
                Widgets
              </h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={toggleShowWidgets} className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              >
                <X className="w-4 h-4" />
    </Button>
            </div>
            
            <div className="space-y-3">
              {STATIC_WIDGETS.weather}
              {STATIC_WIDGETS.stocks}
              {STATIC_WIDGETS.reminders}
            </div>
    </div>
        )}

        {/* Theme Toggle */}
        <Button
          variant="ghost"
          className="w-full flex items-center justify-center p-3 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800"
          onClick={toggleTheme}
        >
          {theme === 'dark' ? <Sun className="w-5 h-5" /> : <Moon className="w-5 h-5" />}
          <span className="ml-2">
            Switch to {theme === 'dark' ? 'Light' : 'Dark'} Mode
          </span>
    </Button>
      </div>

      {/* Shortcut Dialog */}
      <Dialog open={showShortcutDialog} onOpenChange={setShowShortcutDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add Shortcut</DialogTitle>
    </DialogHeader>
          <div className="space-y-4">
            <Input
              placeholder="Search for groups, pages, or friends..."
              value={searchShortcut} onChange={(e) => setSearchShortcut(e.target.value)}
            />
            {/* Add shortcut search results here */}
          </div>
    </DialogContent>
      </Dialog>
    </aside>
    </>
  );
});

OptimizedSidebar.displayName = 'OptimizedSidebar';

export default OptimizedSidebar;
