import React, { Suspense } from "react";
import { Routes, Route, Navigate, Browser<PERSON>outer } from "react-router-dom";
import { Toaster } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { routeConfigs, generateRouteElement } from "@/utils/routeConfig";

// Providers - using new organized structure
import {
  QueryProvider,
  ApolloProviderWrapper,
  SimpleAuthProvider,
  SimpleGlobalProvider,
  ThemeProvider,
  SearchProvider
} from "@/components/providers";
import { composeProviders } from "@/utils/optimizedContexts";
import MessagingNotificationIntegration from '@/components/messaging/MessagingNotificationIntegration';

// Router validation system
import { RouterValidator, RouterContextMonitor } from './components/RouterValidator';

// Components - using new organized structure
import {
  ErrorBoundaryWithLogging,  
  ErrorDiagnostic,
  ResourceHints,
  PWAManager
} from "@/components";

// Missing AppErrorBoundary import
import { ErrorBoundary } from "react-error-boundary";

// Enhanced suspense fallback for app-level loading
const AppSuspenseFallback = () => (
  <div className="min-h-[80vh] flex items-center justify-center bg-gray-50 dark:bg-gray-900">
    <div className="text-center space-y-4">
      <div className="relative">
        <div className="w-12 h-12 border-4 border-t-blue-500 border-blue-200 dark:border-blue-800 rounded-full animate-spin mx-auto"></div>
        <div className="absolute inset-0 w-8 h-8 border-2 border-t-blue-300 border-blue-100 dark:border-blue-600 rounded-full animate-spin-reverse mx-auto mt-2 ml-2"></div>
    </div>
      <div className="space-y-2">
        <div className="text-sm text-gray-600 dark:text-gray-300 font-medium">
          Loading application...
        </div>
        <div className="text-xs text-gray-500 dark:text-gray-400">
          Please wait while we prepare your content
        </div>
    </div>
      {/* Skeleton placeholder to maintain layout stability */}
      <div className="max-w-md mx-auto space-y-3 pt-6">
        <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
        <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-3/4"></div>
        <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-1/2"></div>
    </div>
    </div>
    </div>
);

// Create AppErrorBoundary component
const AppErrorBoundary = ({ children }: { children: React.ReactNode }) => (
  <ErrorBoundary
    fallback={
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-center space-y-4">
          <h1 className="text-xl font-semibold text-red-600">Something went wrong</h1>
          <p className="text-gray-600">Please refresh the page to try again.</p>
    </div>
      </div>
    } onError={(error) => {
      if (process.env.NODE_ENV === 'development') {
        console.error('App Error Boundary:', error);
      }
    }}
  >
    {children}
  </ErrorBoundary>
);

// Optimized provider composition
const AppProviders = composeProviders([SimpleAuthProvider,
  ApolloProviderWrapper,
  QueryProvider,
  SimpleGlobalProvider,
  ThemeProvider,
  TooltipProvider,
  SearchProvider]);

const App = () => {
  return (
    <ErrorBoundaryWithLogging>
      <AppErrorBoundary>
        <Suspense fallback={<AppSuspenseFallback />}>
          <AppProviders>
            <MessagingNotificationIntegration>
              <BrowserRouter
                future={{
                  v7_startTransition: true,
                  v7_relativeSplatPath: true
                }}
              >
                <ResourceHints />
                {process.env.NODE_ENV === 'development' && <ErrorDiagnostic />}
                {process.env.NODE_ENV === 'development' && <RouterValidator />}
                {process.env.NODE_ENV === 'development' && <RouterContextMonitor />}
                <Routes>
                  {routeConfigs.map((config) => (
                    <Route
                      key={config.path}
                      path={config.path}
                      element={generateRouteElement(config)}
                    />
                  ))}
                  <Route path="*" element={<Navigate to="/not-found" replace />} />
    </Routes>
              </BrowserRouter>
              <Toaster 
                position="top-right" 
                closeButton
                toastOptions={{
                  duration: 3000,
                  className: 'group toast overflow-hidden'
                }}
                richColors 
                expand={false} visibleToasts={3}
              />
              <PWAManager />
    </MessagingNotificationIntegration>
          </AppProviders>
    </Suspense>
      </AppErrorBoundary>
    </ErrorBoundaryWithLogging>
  );
};

export default App;