// Global error handling utilities for unhandled promise rejections and window errors

import { toast } from 'sonner';
import { errorLogger } from './errorLogger';

interface ErrorReport {
  type: 'unhandledrejection' | 'error' | 'network' | 'chunk', message: string;
  stack?: string;
  url?: string;
  line?: number;
  column?: number;
  timestamp: string, userAgent: string, errorId: string;
}

class GlobalErrorHandler {
  private isInitialized = false;
  private errorQueue: ErrorReport[] = [];
  private readonly maxQueueSize = 50;

  // Initialize global error handlers
  public initialize(): void {
    if (this.isInitialized) {
      return;
    }

    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', this.handleUnhandledRejection);
    
    // Handle general window errors
    window.addEventListener('error', this.handleError);
    
    // Handle resource loading errors
    window.addEventListener('error', this.handleResourceError, true);

    this.isInitialized = true;
    // Global error handler initialized
  }

  // Clean up event listeners
  public cleanup(): void {
    if (!this.isInitialized) {
      return;
    }

    window.removeEventListener('unhandledrejection', this.handleUnhandledRejection);
    window.removeEventListener('error', this.handleError);
    window.removeEventListener('error', this.handleResourceError, true);
    
    this.isInitialized = false;
  }

  private handleUnhandledRejection = (event: PromiseRejectionEvent): void => {
    // Use error logger instead of console.error
    errorLogger.error('system', event.reason, 'Unhandled promise rejection');
    
    // Check if it's a chunk loading error (common in React apps)
    const isChunkError = event?.reason?.toString().includes('Loading chunk') || 
                        event?.reason?.toString().includes('ChunkLoadError');
    
    if (isChunkError) {
      this.handleChunkLoadError(event.reason);
      return;
    }

    // Check if it's a network error
    const isNetworkError = event?.reason?.toString().includes('fetch') ||
                          event?.reason?.toString().includes('NetworkError') ||
                          event?.reason?.toString().includes('Failed to fetch') ||
                          event?.reason?.toString().includes('ERR_NETWORK') ||
                          event?.reason?.toString().includes('timeout') ||
                          (event?.reason?.name === 'TypeError' && 
                           event?.reason?.message?.includes('fetch'));

    if (isNetworkError) {
      this.handleNetworkError(event.reason);
      return;
    }

    const errorReport: ErrorReport = {
      type: 'unhandledrejection',
      message: event?.reason?.message || event?.reason?.toString() || 'Unknown promise rejection',
      stack: event?.reason?.stack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      errorId: this.generateErrorId()
    };

    this.reportError(errorReport);
    
    // Prevent the default browser behavior
    event.preventDefault();
  };

  private handleError = (event: ErrorEvent): void => {
    // Skip reporting certain non-critical errors
    if (this.shouldSkipError(event)) {
      return;
    }

    // Use error logger with appropriate level
    const level = this.isCriticalError(event.error) ? 'critical' : 'error';
    
    // Only log as 'system' error if it's truly a critical system issue
    const category = this.isCriticalSystemError(event.error) ? 'system' : 'ui';
    
    errorLogger.log(level, category, event.error, 'Global window error', {
  url: event.filename, line: event.lineno, column: event.colno
    });

    const errorReport: ErrorReport = {
      type: 'error',
      message: event.message || 'Unknown error',
      stack: event?.error?.stack,
      url: event.filename,
      line: event.lineno,
      column: event.colno,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      errorId: this.generateErrorId()
    };

    this.reportError(errorReport);
  };

  private handleResourceError = (event: Event): void => {
    const target = event.target as HTMLElement;
    
    if (target && target !== (window as unknown)) {
      const tagName = target?.tagName?.toLowerCase();
      let src = '';
      
      // Safely get src or href based on element type
      if (target instanceof HTMLImageElement || target instanceof HTMLScriptElement) {
        src = target.src;
      } else if (target instanceof HTMLLinkElement) {
        src = target.href;
      }
      
      if (process.env.NODE_ENV === 'development') {
        // Resource loading failed - handled by error reporting
      }
      
      // Don't report every resource error, only critical ones
      if (this.isCriticalResource(tagName, src)) {
        const errorReport: ErrorReport = {
          type: 'network',
          message: `Failed to load ${tagName}: ${src}`,
          url: src,
          timestamp: new Date().toISOString(),
          userAgent: navigator.userAgent,
          errorId: this.generateErrorId()
        };

        this.reportError(errorReport);
      }
    }
  };

  private handleChunkLoadError = (error: unknown): void => {
    errorLogger.warning('system', error, 'Chunk loading error detected');
    
    const errorReport: ErrorReport = {
      type: 'chunk',
      message: 'Chunk loading error - app needs refresh',
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      errorId: this.generateErrorId()
    };

    this.reportError(errorReport);
    
    // Show specific message for chunk errors
    toast.error('App update detected. Refreshing page...', {
  duration: 3000;
    });
    
    // Refresh the page after a short delay
    setTimeout(() => {
      window.location.reload();
    }, 2000);
  };

  private handleNetworkError = (error: unknown): void => {
    errorLogger.error('network', error, 'Network error detected');
    
    const errorReport: ErrorReport = {
      type: 'network',
      message: 'Network error occurred',
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      errorId: this.generateErrorId()
    };

    this.reportError(errorReport);
  };

  private isCriticalError = (error: Error): boolean => {
    if (!error) return false;
    
    const criticalPatterns = [
      'ChunkLoadError',
      'Script error',
      'ReferenceError',
      'TypeError: Cannot read prop'
    ];
    
    return criticalPatterns.some(pattern => 
      error?.message?.includes(pattern) || error?.name?.includes(pattern)
    );
  };

  private isCriticalSystemError = (error: Error): boolean => {
  if (!error) return false;
    
    // Be much more restrictive - only true system-level failures should get generic message
    const systemErrorPatterns = [
      'ChunkLoadError',
      'Loading chunk',
      'Loading CSS chunk',
      'Script error.'  // Only the exact browser script error, not custom script errors
    ];
    
    // Must be an exact match or very specific pattern
    return systemErrorPatterns.some(pattern => {
      const message = error.message || '';
      const name = error.name || '';
      
      // For chunk errors, must be exact match
      if (pattern.includes('chunk') || pattern.includes('Chunk')) {
        return message.includes(pattern) || name.includes(pattern);
      }
      
      // For script error, must be the exact browser error message
      if (pattern === 'Script error.') {
        return message === 'Script error.' || message === 'Script error';
      }
      
      return false;
    });
  };

  private shouldSkipError = (event: ErrorEvent): boolean => {
  if (!event.error && !event.message) return true;
    
    const message = event.message || event?.error?.message || '';
    
    // Skip React DevTools and development-only errors
    if (process.env.NODE_ENV === 'development') {
      const skipPatterns = [
        'ResizeObserver loop limit exceeded',
        'ResizeObserver loop completed with undelivered notifications',
        'Non-Error promise rejection captured',
        'Warning: Invalid DOM property',
        'Warning: React',
        'Warning: Invalid value for prop',
        'Warning: Failed prop type',
        'Download the React DevTools'
      ];
      
      if (skipPatterns.some(pattern => message.includes(pattern))) {
        return true;
      }
    }
    
    // Skip generic JavaScript errors that aren't critical
    const genericErrors = [
      'Uncaught TypeError: Cannot read properties of undefined',
      'Uncaught TypeError: Cannot read property',
      'Cannot read properties of null',
      'Cannot set property',
      'is not a function',
      'is not defined'
    ];
    
    // Only skip these if they're from React components or non-critical contexts
    if (genericErrors.some(pattern => message.includes(pattern))) {
      // Skip if it's clearly a React component error or development context
      if (message.includes('React') || 
          message.includes('Component') || 
          message.includes('useEffect') ||
          message.includes('useState') ||
          message.includes('DevTools') ||
          event?.filename?.includes('node_modules') ||
          event?.filename?.includes('webpack') ||
          event?.filename?.includes('vite')) {
        return true;
      }
    }
    
    return false;
  };

  private isCriticalResource = (tagName: string, src: string): boolean => {
    // Only report failures for critical resources
    const criticalTags = ['script', 'link'];
    const criticalPatterns = ['/static/', '/assets/', '.js', '.css'];
    
    return criticalTags.includes(tagName) && 
           criticalPatterns.some(pattern => src?.includes(pattern));
  };

  private reportError = (errorReport: ErrorReport): void => {
    // Add to queue
    this.errorQueue.push(errorReport);
    
    // Maintain queue size
    if (this.errorQueue.length > this.maxQueueSize) {
      this.errorQueue.shift();
    }
    
    // In production, send to error reporting service
    if (import.meta.env.PROD) {
      this.sendToErrorService(errorReport);
    } else {
      // Error already logged by errorLogger, no need for duplicate console logging
    }
  };

  private sendToErrorService = async (errorReport: ErrorReport): Promise<void> => {
    try {
      // Example implementation - replace with your error service
      console.debug('Sending error report:', errorReport.errorId);
      // await fetch('/api/errors', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(errorReport)
      // });
      
      // Error report would be sent to service in production
    } catch (sendError) {
      errorLogger.warning('system', sendError, 'Failed to send error report');
    }
  };

  private generateErrorId = (): string => {
    return `error_${Date.now()}_${Math.random().toString(36).slice(2, 11)}`;
  };

  // Get current error queue for debugging
  public getErrorQueue = (): ErrorReport[] => {
    return [...this.errorQueue];
  };

  // Clear error queue
  public clearErrorQueue = (): void => {
    this.errorQueue = [];
  };
}

// Create singleton instance
const globalErrorHandler = new GlobalErrorHandler();

// Initialize on module load
if (typeof window !== 'undefined') {
  globalErrorHandler.initialize();
}

export default globalErrorHandler;
export type { ErrorReport };
