import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { 
  Globe, Lock, Users, Calendar, Settings, UserPlus, Flag, Info,
  Image, Pin, ThumbsUp, MessageCircle, Share,
  CalendarDays, UserCheck, UserX, Shield, Crown,
  Download, Upload, File, FileImage, FileVideo, FileAudio
} from 'lucide-react';
import { toast } from 'sonner';
import { useNavigate } from 'react-router-dom';
import { ROUTES, getSafeImage } from '@/lib/constants';

// Types
interface GroupPost {
  id: string, author: {
    id: string, name: string, avatar: string;
    isAdmin?: boolean;
  };
  content: string, timestamp: string, likes: number, comments: number;
  isLiked?: boolean;
  isPinned?: boolean;
  media?: {
    type: 'image' | 'video', url: string;
  }[];
}

interface GroupMember {
  id: string, name: string, avatar: string, role: 'admin' | 'moderator' | 'member', joinedDate: string, isActive: boolean;
}

interface GroupEvent {
  id: string, title: string, description: string, date: string, time: string, location: string, attendees: number, isAttending: boolean;
  image?: string;
}

interface GroupFile {
  id: string, name: string, type: 'image' | 'video' | 'audio' | 'document', size: string, uploadedBy: string, uploadedAt: string, url: string;
}

interface Group {
  id: string, name: string, description: string, privacy: 'public' | 'private', members: number, category: string;
  rules?: string[];
  createdAt?: string;
}

interface GroupViewProps {
  group: Group, isAdmin: boolean, view: 'discussion' | 'members' | 'events' | 'files' | 'about';
}

const GroupView: React.FC<GroupViewProps> = ({ group, isAdmin, view }) => {
  const navigate = useNavigate();
  const [newPostContent, setNewPostContent] = useState('');

  // Mock data - in real app, this would come from props or API
  const mockPosts: GroupPost[] = [
    {
      id: '1',
      author: {
        id: '1',
        name: 'John Doe',
        avatar: getSafeImage('avatars/john.jpg'),
        isAdmin: true
      },
      content: 'Welcome to our group! Please read the rules and introduce yourself.',
      timestamp: '2 hours ago',
      likes: 12,
      comments: 5,
      isLiked: false,
      isPinned: true
    },
    {
      id: '2',
      author: {
        id: '2',
        name: 'Jane Smith',
        avatar: getSafeImage('avatars/jane.jpg')
      },
      content: 'Looking forward to our next meetup! Who\'s planning to attend?',
      timestamp: '4 hours ago',
      likes: 8,
      comments: 3,
      isLiked: true
    }
  ];

  const mockMembers: GroupMember[] = [
    {
      id: '1',
      name: 'John Doe',
      avatar: getSafeImage('avatars/john.jpg'),
      role: 'admin',
      joinedDate: '2023-01-15',
      isActive: true
    },
    {
      id: '2',
      name: 'Jane Smith',
      avatar: getSafeImage('avatars/jane.jpg'),
      role: 'moderator',
      joinedDate: '2023-02-20',
      isActive: true
    },
    {
      id: '3',
      name: 'Bob Johnson',
      avatar: getSafeImage('avatars/bob.jpg'),
      role: 'member',
      joinedDate: '2023-03-10',
      isActive: false
    }
  ];

  const mockEvents: GroupEvent[] = [
    {
      id: '1',
      title: 'Monthly Meetup',
      description: 'Join us for our monthly group meetup and discussion.',
      date: '2024-01-20',
      time: '7:00 PM',
      location: 'Community Center',
      attendees: 15,
      isAttending: true,
      image: getSafeImage('events/meetup.jpg')
    },
    {
      id: '2',
      title: 'Workshop Session',
      description: 'Learn new skills in our hands-on workshop.',
      date: '2024-01-25',
      time: '2:00 PM',
      location: 'Online',
      attendees: 8,
      isAttending: false
    }
  ];

  const mockFiles: GroupFile[] = [
    {
      id: '1',
      name: 'Group Guidelines.pdf',
      type: 'document',
      size: '2.3 MB',
      uploadedBy: 'John Doe',
      uploadedAt: '2024-01-10',
      url: '/files/guidelines.pdf'
    },
    {
      id: '2',
      name: 'Meeting Recording.mp4',
      type: 'video',
      size: '45.2 MB',
      uploadedBy: 'Jane Smith',
      uploadedAt: '2024-01-08',
      url: '/files/recording.mp4'
    },
    {
      id: '3',
      name: 'Group Photo.jpg',
      type: 'image',
      size: '1.8 MB',
      uploadedBy: 'Bob Johnson',
      uploadedAt: '2024-01-05',
      url: '/files/photo.jpg'
    }
  ];

  const handlePost = () => {
    if (!newPostContent.trim()) {
      toast.error('Please enter some content for your post');
      return;
    }
    
    toast.success('Post created successfully!');
    setNewPostContent('');
  };

  const handleLike = (_postId: string) => {
    toast.success(`Post liked!`);
  };

  const handleEventAttendance = (_eventId: string, attending: boolean) => {
    toast.success(attending ? 'Added to your calendar!' : 'Removed from calendar');
  };

  const handleFileDownload = (_fileId: string) => {
    toast.success(`Download started for file`);
  };

  const getFileIcon = (type: string) => {
    switch (type) {
      case 'image': return FileImage;
      case 'video': return FileVideo;
      case 'audio': return FileAudio;
      default: return File;
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'admin': return Crown;
      case 'moderator': return Shield;
      default: return Users;
    }
  };

  const renderDiscussionView = () => (
    <div className="space-y-6">
      {/* Create Post */}
      <Card>
        <CardContent className="p-4">
          <div className="flex space-x-3">
            <Avatar className="w-10 h-10">
              <AvatarImage src={getSafeImage('avatars/current-user.jpg')} />
              <AvatarFallback>You</AvatarFallback>
    </Avatar>
            <div className="flex-1">
              <Textarea
                placeholder="What's on your mind?"
                value={newPostContent} onChange={(e) => setNewPostContent(e.target.value)}, className="min-h-[100px] resize-none"
              />
              <div className="flex justify-between items-center mt-3">
                <Button variant="ghost" size="sm">
                  <Image className="w-4 h-4 mr-2" />
                  Add Photo
                </Button>
                <Button onClick={handlePost} size="sm">
                  Post
                </Button>
    </div>
            </div>
    </div>
        </CardContent>
    </Card>
      {/* Posts */}
      {mockPosts.map((post) => (
        <Card key={post.id}>
          <CardContent className="p-4">
            <div className="flex items-start space-x-3">
              <Avatar className="w-10 h-10">
                <AvatarImage src={post.author.avatar} />
                <AvatarFallback>{post.author.name[0]}</AvatarFallback>
    </Avatar>
              <div className="flex-1">
                <div className="flex items-center space-x-2">
                  <span className="font-semibold">{post.author.name}</span>
                  {post.author.isAdmin && (
                    <Badge variant="secondary" className="text-xs">Admin</Badge>
                  )}
                  {post.isPinned && (
                    <Pin className="w-4 h-4 text-blue-500" />
                  )}
                  <span className="text-sm text-gray-500">{post.timestamp}</span>
    </div>
                <p className="mt-2 text-gray-700 dark:text-gray-300">{post.content}</p>
                <div className="flex items-center space-x-4 mt-3">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleLike(post.id)} className={post.isLiked ? 'text-blue-500' : ''}
                  >
                    <ThumbsUp className="w-4 h-4 mr-1" />
                    {post.likes}
                  </Button>
                  <Button variant="ghost" size="sm">
                    <MessageCircle className="w-4 h-4 mr-1" />
                    {post.comments}
                  </Button>
                  <Button variant="ghost" size="sm">
                    <Share className="w-4 h-4 mr-1" />
                    Share
                  </Button>
    </div>
              </div>
    </div>
          </CardContent>
    </Card>
      ))}
    </div>
  );

  const renderMembersView = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Members ({mockMembers.length})</h3>
        {isAdmin && (
          <Button size="sm">
            <UserPlus className="w-4 h-4 mr-2" />
            Invite Members
          </Button>
        )}
      </div>
      
      <div className="grid gap-4">
        {mockMembers.map((member) => {
          const RoleIcon = getRoleIcon(member.role);
          return (
            <Card key={member.id}>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Avatar className="w-12 h-12">
                      <AvatarImage src={member.avatar} />
                      <AvatarFallback>{member.name[0]}</AvatarFallback>
    </Avatar>
                    <div>
                      <div className="flex items-center space-x-2">
                        <span className="font-semibold">{member.name}</span>
                        <RoleIcon className="w-4 h-4 text-gray-500" />
    </div>
                      <div className="flex items-center space-x-2 text-sm text-gray-500">
                        <span>Joined {member.joinedDate}</span>
                        {member.isActive && (
                          <Badge variant="outline" className="text-xs">Active</Badge>
                        )}
                      </div>
    </div>
                  </div>
                  {isAdmin && member.role !== 'admin' && (
                    <div className="flex space-x-2">
                      <Button variant="outline" size="sm">
                        <UserCheck className="w-4 h-4" />
    </Button>
                      <Button variant="outline" size="sm">
                        <UserX className="w-4 h-4" />
    </Button>
                    </div>
                  )}
                </div>
    </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );

  const renderEventsView = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Upcoming Events</h3>
        {isAdmin && (
          <Button size="sm">
            <CalendarDays className="w-4 h-4 mr-2" />
            Create Event
          </Button>
        )}
      </div>
      
      <div className="grid gap-4">
        {mockEvents.map((event) => (
          <Card key={event.id}>
            <CardContent className="p-4">
              <div className="flex space-x-4">
                {event.image && (
                  <img 
                    src={event.image} alt={event.title}, className="w-20 h-20 object-cover rounded-lg"
                  />
                )}
                <div className="flex-1">
                  <h4 className="font-semibold text-lg">{event.title}</h4>
                  <p className="text-gray-600 dark:text-gray-400 mt-1">{event.description}</p>
                  <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                    <span>{event.date} at {event.time}</span>
                    <span>{event.location}</span>
                    <span>{event.attendees} attending</span>
    </div>
                  <div className="flex space-x-2 mt-3">
                    <Button
                      size="sm"
                      variant={event.isAttending ? "default" : "outline"} onClick={() => handleEventAttendance(event.id, !event.isAttending)}
                    >
                      {event.isAttending ? 'Going' : 'Interested'}
                    </Button>
                    <Button variant="ghost" size="sm">
                      <Share className="w-4 h-4 mr-1" />
                      Share
                    </Button>
    </div>
                </div>
    </div>
            </CardContent>
    </Card>
        ))}
      </div>
    </div>
  );

  const renderFilesView = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Files</h3>
        {isAdmin && (
          <Button size="sm">
            <Upload className="w-4 h-4 mr-2" />
            Upload File
          </Button>
        )}
      </div>
      
      <div className="grid gap-4">
        {mockFiles.map((file) => {
          const FileIcon = getFileIcon(file.type);
          return (
            <Card key={file.id}>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <FileIcon className="w-8 h-8 text-gray-500" />
                    <div>
                      <span className="font-semibold">{file.name}</span>
                      <div className="text-sm text-gray-500">
                        {file.size} • Uploaded by {file.uploadedBy} on {file.uploadedAt}
                      </div>
    </div>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleFileDownload(file.id)}
                  >
                    <Download className="w-4 h-4" />
    </Button>
                </div>
    </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );

  const renderAboutView = () => (
    <Card>
      <CardContent className="p-6">
        <h2 className="text-xl font-bold mb-4 dark:text-white">About This Group</h2>
        <p className="text-gray-700 mb-6 dark:text-gray-300">{group.description}</p>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 className="font-semibold mb-3 flex items-center">
              <Info className="w-5 h-5 mr-2 text-blue-500" />
              Details
            </h3>
            <div className="space-y-2 text-sm">
              <div className="flex items-center space-x-2">
                {group.privacy === 'public' ? (
                  <Globe className="w-4 h-4 text-gray-500" />
                ) : (
                  <Lock className="w-4 h-4 text-gray-500" />
                )}
                <span className="capitalize">{group.privacy} Group</span>
    </div>
              <div className="flex items-center space-x-2">
                <Users className="w-4 h-4 text-gray-500" />
                <span>{group.members.toLocaleString()} members</span>
    </div>
              <div className="flex items-center space-x-2">
                <Calendar className="w-4 h-4 text-gray-500" />
                <span>Created {group.createdAt || 'January 2024'}</span>
    </div>
            </div>
    </div>
          <div>
            <h3 className="font-semibold mb-3">Category</h3>
            <Badge variant="outline">{group.category}</Badge>
    </div>
        </div>
        
        {group.rules && (
          <div className="mt-6">
            <h3 className="font-semibold mb-3">Group Rules</h3>
            <ul className="space-y-2">
              {group.rules.map((rule, index) => (
                <li key={index} className="flex items-start space-x-2">
                  <span className="text-blue-500 font-bold">{index + 1}.</span>
                  <span className="text-sm">{rule}</span>
    </li>
              ))}
            </ul>
    </div>
        )}
        
        <div className="flex space-x-3 mt-6">
          {isAdmin && (
            <Button onClick={() => navigate(ROUTES.GROUPS)} size="sm">
              <Settings className="w-4 h-4 mr-2" />
              Manage Group
            </Button>
          )}
          <Button variant="outline" size="sm">
            <Flag className="w-4 h-4 mr-2" />
            Report Group
          </Button>
    </div>
      </CardContent>
    </Card>
  );

  // Render the appropriate view based on the view prop
  switch (view) {
    case 'discussion':
      return renderDiscussionView();
    case 'members':
      return renderMembersView();
    case 'events':
      return renderEventsView();
    case 'files':
      return renderFilesView();
    case 'about':
      return renderAboutView();
    default:
      return renderDiscussionView();
  }
};

export default GroupView;