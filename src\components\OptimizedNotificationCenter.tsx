import React, { useState, useMemo, memo, useRef, useEffect } from 'react';
import { Bell, Check, Settings, Trash2, MoreVertical, Search } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import { useImmediateOptimization } from '@/hooks/useImmediateOptimizations';
import { useNotifications } from '@/hooks/useNotifications';
import { formatTimeAgo } from '@/utils/timeUtils';
import type { Notification } from '@/services/UnifiedNotificationService';

interface OptimizedNotificationCenterProps {
  isOpen: boolean, onClose: () => void;
  className?: string;
}

// OPTIMIZATION 1: Memoized notification categories for consistent references
const NOTIFICATION_CATEGORIES = [{ id: 'all', label: 'All', icon: Bell },
  { id: 'messages', label: 'Messages', icon: Bell },
  { id: 'reactions', label: 'Reactions', icon: Bell },
  { id: 'mentions', label: 'Mentions', icon: Bell },
  { id: 'friend_requests', label: 'Friends', icon: Bell }] as const;

// OPTIMIZATION 2: Memoized notification item component with deep comparison
const OptimizedNotificationItem = memo<{
  notification: Notification, onMarkAsRead: (id: string) => void; onDelete: (id: string) => void; onInteraction: (id: string, action: string) => void;
}>(({ notification, onMarkAsRead, onDelete, onInteraction }) => {
  // OPTIMIZATION: Create optimized callbacks for this specific notification
  const { createOptimizedCallback } = useImmediateOptimization(`NotificationItem-${notification.id}`);
  
  const handleMarkAsRead = createOptimizedCallback(() => {
    onMarkAsRead(notification.id);
  }, [onMarkAsRead, notification.id]);
  
  const handleDelete = createOptimizedCallback(() => {
    onDelete(notification.id);
  }, [onDelete, notification.id]);
  
  const handleClick = createOptimizedCallback(() => {
    onInteraction(notification.id, 'click');
  }, [onInteraction, notification.id]);
  
  // OPTIMIZATION: Memoized time display to prevent frequent updates
  const timeDisplay = useMemo(() => 
    formatTimeAgo(notification.timestamp), 
    [notification.timestamp]
  );
  
  return (
    <motion.div
      layout
      initial={{ opacity: 0, y: 10 }}, animate={{ opacity: 1, y: 0 }}, exit={{ opacity: 0, y: -10 }}, transition={{ duration: 0.2 }}
    >
      <Card className={cn(
        "mb-2 cursor-pointer transition-all duration-200 hover:shadow-md",
        !notification.isRead && "border-l-4 border-l-blue-500 bg-blue-50/30"
      )}>
        <CardContent className="p-4">
          <div className="flex items-start space-x-3">
            <Avatar className="w-10 h-10 flex-shrink-0">
              <AvatarImage src={notification.imageUrl} alt="" />
              <AvatarFallback>
                {notification.type === 'message' && '💬'}
                {notification.type === 'reaction' && '👍'}
                {notification.type === 'mention' && '@'}
                {notification.type === 'friend_request' && '👋'}
              </AvatarFallback>
    </Avatar>
            <div className="flex-1 min-w-0" onClick={handleClick}>
              <h4 className="font-medium text-sm leading-tight">
                {notification.title}
              </h4>
              <p className="text-gray-600 text-xs mt-1 line-clamp-2">
                {notification.message}
              </p>
              <div className="flex items-center justify-between mt-2">
                <span className="text-xs text-gray-500">{timeDisplay}</span>
                {notification.category && (
                  <Badge variant="secondary" className="text-xs">
                    {notification.category}
                  </Badge>
                )}
              </div>
    </div>
            <div className="flex items-center space-x-1 flex-shrink-0">
              {!notification.isRead && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleMarkAsRead} className="w-8 h-8 p-0"
                >
                  <Check className="w-4 h-4" />
    </Button>
              )}
              
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="w-8 h-8 p-0">
                    <MoreVertical className="w-4 h-4" />
    </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  {!notification.isRead && (
                    <DropdownMenuItem onClick={handleMarkAsRead}>
                      <Check className="w-4 h-4 mr-2" />
                      Mark as read
                    </DropdownMenuItem>
                  )}
                  <DropdownMenuItem onClick={handleDelete} className="text-red-600">
                    <Trash2 className="w-4 h-4 mr-2" />
                    Delete
                  </DropdownMenuItem>
    </DropdownMenuContent>
              </DropdownMenu>
    </div>
          </div>
    </CardContent>
      </Card>
    </motion.div>
  );
}, (prevProps, nextProps) => {
  // OPTIMIZATION: Custom comparison to prevent unnecessary re-renders
  return (
    prevProps.notification.id === nextProps.notification.id &&
    prevProps.notification.isRead === nextProps.notification.isRead &&
    prevProps.notification.timestamp === nextProps.notification.timestamp &&
    prevProps.onMarkAsRead === nextProps.onMarkAsRead &&
    prevProps.onDelete === nextProps.onDelete &&
    prevProps.onInteraction === nextProps.onInteraction
  );
});

OptimizedNotificationItem.displayName = 'OptimizedNotificationItem';

// OPTIMIZATION 3: Main notification center component with comprehensive optimization
const OptimizedNotificationCenter: React.FC<OptimizedNotificationCenterProps> = memo(({
  isOpen,
  onClose,
  className = ''
}) => {
  // OPTIMIZATION: Performance monitoring for the notification center
  const { 
    createOptimizedCallback, 
    createMemoizedObject;
    getPerformanceMetrics 
  } = useImmediateOptimization('OptimizedNotificationCenter');
  
  // OPTIMIZATION: Use optimized notifications hook
  const {
    notifications,
    unreadCount,
    isConnected,
    markAsRead,
    markAllAsRead,
    deleteNotification;
    clearAllNotifications
  } = useNotifications();
  
  // Local state with optimized initial values
  const [searchTerm, setSearchTerm] = useState('');
  const [activeCategory, setActiveCategory] = useState<string>('all');
  const [showSettings, setShowSettings] = useState(false);
  
  // OPTIMIZATION: Refs for stable DOM references
  const searchInputRef = useRef<HTMLInputElement>(null);
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  
  // OPTIMIZATION: Optimized callbacks with stable references
  const optimizedMarkAsRead = createOptimizedCallback((...args: unknown[]) => {
    const id = args[0] as string;
    markAsRead(id);
  }, [markAsRead]);

  const optimizedDeleteNotification = createOptimizedCallback((...args: unknown[]) => {
    const id = args[0] as string;
    deleteNotification(id);
  }, [deleteNotification]);

  const optimizedNotificationInteraction = createOptimizedCallback((...args: unknown[]) => {
    const id = args[0] as string;
    const action = args[1] as string;
    // Log performance metrics in development
    if (process.env.NODE_ENV === 'development') {
      const metrics = getPerformanceMetrics();
      console.log('📊 NotificationCenter Performance:', metrics);
    }
    
    // Handle notification interaction
    const notification = notifications.find(n => n.id === id);
    if (notification && action === 'click') {
      // Mark as read when clicked
      if (!notification.isRead) {
        markAsRead(id);
      }
      
      // Navigate to notification target if available
      if (notification.actionUrl) {
        window.location.href = notification.actionUrl;
      }
    }
  }, [notifications, markAsRead, getPerformanceMetrics]);
  
  const optimizedMarkAllAsRead = createOptimizedCallback(() => {
    markAllAsRead();
  }, [markAllAsRead]);
  
  const optimizedClearAll = createOptimizedCallback(() => {
    clearAllNotifications();
  }, [clearAllNotifications]);
  
  // OPTIMIZATION: Memoized filtered notifications to prevent recalculation
  const filteredNotifications = useMemo(() => {
    let filtered = notifications;
    
    // Filter by category
    if (activeCategory !== 'all') {
      filtered = filtered.filter(n => n.category === activeCategory);
    }
    
    // Filter by search term
    if (searchTerm.trim()) {
      const search = searchTerm.toLowerCase().trim();
      filtered = filtered.filter(n => 
        n.title.toLowerCase().includes(search) ||
        n.message.toLowerCase().includes(search)
      );
    }
    
    return filtered;
  }, [notifications, activeCategory, searchTerm]);
  
  // OPTIMIZATION: Memoized notification statistics
  const notificationStats = useMemo(() => {
    const unreadInCategory = filteredNotifications.filter(n => !n.isRead).length;
    const totalInCategory = filteredNotifications.length;
    
    return {
      unreadInCategory,
      totalInCategory,
      hasUnread: unreadInCategory > 0
    };
  }, [filteredNotifications]);
  
  // OPTIMIZATION: Memoized configuration object
  const centerConfig = createMemoizedObject(() => ({
    isOpen,
    searchTerm,
    activeCategory,
    showSettings
  }), [isOpen, searchTerm, activeCategory, showSettings]);
  
  // Focus search input when modal opens
  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      setTimeout(() => {
        searchInputRef.current?.focus();
      }, 100);
    }
  }, [isOpen]);
  
  // OPTIMIZATION: Log performance metrics in development
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      const metrics = getPerformanceMetrics();
      console.log('⚙️ NotificationCenter Configuration:', centerConfig);
      if (metrics.renderCount > 0 && metrics.renderCount % 5 === 0) {
        console.log('📊 NotificationCenter Performance Metrics:', metrics);
      }
    }
  }, [centerConfig, getPerformanceMetrics]);
  
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className={cn("max-w-2xl max-h-[80vh]", className)}>
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Bell className="w-5 h-5" />
              <span>Notifications</span>
              {unreadCount > 0 && (
                <Badge variant="destructive" className="ml-2">
                  {unreadCount}
                </Badge>
              )}
            </div>
            
            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowSettings(!showSettings)}
              >
                <Settings className="w-4 h-4" />
    </Button>
            </div>
    </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          {/* Search and Filters */}
          <div className="flex items-center space-x-2">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <Input
                ref={searchInputRef} placeholder="Search notifications..."
                value={searchTerm} onChange={(e) => setSearchTerm(e.target.value)}, className="pl-10"
              />
    </div>
            <Button
              variant="outline"
              size="sm"
              onClick={optimizedMarkAllAsRead} disabled={!notificationStats.hasUnread}
            >
              <Check className="w-4 h-4 mr-1" />
              Mark all read
            </Button>
    </div>
          {/* Category Tabs */}
          <Tabs value={activeCategory} onValueChange={setActiveCategory}>
            <TabsList className="grid w-full grid-cols-5">
              {NOTIFICATION_CATEGORIES.map(category => (
                <TabsTrigger key={category.id} value={category.id}, className="text-xs">
                  {category.label}
                </TabsTrigger>
              ))}
            </TabsList>
            
            {/* Notification List */}
            <TabsContent value={activeCategory} className="mt-4">
              <ScrollArea ref={scrollAreaRef} className="h-96">
                <AnimatePresence mode="popLayout">
                  {filteredNotifications.length > 0 ? (
                    filteredNotifications.map(notification => (
                      <OptimizedNotificationItem
                        key={notification.id} notification={notification}, onMarkAsRead={optimizedMarkAsRead} onDelete={optimizedDeleteNotification}, onInteraction={optimizedNotificationInteraction}
                      />
                    ))
                  ) : (
                    <motion.div
                      initial={{ opacity: 0 }}, animate={{ opacity: 1 }}, className="text-center py-8"
                    >
                      <Bell className="w-12 h-12 mx-auto text-gray-300 mb-4" />
                      <p className="text-gray-500">
                        {searchTerm ? 'No notifications match your search' : 'No notifications yet'}
                      </p>
                    </motion.div>
                  )}
                </AnimatePresence>
    </ScrollArea>
            </TabsContent>
    </Tabs>
          {/* Action Buttons */}
          {filteredNotifications.length > 0 && (
            <div className="flex items-center justify-between pt-4 border-t">
              <div className="text-sm text-gray-500">
                {notificationStats.unreadInCategory} unread of {notificationStats.totalInCategory} total
              </div>
              
              <Button
                variant="outline"
                size="sm"
                onClick={optimizedClearAll} className="text-red-600 hover:text-red-700"
              >
                <Trash2 className="w-4 h-4 mr-1" />
                Clear all
              </Button>
    </div>
          )}
          
          {/* Connection Status */}
          <div className="flex items-center justify-center pt-2">
            <div className={cn(
              "flex items-center space-x-1 text-xs",
              isConnected ? "text-green-600" : "text-red-600"
            )}>
              <div className={cn(
                "w-2 h-2 rounded-full",
                isConnected ? "bg-green-500" : "bg-red-500"
              )} />
              <span>{isConnected ? 'Connected' : 'Disconnected'}</span>
    </div>
          </div>
    </div>
      </DialogContent>
    </Dialog>
  );
});

OptimizedNotificationCenter.displayName = 'OptimizedNotificationCenter';

export default OptimizedNotificationCenter;
