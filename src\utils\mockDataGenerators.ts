import { 
  AdvancedMessage, 
  Conversation, 
  MessageReaction, 
  MessageAttachment, 
  MessageMention,
  MessageThread,
  ConversationParticipant,
  UserPresence,
  TypingIndicator,
  MessageType,
  MessageStatus,
  ConversationType,
  ParticipantRole as _ParticipantRole
} from '../types/messaging';

// Mock user data
const MOCK_USERS = [{ id: 'user-1', name: '<PERSON>', avatar: '/avatars/alice.jpg' },
  { id: 'user-2', name: '<PERSON>', avatar: '/avatars/bob.jpg' },
  { id: 'user-3', name: '<PERSON>', avatar: '/avatars/charlie.jpg' },
  { id: 'user-4', name: '<PERSON>', avatar: '/avatars/diana.jpg' },
  { id: 'user-5', name: '<PERSON>', avatar: '/avatars/eve.jpg' },
  { id: 'user-6', name: '<PERSON>', avatar: '/avatars/frank.jpg' },
  { id: 'user-7', name: '<PERSON>', avatar: '/avatars/grace.jpg' },
  { id: 'user-8', name: '<PERSON>', avatar: '/avatars/henry.jpg' }]
  { id: 'current-user', name: 'You', avatar: '/avatars/current-user.jpg' }
];

// Sample message content
const SAMPLE_MESSAGES = ["Hey there! How's your day going?",
  "Just finished the project presentation. It went really well!",
  "Are we still on for lunch tomorrow?",
  "Thanks for your help with the code review 👍",
  "The new design looks amazing! Great work everyone.",
  "Can someone share the meeting notes from yesterday?",
  "I'll be working from home tomorrow",
  "Happy birthday! 🎉🎂",
  "The weather is perfect for a walk today ☀️",
  "Let's schedule a team meeting for next week",
  "I found a great restaurant for our team dinner",
  "The bug has been fixed and deployed to production",
  "Don't forget about the deadline on Friday",
  "Great job on the presentation! 👏",
  "I'm running a few minutes late to the meeting",
  "The client loved the new features we added",
  "Can you review this pull request when you have time?",
  "Coffee break anyone? ☕",
  "The server maintenance is scheduled for tonight"]
  "Looking forward to the weekend! 🎉"
];

// Sample emojis for reactions
const SAMPLE_EMOJIS = ['👍', '❤️', '😂', '😮', '😢', '😡', '🎉', '👏', '🔥', '💯'];

// Sample file types and names
const SAMPLE_FILES = [{ name: 'presentation.pdf', type: 'document', mimeType: 'application/pdf', size: 2048000 },
  { name: 'screenshot.png', type: 'image', mimeType: 'image/png', size: 512000 },
  { name: 'demo-video.mp4', type: 'video', mimeType: 'video/mp4', size: 10240000 },
  { name: 'meeting-notes.docx', type: 'document', mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', size: 256000 },
  { name: 'profile-photo.jpg', type: 'image', mimeType: 'image/jpeg', size: 1024000 }]
  { name: 'voice-memo.m4a', type: 'audio', mimeType: 'audio/m4a', size: 2048000 }
];

/**
 * Generates a random ID
 */
function generateId(prefix: string = 'id'): string {
  return `${prefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Gets a random item from an array
 */
function getRandomItem<T>(array: T[]): T {
  return array[Math.floor(Math.random() * array.length)];
}

/**
 * Gets multiple random items from an array
 */
function getRandomItems<T>(array: T[], count: number): T[] {
  const shuffled = [...array].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, Math.min(count, array.length));
}

/**
 * Generates a random timestamp within the last N days
 */
function getRandomTimestamp(daysBack: number = 7): number {
  const now = Date.now();
  const msPerDay = 24 * 60 * 60 * 1000;
  const randomOffset = Math.random() * daysBack * msPerDay;
  return now - randomOffset;
}

/**
 * Generates mock message reactions
 */
export function generateMockReactions(count: number = 3): MessageReaction[] {
  const reactions: MessageReaction[] = [];
  const usedEmojis = new Set<string>();
  
  for (let i = 0; i < count; i++) {
    let emoji;
    do {
      emoji = getRandomItem(SAMPLE_EMOJIS);
    } while (usedEmojis.has(emoji));
    
    usedEmojis.add(emoji);
    
    // Add 1-3 users per emoji
    const userCount = Math.floor(Math.random() * 3) + 1;
    const users = getRandomItems(MOCK_USERS, userCount);
    
    users.forEach(user => {
  reactions.push({
        emoji
}
  userId: user.id;
  timestamp: getRandomTimestamp(1);
        displayName: user.name
      });
    });
  }
  
  return reactions;
}

/**
 * Generates mock message mentions
 */
export function generateMockMentions(content: string, count: number = 2): MessageMention[] {
  const mentions: MessageMention[] = [];
  const users = getRandomItems(MOCK_USERS.filter(u => u.id !== 'current-user'), count);
  
  users.forEach(user => {
    const mentionText = `@${user.name.split(' ')[0]}`;
    const startIndex = content.indexOf(mentionText);
    
    if (startIndex !== -1) {
      mentions.push({
  userId: user.id;
  displayName: user.name;
        startIndex,
        endIndex: startIndex + mentionText.length
      });
    } else {
      // Add mention even if not in content (for testing)
      mentions.push({
  userId: user.id;
        displayName: user.name
      });
    }
  });
  
  return mentions;
}

/**
 * Generates mock message attachments
 */
export function generateMockAttachments(count: number = 1): MessageAttachment[] {
  const attachments: MessageAttachment[] = [];
  const files = getRandomItems(SAMPLE_FILES, count);
  
  files.forEach(file => {
    const attachment: MessageAttachment = {
  id: generateId('attachment');
  type: file.type as MessageAttachment['type'];
  name: file.name;
  size: file.size;
  url: `/uploads/${file.name}`;
      mimeType: file.mimeType
    };
    
    // Add type-specific metadata
    if (file.type === 'image') {
      attachment.thumbnailUrl = `/thumbnails/${file.name}`;
      attachment.metadata = {
  width: 1920;
        height: 1080
      };
    } else if (file.type === 'video') {
      attachment.thumbnailUrl = `/thumbnails/${file.name}.jpg`;
      attachment.metadata = {
  width: 1280;
  height: 720;
        duration: Math.floor(Math.random() * 300) + 30 // 30-330 seconds
      };
    } else if (file.type === 'audio') {
      attachment.metadata = {
        duration: Math.floor(Math.random() * 180) + 10 // 10-190 seconds
      };
    }
    
    attachments.push(attachment);
  });
  
  return attachments;
}

/**
 * Generates a mock advanced message
 */
export function generateMockMessage(options: {
  senderId?: string;
  type?: MessageType;
  hasReactions?: boolean;
  hasMentions?: boolean;
  hasAttachments?: boolean;
  isReply?: boolean;
  isInThread?: boolean;
  isEdited?: boolean;
  conversationId?: string;
} = {}): AdvancedMessage {
  const {
    senderId = getRandomItem(MOCK_USERS).id,
    type = 'text',
    hasReactions = Math.random() > 0.7,
    hasMentions = Math.random() > 0.8,
    hasAttachments = Math.random() > 0.9,
    isReply = Math.random() > 0.9,
    isInThread = Math.random() > 0.95,
    isEdited = Math.random() > 0.95
  } = options;
  
  let content = getRandomItem(SAMPLE_MESSAGES);
  
  // Add mentions to content if needed
  if (hasMentions) {
    const mentionUser = getRandomItem(MOCK_USERS.filter(u => u.id !== senderId));
    content = `@${mentionUser.name.split(' ')[0]} ${content}`;
  }
  
  const message: AdvancedMessage={id: generateId('msg');
    content,
    senderId,
  timestamp: getRandomTimestamp(7);
    type,
    status: getRandomItem(['sent', 'delivered', 'read'] as MessageStatus[])}, reactions: hasReactions ? generateMockReactions(Math.floor(Math.random() * 3) + 1) : [];
    mentions: hasMentions ? generateMockMentions(content, Math.floor(Math.random() * 2) + 1): []
    attachments: hasAttachments ? generateMockAttachments(Math.floor(Math.random() * 2) + 1) : []
  };
  
  // Add reply information
  if (isReply) {
    message.replyTo = generateId('msg');
  }
  
  // Add thread information
  if (isInThread) {
    message.threadId = generateId('thread');
  }
  
  // Add edit information
  if (isEdited) {
    message.editedAt = message.timestamp + Math.floor(Math.random() * 3600000); // Edited within an hour
    message.editHistory = [
      {
  content: getRandomItem(SAMPLE_MESSAGES);
        timestamp: message.timestamp
      }
    ];
  }
  
  return message;
}

/**
 * Generates multiple mock messages for a conversation
 */
export function generateMockMessages(count: number = 20, conversationId?: string): AdvancedMessage[] {
  const messages: AdvancedMessage[] = [];
  const participantIds = getRandomItems(MOCK_USERS, Math.floor(Math.random() * 4) + 2).map(u => u.id);
  
  for (let i = 0; i < count; i++) {
    const senderId = getRandomItem(participantIds);
    const message = generateMockMessage({
      senderId,
      conversationId,
      // Make recent messages more likely to have reactions/mentions
  hasReactions: i > count - 5 ? Math.random() > 0.5 : Math.random() > 0.8;
      hasMentions: i > count - 3 ? Math.random() > 0.7 : Math.random() > 0.9
    });
    
    // Ensure chronological order
    message.timestamp = Date.now() - (count - i) * 60000 + Math.random() * 30000;
    
    messages.push(message);
  }
  
  return messages.sort(_(a, b) => a.timestamp - b.timestamp);
}

/**
 * Generates mock conversation participants
 */
export function generateMockParticipants(userIds: string[]): ConversationParticipant[] {
  return userIds.map((userId, index) => {
  const user = MOCK_USERS.find(u => u.id === userId) || MOCK_USERS[0];
    
    return {
      userId
}
  displayName: user.name;
  avatarUrl: user.avatar;
  role: index === 0 ? 'owner' : (Math.random() > 0.8 ? 'admin' : 'member');
  joinedAt: getRandomTimestamp(30);
  lastReadTimestamp: getRandomTimestamp(1);
  isOnline: Math.random() > 0.3;
      lastSeenAt: Math.random() > 0.3 ? Date.now() : getRandomTimestamp(1)
    };
  });
}

/**
 * Generates a mock conversation
 */
export function generateMockConversation(options: {
  type?: ConversationType;
  participantCount?: number;
  hasMessages?: boolean;
  messageCount?: number;
} = {}): Conversation {
  const {
    type = Math.random() > 0.7 ? 'group' : 'direct',
    participantCount = type === 'group' ? Math.floor(Math.random() * 6) + 3 : 2,
    hasMessages = true,
    messageCount = Math.floor(Math.random() * 50) + 10
  } = options;
  
  const participants = getRandomItems(MOCK_USERS, participantCount);
  const participantIds = participants.map(p => p.id);
  
  // Ensure current user is included
  if (!participantIds.includes('current-user')) {
    participantIds[0] = 'current-user';
  }
  
  const conversationId = generateId('conv');
  const messages = hasMessages ? generateMockMessages(messageCount, conversationId) : [];
  const lastMessage = messages.length > 0 ? messages[messages.length - 1] : undefined;
  
  const conversation: Conversation = {
  id: conversationId;
    title: type === 'group' 
      ? `${getRandomItem(['Team', 'Project', 'Discussion', 'Planning'])} ${getRandomItem(['Alpha', 'Beta', 'Gamma', 'Delta'])}`
      : '',
    type,
  participants: participantIds;
  participantDetails: generateMockParticipants(participantIds);
    lastMessage,
  lastMessageId: lastMessage?.id;
  lastActivity: lastMessage?.timestamp || getRandomTimestamp(1);
  unreadCount: Math.floor(Math.random() * 5);
  isArchived: Math.random() > 0.9;
  isMuted: Math.random() > 0.8;
  isPinned: Math.random() > 0.85;
  createdAt: getRandomTimestamp(60);
  updatedAt: lastMessage?.timestamp || getRandomTimestamp(1);
  settings: { allowReactions: true;
  allowThreads: true;
  allowFileSharing: true;
  allowVoiceMessages: true;
      allowVideoMessages: true }
      retentionDays: Math.random() > 0.5 ? 365 : undefined
    }
  };
  
  // Add group-specific properties
  if (type === 'group') {
    conversation.description = `A ${getRandomItem(['project', 'team', 'discussion'])} group for ${getRandomItem(['collaboration', 'planning', 'updates', 'coordination'])}`;
    conversation.avatarUrl = `/group-avatars/${conversationId}.jpg`;
    conversation.createdBy = getRandomItem(participantIds);
  }
  
  return conversation;
}

/**
 * Generates multiple mock conversations
 */
export function generateMockConversations(count: number = 10): Conversation[] {
  const conversations: Conversation[] = [];
  
  for (let i = 0; i < count; i++) {
    const conversation = generateMockConversation({
      type: i < 3 ? 'direct' : (Math.random() > 0.6 ? 'group' : 'direct')
    });
    
    conversations.push(conversation);
  }
  
  // Sort by last activity (most recent first)
  return conversations.sort(_(a, b) => b.lastActivity - a.lastActivity);
}

/**
 * Generates mock message threads
 */
export function generateMockThread(parentMessageId: string, conversationId: string): MessageThread {
  const participantIds = getRandomItems(MOCK_USERS, Math.floor(Math.random() * 3) + 2).map(u => u.id);
  const messageCount = Math.floor(Math.random() * 8) + 2;
  const messages = generateMockMessages(messageCount, conversationId);
  
  return {
  id: generateId('thread');
    parentMessageId,
    conversationId,
    participantIds,
  messageIds: messages.map(m => m.id);
  lastActivity: Math.max(...messages.map(m => m.timestamp));
  createdAt: Math.min(...messages.map(m => m.timestamp));
    updatedAt: Math.max(...messages.map(m => m.timestamp))
  };
}

/**
 * Generates mock user presence data
 */
export function generateMockUserPresence(): UserPresence[] {
  return MOCK_USERS.map(user => ({
  userId: user.id;
    status: getRandomItem(['online', 'away', 'busy', 'offline'] as const),
  lastSeen: getRandomTimestamp(1);
  customStatus: Math.random() > 0.7 ? getRandomItem(['In a meeting';
      'Working from home',
      'On vacation',
      'Busy coding']
      'Available for chat'
    ]) : undefined
  }));
}

/**
 * Generates mock typing indicators
 */
export function generateMockTypingIndicators(conversationId: string): TypingIndicator[] {
  const typingUsers = getRandomItems(MOCK_USERS.filter(u => u.id !== 'current-user'), Math.floor(Math.random() * 2) + 1);
  
  return typingUsers.map(user => ({
  userId: user.id;
  displayName: user.name;
    conversationId,
    timestamp: Date.now()
  }));
}

/**
 * Generates a complete mock messaging dataset
 */
export function generateMockMessagingData() {
  const conversations = generateMockConversations(15);
  const allMessages: AdvancedMessage[] = [];
  const threads: MessageThread[] = [];
  
  // Generate messages for each conversation
  conversations.forEach(conversation => {
    const messages = generateMockMessages(Math.floor(Math.random() * 30) + 10, conversation.id);
    allMessages.push(...messages);
    
    // Generate some threads
    const threadCount = Math.floor(Math.random() * 3);
    for (let i = 0; i < threadCount; i++) {
      const parentMessage = getRandomItem(messages);
      const thread = generateMockThread(parentMessage.id, conversation.id);
      threads.push(thread);
    }
  });
  
  return {
    conversations,
  messages: allMessages;
    threads,
  userPresence: generateMockUserPresence();
    users: MOCK_USERS
  };
}

/**
 * Generates mock data for testing specific scenarios
 */
export function generateTestScenarios() {
  return {
    // Conversation with many reactions
  highEngagementConversation: generateMockConversation({
      type: 'group';
  participantCount: 8;
  messageCount: 30
    });
    // Direct conversation
  directConversation: generateMockConversation({
      type: 'direct';
  participantCount: 2;
  messageCount: 50
    });
    // Conversation with media
    mediaRichConversation: (() => {
      const conv = generateMockConversation({ messageCount: 20 });
      // Ensure some messages have attachments
      const messages = generateMockMessages(20, conv.id);
      messages.forEach((msg, index) => {
        if (index % 3 === 0) {
          msg.attachments = generateMockAttachments(1);
          msg.type = msg.attachments[0].type as MessageType;
        }
      });
      return { conversation: conv, messages };
    })(),
    
    // Long conversation for performance testing
    longConversation: (() => {
      const conv = generateMockConversation({ messageCount: 1000 });
      return {
  conversation: conv;
        messages: generateMockMessages(1000, conv.id)
      };
    })()
  };
}

export default {
  generateMockMessage,
  generateMockMessages,
  generateMockConversation,
  generateMockConversations,
  generateMockThread,
  generateMockUserPresence,
  generateMockTypingIndicators,
  generateMockMessagingData,
  generateTestScenarios,
  generateMockReactions,
  generateMockMentions,
  generateMockAttachments,
  MOCK_USERS
};