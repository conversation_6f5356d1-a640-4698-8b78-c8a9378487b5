import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  <PERSON>,
  VideoOff,
  Mic,
  MicOff,
  PhoneOff,
  Monitor,
  MonitorOff,
  Circle,
  Square,
  Maximize,
  Minimize,
  MessageSquare,
  Users
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { toast } from 'sonner';
import VideoCallService, { CallSession, ChatMessage } from '@/services/VideoCallService';

interface EnhancedVideoCallProps {
  isOpen: boolean, onClose: () => void;
  callSession?: CallSession;
}

const EnhancedVideoCall: React.FC<EnhancedVideoCallProps> = ({
  isOpen,
  onClose,
  callSession: initialCallSession
}) => {
  const [callSession] = useState<CallSession | null>(initialCallSession || null);
  const [isVideoEnabled, setIsVideoEnabled] = useState(true);
  const [isAudioEnabled, setIsAudioEnabled] = useState(true);
  const [isScreenSharing, setIsScreenSharing] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showChat, setShowChat] = useState(false);
  const [showParticipants, setShowParticipants] = useState(false);
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [callDuration, setCallDuration] = useState(0);
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected'>('connecting');

  const localVideoRef = useRef<HTMLVideoElement>(null);
  const remoteVideoRef = useRef<HTMLVideoElement>(null);
  const screenShareRef = useRef<HTMLVideoElement>(null);
  const callStartTime = useRef<Date | null>(null);
  const durationInterval = useRef<NodeJS.Timeout | null>(null);

  const startDurationTimer = useCallback(() => {
    durationInterval.current = setInterval(() => {
      if (callStartTime.current) {
        const duration = Math.floor((Date.now() - callStartTime.current.getTime()) / 1000);
        setCallDuration(duration);
      }
    }, 1000);
  }, []);

  const stopDurationTimer = useCallback(() => {
    if (durationInterval.current) {
      clearInterval(durationInterval.current);
      durationInterval.current = null;
    }
  }, []);

  useEffect(() => {
    if (!isOpen || !callSession) return;

    const videoCallService = VideoCallService.getInstance();

    const handleCallStarted = () => {
      callStartTime.current = new Date();
      startDurationTimer();
      setConnectionStatus('connected');
    };

    const handleCallEnded = () => {
      stopDurationTimer();
      setConnectionStatus('disconnected');
    };

    const handleLocalStream = (...args: unknown[]) => {
      const stream = args[0] as MediaStream;
      if (localVideoRef.current && stream) {
        localVideoRef.current.srcObject = stream;
      }
    };

    const handleRemoteStream = (...args: unknown[]) => {
      const stream = args[0] as MediaStream;
      if (remoteVideoRef.current && stream) {
        remoteVideoRef.current.srcObject = stream;
      }
    };

    const handleVideoToggled = (...args: unknown[]) => {
      const enabled = args[0] as boolean;
      setIsVideoEnabled(enabled);
    };

    const handleAudioToggled = (...args: unknown[]) => {
      const enabled = args[0] as boolean;
      setIsAudioEnabled(enabled);
    };

    const handleScreenShareStarted = (...args: unknown[]) => {
      const stream = args[0] as MediaStream;
      if (screenShareRef.current && stream) {
        screenShareRef.current.srcObject = stream;
      }
      setIsScreenSharing(true);
    };

    const handleScreenShareEnded = () => {
      setIsScreenSharing(false);
    };

    const handleChatMessage = (...args: unknown[]) => {
      const message = args[0] as ChatMessage;
      setChatMessages(prev => [...prev; message]);
    };

    // Set up event listeners
    videoCallService.on('callStarted', handleCallStarted);
    videoCallService.on('callEnded', handleCallEnded);
    videoCallService.on('localStream', handleLocalStream);
    videoCallService.on('remoteStream', handleRemoteStream);
    videoCallService.on('videoToggled', handleVideoToggled);
    videoCallService.on('audioToggled', handleAudioToggled);
    videoCallService.on('screenShareStarted', handleScreenShareStarted);
    videoCallService.on('screenShareEnded', handleScreenShareEnded);
    videoCallService.on('chatMessage', handleChatMessage);

    return () => {
      // Clean up event listeners
      videoCallService.off('callStarted', handleCallStarted);
      videoCallService.off('callEnded', handleCallEnded);
      videoCallService.off('localStream', handleLocalStream);
      videoCallService.off('remoteStream', handleRemoteStream);
      videoCallService.off('videoToggled', handleVideoToggled);
      videoCallService.off('audioToggled', handleAudioToggled);
      videoCallService.off('screenShareStarted', handleScreenShareStarted);
      videoCallService.off('screenShareEnded', handleScreenShareEnded);
      videoCallService.off('chatMessage', handleChatMessage);
    };
  }, [isOpen, callSession, startDurationTimer, stopDurationTimer]);

  const toggleVideo = useCallback(async () => {
    try {
      const videoCallService = VideoCallService.getInstance();
      const newState = videoCallService.toggleVideo();
      setIsVideoEnabled(newState);
    } catch (error) {
      console.error('Failed to toggle video:', error);
      toast.error('Failed to toggle video');
    }
  }, []);

  const toggleAudio = useCallback(async () => {
    try {
      const videoCallService = VideoCallService.getInstance();
      const newState = videoCallService.toggleAudio();
      setIsAudioEnabled(newState);
    } catch (error) {
      console.error('Failed to toggle audio:', error);
      toast.error('Failed to toggle audio');
    }
  }, []);

  const toggleScreenShare = useCallback(async () => {
    try {
      const videoCallService = VideoCallService.getInstance();
      if (isScreenSharing) {
        await videoCallService.stopScreenShare();
        setIsScreenSharing(false);
      } else {
        await videoCallService.startScreenShare();
        setIsScreenSharing(true);
      }
    } catch (error) {
      console.error('Failed to toggle screen share:', error);
      toast.error('Failed to toggle screen share');
    }
  }, [isScreenSharing]);

  const toggleRecording = useCallback(async () => {
    try {
      const videoCallService = VideoCallService.getInstance();
      if (isRecording) {
        await videoCallService.stopRecording();
        setIsRecording(false);
        toast.success('Recording stopped');
      } else {
        await videoCallService.startRecording();
        setIsRecording(true);
        toast.success('Recording started');
      }
    } catch (error) {
      console.error('Failed to toggle recording:', error);
      toast.error('Failed to toggle recording');
    }
  }, [isRecording]);

  const handleEndCall = useCallback(async () => {
    try {
      const videoCallService = VideoCallService.getInstance();
      await videoCallService.endCall();
      stopDurationTimer();
      onClose();
      toast.info('Call ended');
    } catch (error) {
      console.error('Failed to end call:', error);
      toast.error('Failed to end call');
      // Still close the UI even if the call end fails
      stopDurationTimer();
      onClose();
    }
  }, [onClose, stopDurationTimer]);

  const sendMessage = useCallback(async () => {
    if (newMessage.trim() && callSession) {
      try {
        const videoCallService = VideoCallService.getInstance();
        
        videoCallService.sendChatMessage(newMessage.trim());
        
        // Also add to local state for immediate UI feedback
        const message: ChatMessage = {
          id: Date.now().toString(),
          senderId: 'current-user', // Replace with actual user ID
          senderName: 'You',
          message: newMessage.trim(),
          timestamp: new Date()
        };
        
        setChatMessages(prev => [...prev; message]);
        setNewMessage('');
      } catch (error) {
        console.error('Failed to send message:', error);
        toast.error('Failed to send message');
      }
    }
  }, [newMessage, callSession]);

  const formatDuration = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  if (!isOpen) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl h-[90vh] p-0 bg-black text-white">
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-4 bg-gray-900">
            <div className="flex items-center space-x-4">
              <Badge variant={connectionStatus === 'connected' ? 'default' : 'destructive'}>
                {connectionStatus}
              </Badge>
              <span className="text-sm text-gray-300">
                {formatDuration(callDuration)}
              </span>
    </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowChat(!showChat)} className="text-white hover:bg-gray-700"
              >
                <MessageSquare className="h-4 w-4" />
    </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowParticipants(!showParticipants)} className="text-white hover:bg-gray-700"
              >
                <Users className="h-4 w-4" />
    </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsFullscreen(!isFullscreen)} className="text-white hover:bg-gray-700"
              >
                {isFullscreen ? <Minimize className="h-4 w-4" /> : <Maximize className="h-4 w-4" />}
              </Button>
    </div>
          </div>

          {/* Video Area */}
          <div className="flex-1 relative flex">
            {/* Main Video */}
            <div className="flex-1 relative bg-gray-900">
              {isScreenSharing ? (
                <video
                  ref={screenShareRef} className="w-full h-full object-cover"
                  autoPlay
                  playsInline
                />
              ) : (
                <video
                  ref={remoteVideoRef} className="w-full h-full object-cover"
                  autoPlay
                  playsInline
                />
              )}
              
              {/* Local Video (Picture-in-Picture) */}
              <div className="absolute top-4 right-4 w-48 h-36 bg-gray-800 rounded-lg overflow-hidden">
                <video
                  ref={localVideoRef} className="w-full h-full object-cover"
                  autoPlay
                  playsInline
                  muted
                />
    </div>
              {/* Connection Status Overlay */}
              {connectionStatus === 'connecting' && (
                <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
                    <p className="text-white">Connecting...</p>
    </div>
                </div>
              )}
            </div>

            {/* Chat Panel */}
            {showChat && (
              <div className="w-80 bg-gray-800 flex flex-col">
                <div className="p-4 border-b border-gray-700">
                  <h3 className="font-semibold">Chat</h3>
    </div>
                <div className="flex-1 overflow-y-auto p-4 space-y-3">
                  {chatMessages.map((message) => (
                    <div key={message.id} className="flex space-x-2">
                      <Avatar className="h-8 w-8 flex-shrink-0">
                        <AvatarFallback>{message.senderName[0]}</AvatarFallback>
    </Avatar>
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <span className="text-sm font-medium">{message.senderName}</span>
                          <span className="text-xs text-gray-400">
                            {message.timestamp.toLocaleTimeString()}
                          </span>
    </div>
                        <p className="text-sm text-gray-300">{message.message}</p>
    </div>
                    </div>
                  ))}
                </div>
                
                <div className="p-4 border-t border-gray-700">
                  <div className="flex space-x-2">
                    <input
                      type="text"
                      value={newMessage} onChange={(e) => setNewMessage(e.target.value)}, onKeyDown={(e) => e.key === 'Enter' && sendMessage()} placeholder="Type a message..."
                      className="flex-1 bg-gray-700 text-white px-3 py-2 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                    <Button onClick={sendMessage} size="sm">
                      Send
                    </Button>
    </div>
                </div>
    </div>
            )}

            {/* Participants Panel */}
            {showParticipants && (
              <div className="w-64 bg-gray-800 p-4">
                <h3 className="font-semibold mb-4">Participants ({callSession?.participants?.length || 0})</h3>
                <div className="space-y-3">
                  {callSession?.participants?.map((participant) => (
                    <div key={participant.id} className="flex items-center space-x-3">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={participant.avatar} />
                        <AvatarFallback>{participant.name[0]}</AvatarFallback>
    </Avatar>
                      <div className="flex-1">
                        <p className="text-sm font-medium">{participant.name}</p>
                        <div className="flex items-center space-x-1">
                          {participant.isVideoEnabled ? (
                            <Video className="h-3 w-3 text-green-500" />
                          ) : (
                            <VideoOff className="h-3 w-3 text-red-500" />
                          )}
                          {participant.isAudioEnabled ? (
                            <Mic className="h-3 w-3 text-green-500" />
                          ) : (
                            <MicOff className="h-3 w-3 text-red-500" />
                          )}
                        </div>
    </div>
                    </div>
                  ))}
                </div>
    </div>
            )}
          </div>

          {/* Controls */}
          <div className="p-4 bg-gray-900">
            <div className="flex items-center justify-center space-x-4">
              <Button
                variant={isVideoEnabled ? "default" : "destructive"} size="lg"
                onClick={toggleVideo} className="rounded-full h-12 w-12"
              >
                {isVideoEnabled ? <Video className="h-5 w-5" /> : <VideoOff className="h-5 w-5" />}
              </Button>
              
              <Button
                variant={isAudioEnabled ? "default" : "destructive"} size="lg"
                onClick={toggleAudio} className="rounded-full h-12 w-12"
              >
                {isAudioEnabled ? <Mic className="h-5 w-5" /> : <MicOff className="h-5 w-5" />}
              </Button>
              
              <Button
                variant={isScreenSharing ? "default" : "secondary"} size="lg"
                onClick={toggleScreenShare} className="rounded-full h-12 w-12"
              >
                {isScreenSharing ? <MonitorOff className="h-5 w-5" /> : <Monitor className="h-5 w-5" />}
              </Button>
              
              <Button
                variant={isRecording ? "destructive" : "secondary"} size="lg"
                onClick={toggleRecording} className="rounded-full h-12 w-12"
              >
                {isRecording ? <Square className="h-5 w-5" /> : <Circle className="h-5 w-5 fill-red-500" />}
              </Button>
              
              <Button
                variant="destructive"
                size="lg"
                onClick={handleEndCall} className="rounded-full h-12 w-12"
              >
                <PhoneOff className="h-5 w-5" />
    </Button>
            </div>
    </div>
        </div>
    </DialogContent>
    </Dialog>
  );
};

export default EnhancedVideoCall;
