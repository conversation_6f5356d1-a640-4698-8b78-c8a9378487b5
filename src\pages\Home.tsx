import React, { useState, useEffect, useCallback, Suspense, useMemo } from 'react';
import { simpleGql as gql, useSimpleQuery as useQuery } from '@/hooks/useSimpleQuery';
import OptimizedStories from '@/components/optimized/OptimizedStories';
import UnifiedNewsFeedFilters from '@/components/UnifiedNewsFeedFilters';
import { BasePost } from '@/types/shared';
import { MOCK_IMAGES, getSafeImage } from '@/lib/constants';
import { aiFeedEngine } from '@/services/AIFeedEngine';
import { performanceMonitoringService } from '@/services/PerformanceMonitoringService';

// Lazy load the heavy virtualized feed component
const VirtualizedNewsFeedRefactored = React.lazy(async () => {
  if (import.meta.env.DEV) console.log('⚡ Loading VirtualizedNewsFeed component...');
  return import('@/components/VirtualizedNewsFeedRefactored');
});

const GET_POSTS = gql`
  query GetPosts {
    posts {
      id
      content
      author {
        id
        name
        avatar
      }
      createdAt
      likesCount
      commentsCount
      isLiked
      images
      location
      feeling
      privacy
      reactions
      isLive
      isPoll
      pollOptions
      pollVotes
      taggedFriends
    }
  }
`;

const GET_STORIES = gql`
  query GetStories {
    stories {
      id
      user {
        id
        name
        avatar
      }
      media {
        type
        url
      }
      createdAt
      expiresAt
      isViewed
      viewsCount
    }
  }
`;

const GET_FRIEND_REQUESTS = gql`
  query GetFriendRequests {
    friendRequests {
      id
      from {
        id
        name
        avatar
        username
      }
      createdAt
      mutualFriends
    }
  }
`;

const Home: React.FC = () => {
  if (import.meta.env.DEV) {
    console.log('🏠 Home component rendering...');
    console.log('🏠 Environment:', import.meta.env.MODE);
  }

  const { loading, error, data } = useQuery(GET_POSTS, {
    fetchPolicy: 'cache-and-network',
  });

  const { data: storiesData } = useQuery(GET_STORIES, {
    fetchPolicy: 'cache-and-network',
  });

  const { data: friendRequestsData } = useQuery(GET_FRIEND_REQUESTS, {
    fetchPolicy: 'cache-and-network',
  });

  // Tab filter state
  const [activeTab, setActiveTab] = useState<'foryou' | 'following'>('foryou');
  const [useAIRanking, setUseAIRanking] = useState(true);
  const [aiRankedPosts, setAiRankedPosts] = useState<BasePost[]>([]);
  const [isRanking, setIsRanking] = useState(false);

  // Get current user ID (in real app, this would come from auth context)
  const currentUserId =
    (typeof window !== 'undefined' && window.localStorage.getItem('userId')) || 'demo-user';

  // Initialize performance monitoring
  useEffect(() => {
    performanceMonitoringService.startRecording();
    performanceMonitoringService.mark('home-page-start');

    return () => {
      performanceMonitoringService.mark('home-page-end');
      performanceMonitoringService.measure('home-page-duration', 'home-page-start', 'home-page-end');
      const report = performanceMonitoringService.generateReport();
      console.log('Performance Report:', report);
    };
  }, []);

  // Advanced filter states
  const [creator, setCreator] = useState('all');
  const [dateRange, setDateRange] = useState<'all' | 'today' | 'week' | 'month'>('all');
  const [location, setLocation] = useState('');
  const [liked, setLiked] = useState(false);
  const [saved, setSaved] = useState(false);

  // Enhanced filter states
  const [contentType, setContentType] = useState('all');
  const [sortBy, setSortBy] = useState<'recent' | 'likes' | 'comments'>('recent');
  const [minLikes, setMinLikes] = useState(0);
  const [minComments, setMinComments] = useState(0);
  const [onlyVerified, setOnlyVerified] = useState(false);
  const [showTrending, setShowTrending] = useState(false);

  // Mock data for news feed - this will be replaced by GraphQL data
  const allPosts: BasePost[] = useMemo(() => {
    if (data?.posts) {
      return data.posts.map((post: unknown, index: number) => {
        const postData = post as {
          id: string;
          content?: string;
          author?: { id: string; name?: string; avatar?: string };
          createdAt: string;
          likesCount?: number;
          commentsCount?: number;
          isLiked?: boolean;
          images?: string[];
          location?: string;
          feeling?: string;
          isLive?: boolean;
        };
        
        return {
          id: String(postData.id),
          user_id: String(postData.author?.id ?? `user-${index}`),
          content: postData.content ?? '',
          image_url: postData.images?.[0] || getSafeImage('POSTS', index % MOCK_IMAGES.POSTS.length),
          created_at: postData.createdAt,
          updated_at: postData.createdAt,
          likes_count: postData.likesCount ?? 0,
          comments_count: postData.commentsCount ?? 0,
          profiles: {
            id: String(postData.author?.id ?? `user-${index}`),
            full_name: postData.author?.name ?? 'Unknown',
            avatar_url: postData.author?.avatar || getSafeImage('AVATARS', index % MOCK_IMAGES.AVATARS.length),
          },
          location: postData.location ?? 'Demo Location',
          feeling: postData.feeling ?? 'demo',
          user_has_liked: !!postData.isLiked,
          is_live: !!postData.isLive,
        };
      });
    }
    return [
      {
        id: '1',
        user_id: 'user1',
        content: '🎉 Welcome to your Facebook-like app! This is demo content with mock GraphQL data.',
        image_url: getSafeImage('POSTS', 0),
        created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        updated_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        likes_count: 42,
        comments_count: 12,
        profiles: { id: 'user1', full_name: 'Demo User', avatar_url: getSafeImage('AVATARS', 0) },
        location: 'Demo City',
        feeling: 'excited',
        user_has_liked: false,
        is_live: false,
      },
      {
        id: '2',
        user_id: 'user2',
        content:
          '🚀 Your app is fully functional! All features work: Stories, Posts, Messages, YouTube videos, and more.',
        image_url: getSafeImage('POSTS', 1),
        created_at: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
        updated_at: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
        likes_count: 128,
        comments_count: 24,
        profiles: { id: 'user2', full_name: 'Tech Demo', avatar_url: getSafeImage('AVATARS', 1) },
        location: 'Silicon Valley',
        feeling: 'productive',
        user_has_liked: true,
        is_live: false,
      },
      {
        id: '3',
        user_id: 'user3',
        content:
          '📱 Check out all the features: Dark/Light theme, PWA capabilities, real-time updates, virtual scrolling, and professional UI components!',
        image_url: getSafeImage('POSTS', 2),
        created_at: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
        updated_at: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
        likes_count: 89,
        comments_count: 18,
        profiles: { id: 'user3', full_name: 'Feature Showcase', avatar_url: getSafeImage('AVATARS', 2) },
        location: 'Global',
        feeling: 'amazing',
        user_has_liked: false,
        is_live: false,
      },
    ];
  }, [data]);

  // Filter logic
  const filteredPosts = useMemo(() => {
    let filtered = [...allPosts];

    // Apply all filters
    if (creator !== 'all') {
      const needle = creator.toLowerCase();
      filtered = filtered.filter(post => post?.profiles?.full_name?.toLowerCase().includes(needle));
    }

    if (dateRange !== 'all') {
      const now = new Date();
      const cutoff = new Date(now);
      switch (dateRange) {
        case 'today':
          cutoff.setHours(0, 0, 0, 0);
          break;
        case 'week':
          cutoff.setDate(now.getDate() - 7);
          break;
        case 'month':
          cutoff.setMonth(now.getMonth() - 1);
          break;
      }
      filtered = filtered.filter(post => new Date(post.created_at) >= cutoff);
    }

    if (location) {
      const loc = location.toLowerCase();
      filtered = filtered.filter(post => post?.location?.toLowerCase().includes(loc));
    }

    if (liked) {
      filtered = filtered.filter(post => post.user_has_liked);
    }

    if (minLikes > 0) {
      filtered = filtered.filter(post => post.likes_count >= minLikes);
    }

    if (minComments > 0) {
      filtered = filtered.filter(post => post.comments_count >= minComments);
    }

    // Sort posts
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'likes':
          return b.likes_count - a.likes_count;
        case 'comments':
          return b.comments_count - a.comments_count;
        case 'recent':
        default:
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
      }
    });

    return filtered;
  }, [allPosts, creator, dateRange, location, liked, minLikes, minComments, sortBy]);

  // Apply AI ranking when in 'foryou' tab
  useEffect(() => {
    let cancelled = false;
    async function rankPosts() {
      if (activeTab === 'foryou' && useAIRanking && filteredPosts.length > 0) {
        setIsRanking(true);
        performanceMonitoringService.mark('ai-ranking-start');

        try {
          const ranked = await aiFeedEngine.rankPosts(filteredPosts, currentUserId);
          if (!cancelled) {
            setAiRankedPosts(ranked);
          }

          performanceMonitoringService.mark('ai-ranking-end');
          performanceMonitoringService.measure('ai-ranking-duration', 'ai-ranking-start', 'ai-ranking-end');

          const insights = aiFeedEngine.getUserInsights(currentUserId);
          console.log('AI Feed Insights:', insights);
        } catch (error) {
          if (import.meta.env.DEV) console.error('AI ranking failed:', error);
          if (!cancelled) setAiRankedPosts(filteredPosts);
        } finally {
          if (!cancelled) setIsRanking(false);
        }
      } else {
        setAiRankedPosts(filteredPosts);
      }
    }

    rankPosts();

    return () => {
      cancelled = true;
    };
  }, [filteredPosts, activeTab, useAIRanking, currentUserId]);

  // Track post interactions for AI learning
  const handlePostInteraction = useCallback((postId: string, action: string, data?: unknown) => {
    console.log(`Post interaction: ${action} on post ${postId}`);
    
    // Track interaction for AI learning
    aiFeedEngine.trackInteraction(currentUserId, postId, action, data);
    
    // Track performance metric
    performanceMonitoringService.mark(`interaction-${action}`);
    
    // In real app, this would also update backend
  }, [currentUserId]);
  
  // Wrapper functions for type-safe prop passing
  const handleTabChange = useCallback((tab: string) => {
    setActiveTab(tab as 'foryou' | 'following');
  }, []);

  const handleDateRangeChange = useCallback((range: string) => {
    setDateRange(range as 'all' | 'today' | 'week' | 'month');
  }, []);

  const handleSortByChange = useCallback((sort: string) => {
    setSortBy(sort as 'recent' | 'likes' | 'comments');
  }, []);
  
  // Use AI ranked posts when available
  const displayPosts = useAIRanking && activeTab === 'foryou' ? aiRankedPosts : filteredPosts;

  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="mt-4 text-muted-foreground">Loading your feed...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    if (import.meta.env.DEV) console.error('GraphQL Error:', error);
    // Continue with mock data on error
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Simple Visual Test */}
      <div style={{
  position: 'fixed', top: '10px', right: '10px', background: 'red', color: 'white', padding: '10px', zIndex: 9999, fontSize: '12px', fontWeight: 'bold'
      }}>
        HOME PAGE LOADED
      </div>
      
      <div className="container mx-auto px-2 md:px-4 py-4 md:py-8 max-w-4xl">
        {/* Stories Section */}
        <div className="mb-4 md:mb-8">
          <OptimizedStories />
        </div>
        {/* Enhanced Demo Banner */}
        <div className="mb-4 md:mb-6 p-3 md:p-6 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
          <h2 className="text-base md:text-lg font-semibold text-blue-900 dark:text-blue-100 mb-3">
            🎊 Enhanced Mock Data Mode
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-2 md:gap-4">
            <div className="bg-white/50 dark:bg-gray-800/50 p-3 rounded-lg">
              <h3 className="font-medium text-blue-800 dark:text-blue-200">Posts</h3>
              <p className="text-sm text-blue-600 dark:text-blue-300">
                {data?.posts?.length || 0} posts loaded with reactions,polls, and tags
              </p>
            </div>
            <div className="bg-white/50 dark:bg-gray-800/50 p-3 rounded-lg">
              <h3 className="font-medium text-blue-800 dark:text-blue-200">Stories</h3>
              <p className="text-sm text-blue-600 dark:text-blue-300">
                {storiesData?.stories?.length || 0} active stories available
              </p>
            </div>
            <div className="bg-white/50 dark:bg-gray-800/50 p-3 rounded-lg">
              <h3 className="font-medium text-blue-800 dark:text-blue-200">Friend Requests</h3>
              <p className="text-sm text-blue-600 dark:text-blue-300">
                {friendRequestsData?.friendRequests?.length || 0} pending requests
              </p>
            </div>
          </div>
          <p className="text-blue-700 dark:text-blue-200 text-sm mt-4">
            ✅ Advanced mock data: Polls,reactions,live posts,location tags, and more!
          </p>
        </div>
        {/* News Feed Filters */}
        <div className="mb-4 md:mb-6">
          <UnifiedNewsFeedFilters
            activeTab={activeTab}
            onTabChange={handleTabChange}
            creator={creator}
            setCreator={setCreator}
            dateRange={dateRange}
            setDateRange={handleDateRangeChange}
            location={location}
            setLocation={setLocation}
            liked={liked}
            setLiked={setLiked}
            saved={saved}
            setSaved={setSaved}
            contentType={contentType}
            setContentType={setContentType}
            sortBy={sortBy}
            setSortBy={handleSortByChange}
            minLikes={minLikes}
            setMinLikes={setMinLikes}
            minComments={minComments}
            setMinComments={setMinComments}
            onlyVerified={onlyVerified}
            setOnlyVerified={setOnlyVerified}
            showTrending={showTrending}
            setShowTrending={setShowTrending}
            onApply={() => {
              // Filters are applied automatically through state changes
              console.log('Filters applied');
            }}
            onClear={() => {
              // Reset all filters to default values
              setActiveTab('foryou');
              setCreator('all');
              setDateRange('all');
              setLocation('');
              setLiked(false);
              setSaved(false);
              setContentType('all');
              setSortBy('recent');
              setMinLikes(0);
              setMinComments(0);
              setOnlyVerified(false);
              setShowTrending(false);
            }}
          />
        </div>
        {/* Virtualized News Feed */}
        <Suspense
          fallback={
            <div className="flex items-center justify-center p-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              <span className="ml-3 text-muted-foreground">Loading feed...</span>
            </div>
          }
        >
          <VirtualizedNewsFeedRefactored
            posts={displayPosts}
            isLoading={loading || isRanking}
            onRefresh={() => {
              console.log('Refreshing feed...');
              // In real app, this would refetch data
            }}
            onPostInteraction={handlePostInteraction}
          />
        </Suspense>
      </div>
    </div>
  );
};

export default Home;
