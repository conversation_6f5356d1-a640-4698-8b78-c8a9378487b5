import React, { useState, useEffect } from 'react';
import { Shield, Eye, Users, Clock, Download, Trash2, AlertTriangle, CheckCircle } from 'lucide-react';
import PrivacyService from '../../services/messaging/PrivacyService';

interface PrivacyDashboardProps {
  userId: string;
  isAdmin?: boolean;
  className?: string;
}

interface PrivacyStats {
  readReceiptsEnabled: boolean, onlineStatusVisible: boolean, blockedUsersCount: number, autoDeleteEnabled: boolean, autoDeleteDays: number, dataCollectionEnabled: boolean, recentActions: unknown[];
}

export const PrivacyDashboard: React.FC<PrivacyDashboardProps> = ({
  userId,
  isAdmin = false,
  className = ''
}) => {
  const [stats, setStats] = useState<PrivacyStats | null>(null);
  const [complianceReport, setComplianceReport] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'overview' | 'activity' | 'compliance'>('overview');

  const privacyService = PrivacyService.getInstance();

  useEffect(() => {
    const loadPrivacyData = () => {
      try {
        const settings = privacyService.getPrivacySettings(userId);
        const blockedUsers = privacyService.getBlockedUsers(userId);
        const recentActions = privacyService.getPrivacyActionHistory(userId, 10);
        
        setStats({
          readReceiptsEnabled: settings.readReceipts.enabled,
          onlineStatusVisible: settings.onlineStatus.visible,
          blockedUsersCount: blockedUsers.length,
          autoDeleteEnabled: settings.messageHistory.autoDeleteEnabled,
          autoDeleteDays: settings.messageHistory.autoDeleteDays,
          dataCollectionEnabled: settings.dataPrivacy.allowDataCollection,
          recentActions
        });

        if (isAdmin) {
          const report = privacyService.getPrivacyComplianceReport();
          setComplianceReport(report);
        }

        setLoading(false);
      } catch (error) {
        console.error('Failed to load privacy data:', error);
        setLoading(false);
      }
    };

    loadPrivacyData();
    const interval = setInterval(loadPrivacyData, 30000); // Refresh every 30 seconds
    return () => clearInterval(interval);
  }, [userId, isAdmin, privacyService]);

  const handleExportData = () => {
    try {
      const data = privacyService.exportUserData(userId);
      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `privacy-data-${userId}-${Date.now()}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Failed to export data:', error);
    }
  };

  const handleDeleteAllData = () => {
    if (confirm('Are you sure you want to delete all your data? This action cannot be undone.')) {
      try {
        privacyService.deleteAllUserData(userId);
        alert('All data has been deleted.');
      } catch (error) {
        console.error('Failed to delete data:', error);
      }
    }
  };

  const formatActionType = (type: string) => {
    switch (type) {
      case 'read_receipt':
        return 'Read Receipt';
      case 'online_status':
        return 'Online Status';
      case 'message_delete':
        return 'Message Deleted';
      case 'conversation_delete':
        return 'Conversation Deleted';
      case 'data_export':
        return 'Data Exported';
      case 'data_delete':
        return 'Data Deleted';
      default:
        return type;
    }
  };

  const formatTimestamp = (timestamp: number) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      return 'Just now';
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)} hours ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  if (loading) {
    return (
      <div className={`privacy-dashboard ${className}`}>
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="space-y-3">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-4 bg-gray-200 rounded"></div>
            ))}
          </div>
    </div>
      </div>
    );
  }

  return (
    <div className={`privacy-dashboard bg-white rounded-lg shadow-sm border ${className}`}>
      {/* Header */}
      <div className="border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Shield className="w-6 h-6 text-blue-600" />
            <div>
              <h2 className="text-lg font-semibold text-gray-900">Privacy Dashboard</h2>
              <p className="text-sm text-gray-600">Monitor your privacy settings and data usage</p>
    </div>
          </div>
          
          <div className="flex items-center gap-2">
            <button
              onClick={handleExportData} className="flex items-center gap-2 px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
            >
              <Download className="w-4 h-4" />
              Export Data
            </button>
    </div>
        </div>
    </div>
      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8 px-6">
          {[
            { id: 'overview', label: 'Overview' },
            { id: 'activity', label: 'Recent Activity' },
            ...(isAdmin ? [{ id: 'compliance', label: 'Compliance' }] : [])
          ].map(tab => (
            <button
              key={tab.id} onClick={() => setActiveTab(tab.id as any)}, className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {tab.label}
            </button>
          ))}
        </nav>
    </div>
      {/* Content */}
      <div className="p-6">
        {activeTab === 'overview' && stats && (
          <div className="space-y-6">
            {/* Privacy Status Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Read Receipts</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {stats.readReceiptsEnabled ? 'ON' : 'OFF'}
                    </p>
    </div>
                  <Eye className={`w-8 h-8 ${stats.readReceiptsEnabled ? 'text-green-500' : 'text-gray-400'}`} />
    </div>
              </div>

              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Online Status</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {stats.onlineStatusVisible ? 'VISIBLE' : 'HIDDEN'}
                    </p>
    </div>
                  <Users className={`w-8 h-8 ${stats.onlineStatusVisible ? 'text-green-500' : 'text-gray-400'}`} />
    </div>
              </div>

              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Blocked Users</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.blockedUsersCount}</p>
    </div>
                  <Shield className="w-8 h-8 text-red-500" />
    </div>
              </div>
    </div>
            {/* Auto-Delete Status */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center gap-3">
                <Clock className="w-6 h-6 text-blue-600" />
                <div>
                  <h3 className="font-medium text-blue-900">Auto-Delete Messages</h3>
                  <p className="text-sm text-blue-700">
                    {stats.autoDeleteEnabled 
                      ? `Messages automatically deleted after ${stats.autoDeleteDays} days`
                      : 'Auto-delete is disabled'
                    }
                  </p>
    </div>
              </div>
    </div>
            {/* Data Collection Status */}
            <div className={`border rounded-lg p-4 ${
              stats.dataCollectionEnabled 
                ? 'bg-yellow-50 border-yellow-200' 
                : 'bg-green-50 border-green-200'
            }`}>
              <div className="flex items-center gap-3">
                {stats.dataCollectionEnabled ? (
                  <AlertTriangle className="w-6 h-6 text-yellow-600" />
                ) : (
                  <CheckCircle className="w-6 h-6 text-green-600" />
                )}
                <div>
                  <h3 className={`font-medium ${
                    stats.dataCollectionEnabled ? 'text-yellow-900' : 'text-green-900'
                  }`}>
                    Data Collection
                  </h3>
                  <p className={`text-sm ${
                    stats.dataCollectionEnabled ? 'text-yellow-700' : 'text-green-700'
                  }`}>
                    {stats.dataCollectionEnabled 
                      ? 'Data collection is enabled for service improvement'
                      : 'Data collection is disabled'
                    }
                  </p>
    </div>
              </div>
    </div>
            {/* Quick Actions */}
            <div className="border-t pt-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
              <div className="flex flex-wrap gap-3">
                <button
                  onClick={handleExportData} className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                >
                  <Download className="w-4 h-4" />
                  Export My Data
                </button>
                
                <button
                  onClick={handleDeleteAllData} className="flex items-center gap-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
                >
                  <Trash2 className="w-4 h-4" />
                  Delete All Data
                </button>
    </div>
            </div>
    </div>
        )}

        {activeTab === 'activity' && stats && (
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">Recent Privacy Actions</h3>
            
            {stats.recentActions.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <Clock className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <p>No recent privacy actions</p>
    </div>
            ) : (
              <div className="space-y-3">
                {stats.recentActions.map((action, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                      <p className="font-medium text-gray-900">{formatActionType(action.type)}</p>
                      <p className="text-sm text-gray-500">
                        {formatTimestamp(action.timestamp)}
                      </p>
                      {action.metadata && (
                        <p className="text-xs text-gray-400 mt-1">
                          {JSON.stringify(action.metadata)}
                        </p>
                      )}
                    </div>
                    <div className="text-sm text-gray-500">
                      {action.targetId && `Target: ${action.targetId.substring(0, 8)}...`}
                    </div>
    </div>
                ))}
              </div>
            )}
          </div>
        )}

        {activeTab === 'compliance' && isAdmin && complianceReport && (
          <div className="space-y-6">
            <h3 className="text-lg font-medium text-gray-900">Privacy Compliance Report</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="bg-blue-50 rounded-lg p-4">
                <div className="text-center">
                  <p className="text-3xl font-bold text-blue-600">{complianceReport.totalUsers}</p>
                  <p className="text-sm text-blue-700">Total Users</p>
    </div>
              </div>

              <div className="bg-green-50 rounded-lg p-4">
                <div className="text-center">
                  <p className="text-3xl font-bold text-green-600">{complianceReport.usersWithCustomSettings}</p>
                  <p className="text-sm text-green-700">Custom Privacy Settings</p>
    </div>
              </div>

              <div className="bg-yellow-50 rounded-lg p-4">
                <div className="text-center">
                  <p className="text-3xl font-bold text-yellow-600">{complianceReport.blockedUserPairs}</p>
                  <p className="text-sm text-yellow-700">Blocked User Pairs</p>
    </div>
              </div>

              <div className="bg-purple-50 rounded-lg p-4">
                <div className="text-center">
                  <p className="text-3xl font-bold text-purple-600">{complianceReport.dataExportRequests}</p>
                  <p className="text-sm text-purple-700">Data Export Requests</p>
    </div>
              </div>

              <div className="bg-red-50 rounded-lg p-4">
                <div className="text-center">
                  <p className="text-3xl font-bold text-red-600">{complianceReport.dataDeletionRequests}</p>
                  <p className="text-sm text-red-700">Data Deletion Requests</p>
    </div>
              </div>

              <div className="bg-indigo-50 rounded-lg p-4">
                <div className="text-center">
                  <p className="text-3xl font-bold text-indigo-600">{complianceReport.autoDeleteEnabledUsers}</p>
                  <p className="text-sm text-indigo-700">Auto-Delete Enabled</p>
    </div>
              </div>
    </div>
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-2">Compliance Status</h4>
              <div className="flex items-center gap-2">
                <CheckCircle className="w-5 h-5 text-green-500" />
                <span className="text-green-700">All privacy requirements are being met</span>
    </div>
            </div>
    </div>
        )}
      </div>
    </div>
  );
};

export default PrivacyDashboard;