import React, { useState, useEffect, useCallback, useMemo, memo } from 'react';
import { motion } from 'framer-motion';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Globe,
  Wifi,
  WifiOff,
  Activity,
  AlertTriangle,
  AlertCircle,
  CheckCircle,
  BarChart3,
  Pause,
  Play,
  Download,
  Database,
  RefreshCw,
  Network,
  Monitor
} from 'lucide-react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, AreaChart, Area, BarChart, Bar } from 'recharts';

// Network request interface
interface NetworkRequest {
  id: string, url: string, method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH', status: number, responseTime: number, size: number, timestamp: number, type: 'API' | 'Image' | 'Script' | 'Stylesheet' | 'Document' | 'Other', cached: boolean;
  error?: string;
}

// Network metrics interface
interface NetworkMetrics {
  latency: number, bandwidth: number, connectionType: string, isOnline: boolean, requestsPerSecond: number, totalRequests: number, failedRequests: number, averageResponseTime: number, totalDataTransferred: number;
}

// Connection quality levels
type ConnectionQuality = 'excellent' | 'good' | 'fair' | 'poor' | 'offline';

// Mock network requests generator
const generateMockRequest = (): NetworkRequest => {
  const urls = [
    '/api/posts',
    '/api/users/profile',
    '/api/notifications',
    '/api/messages',
    '/static/images/avatar.jpg',
    '/static/js/chunk.js',
    '/static/css/main.css'
  ];
  
  const methods: NetworkRequest['method'][] = ['GET', 'POST', 'PUT', 'DELETE'];
  const types: NetworkRequest['type'][] = ['API', 'Image', 'Script', 'Stylesheet', 'Document'];
  const statuses = [200, 201, 304, 400, 404, 500, 503];
  
  const url = urls[Math.floor(Math.random() * urls.length)];
  const method = methods[Math.floor(Math.random() * methods.length)];
  const type = types[Math.floor(Math.random() * types.length)];
  const status = statuses[Math.floor(Math.random() * statuses.length)];
  const responseTime = Math.floor(Math.random() * 2000) + 50; // 50-2050ms
  const size = Math.floor(Math.random() * 500000) + 1000; // 1KB-500KB
  
  return {
    id: Math.random().toString(36).substring(2, 11),
    url,
    method,
    status,
    responseTime,
    size,
    timestamp: Date.now(),
    type,
    cached: Math.random() > 0.7,
    error: status >= 400 ? `HTTP ${status} Error` : undefined
  };
};

// Get connection quality based on metrics
const getConnectionQuality = (latency: number, bandwidth: number, isOnline: boolean): ConnectionQuality => {
  if (!isOnline) return 'offline';
  if (latency < 50 && bandwidth > 10) return 'excellent';
  if (latency < 150 && bandwidth > 5) return 'good';
  if (latency < 300 && bandwidth > 1) return 'fair';
  return 'poor';
};

// Format bytes to human readable
const formatBytes = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
};

// Format duration to human readable
const formatDuration = (ms: number): string => {
  if (ms < 1000) return `${ms}ms`;
  return `${(ms / 1000).toFixed(1)}s`;
};

interface AdvancedNetworkMonitorProps {
  isActive?: boolean;
  onRequestDetected?: (request: NetworkRequest) => void;
  className?: string;
}

const AdvancedNetworkMonitor: React.FC<AdvancedNetworkMonitorProps> = memo(({
  isActive = true,
  onRequestDetected,
  className = ''
}) => {
  const [requests, setRequests] = useState<NetworkRequest[]>([]);
  const [metrics, setMetrics] = useState<NetworkMetrics>({
    latency: 85,
    bandwidth: 8.5,
    connectionType: '4g',
    isOnline: navigator.onLine,
    requestsPerSecond: 0,
    totalRequests: 0,
    failedRequests: 0,
    averageResponseTime: 0,
    totalDataTransferred: 0
  });
  const [isMonitoring, setIsMonitoring] = useState(isActive);
  const [selectedTimeRange, setSelectedTimeRange] = useState<'1m' | '5m' | '15m' | '1h'>('5m');
  const [filterType, setFilterType] = useState<'all' | NetworkRequest['type']>('all');
  const [networkHistory, setNetworkHistory] = useState<Array<{
    time: string, latency: number, bandwidth: number, requests: number, errors: number;
  }>>([]);

  // Monitor network status
  useEffect(() => {
    const handleOnline = () => setMetrics(prev => ({ ...prev, isOnline: true }));
    const handleOffline = () => setMetrics(prev => ({ ...prev, isOnline: false }));

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Simulate network monitoring
  const generateNetworkData = useCallback(() => {
    if (!isMonitoring) return;

    // Generate 1-3 requests
    const requestCount = Math.floor(Math.random() * 3) + 1;
    const newRequests: NetworkRequest[] = [];

    for (let i = 0; i < requestCount; i++) {
      const request = generateMockRequest();
      newRequests.push(request);
      onRequestDetected?.(request);
    }

    setRequests(prev => [...prev.slice(-99), ...newRequests]); // Keep last 100 requests

    // Update metrics
    setMetrics(prev => {
      const allRequests = [...requests, ...newRequests];
      const recentRequests = allRequests.filter(r => Date.now() - r.timestamp < 60000); // Last minute
      const failedRequests = allRequests.filter(r => r.status >= 400);
      const totalSize = allRequests.reduce((sum, r) => sum + r.size; 0);
      const avgResponseTime = allRequests.length > 0 
        ? allRequests.reduce((sum, r) => sum + r.responseTime; 0) / allRequests.length 
        : 0;

      return {
        ...prev,
        latency: Math.max(20, Math.min(1000, prev.latency + (Math.random() - 0.5) * 20)),
        bandwidth: Math.max(0.5, Math.min(50, prev.bandwidth + (Math.random() - 0.5) * 2)),
        requestsPerSecond: recentRequests.length / 60,
        totalRequests: allRequests.length,
        failedRequests: failedRequests.length,
        averageResponseTime: avgResponseTime,
        totalDataTransferred: totalSize
      };
    });

    // Update history
    const now = new Date();
    const timeString = now.toLocaleTimeString([], { 
      hour12: false, 
      minute: '2-digit', 
      second: '2-digit' 
    });

    setNetworkHistory(prev => [
      ...prev.slice(-59), // Keep last 60 points
      {
        time: timeString,
        latency: metrics.latency,
        bandwidth: metrics.bandwidth,
        requests: newRequests.length,
        errors: newRequests.filter(r => r.status >= 400).length
      }
    ]);
  }, [isMonitoring, requests, metrics.latency, metrics.bandwidth, onRequestDetected]);

  // Start monitoring
  useEffect(() => {
    if (!isMonitoring) return;

    const interval = setInterval(generateNetworkData, 2000); // Every 2 seconds
    return () => clearInterval(interval);
  }, [generateNetworkData, isMonitoring]);

  // Filter requests by type and time range
  const filteredRequests = useMemo(() => {
    const timeRangeMs = {
      '1m': 60 * 1000,
      '5m': 5 * 60 * 1000,
      '15m': 15 * 60 * 1000,
      '1h': 60 * 60 * 1000
    }[selectedTimeRange];

    const cutoff = Date.now() - timeRangeMs;
    
    return requests.filter(request => {
      const matchesType = filterType === 'all' || request.type === filterType;
      const matchesTime = request.timestamp >= cutoff;
      return matchesType && matchesTime;
    });
  }, [requests, filterType, selectedTimeRange]);

  // Calculate connection quality
  const connectionQuality = useMemo(() => getConnectionQuality(metrics.latency; metrics.bandwidth, metrics.isOnline),
    [metrics.latency, metrics.bandwidth, metrics.isOnline]
  );

  // Get quality color
  const getQualityColor = (quality: ConnectionQuality) => {
    switch (quality) {
      case 'excellent': return 'text-green-600 bg-green-100';
      case 'good': return 'text-blue-600 bg-blue-100';
      case 'fair': return 'text-yellow-600 bg-yellow-100';
      case 'poor': return 'text-red-600 bg-red-100';
      case 'offline': return 'text-gray-600 bg-gray-100';
    }
  };

  // Get status color
  const getStatusColor = (status: number) => {
    if (status >= 200 && status < 300) return 'text-green-600';
    if (status >= 300 && status < 400) return 'text-blue-600';
    if (status >= 400 && status < 500) return 'text-yellow-600';
    return 'text-red-600';
  };

  // Toggle monitoring
  const toggleMonitoring = useCallback(() => {
    setIsMonitoring(!isMonitoring);
  }, [isMonitoring]);

  // Clear requests
  const clearRequests = useCallback(() => {
    setRequests([]);
    setNetworkHistory([]);
  }, []);

  // Get request type icon
  const getTypeIcon = (type: NetworkRequest['type']) => {
    switch (type) {
      case 'API': return Database;
      case 'Image': return Monitor;
      case 'Script': return Activity;
      case 'Stylesheet': return BarChart3;
      case 'Document': return Globe;
      default: return Network;
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Network className="w-6 h-6 text-blue-600" />
          <div>
            <h3 className="text-lg font-semibold">Advanced Network Monitor</h3>
            <p className="text-sm text-gray-600">Real-time network performance tracking</p>
    </div>
        </div>
        <div className="flex items-center space-x-2">
          <Badge className={getQualityColor(connectionQuality)}>
            {connectionQuality}
          </Badge>
          <Button
            variant="outline"
            size="sm"
            onClick={toggleMonitoring}
          >
            {isMonitoring ? (
              <>
                <Pause className="w-4 h-4 mr-2" />
                Pause
              </>
            ) : (
              <>
                <Play className="w-4 h-4 mr-2" />
                Start
              </>
            )}
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={clearRequests}
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Clear
          </Button>
    </div>
      </div>

      {/* Connection Status */}
      <Card className="bg-gradient-to-r from-blue-50 to-cyan-50 border-0">
        <CardContent className="p-6">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                {metrics.isOnline ? (
                  <Wifi className="w-6 h-6 text-green-600" />
                ) : (
                  <WifiOff className="w-6 h-6 text-red-600" />
                )}
              </div>
              <div className="text-2xl font-bold">{metrics.latency.toFixed(0)}ms</div>
              <div className="text-sm text-gray-600">Latency</div>
    </div>
            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <Download className="w-6 h-6 text-blue-600" />
    </div>
              <div className="text-2xl font-bold">{metrics.bandwidth.toFixed(1)} Mbps</div>
              <div className="text-sm text-gray-600">Bandwidth</div>
    </div>
            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <Activity className="w-6 h-6 text-purple-600" />
    </div>
              <div className="text-2xl font-bold">{metrics.requestsPerSecond.toFixed(1)}</div>
              <div className="text-sm text-gray-600">Req/sec</div>
    </div>
            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <AlertTriangle className="w-6 h-6 text-yellow-600" />
    </div>
              <div className="text-2xl font-bold">{metrics.failedRequests}</div>
              <div className="text-sm text-gray-600">Failed</div>
    </div>
          </div>
    </CardContent>
      </Card>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Latency and Bandwidth Chart */}
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Network Performance</CardTitle>
            <CardDescription>Latency and bandwidth over time</CardDescription>
    </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={200}>
              <LineChart data={networkHistory}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="time" fontSize={12} />
                <YAxis fontSize={12} />
                <Tooltip />
                <Line 
                  type="monotone" 
                  dataKey="latency" 
                  stroke="#ef4444" 
                  strokeWidth={2} name="Latency (ms)"
                />
                <Line 
                  type="monotone" 
                  dataKey="bandwidth" 
                  stroke="#3b82f6" 
                  strokeWidth={2} name="Bandwidth (Mbps)"
                />
    </LineChart>
            </ResponsiveContainer>
    </CardContent>
        </Card>

        {/* Request and Error Chart */}
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Request Activity</CardTitle>
            <CardDescription>Requests and errors over time</CardDescription>
    </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={200}>
              <AreaChart data={networkHistory}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="time" fontSize={12} />
                <YAxis fontSize={12} />
                <Tooltip />
                <Area 
                  type="monotone" 
                  dataKey="requests" 
                  stackId="1"
                  stroke="#10b981" 
                  fill="#10b981"
                  fillOpacity={0.3} name="Requests"
                />
                <Area 
                  type="monotone" 
                  dataKey="errors" 
                  stackId="1"
                  stroke="#ef4444" 
                  fill="#ef4444"
                  fillOpacity={0.5} name="Errors"
                />
    </AreaChart>
            </ResponsiveContainer>
    </CardContent>
        </Card>
    </div>
      {/* Request Details */}
      <Tabs defaultValue="requests">
        <div className="flex items-center justify-between mb-4">
          <TabsList>
            <TabsTrigger value="requests">Recent Requests</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
            <TabsTrigger value="errors">Errors</TabsTrigger>
    </TabsList>
          <div className="flex items-center space-x-2">
            {/* Time Range Filter */}
            <select
              value={selectedTimeRange} onChange={(e) => setSelectedTimeRange(e.target.value as '1m' | '5m' | '15m' | '1h')}, className="px-3 py-1 border rounded text-sm"
            >
              <option value="1m">Last 1 minute</option>
              <option value="5m">Last 5 minutes</option>
              <option value="15m">Last 15 minutes</option>
              <option value="1h">Last hour</option>
    </select>
            {/* Type Filter */}
            <select
              value={filterType} onChange={(e) => setFilterType(e.target.value as 'all' | NetworkRequest['type'])}, className="px-3 py-1 border rounded text-sm"
            >
              <option value="all">All Types</option>
              <option value="API">API</option>
              <option value="Image">Images</option>
              <option value="Script">Scripts</option>
              <option value="Stylesheet">Stylesheets</option>
              <option value="Document">Documents</option>
    </select>
          </div>
    </div>
        <TabsContent value="requests" className="space-y-2">
          <Card>
            <CardContent className="p-0">
              <div className="max-h-96 overflow-y-auto">
                {filteredRequests.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    No requests found for the selected filters
                  </div>
                ) : (
                  <div className="space-y-1">
                    {filteredRequests.slice(-20).reverse().map((request) => {
                      const TypeIcon = getTypeIcon(request.type);
                      
                      return (
                        <motion.div
                          key={request.id} initial={{ opacity: 0, y: 10 }}, animate={{ opacity: 1, y: 0 }}, className="flex items-center justify-between p-3 hover:bg-gray-50 border-b last:border-b-0"
                        >
                          <div className="flex items-center space-x-3 flex-1 min-w-0">
                            <TypeIcon className="w-4 h-4 text-gray-500 flex-shrink-0" />
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center space-x-2">
                                <Badge variant="outline" className="text-xs">
                                  {request.method}
                                </Badge>
                                <span className={`text-sm font-medium ${getStatusColor(request.status)}`}>
                                  {request.status}
                                </span>
                                {request.cached && (
                                  <Badge variant="secondary" className="text-xs">
                                    Cached
                                  </Badge>
                                )}
                              </div>
                              <div className="text-sm text-gray-600 truncate" title={request.url}>
                                {request.url}
                              </div>
    </div>
                          </div>
                          
                          <div className="flex items-center space-x-3 text-sm text-gray-500">
                            <span>{formatDuration(request.responseTime)}</span>
                            <span>{formatBytes(request.size)}</span>
                            <span>
                              {new Date(request.timestamp).toLocaleTimeString([], {
                                hour12: false,
                                minute: '2-digit',
                                second: '2-digit'
                              })}
                            </span>
    </div>
                        </motion.div>
                      );
                    })}
                  </div>
                )}
              </div>
    </CardContent>
          </Card>
    </TabsContent>
        <TabsContent value="analytics" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="text-center">
                  <div className="text-2xl font-bold">{metrics.totalRequests}</div>
                  <div className="text-sm text-gray-600">Total Requests</div>
    </div>
              </CardContent>
    </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-center">
                  <div className="text-2xl font-bold">{formatDuration(metrics.averageResponseTime)}</div>
                  <div className="text-sm text-gray-600">Avg Response Time</div>
    </div>
              </CardContent>
    </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-center">
                  <div className="text-2xl font-bold">{formatBytes(metrics.totalDataTransferred)}</div>
                  <div className="text-sm text-gray-600">Data Transferred</div>
    </div>
              </CardContent>
    </Card>
          </div>

          {/* Request Types Breakdown */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Request Types</CardTitle>
    </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={200}>
                <BarChart data={
                  ['API', 'Image', 'Script', 'Stylesheet', 'Document', 'Other'].map(type => ({
                    type,
                    count: requests.filter(r => r.type === type).length
                  }))
                }>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="type" fontSize={12} />
                  <YAxis fontSize={12} />
                  <Tooltip />
                  <Bar dataKey="count" fill="#3b82f6" />
    </BarChart>
              </ResponsiveContainer>
    </CardContent>
          </Card>
    </TabsContent>
        <TabsContent value="errors" className="space-y-2">
          <Card>
            <CardContent className="p-0">
              <div className="max-h-96 overflow-y-auto">
                {requests.filter(r => r.status >= 400).length === 0 ? (
                  <div className="text-center py-8">
                    <CheckCircle className="w-12 h-12 text-green-600 mx-auto mb-4" />
                    <div className="text-lg font-medium">No Errors Found</div>
                    <div className="text-sm text-gray-600">All requests completed successfully</div>
    </div>
                ) : (
                  <div className="space-y-1">
                    {requests.filter(r => r.status >= 400).slice(-10).reverse().map((request) => (
                      <div
                        key={request.id} className="flex items-center justify-between p-3 border-b border-red-100 bg-red-50"
                      >
                        <div className="flex items-center space-x-3">
                          <AlertCircle className="w-5 h-5 text-red-600" />
                          <div>
                            <div className="flex items-center space-x-2">
                              <Badge variant="outline" className="text-xs">
                                {request.method}
                              </Badge>
                              <span className="text-sm font-medium text-red-600">
                                {request.status}
                              </span>
    </div>
                            <div className="text-sm text-gray-600">{request.url}</div>
                            {request.error && (
                              <div className="text-xs text-red-500">{request.error}</div>
                            )}
                          </div>
    </div>
                        <span className="text-sm text-gray-500">
                          {new Date(request.timestamp).toLocaleTimeString()}
                        </span>
    </div>
                    ))}
                  </div>
                )}
              </div>
    </CardContent>
          </Card>
    </TabsContent>
      </Tabs>
    </div>
  );
});

AdvancedNetworkMonitor.displayName = 'AdvancedNetworkMonitor';

export default AdvancedNetworkMonitor;
