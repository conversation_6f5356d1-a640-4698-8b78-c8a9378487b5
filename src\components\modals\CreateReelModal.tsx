import React, { useState, useRef, useEffect } from 'react';
import { Video, Music, X, Upload, Play, Pause, RotateCw, Check } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'sonner';
import { motion, AnimatePresence } from 'framer-motion';

interface Reel {
  id: string, video: string, thumbnail: string, caption: string;
  music?: {
    title: string, artist: string;
  };
  hashtags: string[], created_at: string, views: number, likes: number, comments: number, shares: number, isLiked: boolean, isSaved: boolean, user: {
    id: string, name: string, avatar: string, isFollowing: boolean;
  };
}

interface CreateReelModalProps {
  isOpen: boolean, onClose: () => void;
  onReelCreated?: (reel: Reel) => void;
}

const CreateReelModal: React.FC<CreateReelModalProps> = ({ isOpen, onClose, onReelCreated }) => {
  const [step, setStep] = useState<'upload' | 'edit' | 'details'>('upload');
  const [videoFile, setVideoFile] = useState<File | null>(null);
  const [videoUrl, setVideoUrl] = useState<string>('');
  const [caption, setCaption] = useState('');
  const [music, setMusic] = useState({ title: '', artist: '' });
  const [hashtags, setHashtags] = useState('');
  const [isPlaying, setIsPlaying] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  
  const videoRef = useRef<HTMLVideoElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  // Cleanup on unmount
  useEffect(() => {
    // Store ref value in a variable
    const currentVideo = videoRef.current;
    
    return () => {
      // Clean up video when component unmounts
      if (currentVideo) {
        currentVideo.pause();
        currentVideo.src = '';
        currentVideo.load();
      }
    };
  }, []);

  // Cleanup object URL when it changes
  useEffect(() => {
    return () => {
      // Clean up object URL if exists
      if (videoUrl && videoFile) {
        URL.revokeObjectURL(videoUrl);
      }
    };
  }, [videoUrl, videoFile]);

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Validate file
      if (!file.type.startsWith('video/')) {
        toast.error('Please select a video file');
        return;
      }
      
      if (file.size > 100 * 1024 * 1024) { // 100MB limit
        toast.error('Video file too large. Maximum size is 100MB');
        return;
      }

      setVideoFile(file);
      setVideoUrl(URL.createObjectURL(file));
      setStep('edit');
    }
  };

  const handlePlayPause = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
      } else {
        videoRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const handleRestart = () => {
    if (videoRef.current) {
      videoRef.current.currentTime = 0;
      videoRef.current.play();
      setIsPlaying(true);
    }
  };

  const handleSampleVideo = () => {
    // Use a sample video URL
    const sampleUrl = 'https://storage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4';
    setVideoUrl(sampleUrl);
    setStep('edit');
  };

  const handlePublish = () => {
    if (!videoUrl && !videoFile) {
      toast.error('Please upload a video');
      return;
    }

    if (!caption.trim()) {
      toast.error('Please add a caption');
      return;
    }

    setIsUploading(true);

    // Simulate upload
    setTimeout(() => {
      const newReel = {
        id: `reel_${Date.now()}`,
        video: videoUrl,
        thumbnail: videoUrl, // In real app, extract thumbnail from video
        caption: caption.trim(),
        music: music.title ? music : undefined,
        hashtags: hashtags.split(' ').filter(tag => tag.startsWith('#')); created_at: new Date().toISOString(),
        views: 0,
        likes: 0,
        comments: 0,
        shares: 0,
        isLiked: false,
        isSaved: false,
        user: {
          id: 'current_user',
          name: 'You',
          avatar: 'https://picsum.photos/400/400?random=1',
          isFollowing: false
        }
      };

      // Save to local storage
      const existingReels = JSON.parse(localStorage.getItem('user_reels') || '[]');
      const updatedReels = [newReel, ...existingReels].slice(0, 50);
      localStorage.setItem('user_reels', JSON.stringify(updatedReels));

      if (onReelCreated) {
        onReelCreated(newReel);
      }

      toast.success('Reel published successfully!');
      setIsUploading(false);
      handleClose();
    }, 2000);
  };

  const handleClose = () => {
    // Clean up video
    if (videoRef.current) {
      videoRef.current.pause();
      videoRef.current.src = '';
      videoRef.current.load();
    }
    
    // Clean up object URL
    if (videoUrl && videoFile) {
      URL.revokeObjectURL(videoUrl);
    }
    
    // Reset state
    setStep('upload');
    setVideoFile(null);
    setVideoUrl('');
    setCaption('');
    setMusic({ title: '', artist: '' });
    setHashtags('');
    setIsPlaying(false);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && handleClose()}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden p-0">
        <DialogHeader className="p-4 border-b">
          <DialogTitle>Create Reel</DialogTitle>
    </DialogHeader>
        <div className="flex h-[80vh]">
          {/* Video Preview Section */}
          <div className="w-1/2 bg-black flex items-center justify-center relative">
            <AnimatePresence mode="wait">
              {step === 'upload' ? (
                <motion.div
                  initial={{ opacity: 0 }}, animate={{ opacity: 1 }}, exit={{ opacity: 0 }}, className="text-center p-8"
                >
                  <Video className="w-20 h-20 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-white text-lg mb-2">Upload a video</h3>
                  <p className="text-gray-400 text-sm mb-6">
                    Share short videos with the world
                  </p>
                  <div className="space-y-3">
                    <Button
                      onClick={() => fileInputRef.current?.click()} className="w-full"
                      size="lg"
                    >
                      <Upload className="w-5 h-5 mr-2" />
                      Select from device
                    </Button>
                    <Button
                      variant="outline"
                      onClick={handleSampleVideo} className="w-full"
                      size="lg"
                    >
                      Use sample video
                    </Button>
    </div>
                  <input
                    ref={fileInputRef} type="file"
                    accept="video/*"
                    onChange={handleFileSelect} className="hidden"
                  />
                </motion.div>
              ) : (
                <motion.div
                  initial={{ opacity: 0 }}, animate={{ opacity: 1 }}, exit={{ opacity: 0 }}, className="w-full h-full relative"
                >
                  <video
                    ref={videoRef} src={videoUrl}, className="w-full h-full object-contain"
                    loop
                    playsInline
                    onEnded={() => setIsPlaying(false)}
                  />
                  
                  {/* Video Controls */}
                  <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex items-center space-x-3">
                    <Button
                      size="icon"
                      variant="secondary"
                      onClick={handlePlayPause} className="bg-white/20 backdrop-blur"
                    >
                      {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                    </Button>
                    <Button
                      size="icon"
                      variant="secondary"
                      onClick={handleRestart} className="bg-white/20 backdrop-blur"
                    >
                      <RotateCw className="w-4 h-4" />
    </Button>
                  </div>

                  {/* Change Video Button */}
                  <Button
                    size="sm"
                    variant="secondary"
                    onClick={() => setStep('upload')} className="absolute top-4 right-4"
                  >
                    <X className="w-4 h-4 mr-1" />
                    Change video
                  </Button>
                </motion.div>
              )}
            </AnimatePresence>
    </div>
          {/* Details Section */}
          <div className="w-1/2 p-6 overflow-y-auto">
            <AnimatePresence mode="wait">
              {step !== 'upload' && (
                <motion.div
                  initial={{ opacity: 0, x: 20 }}, animate={{ opacity: 1, x: 0 }}, exit={{ opacity: 0, x: 20 }}, className="space-y-6"
                >
                  {/* Caption */}
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      Caption
                    </label>
                    <Textarea
                      value={caption} onChange={(e) => setCaption(e.target.value)}, placeholder="Write a caption..."
                      rows={3} className="resize-none"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      {caption.length}/2200
                    </p>
    </div>
                  {/* Music */}
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      <Music className="w-4 h-4 inline mr-1" />
                      Add Music
                    </label>
                    <div className="space-y-2">
                      <Input
                        value={music.title} onChange={(e) => setMusic({ ...music, title: e.target.value })}, placeholder="Song title"
                      />
                      <Input
                        value={music.artist} onChange={(e) => setMusic({ ...music, artist: e.target.value })}, placeholder="Artist name"
                      />
    </div>
                  </div>

                  {/* Hashtags */}
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      Hashtags
                    </label>
                    <Input
                      value={hashtags} onChange={(e) => setHashtags(e.target.value)}, placeholder="#trending #viral #reels"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Separate hashtags with spaces
                    </p>
    </div>
                  {/* Quick Tips */}
                  <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                    <h4 className="font-medium text-blue-800 dark:text-blue-300 mb-2">
                      Tips for great reels
                    </h4>
                    <ul className="text-sm text-blue-700 dark:text-blue-200 space-y-1">
                      <li>• Keep it short and engaging (15-60 seconds)</li>
                      <li>• Use trending music and hashtags</li>
                      <li>• Film in vertical format</li>
                      <li>• Add captions for accessibility</li>
    </ul>
                  </div>

                  {/* Publish Button */}
                  <div className="flex justify-end space-x-3 pt-4">
                    <Button
                      variant="outline"
                      onClick={handleClose} disabled={isUploading}
                    >
                      Cancel
                    </Button>
                    <Button
                      onClick={handlePublish} disabled={isUploading || !caption.trim()}
                    >
                      {isUploading ? (
                        <>
                          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                          Publishing...
                        </>
                      ) : (
                        <>
                          <Check className="w-4 h-4 mr-2" />
                          Publish Reel
                        </>
                      )}
                    </Button>
    </div>
                </motion.div>
              )}
            </AnimatePresence>
    </div>
        </div>
    </DialogContent>
    </Dialog>
  );
};

export default CreateReelModal;
