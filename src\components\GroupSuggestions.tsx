import React, { memo, useCallback, useMemo } from 'react';
import { Users, Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui';
import { toast } from 'sonner';
import { useNavigate } from 'react-router-dom';
import { getSafeImage } from '@/lib/constants';

interface GroupSuggestion {
  id: string, name: string, image: string, members: number, category: string, isJoined: boolean;
  mutualMembers?: number;
  privacy: 'public' | 'private';
}

// Move initial data outside component to prevent recreation
const INITIAL_SUGGESTIONS: GroupSuggestion[] = [
  {
    id: '1',
    name: 'React Developers',
    image: getSafeImage('POSTS', 0),
    members: 12500,
    category: 'Technology',
    isJoined: false,
    mutualMembers: 3,
    privacy: 'public'
  },
  {
    id: '2',
    name: 'Photography Enthusiasts',
    image: getSafeImage('POSTS', 1),
    members: 8900,
    category: 'Photography',
    isJoined: false,
    mutualMembers: 5,
    privacy: 'public'
  },
  {
    id: '3',
    name: 'Hiking Adventures',
    image: getSafeImage('POSTS', 2),
    members: 5600,
    category: 'Outdoors',
    isJoined: false,
    mutualMembers: 2,
    privacy: 'private'
  }
];

const GroupSuggestions = memo(() => {
  const navigate = useNavigate();
  
  // Memoize suggestions to prevent recreation
  const suggestions = useMemo(() => INITIAL_SUGGESTIONS, []);

  const handleJoinGroup = useCallback((groupId: string) => {
    const group = suggestions.find(g => g.id === groupId);
    if (!group) return;
    
    toast.success(
      `Joined ${group.name}! You'll now see posts from this group.`,
      {
        description: `${group.members.toLocaleString()} members`
      }
    );
  }, [suggestions]);

  const handleViewGroup = useCallback((groupId: string) => {
    navigate(`/groups/${groupId}`);
    const group = suggestions.find(g => g.id === groupId);
    if (group) {
      toast.success(`Viewing ${group.name}`);
    }
  }, [navigate, suggestions]);

  const formatNumber = useCallback((num: number) => {
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  }, []);

  return (
    <Card className="hidden lg:block">
      <CardHeader className="p-2 pb-1">
        <CardTitle className="text-sm font-semibold flex items-center">
          <Users className="w-4 h-4 mr-2" />
          <span>Suggested Groups</span>
    </CardTitle>
      </CardHeader>
      <CardContent className="p-2 pt-0">
        <div className="space-y-2">
          {suggestions.map((group) => (
            <div key={group.id} className="flex items-center justify-between p-2 bg-gray-50 rounded-lg dark:bg-gray-800">
              <div 
                className="flex items-center space-x-2 min-w-0 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg p-1"
                onClick={() => handleViewGroup(group.id)}
              >
                <Avatar className="w-7 h-7 sm:w-8 sm:h-8 flex-shrink-0">
                  <AvatarImage src={group.image} />
                  <AvatarFallback>{group.name.charAt(0)}</AvatarFallback>
    </Avatar>
                <div className="min-w-0">
                  <p className="font-semibold text-xs sm:text-sm text-gray-900 truncate dark:text-white hover:text-blue-600 dark:hover:text-blue-400">
                    {group.name}
                  </p>
                  <p className="text-[10px] sm:text-xs text-gray-500 truncate dark:text-gray-400">
                    {formatNumber(group.members)} members
                    {group.mutualMembers && (
                      <span className="text-blue-600 dark:text-blue-400"> - {group.mutualMembers} mutual</span>
                    )}
                  </p>
                  <p className="text-[10px] text-gray-400 dark:text-gray-500">{group.category} - {group.privacy}</p>
    </div>
              </div>
              <div className="flex space-x-1 flex-shrink-0">
                <Button
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleJoinGroup(group.id);
                  }}, className={`h-7 w-7 sm:h-8 sm:w-8 p-0 ${
                    group.isJoined 
                      ? 'bg-green-600 hover:bg-green-700'
                      : 'bg-blue-600 hover:bg-blue-700'
                  }`}
                >
                  {group.isJoined ? (
                    <Users className="w-4 h-4" />
                  ) : (
                    <Plus className="w-4 h-4" />
                  )}
                </Button>
    </div>
            </div>
          ))}
        </div>
    </CardContent>
    </Card>
  );
});

GroupSuggestions.displayName = 'GroupSuggestions';

export default GroupSuggestions;