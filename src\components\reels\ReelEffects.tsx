import React from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface Effect {
  id: string, name: string, type: 'filter' | 'transition' | 'overlay';
}

interface ReelEffectsProps {
  onEffectSelect?: (effect: Effect) => void;
  selectedEffects?: Effect[];
}

const SAMPLE_EFFECTS: Effect[] = [{ id: '1', name: 'Vintage', type: 'filter' },
  { id: '2', name: 'Fade In', type: 'transition' },
  { id: '3', name: 'Text Overlay', type: 'overlay' },
  { id: '4', name: 'Blur', type: 'filter' },
  { id: '5', name: 'Slide', type: 'transition' }];

export const ReelEffects: React.FC<ReelEffectsProps> = ({
  onEffectSelect,
  selectedEffects = []
}) => {
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Reel Effects</CardTitle>
    </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 gap-2">
          {SAMPLE_EFFECTS.map((effect) => {
            const isSelected = selectedEffects.some(e => e.id === effect.id);
            return (
              <Button
                key={effect.id} variant={isSelected ? "default" : "outline"}, size="sm"
                onClick={() => onEffectSelect?.(effect)} className="flex flex-col items-center gap-1 h-auto py-2"
              >
                <span className="text-xs">{effect.name}</span>
                <Badge variant="secondary" className="text-xs">
                  {effect.type}
                </Badge>
    </Button>
            );
          })}
        </div>
    </CardContent>
    </Card>
  );
};

export default ReelEffects;