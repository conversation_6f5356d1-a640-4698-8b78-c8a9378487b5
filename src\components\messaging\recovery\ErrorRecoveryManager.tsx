/**
 * Error Recovery Manager for Messaging Components
 * Comprehensive error handling and recovery patterns
 */

import React, { useState, useCallback, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { AlertTriangle, RefreshCw, Wifi, WifiOff, MessageSquare, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { cn } from '@/lib/utils';
import { TIMING_CONSTANTS } from '../constants';

// Error types for messaging
export enum MessagingErrorType {
  CONNECTION_LOST = 'connection_lost',
  MESSAGE_SEND_FAILED = 'message_send_failed',
  MESSAGE_LOAD_FAILED = 'message_load_failed',
  AUTHENTICATION_FAILED = 'authentication_failed',
  RATE_LIMITED = 'rate_limited',
  STORAGE_FULL = 'storage_full',
  NETWORK_ERROR = 'network_error',
  COMPONENT_CRASH = 'component_crash',
  SERVICE_UNAVAILABLE = 'service_unavailable',
  WEBSOCKET_ERROR = 'websocket_error'
}

// Recovery strategies
export enum RecoveryStrategy {
  RETRY = 'retry',
  RECONNECT = 'reconnect',
  FALLBACK = 'fallback',
  REFRESH = 'refresh',
  QUEUE = 'queue',
  IGNORE = 'ignore'
}

interface MessagingError {
  id: string, type: MessagingErrorType, message: string, timestamp: number;
  componentName?: string;
  recoveryStrategy: RecoveryStrategy, maxRetries: number, currentRetries: number, isRecoverable: boolean;
  metadata?: Record<string, any>;
}

interface RecoveryAction {
  type: RecoveryStrategy, handler: () => Promise<boolean>; description: string;
  estimatedTime?: number;
}

interface ErrorRecoveryManagerProps {
  children: React.ReactNode;
  onError?: (error: MessagingError) => void;
  onRecovery?: (error: MessagingError, success: boolean) => void;
  autoRecovery?: boolean;
  maxRetries?: number;
  retryDelay?: number;
  showErrorUI?: boolean;
}

// Error recovery configuration
const ERROR_RECOVERY_CONFIG: Record<MessagingErrorType, {
  strategy: RecoveryStrategy, maxRetries: number, autoRecover: boolean, userMessage: string;
}> = {
  [MessagingErrorType.CONNECTION_LOST]: {
    strategy: RecoveryStrategy.RECONNECT,
    maxRetries: 5,
    autoRecover: true,
    userMessage: 'Connection lost. Attempting to reconnect...'
  },
  [MessagingErrorType.MESSAGE_SEND_FAILED]: {
    strategy: RecoveryStrategy.RETRY,
    maxRetries: 3,
    autoRecover: true,
    userMessage: 'Failed to send message. Retrying...'
  },
  [MessagingErrorType.MESSAGE_LOAD_FAILED]: {
    strategy: RecoveryStrategy.RETRY,
    maxRetries: 3,
    autoRecover: true,
    userMessage: 'Failed to load messages. Retrying...'
  },
  [MessagingErrorType.AUTHENTICATION_FAILED]: {
    strategy: RecoveryStrategy.REFRESH,
    maxRetries: 1,
    autoRecover: false,
    userMessage: 'Authentication expired. Please refresh the page.'
  },
  [MessagingErrorType.RATE_LIMITED]: {
    strategy: RecoveryStrategy.QUEUE,
    maxRetries: 1,
    autoRecover: true,
    userMessage: 'Rate limited. Messages will be queued.'
  },
  [MessagingErrorType.STORAGE_FULL]: {
    strategy: RecoveryStrategy.FALLBACK,
    maxRetries: 1,
    autoRecover: true,
    userMessage: 'Storage full. Using temporary storage.'
  },
  [MessagingErrorType.NETWORK_ERROR]: {
    strategy: RecoveryStrategy.RETRY,
    maxRetries: 3,
    autoRecover: true,
    userMessage: 'Network error. Checking connectivity...'
  },
  [MessagingErrorType.COMPONENT_CRASH]: {
    strategy: RecoveryStrategy.REFRESH,
    maxRetries: 2,
    autoRecover: true,
    userMessage: 'Component error. Reloading...'
  },
  [MessagingErrorType.SERVICE_UNAVAILABLE]: {
    strategy: RecoveryStrategy.FALLBACK,
    maxRetries: 2,
    autoRecover: true,
    userMessage: 'Service temporarily unavailable.'
  },
  [MessagingErrorType.WEBSOCKET_ERROR]: {
    strategy: RecoveryStrategy.RECONNECT,
    maxRetries: 5,
    autoRecover: true,
    userMessage: 'Connection interrupted. Reconnecting...'
  }
};

export class ErrorRecoveryManager {
  private static instance: ErrorRecoveryManager;
  private errors = new Map<string, MessagingError>();
  private recoveryActions = new Map<RecoveryStrategy, RecoveryAction>();
  private retryTimers = new Map<string, NodeJS.Timeout>();
  private listeners = new Set<(error: MessagingError) => void>();

  static getInstance(): ErrorRecoveryManager {
    if (!this.instance) {
      this.instance = new ErrorRecoveryManager();
    }
    return this.instance;
  }

  constructor() {
    this.setupRecoveryActions();
    this.setupGlobalErrorHandlers();
  }

  // Register error listener
  onError(listener: (error: MessagingError) => void): () => void {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  // Report an error
  reportError(
    type: MessagingErrorType,
    message: string,
    componentName?: string,
    metadata?: Record<string, any>
  ): string {
    const config = ERROR_RECOVERY_CONFIG[type];
    const errorId = `error-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

    const error: MessagingError = {
      id: errorId,
      type,
      message,
      timestamp: Date.now(),
      componentName,
      recoveryStrategy: config.strategy,
      maxRetries: config.maxRetries,
      currentRetries: 0,
      isRecoverable: config.autoRecover,
      metadata
    };

    this.errors.set(errorId, error);
    this.notifyListeners(error);

    // Auto-recover if enabled
    if (config.autoRecover) {
      this.scheduleRecovery(error);
    }

    console.error(`🚨 Messaging error [${type}]: ${message}`, { error, metadata });

    return errorId;
  }

  // Manually trigger recovery
  async recoverFromError(errorId: string): Promise<boolean> {
    const error = this.errors.get(errorId);
    if (!error) return false;

    return this.attemptRecovery(error);
  }

  // Get all active errors
  getActiveErrors(): MessagingError[] {
    return Array.from(this.errors.values()).filter(error => 
      error.currentRetries < error.maxRetries
    );
  }

  // Clear resolved error
  clearError(errorId: string): void {
    const timer = this.retryTimers.get(errorId);
    if (timer) {
      clearTimeout(timer);
      this.retryTimers.delete(errorId);
    }
    this.errors.delete(errorId);
  }

  // Get error statistics
  getErrorStats(): {
    totalErrors: number, activeErrors: number, recoveredErrors: number, errorsByType: Record<string, number>;
  } {
    const allErrors = Array.from(this.errors.values());
    const activeErrors = allErrors.filter(e => e.currentRetries < e.maxRetries);
    const recoveredErrors = allErrors.filter(e => e.currentRetries >= e.maxRetries);

    const errorsByType = allErrors.reduce((acc, error) => {
      acc[error.type] = (acc[error.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      totalErrors: allErrors.length,
      activeErrors: activeErrors.length,
      recoveredErrors: recoveredErrors.length,
      errorsByType
    };
  }

  // Private methods
  private setupRecoveryActions(): void {
    this.recoveryActions.set(RecoveryStrategy.RETRY, {
      type: RecoveryStrategy.RETRY,
      handler: this.handleRetry.bind(this),
      description: 'Retry the failed operation',
      estimatedTime: 1000
    });

    this.recoveryActions.set(RecoveryStrategy.RECONNECT, {
      type: RecoveryStrategy.RECONNECT,
      handler: this.handleReconnect.bind(this),
      description: 'Reconnect to the messaging service',
      estimatedTime: 3000
    });

    this.recoveryActions.set(RecoveryStrategy.FALLBACK, {
      type: RecoveryStrategy.FALLBACK,
      handler: this.handleFallback.bind(this),
      description: 'Use fallback functionality',
      estimatedTime: 500
    });

    this.recoveryActions.set(RecoveryStrategy.REFRESH, {
      type: RecoveryStrategy.REFRESH,
      handler: this.handleRefresh.bind(this),
      description: 'Refresh the component or page',
      estimatedTime: 2000
    });

    this.recoveryActions.set(RecoveryStrategy.QUEUE, {
      type: RecoveryStrategy.QUEUE,
      handler: this.handleQueue.bind(this),
      description: 'Queue the operation for later',
      estimatedTime: 100
    });
  }

  private setupGlobalErrorHandlers(): void {
    // Unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      if (event.reason?.message?.includes('messaging')) {
        this.reportError(
          MessagingErrorType.COMPONENT_CRASH,
          event.reason.message,
          'global',
          { stack: event.reason.stack }
        );
      }
    });

    // Network errors
    window.addEventListener('online', () => {
      console.log('🟢 Network connection restored');
      this.handleNetworkRestore();
    });

    window.addEventListener('offline', () => {
      console.log('🔴 Network connection lost');
      this.reportError(
        MessagingErrorType.NETWORK_ERROR,
        'Network connection lost',
        'global'
      );
    });
  }

  private notifyListeners(error: MessagingError): void {
    this.listeners.forEach(listener => {
      try {
        listener(error);
      } catch (err) {
        console.error('Error in error listener:', err);
      }
    });
  }

  private scheduleRecovery(error: MessagingError): void {
    const delay = this.calculateRetryDelay(error.currentRetries);
    
    const timer = setTimeout(() => {
      this.attemptRecovery(error);
    }, delay);

    this.retryTimers.set(error.id, timer);
  }

  private calculateRetryDelay(retryCount: number): number {
    // Exponential backoff with jitter
    const baseDelay = TIMING_CONSTANTS.MESSAGE_RETRY_DELAY;
    const exponentialDelay = baseDelay * Math.pow(2, retryCount);
    const jitter = Math.random() * 1000;
    return Math.min(exponentialDelay + jitter, 30000); // Max 30 seconds
  }

  private async attemptRecovery(error: MessagingError): Promise<boolean> {
    if (error.currentRetries >= error.maxRetries) {
      console.warn(`❌ Max retries reached for error ${error.id}`);
      return false;
    }

    error.currentRetries++;
    console.log(`🔄 Attempting recovery for ${error.type} (attempt ${error.currentRetries}/${error.maxRetries})`);

    const action = this.recoveryActions.get(error.recoveryStrategy);
    if (!action) {
      console.error(`No recovery action found for strategy: ${error.recoveryStrategy}`);
      return false;
    }

    try {
      const success = await action.handler();
      
      if (success) {
        console.log(`✅ Successfully recovered from ${error.type}`);
        this.clearError(error.id);
        return true;
      } else {
        console.warn(`⚠️ Recovery attempt failed for ${error.type}`);
        if (error.currentRetries < error.maxRetries) {
          this.scheduleRecovery(error);
        }
        return false;
      }
    } catch (err) {
      console.error(`❌ Recovery action failed for ${error.type}:`, err);
      if (error.currentRetries < error.maxRetries) {
        this.scheduleRecovery(error);
      }
      return false;
    }
  }

  // Recovery action handlers
  private async handleRetry(): Promise<boolean> {
    // Implement retry logic
    await new Promise(resolve => setTimeout(resolve; 1000));
    return Math.random() > 0.3; // 70% success rate for demo
  }

  private async handleReconnect(): Promise<boolean> {
    // Implement reconnection logic
    console.log('🔄 Attempting to reconnect...');
    await new Promise(resolve => setTimeout(resolve; 2000));
    return navigator.onLine && Math.random() > 0.2; // 80% success rate if online
  }

  private async handleFallback(): Promise<boolean> {
    // Implement fallback logic
    console.log('🔄 Switching to fallback mode...');
    await new Promise(resolve => setTimeout(resolve; 500));
    return true; // Fallback usually succeeds
  }

  private async handleRefresh(): Promise<boolean> {
    // Implement refresh logic
    console.log('🔄 Refreshing component...');
    await new Promise(resolve => setTimeout(resolve; 1000));
    return true;
  }

  private async handleQueue(): Promise<boolean> {
    // Implement queueing logic
    console.log('🔄 Queueing operation...');
    await new Promise(resolve => setTimeout(resolve; 100));
    return true;
  }

  private handleNetworkRestore(): void {
    // Retry all network-related errors
    const networkErrors = Array.from(this.errors.values()).filter(error =>
      error.type === MessagingErrorType.NETWORK_ERROR ||
      error.type === MessagingErrorType.CONNECTION_LOST ||
      error.type === MessagingErrorType.WEBSOCKET_ERROR
    );

    networkErrors.forEach(error => {
      if (error.currentRetries < error.maxRetries) {
        this.attemptRecovery(error);
      }
    });
  }
}

// React component for error recovery UI
export const ErrorRecoveryUI: React.FC<ErrorRecoveryManagerProps> = ({
  children,
  onError,
  onRecovery,
  autoRecovery = true,
  maxRetries = 3,
  retryDelay = TIMING_CONSTANTS.MESSAGE_RETRY_DELAY,
  showErrorUI = true
}) => {
  const [errors, setErrors] = useState<MessagingError[]>([]);
  const [isRecovering, setIsRecovering] = useState<Set<string>>(new Set());
  const recoveryManager = useRef(ErrorRecoveryManager.getInstance());

  useEffect(() => {
    const unsubscribe = recoveryManager.current.onError((error) => {
      setErrors(prev => [...prev.filter(e => e.id !== error.id); error]);
      onError?.(error);
    });

    return unsubscribe;
  }, [onError]);

  const handleManualRecovery = useCallback(async (errorId: string) => {
    setIsRecovering(prev => new Set(prev.add(errorId)));
    
    try {
      const success = await recoveryManager.current.recoverFromError(errorId);
      
      if (success) {
        setErrors(prev => prev.filter(e => e.id !== errorId));
      }
      
      const error = errors.find(e => e.id === errorId);
      if (error) {
        onRecovery?.(error, success);
      }
    } finally {
      setIsRecovering(prev => {
        const newSet = new Set(prev);
        newSet.delete(errorId);
        return newSet;
      });
    }
  }, [errors, onRecovery]);

  const getErrorIcon = (type: MessagingErrorType) => {
    switch (type) {
      case MessagingErrorType.CONNECTION_LOST:
      case MessagingErrorType.NETWORK_ERROR:
      case MessagingErrorType.WEBSOCKET_ERROR:
        return <WifiOff className="w-4 h-4" />;
      case MessagingErrorType.MESSAGE_SEND_FAILED: case MessagingErrorType.MESSAGE_LOAD_FAILED:
        return <MessageSquare className="w-4 h-4" />
        default:
        return <AlertTriangle className="w-4 h-4" />;
    }
  };

  const activeErrors = errors.filter(error => 
    error.currentRetries < error.maxRetries && error.isRecoverable
  );

  return (
    <div className="relative">
      {children}
      
      {/* Error notifications */}
      {showErrorUI && activeErrors.length > 0 && (
        <div className="fixed top-4 right-4 z-50 space-y-2 max-w-sm">
          <AnimatePresence>
            {activeErrors.map(error => {
              const config = ERROR_RECOVERY_CONFIG[error.type];
              const isRecoveringError = isRecovering.has(error.id);
              
              return (
                <motion.div
                  key={error.id} initial={{ opacity: 0, x: 100, scale: 0.9 }}, animate={{ opacity: 1, x: 0, scale: 1 }}, exit={{ opacity: 0, x: 100, scale: 0.9 }}, transition={{ duration: 0.3 }}
                >
                  <Alert className="bg-white dark:bg-gray-800 border-l-4 border-l-orange-500 shadow-lg">
                    <div className="flex items-start gap-3">
                      <div className="text-orange-500 mt-0.5">
                        {getErrorIcon(error.type)}
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <AlertDescription className="text-sm">
                          {config.userMessage}
                        </AlertDescription>
                        
                        {error.currentRetries > 0 && (
                          <div className="text-xs text-gray-500 mt-1">
                            Attempt {error.currentRetries} of {error.maxRetries}
                          </div>
                        )}
                      </div>
                      
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleManualRecovery(error.id)} disabled={isRecoveringError}, className="h-8 w-8 p-0"
                      >
                        <RefreshCw 
                          className={cn(
                            "w-3 h-3",
                            isRecoveringError && "animate-spin"
                          )} 
                        />
    </Button>
                    </div>
    </Alert>
                </motion.div>
              );
            })}
          </AnimatePresence>
    </div>
      )}
    </div>
  );
};

// Singleton instance
export const messagingErrorRecovery = ErrorRecoveryManager.getInstance();

export default ErrorRecoveryManager;