/**
 * Message Status Indicator Component
 * Visual indicators for message delivery status with privacy controls and real-time updates
 */

import React, { useMemo, useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Check, CheckCheck, Clock, AlertCircle, Eye, Send, EyeOff, Shield } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import { MessageStatus } from '@/types/messaging';
import { useMessageStatus } from '../../hooks/useMessageSync';

interface MessageStatusIndicatorProps {
  status: MessageStatus;
  timestamp?: Date;
  readBy?: string[];
  showTooltip?: boolean;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  getUserName?: (userId: string) => string;
}

const statusConfig = {
  sending: {
    icon: Clock,
    color: 'text-gray-400',
    bgColor: 'bg-gray-100',
    label: 'Sending...',
    animate: true
  },
  sent: {
    icon: Check,
    color: 'text-gray-500',
    bgColor: 'bg-gray-100',
    label: 'Sent',
    animate: false
  },
  delivered: {
    icon: CheckCheck,
    color: 'text-gray-600',
    bgColor: 'bg-gray-100',
    label: 'Delivered',
    animate: false
  },
  read: {
    icon: CheckCheck,
    color: 'text-blue-500',
    bgColor: 'bg-blue-100',
    label: 'Read',
    animate: false
  }
};

const sizeConfig = {
  sm: { icon: 'w-3 h-3', container: 'w-5 h-5' },
  md: { icon: 'w-4 h-4', container: 'w-6 h-6' },
  lg: { icon: 'w-5 h-5', container: 'w-7 h-7' }
};

export const MessageStatusIndicator: React.FC<MessageStatusIndicatorProps> = ({
  status,
  timestamp,
  readBy = [],
  showTooltip = true,
  size = 'sm',
  className,
  getUserName = (id) => `User ${id.slice(-4)}`
}) => {
  const config = statusConfig[status];
  const sizeClasses = sizeConfig[size];
  const StatusIcon = config.icon;

  // Generate tooltip content
  const tooltipContent = useMemo(() => {
    let content = config.label;
    
    if (timestamp) {
      content += ` at ${timestamp.toLocaleTimeString()}`;
    }
    
    if (status === 'read' && readBy.length > 0) {
      const readers = readBy.map(getUserName);
      if (readers.length === 1) {
        content += ` by ${readers[0]}`;
      } else if (readers.length <= 3) {
        content += ` by ${readers.join(', ')}`;
      } else {
        content += ` by ${readers.slice(0, 2).join(', ')} and ${readers.length - 2} others`;
      }
    }
    
    return content;
  }, [config.label, timestamp, status, readBy, getUserName]);

  const indicator = (
    <motion.div
      initial={{ scale: 0, opacity: 0 }}, animate={{ scale: 1, opacity: 1 }}, transition={{ 
        type: "spring", 
        stiffness: 400, 
        damping: 25,
        delay: status === 'sending' ? 0 : 0.1
      }}, className={cn(
        "flex items-center justify-center rounded-full",
        sizeClasses.container,
        config.bgColor,
        className
      )}
    >
      <StatusIcon 
        className={cn(
          sizeClasses.icon,
          config.color,
          config.animate && "animate-pulse"
        )}
      />
    </motion.div>
  );

  if (!showTooltip) {
    return indicator;
  }

  return (
    <Tooltip>
      <TooltipTrigger asChild>
        {indicator}
      </TooltipTrigger>
      <TooltipContent side="top" className="text-xs">
        {tooltipContent}
      </TooltipContent>
    </Tooltip>
  );
};

// Batch status indicator for multiple messages
interface BatchStatusIndicatorProps {
  messages: Array<{ id: string, status: MessageStatus, timestamp: Date }>;
  className?: string;
}

export const BatchStatusIndicator: React.FC<BatchStatusIndicatorProps> = ({
  messages,
  className
}) => {
  const statusCounts = useMemo(() => {
    return messages.reduce((acc, message) => {
      acc[message.status] = (acc[message.status] || 0) + 1;
      return acc;
    }, {} as Record<MessageStatus, number>);
  }, [messages]);

  const overallStatus = useMemo(() => {
    if (statusCounts.sending > 0) return 'sending';
    if (statusCounts.sent > 0 && !statusCounts.delivered && !statusCounts.read) return 'sent';
    if (statusCounts.delivered > 0 && !statusCounts.read) return 'delivered';
    if (statusCounts.read > 0) return 'read';
    return 'sent';
  }, [statusCounts]);

  return (
    <div className={cn("flex items-center space-x-1", className)}>
      <MessageStatusIndicator
        status={overallStatus} showTooltip={true}, size="sm"
      />
      {messages.length > 1 && (
        <span className="text-xs text-gray-500">
          {messages.length}
        </span>
      )}
    </div>
  );
};

// Read receipts component
interface ReadReceiptsProps {
  readBy: Array<{ userId: string, readAt: Date }>;
  maxVisible?: number;
  getUserName?: (userId: string) => string;
  getUserAvatar?: (userId: string) => string;
  className?: string;
}

export const ReadReceipts: React.FC<ReadReceiptsProps> = ({
  readBy,
  maxVisible = 3,
  getUserName = (id) => `User ${id.slice(-4)}`,
  getUserAvatar,
  className
}) => {
  const visibleReaders = readBy.slice(0, maxVisible);
  const hiddenCount = Math.max(0, readBy.length - maxVisible);

  if (readBy.length === 0) return null;

  return (
    <div className={cn("flex items-center space-x-1", className)}>
      <Eye className="w-3 h-3 text-blue-500" />
      <div className="flex -space-x-1">
        {visibleReaders.map(({ userId, readAt }) => (
          <Tooltip key={userId}>
            <TooltipTrigger asChild>
              <div className="w-4 h-4 rounded-full bg-blue-500 border border-white flex items-center justify-center">
                {getUserAvatar ? (
                  <img 
                    src={getUserAvatar(userId)} alt={getUserName(userId)}, className="w-full h-full rounded-full object-cover"
                  />
                ) : (
                  <span className="text-xs text-white font-bold">
                    {getUserName(userId).charAt(0)}
                  </span>
                )}
              </div>
    </TooltipTrigger>
            <TooltipContent side="top" className="text-xs">
              {getUserName(userId)} read at {readAt.toLocaleTimeString()}
            </TooltipContent>
    </Tooltip>
        ))}
        {hiddenCount > 0 && (
          <div className="w-4 h-4 rounded-full bg-gray-400 border border-white flex items-center justify-center">
            <span className="text-xs text-white font-bold">
              +{hiddenCount}
            </span>
    </div>
        )}
      </div>
    </div>
  );
};

export default MessageStatusIndicator;