/**
 * Messaging Memory Manager
 * Handles memory leaks, cleanup, and resource management for messaging services
 */

import { performanceMonitoringService } from '@/services/messaging/PerformanceMonitoringService';

interface MemoryMetrics {
  heapUsed: number;
  heapTotal: number;
  heapLimit: number;
  timestamp: number;
}

interface WebSocketResource {
  readyState: number;
  close(): void;
}

interface TimerResource {
  [Symbol.toPrimitive](): number;
}

interface ListenerResource {
  element: EventTarget;
  event: string;
  handler: EventListener;
}

interface CacheResource {
  clear(): void;
}

interface ObserverResource {
  disconnect(): void;
}

type ResourceType = WebSocketResource | TimerResource | ListenerResource | CacheResource | ObserverResource | unknown;

interface ResourceTracker {
  id: string;
  type: 'websocket' | 'timer' | 'listener' | 'cache' | 'observer';
  resource: ResourceType;
  createdAt: number;
  lastAccessed: number;
}

export class MessagingMemoryManager {
  private static instance: MessagingMemoryManager | null = null;
  
  // Resource tracking
  private resources = new Map<string, ResourceTracker>();
  private memoryHistory: MemoryMetrics[] = [];
  
  // Monitoring intervals
  private memoryMonitorInterval: NodeJS.Timeout | null = null;
  private cleanupInterval: NodeJS.Timeout | null = null;
  
  // Configuration
  private readonly MAX_MEMORY_HISTORY = 100;
  private readonly MEMORY_CHECK_INTERVAL = 10000; // 10 seconds
  private readonly CLEANUP_INTERVAL = 30000; // 30 seconds
  private readonly RESOURCE_TIMEOUT = 300000; // 5 minutes
  private readonly MEMORY_THRESHOLD = 100 * 1024 * 1024; // 100MB

  private constructor() {
    this.startMonitoring();
    this.setupCleanupHandlers();
  }

  public static getInstance(): MessagingMemoryManager {
    if (!MessagingMemoryManager.instance) {
      MessagingMemoryManager.instance = new MessagingMemoryManager();
    }
    return MessagingMemoryManager.instance;
  }

  /**
   * Start memory monitoring
   */
  private startMonitoring(): void {
    // Monitor memory usage
    this.memoryMonitorInterval = setInterval(() => {
      this.checkMemoryUsage();
    }, this.MEMORY_CHECK_INTERVAL);

    // Periodic cleanup
    this.cleanupInterval = setInterval(() => {
      this.performResourceCleanup();
    }, this.CLEANUP_INTERVAL);
  }

  /**
   * Setup cleanup handlers for page unload events
   */
  private setupCleanupHandlers(): void {
    if (typeof window !== 'undefined') {
      const cleanup = () => {
        this.destroy();
      };

      window.addEventListener('beforeunload', cleanup);
      window.addEventListener('unload', cleanup);
      window.addEventListener('pagehide', cleanup);

      // Cleanup when visibility changes (mobile browsers)
      document.addEventListener('visibilitychange',() => {
        if (document.visibilityState === 'hidden') {
          this.performResourceCleanup();
        }
      });
    }
  }

  /**
   * Check current memory usage
   */
  private checkMemoryUsage(): void {
    try {
      const perfWithMemory = performance as Performance & { memory?: { usedJSHeapSize: number; totalJSHeapSize: number; jsHeapSizeLimit: number } };
      if (typeof perfWithMemory.memory !== 'undefined') {
        const memory = perfWithMemory.memory;
        const metrics: MemoryMetrics = {
  heapUsed: memory.usedJSHeapSize;
  heapTotal: memory.totalJSHeapSize;
  heapLimit: memory.jsHeapSizeLimit;
          timestamp: Date.now()
        };

        this.memoryHistory.push(metrics);
        
        // Keep only recent history
        if (this.memoryHistory.length > this.MAX_MEMORY_HISTORY) {
          this.memoryHistory = this.memoryHistory.slice(-this.MAX_MEMORY_HISTORY);
        }

        // Record performance metric
        performanceMonitoringService.recordMetric({
  name: 'messaging-memory-usage';
          value: metrics.heapUsed / (1024 * 1024), // Convert to MB
  timestamp: new Date();
  category: 'memory';
  metadata: { heapTotal: metrics.heapTotal / (1024 * 1024);
            heapLimit: metrics.heapLimit / (1024 * 1024) }
            resourceCount: this.resources.size
          }
        });

        // Check for memory threshold breaches
        if (metrics.heapUsed > this.MEMORY_THRESHOLD) {
          console.warn('🚨 Memory threshold exceeded:', {
  used: `${(metrics.heapUsed / 1024 / 1024).toFixed(2)}MB`;
            threshold: `${(this.MEMORY_THRESHOLD / 1024 / 1024).toFixed(2)}MB`
          });
          
          // Trigger aggressive cleanup
          this.performAggressiveCleanup();
        }
      }
    } catch (error) {
      console.error('Failed to check memory usage:', error);
    }
  }

  /**
   * Track a resource for cleanup
   */
  public trackResource(
  id: string;
  type: ResourceTracker['type'];
  resource: unknown
  ): void {
    const tracker: ResourceTracker={id;
      type,
      resource}, createdAt: Date.now();
      lastAccessed: Date.now()
    };

    this.resources.set(id, tracker);
    
    console.log(`📝 Tracking ${type} resource:`, id);
  }

  /**
   * Update last accessed time for a resource
   */
  public touchResource(id: string): void {
    const resource = this.resources.get(id);
    if (resource) {
      resource.lastAccessed = Date.now();
    }
  }

  /**
   * Manually cleanup a specific resource
   */
  public cleanupResource(id: string): boolean {
    const resource = this.resources.get(id);
    if (!resource) return false;

    try {
      this.cleanupResourceByType(resource);
      this.resources.delete(id);
      console.log(`✅ Cleaned up ${resource.type} resource:`, id);
      return true;
    } catch (error) {
      console.error(`❌ Failed to cleanup resource ${id}:`, error);
      return false;
    }
  }

  /**
   * Type guards for resource types
   */
  private isWebSocketResource(resource: ResourceType): resource is WebSocketResource {
    return resource !== null && typeof resource === 'object' && 'readyState' in resource && 'close' in resource;
  }

  private isTimerResource(resource: ResourceType): resource is TimerResource {
    return typeof resource === 'number' || (resource !== null && typeof resource === 'object' && Symbol.toPrimitive in resource);
  }

  private isListenerResource(resource: ResourceType): resource is ListenerResource {
    return resource !== null && typeof resource === 'object' && 'element' in resource && 'event' in resource && 'handler' in resource;
  }

  private isCacheResource(resource: ResourceType): resource is CacheResource {
    return resource !== null && typeof resource === 'object' && 'clear' in resource;
  }

  private isObserverResource(resource: ResourceType): resource is ObserverResource {
    return resource !== null && typeof resource === 'object' && 'disconnect' in resource;
  }

  /**
   * Cleanup resource based on its type
   */
  private cleanupResourceByType(resource: ResourceTracker): void {
    switch (resource.type) {
      case 'websocket':
        if (this.isWebSocketResource(resource.resource) && resource.resource.readyState === WebSocket.OPEN) {
          resource.resource.close();
        }
        break;

      case 'timer':
        if (this.isTimerResource(resource.resource)) {
          const timerId = typeof resource.resource === 'number' ? resource.resource : Number(resource.resource);
          clearInterval(timerId);
          clearTimeout(timerId);
        }
        break;

      case 'listener':
        if (this.isListenerResource(resource.resource)) {
          resource.resource.element.removeEventListener(
            resource.resource.event,
            resource.resource.handler
          );
        }
        break;

      case 'cache':
        if (this.isCacheResource(resource.resource)) {
          resource.resource.clear();
        }
        break;

      case 'observer':
        if (this.isObserverResource(resource.resource)) {
          resource.resource.disconnect();
        }
        break;

      default:
        // Try generic cleanup methods
        if (resource.resource) {
          if (typeof resource.resource.destroy === 'function') {
            resource.resource.destroy();
          } else if (typeof resource.resource.close === 'function') {
            resource.resource.close();
          } else if (typeof resource.resource.disconnect === 'function') {
            resource.resource.disconnect();
          }
        }
    }
  }

  /**
   * Perform regular resource cleanup
   */
  private performResourceCleanup(): void {
    const now = Date.now();
    const expiredResources: string[] = [];

    // Find expired resources
    for (const [id, resource] of this.resources.entries()) {
      const age = now - resource.lastAccessed;
      
      if (age > this.RESOURCE_TIMEOUT) {
        expiredResources.push(id);
      }
    }

    // Cleanup expired resources
    let cleanedCount = 0;
    for (const id of expiredResources) {
      if (this.cleanupResource(id)) {
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      console.log(`🧹 Cleaned up ${cleanedCount} expired resources`);
      
      // Record cleanup metric
      performanceMonitoringService.recordMetric({
  name: 'resource-cleanup';
  value: cleanedCount;
  timestamp: new Date();
  category: 'memory';
        metadata: { totalResources: this.resources.size }
          cleanupReason: 'timeout'
        }
      });
    }
  }

  /**
   * Perform aggressive cleanup when memory threshold is exceeded
   */
  private performAggressiveCleanup(): void {
    console.log('🚨 Performing aggressive memory cleanup...');
    
    const cleanupResults = {
  resourcesCleaned: 0;
  cachesCleared: 0;
      errorsOccurred: 0
    };

    try {
      // Clean up all unused resources immediately
      const now = Date.now();
      const resourcesToCleanup: string[] = [];

      for (const [id, resource] of this.resources.entries()) {
        // More aggressive timeout for cleanup
        const age = now - resource.lastAccessed;
        if (age > 60000) { // 1 minute instead of 5
          resourcesToCleanup.push(id);
        }
      }

      for (const id of resourcesToCleanup) {
        if (this.cleanupResource(id)) {
          cleanupResults.resourcesCleaned++;
        } else {
          cleanupResults.errorsOccurred++;
        }
      }

      // Clear browser caches if possible
      this.clearBrowserCaches();
      cleanupResults.cachesCleared++;

      // Force garbage collection if available (development only)
      const windowWithGc = window as Window & { gc?: () => void };
      if (typeof windowWithGc.gc === 'function') {
        windowWithGc.gc();
        console.log('🗑️ Forced garbage collection');
      }

      console.log('✅ Aggressive cleanup completed:', cleanupResults);

      // Record aggressive cleanup metric
      performanceMonitoringService.recordMetric({
  name: 'aggressive-cleanup';
  value: cleanupResults.resourcesCleaned;
  timestamp: new Date();
  category: 'memory';
        metadata: cleanupResults
      });

    } catch (error) {
      console.error('❌ Aggressive cleanup failed:', error);
      cleanupResults.errorsOccurred++;
    }
  }

  /**
   * Clear browser caches to free memory
   */
  private clearBrowserCaches(): void {
    try {
      // Clear URL object cache
      const urlsToRevoke: string[] = [];
      
      // This is a simple way to clear some memory, though browsers limit what we can do
      if (typeof window !== 'undefined') {
        // Clear any data URLs or blob URLs that might be hanging around
        // Note: This is a simplified approach - in a real app you'd track these more carefully
        console.log('🧹 Clearing browser caches...');
        
        // Clear localStorage items that might be large
        const largeStorageKeys = ['message_cache',
          'conversation_cache', 
          'attachment_cache']
          'media_cache'
        ];
        
        largeStorageKeys.forEach(key => {
          if (localStorage.getItem(key)) {
            const size = localStorage.getItem(key)?.length || 0;
            if (size > 10000) { // Only clear large items
              localStorage.removeItem(key);
              console.log(`🧹 Cleared large localStorage item: ${key} (${size} chars)`);
            }
          }
        });
      }

    } catch (error) {
      console.error('Failed to clear browser caches:', error);
    }
  }

  /**
   * Get memory usage statistics
   */
  public getMemoryStats(): {
    current: MemoryMetrics | null;
    history: MemoryMetrics[];
    resources: { type: string; count: number }[];
    recommendations: string[];
  } {
    const current = this.memoryHistory[this.memoryHistory.length - 1] || null;
    
    // Count resources by type
    const resourceCounts = new Map<string, number>();
    for (const resource of this.resources.values()) {
      resourceCounts.set(resource.type, (resourceCounts.get(resource.type) || 0) + 1);
    }
    
    const resources = Array.from(resourceCounts.entries()).map(([type,count]) => ({
      type,
      count
    }));

    // Generate recommendations
    const recommendations: string[] = [];
    
    if (current && current.heapUsed > this.MEMORY_THRESHOLD * 0.8) {
      recommendations.push('Memory usage is high - consider closing unused conversations');
    }
    
    if (this.resources.size > 50) {
      recommendations.push('Many resources are being tracked - some cleanup may be beneficial');
    }
    
    const recentUsage = this.memoryHistory.slice(-5);
    if (recentUsage.length >= 5) {
      const trend = recentUsage[recentUsage.length - 1].heapUsed - recentUsage[0].heapUsed;
      if (trend > 10 * 1024 * 1024) { // 10MB increase
        recommendations.push('Memory usage is trending upward - monitor for memory leaks');
      }
    }

    return {
      current,
  history: [...this.memoryHistory];
      resources,
      recommendations
    };
  }

  /**
   * Create a helper for tracking WebSocket connections
   */
  public trackWebSocket(id: string, ws: WebSocket): void {
    this.trackResource(id, 'websocket', ws);
    
    // Automatically cleanup when WebSocket closes
    ws.addEventListener('close',() => {
      this.cleanupResource(id);
    });
  }

  /**
   * Create a helper for tracking timers
   */
  public trackTimer(id: string, timer: NodeJS.Timeout): void {
    this.trackResource(id, 'timer', timer);
  }

  /**
   * Create a helper for tracking event listeners
   */
  public trackEventListener(
  id: string;
  element: EventTarget;
  event: string;
    handler: EventListener
  ): void {
    this.trackResource(id, 'listener', { element, event, handler });
  }

  /**
   * Create a helper for tracking caches
   */
  public trackCache(id: string, cache: Map<unknown, unknown> | Set<unknown>): void {
    this.trackResource(id, 'cache', cache);
  }

  /**
   * Destroy the memory manager and cleanup all resources
   */
  public destroy(): void {
    console.log('🔥 Destroying MessagingMemoryManager...');
    
    // Stop monitoring
    if (this.memoryMonitorInterval) {
      clearInterval(this.memoryMonitorInterval);
      this.memoryMonitorInterval = null;
    }
    
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }

    // Cleanup all tracked resources
    const resourceIds = Array.from(this.resources.keys());
    let cleanedCount = 0;
    
    for (const id of resourceIds) {
      if (this.cleanupResource(id)) {
        cleanedCount++;
      }
    }

    console.log(`✅ MessagingMemoryManager destroyed, cleaned ${cleanedCount} resources`);

    // Clear references
    this.resources.clear();
    this.memoryHistory = [];
    
    MessagingMemoryManager.instance = null;
  }

  /**
   * Get resource count by type
   */
  public getResourceStats(): { [type: string]: number } {
    const stats: { [type: string]: number } = {};
    
    for (const resource of this.resources.values()) {
      stats[resource.type] = (stats[resource.type] || 0) + 1;
    }
    
    return stats;
  }

  /**
   * Force cleanup of all resources of a specific type
   */
  public cleanupResourcesByType(type: ResourceTracker['type']): number {
    const toCleanup: string[] = [];
    
    for (const [id, resource] of this.resources.entries()) {
      if (resource.type === type) {
        toCleanup.push(id);
      }
    }
    
    let cleanedCount = 0;
    for (const id of toCleanup) {
      if (this.cleanupResource(id)) {
        cleanedCount++;
      }
    }
    
    return cleanedCount;
  }
}

// Export singleton instance and helper functions
export const messagingMemoryManager = MessagingMemoryManager.getInstance();

export const trackWebSocket = (id: string, ws: WebSocket) => 
  messagingMemoryManager.trackWebSocket(id, ws);

export const trackTimer = (id: string, timer: NodeJS.Timeout) => 
  messagingMemoryManager.trackTimer(id, timer);

export const trackEventListener = (id: string, element: EventTarget, event: string, handler: EventListener) => 
  messagingMemoryManager.trackEventListener(id, element, event, handler);

export const trackCache = (id: string, cache: Map<unknown,unknown> | Set<unknown>) =>
  messagingMemoryManager.trackCache(id, cache);

export const getMemoryStats = () => messagingMemoryManager.getMemoryStats();
export const touchResource = (id: string) => messagingMemoryManager.touchResource(id);
export const cleanupResource = (id: string) => messagingMemoryManager.cleanupResource(id);

export default MessagingMemoryManager;