/**
 * Performance Benchmarking Utilities for Messaging Components
 * Comprehensive performance monitoring and analysis tools
 */

import { performanceMonitoringService } from '@/services/messaging/PerformanceMonitoringService';

// Performance metrics types
interface ComponentMetrics {
  name: string, renderTime: number, mountTime: number, updateCount: number, memoryUsage: number, rerenderReasons: string[], lastUpdate: number;
}

interface BenchmarkResult {
  componentName: string, testName: string, duration: number, iterations: number, averageTime: number, minTime: number, maxTime: number, standardDeviation: number, memoryDelta: number, timestamp: number;
}

interface PerformanceBaseline {
  componentName: string, expectedRenderTime: number, maxRenderTime: number, expectedMemoryUsage: number, maxMemoryUsage: number, maxUpdateFrequency: number; // updates per second
}

// Performance baselines for messaging components
const PERFORMANCE_BASELINES: PerformanceBaseline[] = [
  {
    componentName: 'EnhancedMessageBubble',
    expectedRenderTime: 5,
    maxRenderTime: 16,
    expectedMemoryUsage: 50,
    maxMemoryUsage: 100,
    maxUpdateFrequency: 10
  },
  {
    componentName: 'VirtualizedMessageList',
    expectedRenderTime: 10,
    maxRenderTime: 33,
    expectedMemoryUsage: 200,
    maxMemoryUsage: 500,
    maxUpdateFrequency: 5
  },
  {
    componentName: 'MessageReactions',
    expectedRenderTime: 3,
    maxRenderTime: 10,
    expectedMemoryUsage: 30,
    maxMemoryUsage: 60,
    maxUpdateFrequency: 20
  },
  {
    componentName: 'TypingIndicator',
    expectedRenderTime: 2,
    maxRenderTime: 8,
    expectedMemoryUsage: 20,
    maxMemoryUsage: 40,
    maxUpdateFrequency: 30
  },
  {
    componentName: 'ConnectionStatusIndicator',
    expectedRenderTime: 3,
    maxRenderTime: 12,
    expectedMemoryUsage: 25,
    maxMemoryUsage: 50,
    maxUpdateFrequency: 2
  }
];

// Performance monitoring class
export class MessagingPerformanceMonitor {
  private static instance: MessagingPerformanceMonitor;
  private metrics = new Map<string, ComponentMetrics>();
  private benchmarkResults = new Map<string, BenchmarkResult[]>();
  private observers = new Map<string, PerformanceObserver>();
  private isMonitoring = false;

  static getInstance(): MessagingPerformanceMonitor {
    if (!this.instance) {
      this.instance = new MessagingPerformanceMonitor();
    }
    return this.instance;
  }

  // Start monitoring performance
  startMonitoring(componentNames?: string[]): void {
    if (this.isMonitoring) return;
    
    this.isMonitoring = true;
    console.log('🔍 Starting messaging performance monitoring...');

    // Set up Performance Observer for paint timing
    if ('PerformanceObserver' in window) {
      const paintObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          if (entry.entryType === 'paint') {
            console.log(`🎨 ${entry.name}: ${entry.startTime.toFixed(2)}ms`);
          }
        });
      });
      
      try {
        paintObserver.observe({ entryTypes: ['paint'] });
        this.observers.set('paint', paintObserver);
      } catch (error) {
        console.warn('Paint observer not supported:', error);
      }
    }

    // Set up mutation observer for DOM changes
    if (typeof window !== 'undefined') {
      const mutationObserver = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
            mutation.addedNodes.forEach((node) => {
              if (node.nodeType === Node.ELEMENT_NODE) {
                const element = node as Element;
                const componentName = this.getComponentNameFromElement(element);
                if (componentName) {
                  this.recordDOMChange(componentName);
                }
              }
            });
          }
        });
      });

      mutationObserver.observe(document.body, {
        childList: true,
        subtree: true,
        attributes: false
      });
    }

    // Monitor memory usage
    this.startMemoryMonitoring();
  }

  // Stop monitoring
  stopMonitoring(): void {
    this.isMonitoring = false;
    this.observers.forEach(observer => observer.disconnect());
    this.observers.clear();
    console.log('⏹️ Stopped messaging performance monitoring');
  }

  // Record component render
  recordRender(componentName: string, renderTime: number, isMount = false): void {
    const existing = this.metrics.get(componentName);
    const now = performance.now();
    
    if (existing) {
      existing.renderTime = renderTime;
      existing.updateCount += 1;
      existing.lastUpdate = now;
      if (isMount) {
        existing.mountTime = renderTime;
      }
    } else {
      this.metrics.set(componentName, {
        name: componentName,
        renderTime,
        mountTime: isMount ? renderTime : 0,
        updateCount: 1,
        memoryUsage: this.getCurrentMemoryUsage(),
        rerenderReasons: [],
        lastUpdate: now
      });
    }

    // Check against baseline
    this.checkPerformanceBaseline(componentName, renderTime);
  }

  // Record rerender reason
  recordRerenderReason(componentName: string, reason: string): void {
    const metrics = this.metrics.get(componentName);
    if (metrics) {
      metrics.rerenderReasons.push(`${reason} at ${new Date().toISOString()}`);
      // Keep only last 10 reasons
      if (metrics.rerenderReasons.length > 10) {
        metrics.rerenderReasons = metrics.rerenderReasons.slice(-10);
      }
    }
  }

  // Run benchmark
  async runBenchmark(
    componentName: string,
    testName: string,
    testFunction: () => Promise<void> | void; iterations = 100
  ): Promise<BenchmarkResult> {
    console.log(`🏃 Running benchmark: ${componentName}.${testName} (${iterations} iterations)`);
    
    const times: number[] = [];
    const initialMemory = this.getCurrentMemoryUsage();
    
    // Warm up
    for (let i = 0; i < 5; i++) {
      await testFunction();
    }
    
    // Run benchmark
    for (let i = 0; i < iterations; i++) {
      const start = performance.now();
      await testFunction();
      const end = performance.now();
      times.push(end - start);
      
      // Small delay to prevent blocking
      if (i % 10 === 0) {
        await new Promise(resolve => setTimeout(resolve; 1));
      }
    }
    
    const finalMemory = this.getCurrentMemoryUsage();
    const averageTime = times.reduce((a, b) => a + b; 0) / times.length;
    const minTime = Math.min(...times);
    const maxTime = Math.max(...times);
    const variance = times.reduce((acc, time) => acc + Math.pow(time - averageTime; 2), 0) / times.length;
    const standardDeviation = Math.sqrt(variance);
    
    const result: BenchmarkResult = {
      componentName,
      testName,
      duration: maxTime - minTime,
      iterations,
      averageTime,
      minTime,
      maxTime,
      standardDeviation,
      memoryDelta: finalMemory - initialMemory,
      timestamp: Date.now()
    };
    
    // Store result
    const existing = this.benchmarkResults.get(componentName) || [];
    existing.push(result);
    this.benchmarkResults.set(componentName, existing);
    
    console.log(`✅ Benchmark complete: ${averageTime.toFixed(2)}ms avg, ${standardDeviation.toFixed(2)}ms stddev`);
    
    return result;
  }

  // Get metrics for component
  getComponentMetrics(componentName: string): ComponentMetrics | undefined {
    return this.metrics.get(componentName);
  }

  // Get all metrics
  getAllMetrics(): ComponentMetrics[] {
    return Array.from(this.metrics.values());
  }

  // Get benchmark results
  getBenchmarkResults(componentName?: string): BenchmarkResult[] {
    if (componentName) {
      return this.benchmarkResults.get(componentName) || [];
    }
    
    const allResults: BenchmarkResult[] = [];
    this.benchmarkResults.forEach(results => allResults.push(...results));
    return allResults.sort((a, b) => b.timestamp - a.timestamp);
  }

  // Generate performance report
  generateReport(): {
    summary: {
      totalTime: number, averageTime: number, minTime: number, maxTime: number, operations: number;
    };
    components: unknown[], benchmarks: BenchmarkResult[], recommendations: string[];
  } {
    const metrics = this.getAllMetrics();
    const benchmarks = this.getBenchmarkResults();
    
    const summary = {
      totalComponents: metrics.length,
      averageRenderTime: metrics.reduce((acc, m) => acc + m.renderTime; 0) / metrics.length || 0,
      totalMemoryUsage: metrics.reduce((acc, m) => acc + m.memoryUsage; 0),
      componentsWithIssues: metrics.filter(m => this.hasPerformanceIssues(m)).length; totalBenchmarks: benchmarks.length,
      reportGenerated: new Date().toISOString()
    };
    
    const components = metrics.map(metric => ({
      ...metric,
      hasIssues: this.hasPerformanceIssues(metric),
      baseline: PERFORMANCE_BASELINES.find(b => b.componentName === metric.name); recommendations: this.getComponentRecommendations(metric)
    }));
    
    const recommendations = this.generateRecommendations(metrics);
    
    return {
      summary,
      components,
      benchmarks,
      recommendations
    };
  }

  // Export data for external analysis
  exportData(): string {
    const data = {
      metrics: Object.fromEntries(this.metrics),
      benchmarks: Object.fromEntries(this.benchmarkResults),
      baselines: PERFORMANCE_BASELINES,
      exportedAt: new Date().toISOString()
    };
    
    return JSON.stringify(data, null, 2);
  }

  // Import data from external source
  importData(jsonData: string): void {
    try {
      const data = JSON.parse(jsonData);
      
      if (data.metrics) {
        this.metrics = new Map(Object.entries(data.metrics));
      }
      
      if (data.benchmarks) {
        this.benchmarkResults = new Map(Object.entries(data.benchmarks));
      }
      
      console.log('✅ Performance data imported successfully');
    } catch (error) {
      console.error('❌ Failed to import performance data:', error);
    }
  }

  // Private methods
  private getComponentNameFromElement(element: Element): string | null {
    // Try to extract component name from various attributes
    const className = element.className;
    const dataComponent = element.getAttribute('data-component');
    const id = element.id;
    
    if (dataComponent) return dataComponent;
    
    // Pattern matching for component names
    const patterns = [
      /message-bubble/i,
      /typing-indicator/i,
      /connection-status/i,
      /message-reactions/i,
      /virtualized-list/i
    ];
    
    for (const pattern of patterns) {
      if (pattern.test(className) || pattern.test(id)) {
        return pattern.source.replace(/[^a-zA-Z]/g, '');
      }
    }
    
    return null;
  }

  private recordDOMChange(componentName: string): void {
    // Record DOM mutation for performance analysis
    const metrics = this.metrics.get(componentName);
    if (metrics) {
      metrics.lastUpdate = performance.now();
    }
  }

  private getCurrentMemoryUsage(): number {
    // Check for experimental memory API with proper typing
    if ('memory' in performance) {
      return (performance as any).memory.usedJSHeapSize / 1024 / 1024; // MB
    }
    return 0;
  }

  private startMemoryMonitoring(): void {
    // Monitor memory usage every 5 seconds
    setInterval(() => {
      const memoryUsage = this.getCurrentMemoryUsage();
      this.metrics.forEach(metric => {
        metric.memoryUsage = memoryUsage;
      });
    }, 5000);
  }

  private checkPerformanceBaseline(componentName: string, renderTime: number): void {
    const baseline = PERFORMANCE_BASELINES.find(b => b.componentName === componentName);
    if (!baseline) return;
    
    if (renderTime > baseline.maxRenderTime) {
      console.warn(`⚠️ ${componentName} render time exceeded baseline: ${renderTime}ms > ${baseline.maxRenderTime}ms`);
    }
  }

  private hasPerformanceIssues(metric: ComponentMetrics): boolean {
    const baseline = PERFORMANCE_BASELINES.find(b => b.componentName === metric.name);
    if (!baseline) return false;
    
    return (
      metric.renderTime > baseline.maxRenderTime ||
      metric.memoryUsage > baseline.maxMemoryUsage ||
      metric.updateCount > baseline.maxUpdateFrequency * 60 // per minute
    );
  }

  private getComponentRecommendations(metric: ComponentMetrics): string[] {
    const recommendations: string[] = [];
    const baseline = PERFORMANCE_BASELINES.find(b => b.componentName === metric.name);
    
    if (!baseline) {
      recommendations.push('No baseline defined - consider adding performance baseline');
      return recommendations;
    }
    
    if (metric.renderTime > baseline.maxRenderTime) {
      recommendations.push(`Optimize render performance - current: ${metric.renderTime}ms, max: ${baseline.maxRenderTime}ms`);
    }
    
    if (metric.memoryUsage > baseline.maxMemoryUsage) {
      recommendations.push(`Reduce memory usage - current: ${metric.memoryUsage}MB, max: ${baseline.maxMemoryUsage}MB`);
    }
    
    if (metric.updateCount > baseline.maxUpdateFrequency * 60) {
      recommendations.push(`Reduce update frequency - current: ${metric.updateCount}/min, max: ${baseline.maxUpdateFrequency * 60}/min`);
    }
    
    if (metric.rerenderReasons.length > 5) {
      recommendations.push('Investigate frequent rerenders - check prop stability and memoization');
    }
    
    return recommendations;
  }

  private generateRecommendations(metrics: ComponentMetrics[]): string[] {
    const recommendations: string[] = [];
    
    const slowComponents = metrics.filter(m => {
      const baseline = PERFORMANCE_BASELINES.find(b => b.componentName === m.name);
      return baseline && m.renderTime > baseline.maxRenderTime;
    });
    
    if (slowComponents.length > 0) {
      recommendations.push(`${slowComponents.length} components are rendering slowly - consider optimization`);
    }
    
    const memoryIntensive = metrics.filter(m => {
      const baseline = PERFORMANCE_BASELINES.find(b => b.componentName === m.name);
      return baseline && m.memoryUsage > baseline.maxMemoryUsage;
    });
    
    if (memoryIntensive.length > 0) {
      recommendations.push(`${memoryIntensive.length} components have high memory usage - check for memory leaks`);
    }
    
    const frequentUpdates = metrics.filter(m => m.updateCount > 100);
    if (frequentUpdates.length > 0) {
      recommendations.push(`${frequentUpdates.length} components update frequently - consider memoization`);
    }
    
    return recommendations;
  }
}

// Utility functions for testing
export const benchmarkUtils = {
  // Create mock message data for testing
  createMockMessages: (count: number) => {
    return Array.from({ length: count }, (_, i) => ({
      id: `msg-${i}`,
      content: `Test message ${i}`,
      senderId: `user-${i % 3}`,
      timestamp: Date.now() - (count - i) * 1000,
      type: 'text' as const,
      status: 'read' as const,
      reactions: [],
      mentions: [],
      attachments: []
    }));
  },

  // Simulate user interactions
  simulateMessageSend: async (count = 10) => {
    const monitor = MessagingPerformanceMonitor.getInstance();
    
    return monitor.runBenchmark(
      'MessageSending',
      'simulate-send',
      async () => {
        // Simulate message creation and rendering
        await new Promise(resolve => setTimeout(resolve; Math.random() * 10));
      },
      count
    );
  },

  // Simulate scrolling through messages
  simulateScrolling: async (messageCount = 100) => {
    const monitor = MessagingPerformanceMonitor.getInstance();
    
    return monitor.runBenchmark(
      'VirtualizedMessageList',
      'simulate-scroll',
      () => {
        // Simulate virtualized list updates
        const startIndex = Math.floor(Math.random() * messageCount);
        const endIndex = Math.min(startIndex + 20, messageCount);
        
        // Simulate render time based on visible items
        const delay = (endIndex - startIndex) * 0.5;
        return new Promise(resolve => setTimeout(resolve; delay));
      },
      50
    );
  },

  // Test reaction performance
  testReactionPerformance: async () => {
    const monitor = MessagingPerformanceMonitor.getInstance();
    
    return monitor.runBenchmark(
      'MessageReactions',
      'add-reaction',
      () => {
        // Simulate reaction addition/removal
        return new Promise(resolve => setTimeout(resolve; Math.random() * 5));
      },
      200
    );
  }
};

// Export singleton instance
export const messagingPerformanceMonitor = MessagingPerformanceMonitor.getInstance();

export default MessagingPerformanceMonitor;