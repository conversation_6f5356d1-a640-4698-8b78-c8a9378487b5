/**
 * Resilient Messaging Hook
 * Messaging hook with built-in error recovery and offline support
 */

import React from 'react';
import { useOptimizedMessaging } from './useOptimizedMessaging';
import { messagingErrorRecovery, MessagingErrorType } from '../recovery/ErrorRecoveryManager';
import { TIMING_CONSTANTS } from '../constants';
import type { AdvancedMessage, Conversation } from '@/types/messaging';

interface OfflineMessage {
  id: string, content: string, conversationId: string, timestamp: number, retryCount: number, maxRetries: number;
}

interface UseResilientMessagingOptions {
  enableOfflineSupport?: boolean;
  enableAutoRetry?: boolean;
  maxRetries?: number;
  retryDelay?: number;
  enableMessageQueue?: boolean;
  queueSizeLimit?: number;
  enableOptimisticUpdates?: boolean;
  enableErrorReporting?: boolean;
}

interface UseResilientMessagingReturn {
  // State (same as optimized messaging)
  messages: AdvancedMessage[], conversations: Conversation[], activeConversation: Conversation | null, isTyping: boolean, typingUsers: string[];
  
  // Connection state
  isOnline: boolean, isConnected: boolean, connectionQuality: 'excellent' | 'good' | 'poor' | 'offline';
  
  // Queue state
  queuedMessages: OfflineMessage[], pendingOperations: number;
  
  // Resilient actions
  sendMessage: (content: string, options?: { conversationId?: string; type?: string }) => Promise<{ success: boolean; messageId?: string; error?: string }>;
  sendBatchMessages: (messages: { content: string, conversationId: string }[]) => Promise<{ success: boolean, results: Array<{ success: boolean; messageId?: string; error?: string }> }>;
  
  // Error recovery
  retryFailedMessages: () => Promise<void>; clearFailedMessages: () => void; retryMessage: (messageId: string) => Promise<boolean>;
  
  // Offline support
  syncWhenOnline: () => Promise<void>; getOfflineCapabilities: () => {
    canSendMessages: boolean, canReceiveMessages: boolean, canSync: boolean;
  };
  
  // Performance utilities (inherited)
  formatMessage: (message: AdvancedMessage) => string; shouldUpdateMessage: (prev: AdvancedMessage, next: AdvancedMessage) => boolean; cacheStats: { hits: number, misses: number, size: number };
  batchOperations: { pending: number, completed: number, failed: number };
}

export const useResilientMessaging = (
  currentUserId: string,
  options: UseResilientMessagingOptions = {}
): UseResilientMessagingReturn => {
  const {
    enableOfflineSupport = true,
    enableAutoRetry = true,
    maxRetries = 3,
    retryDelay = TIMING_CONSTANTS.MESSAGE_RETRY_DELAY,
    enableMessageQueue = true,
    queueSizeLimit = 100,
    enableOptimisticUpdates = true,
    enableErrorReporting = true
  } = options;

  // Use optimized messaging as base
  const optimizedMessaging = useOptimizedMessaging(currentUserId, {
    enableMessageCaching: true,
    enableBatchOperations: true,
    debounceDelay: TIMING_CONSTANTS.DEBOUNCE_DELAY
  });

  // Resilient state
  const [isOnline, setIsOnline] = React.useState(navigator.onLine);
  const [isConnected, setIsConnected] = React.useState(true);
  const [connectionQuality, setConnectionQuality] = React.useState<'excellent' | 'good' | 'poor' | 'offline'>('excellent');
  const [queuedMessages, setQueuedMessages] = React.useState<OfflineMessage[]>([]);
  const [pendingOperations, setPendingOperations] = React.useState(0);

  // Refs for persistence
  const retryTimeouts = React.useRef<Map<string, NodeJS.Timeout>>(new Map());
  const messageQueue = React.useRef<OfflineMessage[]>([]);
  const lastSyncTime = React.useRef<number>(Date.now());

  // Network monitoring
  React.useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      setConnectionQuality('excellent');
      console.log('🟢 Network connection restored');
      
      if (enableOfflineSupport) {
        syncWhenOnline();
      }
    };

    const handleOffline = () => {
      setIsOnline(false);
      setConnectionQuality('offline');
      console.log('🔴 Network connection lost');
      
      if (enableErrorReporting) {
        messagingErrorRecovery.reportError(
          MessagingErrorType.NETWORK_ERROR,
          'Network connection lost',
          'ResilientMessaging'
        );
      }
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [enableOfflineSupport, enableErrorReporting]);

  // Connection quality monitoring
  React.useEffect(() => {
    if (!isOnline) {
      setConnectionQuality('offline');
      return;
    }

    // Monitor connection quality based on request timing
    const checkConnectionQuality = async () => {
      const start = performance.now();
      
      try {
        // Ping test (in a real app, this would be to your messaging server)
        await fetch('/api/ping', { 
          method: 'HEAD',
          signal: AbortSignal.timeout(5000)
        });
        
        const latency = performance.now() - start;
        
        if (latency < 100) {
          setConnectionQuality('excellent');
        } else if (latency < 300) {
          setConnectionQuality('good');
        } else {
          setConnectionQuality('poor');
        }
      } catch (error) {
        setConnectionQuality('poor');
      }
    };

    const interval = setInterval(checkConnectionQuality, 10000); // Check every 10 seconds
    checkConnectionQuality(); // Initial check

    return () => clearInterval(interval);
  }, [isOnline]);

  // Offline message queue management
  const addToQueue = React.useCallback((content: string, conversationId: string): OfflineMessage => {
    const queuedMessage: OfflineMessage = {
      id: `queued-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      content,
      conversationId,
      timestamp: Date.now(),
      retryCount: 0,
      maxRetries
    };

    if (enableMessageQueue) {
      setQueuedMessages(prev => {
        const newQueue = [...prev, queuedMessage];
        
        // Respect queue size limit
        if (newQueue.length > queueSizeLimit) {
          const removed = newQueue.shift();
          if (removed && enableErrorReporting) {
            messagingErrorRecovery.reportError(
              MessagingErrorType.STORAGE_FULL,
              'Message queue is full, removing oldest message',
              'ResilientMessaging'
            );
          }
        }
        
        return newQueue;
      });

      messageQueue.current = [...messageQueue.current, queuedMessage];
    }

    return queuedMessage;
  }, [enableMessageQueue, maxRetries, queueSizeLimit, enableErrorReporting]);

  // Remove from queue
  const removeFromQueue = React.useCallback((messageId: string) => {
    setQueuedMessages(prev => prev.filter(msg => msg.id !== messageId));
    messageQueue.current = messageQueue.current.filter(msg => msg.id !== messageId);
  }, []);

  // Resilient message sending
  const sendMessage = React.useCallback(async (
    content: string, 
    options: unknown = {}
  ): Promise<{ success: boolean; messageId?: string; error?: string }> => {
    const conversationId = optimizedMessaging.activeConversation?.id;
    if (!conversationId) {
      return { success: false, error: 'No active conversation' };
    }

    // Optimistic update if enabled
    let optimisticMessageId: string | undefined;
    if (enableOptimisticUpdates) {
      try {
        // This would normally create an optimistic message in the UI
        optimisticMessageId = `optimistic-${Date.now()}`;
        console.log('📤 Creating optimistic message:', optimisticMessageId);
      } catch (error) {
        console.warn('Failed to create optimistic message:', error);
      }
    }

    // If offline, queue the message
    if (!isOnline || !isConnected) {
      if (enableOfflineSupport) {
        const queuedMessage = addToQueue(content, conversationId);
        console.log('📱 Message queued for offline:', queuedMessage.id);
        return { success: true, messageId: queuedMessage.id };
      } else {
        return { success: false, error: 'No connection available' };
      }
    }

    // Try to send the message
    setPendingOperations(prev => prev + 1);
    
    try {
      await optimizedMessaging.sendMessage(content, options);
      
      console.log('✅ Message sent successfully');
      return { success: true, messageId: optimisticMessageId };
      
    } catch (error) {
      console.error('❌ Failed to send message:', error);
      
      // Report error
      if (enableErrorReporting) {
        messagingErrorRecovery.reportError(
          MessagingErrorType.MESSAGE_SEND_FAILED,
          error instanceof Error ? error.message : 'Unknown error',
          'ResilientMessaging',
          { content, conversationId, options }
        );
      }

      // Queue for retry if auto-retry is enabled
      if (enableAutoRetry && enableOfflineSupport) {
        const queuedMessage = addToQueue(content, conversationId);
        scheduleRetry(queuedMessage);
        return { success: false, error: 'Message queued for retry', messageId: queuedMessage.id };
      }

      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to send message' 
      };
      
    } finally {
      setPendingOperations(prev => prev - 1);
    }
  }, [
    optimizedMessaging,
    isOnline,
    isConnected,
    enableOptimisticUpdates,
    enableOfflineSupport,
    enableAutoRetry,
    enableErrorReporting,
    addToQueue
  ]);

  // Schedule retry for failed message
  const scheduleRetry = React.useCallback((queuedMessage: OfflineMessage) => {
    if (queuedMessage.retryCount >= queuedMessage.maxRetries) {
      console.warn(`❌ Max retries reached for message ${queuedMessage.id}`);
      return;
    }

    const delay = retryDelay * Math.pow(2, queuedMessage.retryCount); // Exponential backoff
    const timeoutId = setTimeout(async () => {
      await retryMessage(queuedMessage.id);
    }, delay);

    retryTimeouts.current.set(queuedMessage.id, timeoutId);
  }, [retryDelay]);

  // Retry a specific message
  const retryMessage = React.useCallback(async (messageId: string): Promise<boolean> => {
    const queuedMessage = queuedMessages.find(msg => msg.id === messageId);
    if (!queuedMessage) return false;

    // Clear any existing timeout
    const timeoutId = retryTimeouts.current.get(messageId);
    if (timeoutId) {
      clearTimeout(timeoutId);
      retryTimeouts.current.delete(messageId);
    }

    // Increment retry count
    queuedMessage.retryCount++;

    console.log(`🔄 Retrying message ${messageId} (attempt ${queuedMessage.retryCount}/${queuedMessage.maxRetries})`);

    try {
      await optimizedMessaging.sendMessage(queuedMessage.content);
      
      console.log(`✅ Successfully retried message ${messageId}`);
      removeFromQueue(messageId);
      return true;
      
    } catch (error) {
      console.error(`❌ Retry failed for message ${messageId}:`, error);
      
      if (queuedMessage.retryCount < queuedMessage.maxRetries) {
        scheduleRetry(queuedMessage);
      } else {
        console.warn(`❌ Giving up on message ${messageId} after ${queuedMessage.maxRetries} attempts`);
        
        if (enableErrorReporting) {
          messagingErrorRecovery.reportError(
            MessagingErrorType.MESSAGE_SEND_FAILED,
            `Failed to send message after ${queuedMessage.maxRetries} retries`,
            'ResilientMessaging',
            { messageId, content: queuedMessage.content }
          );
        }
      }
      
      return false;
    }
  }, [queuedMessages, optimizedMessaging, removeFromQueue, scheduleRetry, enableErrorReporting]);

  // Batch message sending with resilience
  const sendBatchMessages = React.useCallback(async (
    messagesToSend: { content: string, conversationId: string }[]
  ): Promise<{ success: boolean, results: Array<{ success: boolean; messageId?: string; error?: string }> }> => {
    const results: Array<{ success: boolean; messageId?: string; error?: string }> = [];

    for (const msg of messagesToSend) {
      const result = await sendMessage(msg.content);
      results.push(result);
    }

    const successCount = results.filter(r => r.success).length;
    
    return {
      success: successCount === messagesToSend.length,
      results
    };
  }, [sendMessage]);

  // Retry all failed messages
  const retryFailedMessages = React.useCallback(async (): Promise<void> => {
    console.log(`🔄 Retrying ${queuedMessages.length} failed messages`);
    
    const retryPromises = queuedMessages.map(msg => retryMessage(msg.id));
    await Promise.allSettled(retryPromises);
    
    console.log('✅ Finished retrying failed messages');
  }, [queuedMessages, retryMessage]);

  // Clear all failed messages
  const clearFailedMessages = React.useCallback(() => {
    console.log(`🗑️ Clearing ${queuedMessages.length} failed messages`);
    
    // Clear all timeouts
    retryTimeouts.current.forEach(timeout => clearTimeout(timeout));
    retryTimeouts.current.clear();
    
    // Clear queue
    setQueuedMessages([]);
    messageQueue.current = [];
  }, [queuedMessages]);

  // Sync when online
  const syncWhenOnline = React.useCallback(async (): Promise<void> => {
    if (!isOnline || queuedMessages.length === 0) return;

    console.log(`🔄 Syncing ${queuedMessages.length} offline messages`);
    
    try {
      await retryFailedMessages();
      lastSyncTime.current = Date.now();
      console.log('✅ Offline sync completed');
    } catch (error) {
      console.error('❌ Offline sync failed:', error);
    }
  }, [isOnline, queuedMessages.length, retryFailedMessages]);

  // Get offline capabilities
  const getOfflineCapabilities = React.useCallback(() => {
    return {
      canSendMessages: enableOfflineSupport && enableMessageQueue,
      canReceiveMessages: false, // Usually requires connection
      canSync: enableOfflineSupport && isOnline
    };
  }, [enableOfflineSupport, enableMessageQueue, isOnline]);

  // Cleanup on unmount
  React.useEffect(() => {
    return () => {
      retryTimeouts.current.forEach(timeout => clearTimeout(timeout));
      retryTimeouts.current.clear();
    };
  }, []);

  return {
    // Inherited from optimized messaging
    ...optimizedMessaging,
    
    // Override sendMessage with resilient version
    sendMessage,
    sendBatchMessages,
    
    // Connection state
    isOnline,
    isConnected,
    connectionQuality,
    
    // Queue state
    queuedMessages,
    pendingOperations,
    
    // Error recovery
    retryFailedMessages,
    clearFailedMessages,
    retryMessage,
    
    // Offline support
    syncWhenOnline,
    getOfflineCapabilities
  };
};

export default useResilientMessaging;