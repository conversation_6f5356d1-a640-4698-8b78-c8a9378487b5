import * as React from 'react';
import { createContext, useContext, useMemo, useCallback, memo, useRef } from 'react';
import { useMemoizedValue } from '@/hooks/useOptimized';

// Generic optimized context creator with re-render prevention
export function createOptimizedContext<T>(
  displayName: string;
  defaultValue?: T
) {
  const Context = createContext<T | undefined>(defaultValue);
  Context.displayName = displayName;

  // Optimized provider with memoization
  const Provider = memo(_({
    value,
    children
  }: {
    value: T, children: React.ReactNode;
  }) => {
    // Deep memoization to prevent unnecessary re-renders
    const memoizedValue = useMemoizedValue(value);

    return (
      <Context.Provider value={memoizedValue}>
        {children}
      </Context.Provider>
    );
  });

  Provider.displayName = `${displayName}Provider`;

  // Optimized hook with error handling
  const useContextValue = () => {
    const context = useContext(Context);
    if (context === undefined) {
      throw new Error(`use${displayName} must be used within a ${displayName}Provider`);
    }
    return context;
  };

  return {
    Context,
    Provider,
    useContext: useContextValue
  };
}

// Optimized state context creator with actions
export function createOptimizedStateContext<State, Actions>(
  displayName: string, initialState: State, actionsCreator: (setState: (updater: (prev: State) => State) => void) => Actions
) {
  type ContextValue = {
    state: State, actions: Actions;
  };

  const Context = createContext<ContextValue | undefined>(undefined);
  Context.displayName = displayName;

  const Provider = memo(({ children }: { children: React.ReactNode }) => {
    const stateRef = useRef<State>(initialState);
    const listenersRef = useRef<Set<() => void>>(new Set());

    // Optimized setState that batches updates
    const setState = useCallback((updater: (prev: State) => State) => {
      const newState = updater(stateRef.current);

      // Only update if state actually changed
      if (newState !== stateRef.current) {
        stateRef.current = newState;

        // Batch notify all listeners
        Promise.resolve().then(() => {
          listenersRef.current.forEach(listener => listener());
        });
      }
    }, []);

    // Memoized actions to prevent recreation
    const actions = useMemo(() => actionsCreator(setState),
      [setState]
    );

    // Subscribe to state changes
    const subscribe = useCallback((listener: () => void) => {
      listenersRef.current.add(listener);
      return () => {
        listenersRef.current.delete(listener);
      };
    }, []);

    const getState = useCallback(() => stateRef.current, []);

    const contextValue = useMemo(() => ({
  state: stateRef.current;
      actions,
      subscribe,
      getState
    }), [actions, subscribe, getState]);

    return (
      <Context.Provider value={contextValue as ContextValue}>
        {children}
      </Context.Provider>
    );
  });

  Provider.displayName = `${displayName}Provider`;

  // Hook with state subscription
  const useContextValue = () => {
    const context = useContext(Context);
    if (!context) {
      throw new Error(`use${displayName} must be used within a ${displayName}Provider`);
    }
    return context;
  };

  return {
    Context,
    Provider,
    useContext: useContextValue
  };
}

// Selector-based context for large state objects
export function createSelectorContext<State>(
  displayName: string, initialState: State
) {
  const StateContext = createContext<State>(initialState);
  const UpdaterContext = createContext<((updater: (prev: State) => State) => void) | null>(null);

  StateContext.displayName = `${displayName}StateContext`;
  UpdaterContext.displayName = `${displayName}UpdaterContext`;

  const Provider = memo(({ children }: { children: React.ReactNode }) => {
    const stateRef = useRef<State>(initialState);
    const listenersRef = useRef<Map<string, Set<() => void>>>(new Map());

    const setState = useCallback((updater: (prev: State) => State) => {
      const newState = updater(stateRef.current);

      if (newState !== stateRef.current) {
        stateRef.current = newState;

        // Notify relevant selectors
        Promise.resolve().then(() => {
          listenersRef.current.forEach((listeners) => {
            listeners.forEach(listener => listener());
          });
        });
      }
    }, []);

    return (
      <UpdaterContext.Provider value={setState}>
        <StateContext.Provider value={stateRef.current}>
          {children}
        </StateContext.Provider>
      </UpdaterContext.Provider>
    );
  });

  Provider.displayName = `${displayName}Provider`;

  // Selector hook to prevent unnecessary re-renders  
  function useSelector<Selected>(selector: (state: State) => Selected) {
    const state = useContext(StateContext);
    const selectorRef = useRef(selector);
    const selectedRef = useRef<Selected>(selector(state));

    // Update selector and selected value if selector changes
    if (selectorRef.current !== selector) {
      selectorRef.current = selector;
      selectedRef.current = selector(state);
    }

    // Only re-render if selected value changes
    return useMemo(() => {
      const newSelected = selectorRef.current(state);
      if (newSelected !== selectedRef.current) {
        selectedRef.current = newSelected;
      }
      return selectedRef.current;
    }, [state]);
  }

  const useUpdater = () => {
    const updater = useContext(UpdaterContext);
    if (!updater) {
      throw new Error(`use${displayName}Updater must be used within a ${displayName}Provider`);
    }
    return updater;
  };

  return {
    Provider,
    useSelector,
    useUpdater
  };
}

// Performance monitoring for contexts
export class ContextPerformanceMonitor {
  private static renderCounts = new Map<string, number>();
  private static lastRenderTime = new Map<string, number>();

  static trackRender(contextName: string) {
    const now = performance.now();
    const count = this.renderCounts.get(contextName) || 0;
    const lastTime = this.lastRenderTime.get(contextName) || now;

    this.renderCounts.set(contextName, count + 1);
    this.lastRenderTime.set(contextName, now);

    if (process.env.NODE_ENV === 'development') {
      const timeSinceLastRender = now - lastTime;
      if (timeSinceLastRender < 16) { // Less than one frame
        console.warn(`⚠️ ${contextName} re-rendered too frequently (${timeSinceLastRender.toFixed(2)}ms since last render)`);
      }
    }
  }

  static getStats(contextName: string) {
    return {
  renders: this.renderCounts.get(contextName) || 0, lastRender: this.lastRenderTime.get(contextName) || 0
    };
  }

  static getAllStats() {
    return Array.from(this.renderCounts.entries()).map(([name,renders]) => ({
      name,
      renders,
      lastRender: this.lastRenderTime.get(name) || 0
    }));
  }
}

// HOC for context performance monitoring
export function withContextMonitoring<P extends object>(
  Component: React.ComponentType<P>, contextName: string
): React.ComponentType<P> {
  const MonitoredComponent = memo((props: P) => {
    ContextPerformanceMonitor.trackRender(contextName);
    return <Component {...props} />;
  }) as React.ComponentType<P>;

  MonitoredComponent.displayName = `withContextMonitoring(${Component.displayName || Component.name})`;

  return MonitoredComponent;
}

// Batch context updates to prevent cascade re-renders
export function createBatchedUpdates() {
  let pendingUpdates: (() => void)[] = [];
  let isScheduled = false;

  const flushUpdates = () => {
    const updates = pendingUpdates;
    pendingUpdates = [];
    isScheduled = false;

    // Execute all updates in a single batch
    updates.forEach(update => update());
  };

  const batchUpdate = (update: () => void) => {
    pendingUpdates.push(update);

    if (!isScheduled) {
      isScheduled = true;
      // Use scheduler if available, otherwise setTimeout
      if (typeof MessageChannel !== 'undefined') {
        const channel = new MessageChannel();
        channel.port1.onmessage = flushUpdates;
        channel.port2.postMessage(null);
      } else {
        setTimeout(flushUpdates, 0);
      }
    }
  };

  return { batchUpdate };
}

// Context composition utility
export function composeProviders(providers: React.ComponentType<{ children: React.ReactNode }>[]) {
  return memo(({ children }: { children: React.ReactNode }) => {
    return providers.reduceRight(_(acc,Provider) => <Provider>{acc}</Provider>,
      children
    );
  });
}

// Export main utilities
export const _contextUtils={createOptimizedContext,
  createOptimizedStateContext,
  createSelectorContext,
  ContextPerformanceMonitor,
  withContextMonitoring,
  createBatchedUpdates}, composeProviders
};