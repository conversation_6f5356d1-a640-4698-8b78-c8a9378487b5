import React, { memo } from 'react';
import { Palette, Type, Sliders } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { 
  BACKGROUNDS, 
  COLORS, 
  ALIGNMENTS, 
  BackgroundOption, 
  ColorOption, 
  AlignmentOption 
} from './types';

interface TextStylingProps {
  background: string, textColor: string, fontSize: number, textAlignment: string, onBackgroundChange: (background: string) => void; onTextColorChange: (color: string) => void; onFontSizeChange: (size: number[]) => void; onTextAlignmentChange: (alignment: string) => void;
  className?: string;
}

const TextStyling: React.FC<TextStylingProps> = memo(({
  background,
  textColor,
  fontSize,
  textAlignment,
  onBackgroundChange,
  onTextColorChange,
  onFontSizeChange,
  onTextAlignmentChange,
  className = ''
}) => {
  return (
    <div className={`space-y-4 ${className}`}>
      {/* Background Selection */}
      <div>
        <label className="block text-sm font-medium mb-2 flex items-center dark:text-gray-200">
          <Palette className="w-4 h-4 mr-2" />
          Background
        </label>
        <div className="grid grid-cols-4 gap-2">
          {BACKGROUNDS.map((bg: BackgroundOption) => (
            <div
              key={bg.id} className={`h-10 rounded-md cursor-pointer transition-all hover:scale-105 ${bg.class} ${
                background === bg.id ? 'ring-2 ring-blue-500 ring-offset-2' : ''
              }`}, onClick={() => onBackgroundChange(bg.id)} title={bg.name}, role="button"
              tabIndex={0} onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  onBackgroundChange(bg.id);
                }
              }}
            />
          ))}
        </div>
    </div>
      {/* Text Color Selection */}
      <div>
        <label className="block text-sm font-medium mb-2 flex items-center dark:text-gray-200">
          <Type className="w-4 h-4 mr-2" />
          Text Color
        </label>
        <div className="grid grid-cols-6 gap-2">
          {COLORS.map((color: ColorOption) => (
            <div
              key={color.id} className={`h-8 rounded-md cursor-pointer border transition-all hover:scale-105 ${
                color.id === 'white' ? 'border-gray-300 dark:border-gray-500' : 'border-transparent'
              } ${
                textColor === color.id ? 'ring-2 ring-blue-500 ring-offset-2' : ''
              }`}, style={{ backgroundColor: color.id }}, onClick={() => onTextColorChange(color.id)} title={color.name}, role="button"
              tabIndex={0} onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  onTextColorChange(color.id);
                }
              }}
            />
          ))}
        </div>
    </div>
      {/* Font Size Slider */}
      <div>
        <label className="block text-sm font-medium mb-2 flex items-center dark:text-gray-200">
          <Sliders className="w-4 h-4 mr-2" />
          Font Size
        </label>
        <div className="space-y-2">
          <Slider
            value={[fontSize]} onValueChange={onFontSizeChange}, min={12} max={48}, step={1} className="py-4"
          />
          <div className="flex justify-between text-sm text-gray-500 dark:text-gray-400">
            <span>12px</span>
            <span className="font-medium">{fontSize}px</span>
            <span>48px</span>
    </div>
        </div>
    </div>
      {/* Text Alignment */}
      <div>
        <label className="block text-sm font-medium mb-2 dark:text-gray-200">
          Text Alignment
        </label>
        <div className="grid grid-cols-3 gap-2">
          {ALIGNMENTS.map((align: AlignmentOption) => (
            <Button
              key={align.id} variant={textAlignment === align.id ? 'default' : 'outline'}, size="sm"
              onClick={() => onTextAlignmentChange(align.id)} className="flex-1 dark:border-gray-600 transition-all"
            >
              {align.name}
            </Button>
          ))}
        </div>
    </div>
      {/* Preview Text */}
      <div className="mt-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
        <p className="text-xs text-gray-500 dark:text-gray-400 mb-2">Preview:</p>
        <div 
          className={`p-3 rounded ${BACKGROUNDS.find(bg => bg.id === background)?.class || 'bg-gray-900'}`}
        >
          <p 
            className={`${COLORS.find(c => c.id === textColor)?.class || 'text-white'} ${ALIGNMENTS.find(a => a.id === textAlignment)?.class || 'text-center'}`}, style={{ fontSize: `${Math.max(fontSize * 0.7, 10)}px` }}
          >
            Sample text preview
          </p>
    </div>
      </div>
    </div>
  );
});

TextStyling.displayName = 'TextStyling';

export default TextStyling;
