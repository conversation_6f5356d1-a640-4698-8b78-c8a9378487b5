import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { ReelsSuspenseFallback, MinimalReelsSuspenseFallback } from '../ReelsSuspenseFallback';

// Mock framer-motion to avoid animation issues in tests
jest.mock('framer-motion',() => ({
  motion: {
    div: ({ children, ...props }: React.ComponentProps<'div'>) => <div {...props}>{children}</div>,
    p: (_{ children, ...props }: React.ComponentProps<'p'>) => <p {...props}>{children}</p>
  }
}));

// Mock timers for testing intervals
jest.useFakeTimers();

describe('ReelsSuspenseFallback',() => {
  beforeEach(() => {
    jest.clearAllTimers();
  });

  afterEach(() => {
    jest.runOnlyPendingTimers();
    jest.useRealTimers();
    jest.useFakeTimers();
  });

  afterAll(() => {
    jest.useRealTimers();
  });

  it('should render with default message',() => {
    render(<ReelsSuspenseFallback />);
    
    expect(screen.getByText('Loading Reels...')).toBeInTheDocument();
  });

  it('should render with custom message',() => {
    render(<ReelsSuspenseFallback message="Custom loading message" />);
    
    expect(screen.getByText('Custom loading message')).toBeInTheDocument();
  });

  it('should show progress steps when showProgress is true',() => {
    render(<ReelsSuspenseFallback showProgress={true} />);
    
    expect(screen.getByText('Connecting to video servers...')).toBeInTheDocument();
  });

  it('should not show progress steps when showProgress is false',() => {
    render(<ReelsSuspenseFallback showProgress={false} />);
    
    expect(screen.queryByText('Connecting to video servers...')).not.toBeInTheDocument();
  });

  it('should show loading tips when showTips is true',() => {
    render(<ReelsSuspenseFallback showTips={true} />);
    
    expect(screen.getByText(/💡 Tip:/)).toBeInTheDocument();
  });

  it('should not show loading tips when showTips is false',() => {
    render(<ReelsSuspenseFallback showTips={false} />);
    
    expect(screen.queryByText(/💡 Tip:/)).not.toBeInTheDocument();
  });

  it('should cycle through loading steps',async () => {
    render(<ReelsSuspenseFallback showProgress={true} />);
    
    // Initial step
    expect(screen.getByText('Connecting to video servers...')).toBeInTheDocument();
    
    // Advance time to trigger step change
    jest.advanceTimersByTime(1500);
    
    await waitFor(() => {
      expect(screen.getByText('Loading video content...')).toBeInTheDocument();
    });
    
    // Advance time again
    jest.advanceTimersByTime(1500);
    
    await waitFor(() => {
      expect(screen.getByText('Preparing your feed...')).toBeInTheDocument();
    });
  });

  it('should cycle through loading tips',async () => {
    render(<ReelsSuspenseFallback showTips={true} />);
    
    // Initial tip
    expect(screen.getByText(/💡 Tip: Tap a reel to view it in full screen/)).toBeInTheDocument();
    
    // Advance time to trigger tip change
    jest.advanceTimersByTime(3000);
    
    await waitFor(() => {
      expect(screen.getByText(/🎵 Tip: Many reels have amazing soundtracks/)).toBeInTheDocument();
    });
  });

  it('should render header skeleton',() => {
    render(<ReelsSuspenseFallback />);
    
    // Check for header elements
    const header = screen.getByRole('banner', { hidden: true }) || 
                  document.querySelector('.h-14.bg-white');
    
    // Should have header-like structure
    expect(document.querySelector('.h-14')).toBeInTheDocument();
  });

  it('should render tabs skeleton',() => {
    render(<ReelsSuspenseFallback />);
    
    expect(screen.getByText('For You')).toBeInTheDocument();
    expect(screen.getByText('Following')).toBeInTheDocument();
    expect(screen.getByText('Trending')).toBeInTheDocument();
    expect(screen.getByText('New')).toBeInTheDocument();
    expect(screen.getByText('Saved')).toBeInTheDocument();
  });

  it('should render skeleton reel cards',() => {
    render(<ReelsSuspenseFallback />);
    
    // Should render multiple skeleton cards
    const skeletonCards = document.querySelectorAll('.h-\\[400px\\]');
    expect(skeletonCards.length).toBeGreaterThan(0);
  });

  it('should have proper accessibility attributes',() => {
    render(<ReelsSuspenseFallback />);
    
    // Should have proper heading structure
    const heading = screen.getByRole('heading', { level: 3 });
    expect(heading).toHaveTextContent('Loading Reels...');
  });

  it('should clean up intervals on unmount',() => {
    const { unmount } = render(<ReelsSuspenseFallback showProgress={true} showTips={true} />);
    
    // Verify intervals are set
    expect(jest.getTimerCount()).toBeGreaterThan(0);
    
    unmount();
    
    // Intervals should be cleared
    jest.runOnlyPendingTimers();
  });

  it('should handle dark mode classes',() => {
    render(<ReelsSuspenseFallback />);
    
    // Should have dark mode classes
    const darkModeElements = document.querySelectorAll('.dark\\:bg-gray-900, .dark\\:bg-gray-800, .dark\\:text-gray-100');
    expect(darkModeElements.length).toBeGreaterThan(0);
  });
});

describe('MinimalReelsSuspenseFallback',() => {
  it('should render minimal loading state',() => {
    render(<MinimalReelsSuspenseFallback />);
    
    expect(screen.getByText('Loading Reels...')).toBeInTheDocument();
    expect(screen.getByText('Preparing your video content')).toBeInTheDocument();
  });

  it('should have spinner animation',() => {
    render(<MinimalReelsSuspenseFallback />);
    
    const spinner = document.querySelector('.animate-spin');
    expect(spinner).toBeInTheDocument();
  });

  it('should have Film icon',() => {
    render(<MinimalReelsSuspenseFallback />);
    
    // Check for Film icon (Lucide React icons render as SVG)
    const filmIcon = document.querySelector('svg');
    expect(filmIcon).toBeInTheDocument();
  });

  it('should be centered on screen',() => {
    render(<MinimalReelsSuspenseFallback />);
    
    const container = document.querySelector('.min-h-\\[80vh\\]');
    expect(container).toHaveClass('flex', 'items-center', 'justify-center');
  });

  it('should support dark mode',() => {
    render(<MinimalReelsSuspenseFallback />);
    
    const container = document.querySelector('.bg-gray-50');
    expect(container).toHaveClass('dark:bg-gray-900');
  });
});

describe('ReelCardSkeleton',() => {
  it('should render skeleton elements',() => {
    render(<ReelsSuspenseFallback />);
    
    // Should have card-like structures
    const cards = document.querySelectorAll('.h-\\[400px\\]');
    expect(cards.length).toBeGreaterThan(0);
  });

  it('should have proper aspect ratio',() => {
    render(<ReelsSuspenseFallback />);
    
    // Cards should have fixed height
    const cards = document.querySelectorAll('.h-\\[400px\\]');
    cards.forEach(card => {
      expect(card).toHaveClass('h-[400px]');
    });
  });

  it('should include interactive elements skeletons',() => {
    render(<ReelsSuspenseFallback />);
    
    // Should have heart, message, and share icon placeholders
    const heartIcons = document.querySelectorAll('[data-testid="heart-icon"], .lucide-heart');
    const messageIcons = document.querySelectorAll('[data-testid="message-icon"], .lucide-message-circle');
    const shareIcons = document.querySelectorAll('[data-testid="share-icon"], .lucide-share');
    
    // At least some icons should be present in the skeleton
    expect(heartIcons.length + messageIcons.length + shareIcons.length).toBeGreaterThan(0);
  });
});