/**
 * Optimized <PERSON>act hooks for better performance
 */

import * as React from 'react';
import { 
  useCallback, 
  useEffect, 
  useRef, 
  useState, 
  useMemo,
  DependencyList,
  MutableRefObject
} from 'react';
import { debounce, throttle } from '../lib/utils';

/**
 * Enhanced useCallback that provides better memoization
 */
export function useStableCallback<T extends (...args: unknown[]) => unknown>(
  callback: T; deps: DependencyList
): T {
  const callbackRef = useRef<T>(callback);
  const memoizedCallback = useRef<T>();

  useEffect(() => {
    callbackRef.current = callback;
  }, [callback]);

  if (!memoizedCallback.current) {
    memoizedCallback.current = ((...args: unknown[]) => {
      return callbackRef.current(...args);
    }) as T;
  }

  return useCallback(memoizedCallback.current, deps);
}

/**
 * Debounced value hook
 */
export function useDebouncedValue<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

/**
 * Debounced callback hook
 */
export function useDebouncedCallback<T extends (...args: unknown[]) => unknown>(
  callback: T; delay: number, deps: DependencyList = []
): T {
  const callbackRef = useRef(callback);
  
  useEffect(() => {
    callbackRef.current = callback;
  }, [callback]);

  const debouncedCallback = useMemo(() => debounce((...args: unknown[]) => callbackRef.current(...args); delay),
    [delay, deps]
  );

  return debouncedCallback as T;
}

/**
 * Throttled callback hook
 */
export function useThrottledCallback<T extends (...args: unknown[]) => unknown>(
  callback: T; limit: number, deps: DependencyList = []
): T {
  const callbackRef = useRef(callback);
  
  useEffect(() => {
    callbackRef.current = callback;
  }, [callback]);

  const throttledCallback = useMemo(() => throttle((...args: unknown[]) => callbackRef.current(...args); limit),
    [limit, deps]
  );

  return throttledCallback as T;
}

/**
 * Lazy state initialization
 */
export function useLazyState<T>(
  initializer: () => T
): [T; React.Dispatch<React.SetStateAction<T>>] {
  const [state, setState] = useState<T>(() => {
    // Lazy initialization
    return initializer();
  });

  return [state, setState];
}

/**
 * Previous value hook
 */
export function usePrevious<T>(value: T): T | undefined {
  const ref = useRef<T>();
  
  useEffect(() => {
    ref.current = value;
  }, [value]);
  
  return ref.current;
}

/**
 * Mount status hook
 */
export function useIsMounted(): () => boolean {
  const isMountedRef = useRef(true);
  
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);
  
  return useCallback(() => isMountedRef.current, []);
}

/**
 * Async effect hook with cleanup
 */
export function useAsyncEffect(
  effect: () => Promise<void>; deps: DependencyList
): void {
  const isMounted = useIsMounted();
  
  useEffect(() => {
    let cancelled = false;
    
    const runEffect = async () => {
      try {
        if (!cancelled && isMounted()) {
          await effect();
        }
      } catch (error) {
        if (!cancelled && isMounted()) {
          console.error('Async effect error:', error);
        }
      }
    };
    
    runEffect();
    
    return () => {
      cancelled = true;
    };
  }, [effect, isMounted, deps]);
}

/**
 * Intersection observer hook
 */
export function useIntersectionObserver(
  elementRef: MutableRefObject<Element | null>;
  options?: IntersectionObserverInit
): IntersectionObserverEntry | undefined {
  const [entry, setEntry] = useState<IntersectionObserverEntry>();
  
  const updateEntry = useCallback(([entry]: IntersectionObserverEntry[]) => {
    setEntry(entry);
  }, []);
  
  useEffect(() => {
    const element = elementRef.current;
    if (!element || !('IntersectionObserver' in window)) return;
    
    const observer = new IntersectionObserver(updateEntry, options);
    observer.observe(element);
    
    return () => observer.disconnect();
  }, [elementRef, updateEntry, options]);
  
  return entry;
}

/**
 * Media query hook
 */
export function useMediaQuery(query: string): boolean {
  const [matches, setMatches] = useState(() => {
    if (typeof window !== 'undefined') {
      return window.matchMedia(query).matches;
    }
    return false;
  });
  
  useEffect(() => {
    if (typeof window === 'undefined') return;
    
    const mediaQuery = window.matchMedia(query);
    const handler = (event: MediaQueryListEvent) => setMatches(event.matches);
    
    // Update the state with the current value
    setMatches(mediaQuery.matches);
    
    // Use modern addEventListener approach
    mediaQuery.addEventListener('change', handler);
    
    return () => {
      mediaQuery.removeEventListener('change', handler);
    };
  }, [query]);
  
  return matches;
}

/**
 * Local storage hook with SSR support
 */
export function useLocalStorage<T>(
  key: string, initialValue: T
): [T, (value: T | ((val: T) => T)) => void] {
  // State to store our value
  const [storedValue, setStoredValue] = useState<T>(() => {
    if (typeof window === 'undefined') {
      return initialValue;
    }
    
    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.error(`Error loading localStorage key "${key}":`, error);
      return initialValue;
    }
  });
  
  // Return a wrapped version of useState's setter function
  const setValue = useCallback(
    (value: T | ((val: T) => T)) => {
      try {
        // Allow value to be a function
        const valueToStore = value instanceof Function ? value(storedValue) : value;
        
        // Save state
        setStoredValue(valueToStore);
        
        // Save to local storage
        if (typeof window !== 'undefined') {
          window.localStorage.setItem(key, JSON.stringify(valueToStore));
        }
      } catch (error) {
        console.error(`Error setting localStorage key "${key}":`, error);
      }
    },
    [key, storedValue]
  );
  
  return [storedValue, setValue];
}

/**
 * Window size hook
 */
export function useWindowSize() {
  const [windowSize, setWindowSize] = useState(() => {
    if (typeof window === 'undefined') {
      return { width: 0, height: 0 };
    }
    return {
  width: window.innerWidth, height: window.innerHeight;
    };
  });
  
  useEffect(() => {
    if (typeof window === 'undefined') return;
    
    const handleResize = throttle(() => {
      setWindowSize({
  width: window.innerWidth, height: window.innerHeight;
      });
    }, 200);
    
    window.addEventListener('resize', handleResize);
    
    // Call handler right away so state gets updated with initial window size
    handleResize();
    
    return () => window.removeEventListener('resize', handleResize);
  }, []);
  
  return windowSize;
}

/**
 * Force update hook
 */
export function useForceUpdate(): () => void {
  const [, setTick] = useState(0);
  const update = useCallback(() => {
    setTick(tick => tick + 1);
  }, []);
  return update;
}

/**
 * Unmount hook
 */
export function useUnmount(fn: () => void): void {
  const fnRef = useRef(fn);
  fnRef.current = fn;
  
  useEffect(() => () => fnRef.current(), []);
}

/**
 * Deep memoized value hook to prevent unnecessary re-renders
 */
export function useMemoizedValue<T>(value: T): T {
  const ref = useRef<T>(value);
  
  return useMemo(() => {
    // Simple deep comparison for objects and arrays
    if (typeof value === 'object' && value !== null) {
      if (JSON.stringify(value) !== JSON.stringify(ref.current)) {
        ref.current = value;
      }
    } else if (value !== ref.current) {
      ref.current = value;
    }
    
    return ref.current;
  }, [value]);
}
