import React from 'react';
import { AlertTriangle, RefreshCw } from 'lucide-react';
import { Button } from '../ui/button';
import UniversalErrorBoundary from '../ui/UniversalErrorBoundary';

interface ComponentErrorFallbackProps {
  error?: Error;
  onRetry?: () => void;
  componentName?: string;
  minimal?: boolean;
}

const ComponentErrorFallback: React.FC<ComponentErrorFallbackProps> = ({ 
  error, 
  onRetry, 
  componentName = 'Component',
  minimal = false 
}) => {
  if (minimal) {
    return (
      <div className="p-2 text-center text-red-500 text-sm border border-red-200 rounded bg-red-50 dark:bg-red-900/20">
        <AlertTriangle className="w-4 h-4 inline mr-1" />
        Error loading {componentName}
        {onRetry && (
          <Button
            size="sm"
            variant="ghost"
            onClick={onRetry} className="ml-2 h-5 px-1 text-xs"
          >
            <RefreshCw className="w-3 h-3" />
    </Button>
        )}
      </div>
    );
  }

  return (
    <div className="p-6 text-center border border-red-200 rounded-lg bg-red-50 dark:bg-red-900/20">
      <AlertTriangle className="w-8 h-8 text-red-500 mx-auto mb-3" />
      
      <h3 className="text-lg font-medium text-red-700 dark:text-red-400 mb-2">
        {componentName} Error
      </h3>
      
      <p className="text-sm text-red-600 dark:text-red-300 mb-4">
        {error?.message || `The ${componentName} component encountered an error.`}
      </p>
      
      {onRetry && (
        <Button onClick={onRetry} size="sm" variant="outline">
          <RefreshCw className="w-4 h-4 mr-2" />
          Try Again
        </Button>
      )}
      
      {process.env.NODE_ENV === 'development' && error && (
        <details className="mt-4 text-left">
          <summary className="text-xs text-gray-600 cursor-pointer">Error Details</summary>
          <pre className="text-xs text-gray-500 mt-2 p-2 bg-gray-100 rounded overflow-auto">
            {error.stack}
          </pre>
    </details>
      )}
    </div>
  );
};

interface ComponentErrorBoundaryProps {
  children: React.ReactNode;
  componentName?: string;
  minimal?: boolean;
  onError?: (error: Error) => void;
}

const ComponentErrorBoundary: React.FC<ComponentErrorBoundaryProps> = ({ 
  children, 
  componentName,
  minimal = false,
  onError 
}) => {
  const [retryKey, setRetryKey] = React.useState(0);

  const handleRetry = () => {
    setRetryKey(prev => prev + 1);
  };

  return (
    <UniversalErrorBoundary
      key={retryKey} level="component"
      onError={onError} fallback={
        <ComponentErrorFallback 
          componentName={componentName} minimal={minimal}, onRetry={handleRetry}
        />
      }
    >
      {children}
    </UniversalErrorBoundary>
  );
};

export default ComponentErrorBoundary;
