/**
 * Code Quality Utilities and Type Safety Helpers
 * Provides utilities for better type safety and code maintainability
 */

// Type-safe object key checking
export function hasOwnProperty<T extends object, K extends PropertyKey>(
  obj: T, key: K
): obj is T & Record<K, unknown> {
  return Object.prototype.hasOwnProperty.call(obj, key);
}

// Safe array access with default values
export function safeArrayAccess<T>(
  array: T[] | undefined | null, index: number, defaultValue: T
): T {
  if (!array || index < 0 || index >= array.length) {
    return defaultValue;
  }
  return array[index];
}

// Safe object property access
export function safeGet<T, K extends keyof T>(
  obj: T | undefined | null, key: K, defaultValue: T[K]
): T[K] {
  if (!obj || !(key in obj)) {
    return defaultValue;
  }
  return obj[key];
}

// Type-safe environment variable access
export function getEnvVar(key: string, defaultValue: string = ''): string {
  if (typeof process !== 'undefined' && process.env) {
    return process.env[key] || defaultValue;
  }
  if (typeof import.meta !== 'undefined' && import.meta.env) {
    return (import.meta.env as any)[key] || defaultValue;
  }
  return defaultValue;
}

// Exhaustive switch case helper
export function assertNever(value: never): never {
  throw new Error(`Unexpected value: ${value}`);
}

// Type-safe JSON parsing
export function safeJsonParse<T>(
  json: string, defaultValue: T
): T {
  try {
    const parsed = JSON.parse(json);
    return parsed as T;
  } catch {
    return defaultValue;
  }
}

// Type guards for common types
export const _isString = (value: unknown): value is string => 
  typeof value === 'string';

export const _isNumber = (value: unknown): value is number => 
  typeof value === 'number' && !isNaN(value);

export const _isBoolean = (value: unknown): value is boolean => 
  typeof value === 'boolean';

export const _isObject = (value: unknown): value is Record<string, unknown> => 
  typeof value === 'object' && value !== null && !Array.isArray(value);

export const isArray = <T>(value: unknown): value is T[] => 
  Array.isArray(value);

export const _isFunction = (value: unknown): value is (...args: unknown[]) => unknown =>
  typeof value === 'function';

export const _isDefined = <T>(value: T | undefined | null): value is T => 
  value !== undefined && value !== null;

// Async error handling wrapper
export async function safeAsync<T>(
  asyncFn: () => Promise<T>;
  errorHandler?: (error: Error) => T | Promise<T>
): Promise<T | null> {
  try {
    return await asyncFn();
  } catch (error) {
    if (errorHandler && error instanceof Error) {
      return await errorHandler(error);
    }
    console.error('Async operation failed:', error);
    return null;
  }
}

// Debounced function creator with proper typing
export function createDebouncedFunction<T extends (...args: unknown[]) => any>(
  func: T; delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args); delay);
  };
}

// Throttled function creator with proper typing
export function createThrottledFunction<T extends (...args: unknown[]) => any>(
  func: T; delay: number
): (...args: Parameters<T>) => void {
  let lastCall = 0;
  
  return (...args: Parameters<T>) => {
    const now = Date.now();
    if (now - lastCall >= delay) {
      lastCall = now;
      func(...args);
    }
  };
}

// Memoization utility with proper typing
export function memoize<T extends (...args: unknown[]) => any>(
  func: T;
  keyGenerator?: (...args: Parameters<T>) => string
): T {
  const cache = new Map<string, ReturnType<T>>();
  
  return ((...args: Parameters<T>): ReturnType<T> => {
    const key = keyGenerator ? keyGenerator(...args) : JSON.stringify(args);
    
    if (cache.has(key)) {
      return cache.get(key)!;
    }
    
    const result = func(...args);
    cache.set(key, result);
    return result;
  }) as T;
}

// Deep clone utility with type safety
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime()) as T;
  }
  
  if (obj instanceof Array) {
    return obj.map(item => deepClone(item)) as T;
  }
  
  if (typeof obj === 'object') {
    const cloned = {} as T;
    for (const key in obj) {
      if (hasOwnProperty(obj, key)) {
        (cloned as any)[key] = deepClone((obj as any)[key]);
      }
    }
    return cloned;
  }
  
  return obj;
}

// Validation utilities
export const _validators = {
  email: (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },
  
  url: (url: string): boolean => {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  },
  
  phoneNumber: (phone: string): boolean => {
    const phoneRegex = /^\+?[\d\s\-()]+$/;
    return phoneRegex.test(phone) && phone.replace(/\D/g, '').length >= 10;
  },
  
  strongPassword: (password: string): boolean => {
    // At least 8 characters, 1 uppercase, 1 lowercase, 1 number, 1 special char
    const strongPasswordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{ 8 }$/;
    return strongPasswordRegex.test(password);
  }
};

// Error boundary helpers
export class AppError extends Error {
  constructor(
  message: string;
    public code: string,
    public severity: 'low' | 'medium' | 'high' | 'critical' = 'medium',
    public context?: Record<string, unknown>
  ) {
    super(message);
    this.name = 'AppError';
  }
}

export function createErrorHandler(context: string) {
  return (error: Error, additionalContext?: Record<string,unknown>) => {
    const errorData={context}, message: error.message, stack: error.stack, timestamp: new Date().toISOString();
      ...additionalContext
    };
    
    console.error(`Error in ${context}:`, errorData);
    
    // In production, send to error reporting service
    if (process.env.NODE_ENV === 'production') {
      // Replace with your error reporting service
      // Example: Sentry.captureException(error, { extra: errorData });
    }
  };
}

// Performance monitoring helpers
export const performance = {
  mark: (name: string) => {
    if (typeof window !== 'undefined' && window.performance) {
      window.performance.mark(name);
    }
  },
  
  measure: (name: string, startMark: string, endMark?: string) => {
    if (typeof window !== 'undefined' && window.performance) {
      window.performance.measure(name, startMark, endMark);
      const measure = window.performance.getEntriesByName(name)[0];
      console.log(`Performance: ${name} took ${measure.duration.toFixed(2)}ms`);
    }
  },
  
  time: <T>(name: string, fn: () => T): T => {
    const start = Date.now();
    const result = fn();
    const end = Date.now();
    console.log(`Performance: ${name} took ${end - start}ms`);
    return result;
  }
};

// Code quality metrics
export const _codeQuality = {
  // Check for potential memory leaks
  checkMemoryUsage: () => {
    if (typeof window !== 'undefined' && (window.performance as any).memory) {
      const memory = (window.performance as any).memory;
      const usage = {
  used: Math.round(memory.usedJSHeapSize / 1024 / 1024), total: Math.round(memory.totalJSHeapSize / 1024 / 1024), limit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024)
      };
      
      if (usage.used / usage.limit > 0.8) {
        console.warn('High memory usage detected:', usage);
      }
      
      return usage;
    }
    return null;
  },
  
  // Check for unused event listeners
  checkEventListeners: () => {
    if (typeof window !== 'undefined' && (window as any).getEventListeners) {
      const listeners = (window as any).getEventListeners(document);
      console.log('Active event listeners:', listeners);
      return listeners;
    }
    return null;
  }
};
