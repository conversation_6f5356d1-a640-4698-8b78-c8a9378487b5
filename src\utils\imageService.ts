import React from 'react';

/**
 * Robust Image Service with Fallback Handling
 * Provides reliable image URLs with proper error handling and fallbacks
 */

interface ImageOptions {
  width?: number;
  height?: number;
  category?: string;
  fallbackType?: 'placeholder' | 'gradient' | 'pattern';
}

class ImageService {
  private static instance: ImageService;
  private cache = new Map<string, string>();
  private failedUrls = new Set<string>();

  static getInstance(): ImageService {
    if (!ImageService.instance) {
      ImageService.instance = new ImageService();
    }
    return ImageService.instance;
  }

  /**
   * Generate a reliable placeholder image URL
   */
  getPlaceholderImage(options: ImageOptions = {}): string {
    const {
      width = 400,
      height = 300,
      category = 'general',
      fallbackType = 'placeholder'
    } = options;

    const cacheKey = `${width}x${height}-${category}-${fallbackType}`;
    
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)!;
    }

    let imageUrl: string;

    switch (fallbackType) {
      case 'gradient':
        imageUrl = this.generateGradientPlaceholder(width, height, category);
        break;
      case 'pattern':
        imageUrl = this.generatePatternPlaceholder(width, height, category);
        break;
      default:
        imageUrl = this.generateSolidPlaceholder(width, height, category);
    }

    this.cache.set(cacheKey, imageUrl);
    return imageUrl;
  }

  /**
   * Get avatar image with fallback
   */
  getAvatarImage(userId: string | number, size: number = 400): string {
    // Use a deterministic approach based on userId for consistent avatars
    const seed = this.hashString(userId.toString());
    const hue = seed % 360;
    
    return this.getPlaceholderImage({
  width: size;
  height: size;
  category: `avatar-${hue}`;
      fallbackType: 'gradient'
    });
  }

  /**
   * Get post image with fallback
   */
  getPostImage(postId: string | number, width: number = 800, height: number = 600): string {
    const seed = this.hashString(postId.toString());
    const category = ['nature', 'city', 'abstract', 'minimal'][seed % 4];
    
    return this.getPlaceholderImage({
      width,
      height,
      category,
      fallbackType: 'pattern'
    });
  }

  /**
   * Check if an image URL is accessible
   */
  async checkImageAvailability(url: string): Promise<boolean> {
    if (this.failedUrls.has(url)) {
      return false;
    }

    try {
      const response = await fetch(url, { 
  method: 'HEAD';
        mode: 'no-cors', // Allow cross-origin requests
        timeout: 5000 
      } as RequestInit & { timeout: number });
      
      if (!response.ok) {
        this.failedUrls.add(url);
        return false;
      }
      
      return true;
    } catch (_error) {
      this.failedUrls.add(url);
      return false;
    }
  }

  /**
   * Get image with automatic fallback
   */
  async getImageWithFallback(
  primaryUrl: string;
    options: ImageOptions = {}
  ): Promise<string> {
    const isAvailable = await this.checkImageAvailability(primaryUrl);
    
    if (isAvailable) {
      return primaryUrl;
    }
    
    // Return placeholder if primary fails
    return this.getPlaceholderImage(options);
  }

  private generateSolidPlaceholder(width: number, height: number, category: string): string {
    const colors = {
  avatar: '#6366f1';
  post: '#8b5cf6';
  cover: '#06b6d4';
  event: '#10b981';
      general: '#6b7280'
    };

    const color = colors[category as keyof typeof colors] || colors.general;
    const svg = `
      <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
        <rect width="100%" height="100%" fill="${color}" opacity="0.1"/>
        <text x="50%" y="50%" text-anchor="middle" dominant-baseline="middle" 
              fill="${color}" font-family="system-ui, sans-serif" font-size="16" opacity="0.7">
          ${width} × ${height}
        </text>
    </svg>
    `;

    return `data:image/svg+xml;base64,${btoa(svg)}`;
  }

  private generateGradientPlaceholder(width: number, height: number, category: string): string {
    const seed = this.hashString(category);
    const hue1 = seed % 360;
    const hue2 = (seed + 60) % 360;
    
    const svg = `
      <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:hsl(${hue1}, 60%, 70%);stop-opacity:1" />
            <stop offset="100%" style="stop-color:hsl(${hue2}, 60%, 80%);stop-opacity:1" />
    </linearGradient>
        </defs>
        <rect width="100%" height="100%" fill="url(#grad)" />
    </svg>
    `;

    return `data:image/svg+xml;base64,${btoa(svg)}`;
  }

  private generatePatternPlaceholder(width: number, height: number, category: string): string {
    const seed = this.hashString(category);
    const hue = seed % 360;
    
    const svg = `
      <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <pattern id="pattern" x="0" y="0" width="40" height="40" patternUnits="userSpaceOnUse">
            <circle cx="20" cy="20" r="3" fill="hsl(${hue}, 40%, 60%)" opacity="0.3"/>
    </pattern>
        </defs>
        <rect width="100%" height="100%" fill="hsl(${hue}, 50%, 95%)"/>
        <rect width="100%" height="100%" fill="url(#pattern)"/>
    </svg>
    `;

    return `data:image/svg+xml;base64,${btoa(svg)}`;
  }

  private hashString(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash);
  }

  /**
   * Clear failed URLs cache (useful for retry logic)
   */
  clearFailedCache(): void {
    this.failedUrls.clear();
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): { cacheSize: number; failedUrls: number } {
    return {
  cacheSize: this.cache.size;
      failedUrls: this.failedUrls.size
    };
  }
}

// Export singleton instance and utility functions
export const imageService = ImageService.getInstance();

/**
 * React hook for image loading with fallbacks
 */
export function useImageWithFallback(primaryUrl: string, options: ImageOptions = {}) {
  const [imageUrl, setImageUrl] = React.useState<string>(primaryUrl);
  const [isLoading, setIsLoading] = React.useState(true);
  const [hasError, setHasError] = React.useState(false);

  React.useEffect(() => {
    let mounted = true;
    
    setIsLoading(true);
    setHasError(false);
    
    imageService.getImageWithFallback(primaryUrl, options)
      .then(url => {
        if (mounted) {
          setImageUrl(url);
          setHasError(url !== primaryUrl);
        }
      })
      .finally(() => {
        if (mounted) {
          setIsLoading(false);
        }
      });

    return () => { mounted = false; };
  }, [primaryUrl, options]);

  return { imageUrl, isLoading, hasError };
}

/**
 * Improved getSafeImage function with better fallbacks
 */
export function getSafeImageUrl(
  type: 'AVATARS' | 'POSTS' | 'COVERS' | 'EVENTS';
  _index: number
): string {
  const dimensions = {
    AVATARS: { width: 400, height: 400 },
    POSTS: { width: 800, height: 600 },
    COVERS: { width: 1200, height: 400 },
    EVENTS: { width: 800, height: 600 }
  };

  const dim = dimensions[type];
  return imageService.getPlaceholderImage({
  width: dim.width;
  height: dim.height;
  category: type.toLowerCase();
    fallbackType: type === 'AVATARS' ? 'gradient' : 'pattern'
  });
}

export default imageService;