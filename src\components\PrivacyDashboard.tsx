import React, { useState } from 'react';
import {
  Shield,
  Settings,
  Activity,
  Users,
  Lock,
  ChevronRight
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { cn } from '@/lib/utils';

// Import privacy components
import ActivityPrivacyControls from './ActivityPrivacyControls';
import PrivacyAuditLog from './PrivacyAuditLog';
import PrivacyShortcuts from './PrivacyShortcuts';
// import EnhancedPrivacySettings from './EnhancedPrivacySettings'; // TODO: Create this component

// Import services
import { unifiedPrivacyService as GranularPrivacyService, ActivityPrivacySettings, PrivacySettings, AdvancedPrivacySettings } from '@/services/UnifiedPrivacyService';
import { PrivacyService } from '@/services/messaging/PrivacyService';
import { unifiedPrivacyService } from "@/services/UnifiedPrivacyService";

// Create placeholder for AdvancedPrivacyService
const AdvancedPrivacyService = {
  getInstance: () => ({
    getAdvancedSettings: (_userId: string) => ({
      dataExportRequest: false,
      dataDeletionRequest: false,
      advertisingConsent: true,
      analyticsConsent: true
    })
  })
};

interface PrivacyDashboardProps {
  userId: string;
  className?: string;
}

interface PrivacyMetric {
  label: string, value: number, max: number, color: string, icon: React.ComponentType<{ className?: string }>;
}

const PrivacyDashboard: React.FC<PrivacyDashboardProps> = ({
  userId,
  className
}) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [privacySettingsOpen, setPrivacySettingsOpen] = useState(false);

  // Get privacy metrics
  const getPrivacyMetrics = (): PrivacyMetric[] => {
    const basicSettings = unifiedPrivacyService.getUserPrivacySettings(userId);
    const advancedSettings = unifiedPrivacyService.getUserPrivacySettings(userId);
    const activitySettings = GranularPrivacyService.getActivityPrivacySettings(userId);
    const complianceReport = GranularPrivacyService.generateComplianceReport(userId);

    // Calculate metrics
    let profileScore = 0;
    if (basicSettings.profileVisibility === 'private') profileScore = 100;
    else if (basicSettings.profileVisibility === 'friends') profileScore = 75;
    else profileScore = 25;

    let activityScore = 0;
    let activityCount = 0;
    Object.values(activitySettings).forEach((setting: ActivityPrivacySettings[keyof ActivityPrivacySettings]) => {
      if (setting.visibility) {
        activityCount++;
        if (setting.visibility === 'only_me') activityScore += 100;
        else if (setting.visibility === 'friends') activityScore += 50;
      }
    });
    activityScore = activityCount > 0 ? activityScore / activityCount : 0;

    const securityScore = advancedSettings.twoFactorEnabled ? 100 : 0;

    return [
      {
        label: 'Overall Privacy',
        value: complianceReport.score,
        max: 100,
        color: 'bg-blue-500',
        icon: Shield
      },
      {
        label: 'Profile Privacy',
        value: profileScore,
        max: 100,
        color: 'bg-green-500',
        icon: Users
      },
      {
        label: 'Activity Privacy',
        value: activityScore,
        max: 100,
        color: 'bg-purple-500',
        icon: Activity
      },
      {
        label: 'Security',
        value: securityScore,
        max: 100,
        color: 'bg-orange-500',
        icon: Lock
      }
    ];
  };

  const metrics = getPrivacyMetrics();

  // Get recent privacy changes
  const getRecentChanges = () => {
    const auditLog = GranularPrivacyService.getPrivacyAuditLog(userId, {
      startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // Last 7 days
    });
    return auditLog.slice(0, 5);
  };

  const recentChanges = getRecentChanges();

  return (
    <div className={cn('space-y-6', className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <Shield className="w-8 h-8 text-blue-600" />
            Privacy Dashboard
          </h1>
          <p className="text-muted-foreground mt-1">
            Manage your privacy settings and monitor your data protection
          </p>
    </div>
        <Button
          onClick={() => setPrivacySettingsOpen(true)} className="gap-2"
        >
          <Settings className="w-4 h-4" />
          Privacy Settings
        </Button>
    </div>
      {/* Privacy Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {metrics.map((metric, index) => {
          const Icon = metric.icon;
          return (
            <Card key={index}>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between mb-2">
                  <Icon className="w-5 h-5 text-muted-foreground" />
                  <span className="text-2xl font-bold">{metric.value}%</span>
    </div>
                <p className="text-sm font-medium">{metric.label}</p>
                <Progress
                  value={metric.value} max={metric.max}, className={cn('mt-2 h-2', metric.color)}
                />
    </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-4 w-full">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="activity">Activity Privacy</TabsTrigger>
          <TabsTrigger value="shortcuts">Quick Settings</TabsTrigger>
          <TabsTrigger value="audit">Audit Log</TabsTrigger>
    </TabsList>
        <TabsContent value="overview" className="space-y-6">
          {/* Privacy Status */}
          <Card>
            <CardHeader>
              <CardTitle>Privacy Status</CardTitle>
              <CardDescription>
                Your current privacy configuration and recommendations
              </CardDescription>
    </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Current Settings Summary */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <h4 className="font-medium">Profile Settings</h4>
                    <div className="space-y-1">
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">Profile Visibility</span>
                        <Badge variant="secondary">
                          {unifiedPrivacyService.getUserPrivacySettings(userId).profileVisibility}
                        </Badge>
    </div>
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">Post Visibility</span>
                        <Badge variant="secondary">
                          {unifiedPrivacyService.getUserPrivacySettings(userId).postVisibility}
                        </Badge>
    </div>
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">Location Sharing</span>
                        <Badge variant={unifiedPrivacyService.getUserPrivacySettings(userId).locationSharing ? "destructive" : "secondary"}>
                          {unifiedPrivacyService.getUserPrivacySettings(userId).locationSharing ? 'On' : 'Off'}
                        </Badge>
    </div>
                    </div>
    </div>
                  <div className="space-y-2">
                    <h4 className="font-medium">Security Settings</h4>
                    <div className="space-y-1">
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">Two-Factor Auth</span>
                        <Badge variant={unifiedPrivacyService.getAdvancedSettings(userId).twoFactorEnabled ? "success" : "destructive"}>
                          {unifiedPrivacyService.getAdvancedSettings(userId).twoFactorEnabled ? 'Enabled' : 'Disabled'}
                        </Badge>
    </div>
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">Login Approvals</span>
                        <Badge variant="secondary">
                          {unifiedPrivacyService.getAdvancedSettings(userId).loginApprovals ? 'On' : 'Off'}
                        </Badge>
    </div>
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">Trusted Devices</span>
                        <Badge variant="secondary">
                          {unifiedPrivacyService.getTrustedDevices(userId).length}
                        </Badge>
    </div>
                    </div>
    </div>
                </div>

                {/* Recent Changes */}
                {recentChanges.length > 0 && (
                  <div className="mt-6 pt-6 border-t">
                    <h4 className="font-medium mb-3">Recent Privacy Changes</h4>
                    <div className="space-y-2">
                      {recentChanges.map((change) => (
                        <div key={change.id} className="flex items-center justify-between text-sm">
                          <span className="text-muted-foreground">
                            {change.category.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                          </span>
                          <span className="text-xs text-muted-foreground">
                            {new Date(change.timestamp).toLocaleDateString()}
                          </span>
    </div>
                      ))}
                    </div>
    </div>
                )}
              </div>
    </CardContent>
          </Card>

          {/* Privacy Recommendations */}
          <Card>
            <CardHeader>
              <CardTitle>Privacy Recommendations</CardTitle>
              <CardDescription>
                Suggestions to improve your privacy and security
              </CardDescription>
    </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {unifiedPrivacyService.generateComplianceReport(userId).recommendations.map((rec, index) => (
                  <div key={index} className="flex items-start gap-3 p-3 rounded-lg bg-muted/50">
                    <ChevronRight className="w-4 h-4 text-muted-foreground mt-0.5" />
                    <div className="flex-1">
                      <p className="text-sm">{rec}</p>
    </div>
                  </div>
                ))}
              </div>
    </CardContent>
          </Card>
    </TabsContent>
        <TabsContent value="activity">
          <ActivityPrivacyControls userId={userId} />
    </TabsContent>
        <TabsContent value="shortcuts">
          <PrivacyShortcuts userId={userId} />
    </TabsContent>
        <TabsContent value="audit">
          <PrivacyAuditLog userId={userId} />
    </TabsContent>
      </Tabs>

      {/* Enhanced Privacy Settings Modal */}
      {/* TODO: Implement EnhancedPrivacySettings component
      <EnhancedPrivacySettings
        isOpen={privacySettingsOpen} onClose={() => setPrivacySettingsOpen(false)}, onSave={(_settings) => {
          // Handle saving settings
          setPrivacySettingsOpen(false);
        }}
      />
      */}
    </div>
  );
};

export default PrivacyDashboard;
