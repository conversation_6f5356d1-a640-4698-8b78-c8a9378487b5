import React, { useState, useRef, useCallback } from 'react';
import { X, Tag, Search, Check, Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';

interface Friend {
  id: string, name: string, avatar: string, username: string;
}

interface PhotoTag {
  id: string, friendId: string, friend: Friend, x: number; // Percentage from left
  y: number; // Percentage from top
  width: number, height: number;
}

interface EnhancedPhotoTaggerProps {
  imageUrl: string;
  existingTags?: PhotoTag[];
  friends: Friend[], onTagsChange: (tags: PhotoTag[]) => void; onClose: () => void; isOpen: boolean;
}

const EnhancedPhotoTagger: React.FC<EnhancedPhotoTaggerProps> = ({
  imageUrl,
  existingTags = [],
  friends,
  onTagsChange,
  onClose,
  isOpen
}) => {
  const [tags, setTags] = useState<PhotoTag[]>(existingTags);
  const [isTagging, setIsTagging] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [selectedFriend, setSelectedFriend] = useState<Friend | null>(null);
  
  const [tagPosition, setTagPosition] = useState<{ x: number, y: number } | null>(null);
  const [showFriendsList, setShowFriendsList] = useState(false);
  const imageRef = useRef<HTMLImageElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  const filteredFriends = friends.filter(friend =>
    friend.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    friend.username.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleImageClick = useCallback((event: React.MouseEvent<HTMLImageElement>) => {
    if (!isTagging || !imageRef.current || !containerRef.current) return;

    const rect = imageRef.current.getBoundingClientRect();
    containerRef.current.getBoundingClientRect();
    
    const x = ((event.clientX - rect.left) / rect.width) * 100;
    const y = ((event.clientY - rect.top) / rect.height) * 100;

    setTagPosition({ x, y });
    setShowFriendsList(true);
    setSearchQuery('');
  }, [isTagging]);

  const handleFriendSelect = (friend: Friend) => {
    if (!tagPosition) return;

    const newTag: PhotoTag = {
      id: `tag-${Date.now()}`,
      friendId: friend.id,
      friend,
      x: tagPosition.x,
      y: tagPosition.y,
      width: 15, // Default tag size
      height: 8
    };

    const updatedTags = [...tags, newTag];
    setTags(updatedTags);
    onTagsChange(updatedTags);
    
    setTagPosition(null);
    setShowFriendsList(false);
    setSelectedFriend(null);
    setIsTagging(false);
    
    toast.success(`Tagged ${friend.name}`);
  };

  const handleRemoveTag = (tagId: string) => {
    const updatedTags = tags.filter(tag => tag.id !== tagId);
    setTags(updatedTags);
    onTagsChange(updatedTags);
    toast.info('Tag removed');
  };

  const startTagging = () => {
    setIsTagging(true);
    toast.info('Click on the photo to tag someone');
  };

  const cancelTagging = () => {
    setIsTagging(false);
    setTagPosition(null);
    setShowFriendsList(false);
    setSelectedFriend(null);
  };

  const handleSave = () => {
    onTagsChange(tags);
    onClose();
    toast.success(`Photo saved with ${tags.length} tag${tags.length !== 1 ? 's' : ''}`);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-4">
      <div className="bg-white dark:bg-gray-900 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b dark:border-gray-700">
          <h2 className="text-xl font-semibold dark:text-white">Tag People</h2>
          <div className="flex items-center space-x-2">
            <Badge variant="secondary">
              {tags.length} tag{tags.length !== 1 ? 's' : ''}
            </Badge>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="w-4 h-4" />
    </Button>
          </div>
    </div>
        <div className="flex flex-col lg:flex-row">
          {/* Image Area */}
          <div className="flex-1 p-4">
            <div 
              ref={containerRef} className="relative inline-block max-w-full"
            >
              <img
                ref={imageRef} src={imageUrl}, alt="Photo to tag"
                className={`max-w-full h-auto rounded-lg ${isTagging ? 'cursor-crosshair' : 'cursor-default'}`}, onClick={handleImageClick}
              />
              
              {/* Existing Tags */}
              {tags.map((tag) => (
                <div
                  key={tag.id} className="absolute border-2 border-blue-500 bg-blue-500/20 rounded"
                  style={{
                    left: `${tag.x}%`,
                    top: `${tag.y}%`,
                    width: `${tag.width}%`,
                    height: `${tag.height}%`,
                    transform: 'translate(-50%, -50%)'
                  }}
                >
                  <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 bg-blue-500 text-white px-2 py-1 rounded text-xs whitespace-nowrap">
                    {tag.friend.name}
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleRemoveTag(tag.id);
                      }}, className="ml-2 hover:text-red-300"
                    >
                      <X className="w-3 h-3 inline" />
    </button>
                  </div>
    </div>
              ))}
              
              {/* New Tag Position */}
              {tagPosition && (
                <div
                  className="absolute w-4 h-4 border-2 border-yellow-500 bg-yellow-500/50 rounded-full"
                  style={{
                    left: `${tagPosition.x}%`,
                    top: `${tagPosition.y}%`,
                    transform: 'translate(-50%, -50%)'
                  }}
                />
              )}
            </div>

            {/* Tagging Controls */}
            <div className="mt-4 flex items-center space-x-2">
              {!isTagging ? (
                <Button onClick={startTagging} className="flex items-center space-x-2">
                  <Tag className="w-4 h-4" />
                  <span>Tag People</span>
    </Button>
              ) : (
                <div className="flex items-center space-x-2">
                  <Button variant="outline" onClick={cancelTagging}>
                    Cancel
                  </Button>
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    Click on the photo to tag someone
                  </span>
    </div>
              )}
            </div>
    </div>
          {/* Friends List Sidebar */}
          <div className="w-full lg:w-80 border-l dark:border-gray-700 p-4">
            <div className="space-y-4">
              <div>
                <h3 className="font-semibold mb-2 dark:text-white">Tag Friends</h3>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                  <Input
                    placeholder="Search friends..."
                    value={searchQuery} onChange={(e) => setSearchQuery(e.target.value)}, className="pl-10"
                  />
    </div>
              </div>

              {/* Friends List */}
              <div className="max-h-64 overflow-y-auto space-y-2">
                {filteredFriends.map((friend) => {
                  const isTagged = tags.some(tag => tag.friendId === friend.id);
                  return (
                    <Card 
                      key={friend.id} className={`cursor-pointer transition-colors ${
                        isTagged ? 'bg-blue-50 border-blue-200 dark:bg-blue-900/20' : 'hover:bg-gray-50 dark:hover:bg-gray-800'
                      }`}, onClick={() => !isTagged && showFriendsList && handleFriendSelect(friend)}
                    >
                      <CardContent className="p-3">
                        <div className="flex items-center space-x-3">
                          <Avatar className="w-8 h-8">
                            <AvatarImage src={friend.avatar} />
                            <AvatarFallback>{friend.name.charAt(0)}</AvatarFallback>
    </Avatar>
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium truncate dark:text-white">
                              {friend.name}
                            </p>
                            <p className="text-xs text-gray-500 truncate">
                              @{friend.username}
                            </p>
    </div>
                          {isTagged ? (
                            <Check className="w-4 h-4 text-blue-500" />
                          ) : showFriendsList ? (
                            <Plus className="w-4 h-4 text-gray-400" />
                          ) : null}
                        </div>
    </CardContent>
                    </Card>
                  );
                })}
              </div>

              {/* Tagged Friends Summary */}
              {tags.length > 0 && (
                <div>
                  <h4 className="font-medium mb-2 dark:text-white">Tagged ({tags.length})</h4>
                  <div className="space-y-1">
                    {tags.map((tag) => (
                      <div key={tag.id} className="flex items-center justify-between text-sm">
                        <span className="dark:text-gray-300">{tag.friend.name}</span>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleRemoveTag(tag.id)} className="h-6 w-6 p-0 text-gray-400 hover:text-red-500"
                        >
                          <X className="w-3 h-3" />
    </Button>
                      </div>
                    ))}
                  </div>
    </div>
              )}
            </div>
    </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-4 border-t dark:border-gray-700">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSave}>
            Save Tags
          </Button>
    </div>
      </div>
    </div>
  );
};

export default EnhancedPhotoTagger;
