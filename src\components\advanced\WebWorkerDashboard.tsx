import React, { useState, useCallback, useMemo } from 'react';
import { 
  <PERSON><PERSON>, 
  <PERSON>pu, 
  Bar<PERSON>hart3, 
  Filter, 
  ArrowUpDown, 
  Database, 
  TrendingUp, 
  Play, 
  Square, 
  RefreshCw,
  Activity
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import {
  useDataProcessing,
  useAnalyticsGeneration,
  usePostSorting,
  usePostFiltering,
  useCacheOptimization,
  useWebWorkerStats
} from '@/hooks/useWebWorker';

interface ProcessedData {
  processedPosts?: unknown[];
  totalProcessed?: number;
  filtered?: unknown[];
  totalFiltered?: number;
}

interface AnalyticsData {
  totalPosts: number, totalLikes: number, totalComments: number, avgEngagement: number;
}

interface WebWorkerDashboardProps {
  posts: unknown[];
  className?: string;
}

// Safe wrapper component that handles React availability check
const WebWorkerDashboard: React.FC<WebWorkerDashboardProps> = (props) => {
  // Check React availability first
  if (typeof React === 'undefined' || !React.useState) {
    return (
      <div className="p-8 text-center">
        <p className="text-red-500">React context not available. Please refresh the page.</p>
    </div>
    );
  }
  
  return <WebWorkerDashboardContent {...props} />;
};

// Main component with all hooks
const WebWorkerDashboardContent: React.FC<WebWorkerDashboardProps> = ({ 
  posts, 
  className = '' 
}) => {
  const [activeDemo, setActiveDemo] = useState<string>('processing');
  const [autoExecute, setAutoExecute] = useState(false);
  
  // All hooks must be called unconditionally
  const workerStats = useWebWorkerStats();

  // Generate sample cache entries for cache optimization demo
  const sampleCacheEntries = useMemo(() => {
    return Array.from({ length: 50 }, (_, i) => ({
      key: `cache-item-${i}`,
      data: { id: i, content: `Sample data ${i}` },
      size: Math.random() * 2, // MB
      timestamp: Date.now() - Math.random() * 86400000,
      lastAccessed: Date.now() - Math.random() * 3600000,
      accessCount: Math.floor(Math.random() * 20),
      priority: ['low', 'medium', 'high'][Math.floor(Math.random() * 3)]
    }));
  }, []);

  // WebWorker hooks for different operations
  const analytics = useAnalyticsGeneration(
    posts,
    { priority: 'high', autoExecute }
  );

  const sorting = usePostSorting(
    posts?.length > 0 ? { posts, criteria: 'engagement', direction: 'desc' } : undefined,
    { priority: 'medium' }
  );

  const filtering = usePostFiltering(
    posts?.length > 0 ? { 
      posts, 
      filters: { 
        categories: ['trending', 'recent'],
        minEngagement: 10
      } 
    } : undefined,
    { priority: 'medium' }
  );

  const cacheOptimization = useCacheOptimization(
    { entries: sampleCacheEntries, maxSize: 25, strategy: 'lru' },
    { priority: 'low' }
  );

  const dataProcessing = useDataProcessing(
    posts?.length > 0 ? {
      posts,
      filters: { category: 'trending' },
      sortBy: 'engagement'
    } : undefined,
    { priority: 'high' }
  );

  const handleExecuteAll = useCallback(() => {
    if (posts?.length > 0) {
      analytics.execute({ posts });
      sorting.execute({ posts, criteria: 'engagement', direction: 'desc' });
      filtering.execute({ posts, filters: { categories: ['trending'], minEngagement: 5 } });
      dataProcessing.execute({ posts, filters: { category: 'trending' }, sortBy: 'engagement' });
    }
    cacheOptimization.execute({ entries: sampleCacheEntries, maxSize: 25, strategy: 'lru' });
  }, [posts, analytics, sorting, filtering, dataProcessing, cacheOptimization, sampleCacheEntries]);

  const getDemoStatus = (demo: { data: unknown, loading: boolean, error: Error | null, progress: number, executed: boolean }) => {
    if (demo.loading) return { status: 'running', color: 'blue' };
    if (demo.error) return { status: 'error', color: 'red' };
    if (demo.data) return { status: 'completed', color: 'green' };
    return { status: 'ready', color: 'gray' };
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            WebWorker Background Processing
          </CardTitle>
    </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
            <div className="text-center">
              <div className="text-2xl font-bold">{workerStats.totalWorkers}</div>
              <div className="text-sm text-muted-foreground">Total Workers</div>
    </div>
            <div className="text-center">
              <div className="text-2xl font-bold">{workerStats.activeWorkers}</div>
              <div className="text-sm text-muted-foreground">Active Workers</div>
    </div>
            <div className="text-center">
              <div className="text-2xl font-bold">{workerStats.queuedTasks}</div>
              <div className="text-sm text-muted-foreground">Queued Tasks</div>
    </div>
            <div className="text-center">
              <div className="text-2xl font-bold">{workerStats.activeTasks}</div>
              <div className="text-sm text-muted-foreground">Running Tasks</div>
    </div>
          </div>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Switch
                  id="auto-execute"
                  checked={autoExecute} onCheckedChange={setAutoExecute}
                />
                <Label htmlFor="auto-execute">Auto Execute</Label>
    </div>
            </div>
            
            <div className="flex gap-2">
              <Button onClick={handleExecuteAll} disabled={!posts?.length}>
                <Play className="h-4 w-4 mr-2" />
                Execute All Demos
              </Button>
    </div>
          </div>
    </CardContent>
      </Card>

      {/* Worker Task Demos */}
      <Tabs value={activeDemo} onValueChange={setActiveDemo}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="processing">
            <Cpu className="h-4 w-4 mr-2" />
            Processing
          </TabsTrigger>
          <TabsTrigger value="analytics">
            <BarChart3 className="h-4 w-4 mr-2" />
            Analytics
          </TabsTrigger>
          <TabsTrigger value="sorting">
            <ArrowUpDown className="h-4 w-4 mr-2" />
            Sorting
          </TabsTrigger>
          <TabsTrigger value="filtering">
            <Filter className="h-4 w-4 mr-2" />
            Filtering
          </TabsTrigger>
          <TabsTrigger value="cache">
            <Database className="h-4 w-4 mr-2" />
            Cache
          </TabsTrigger>
    </TabsList>
        <TabsContent value="processing" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                Data Processing
                <Badge variant={getDemoStatus(dataProcessing).color === 'green' ? 'default' : 'secondary'}>
                  {getDemoStatus(dataProcessing).status}
                </Badge>
    </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  Process feed data with filtering and sorting in background workers.
                </p>
                
                {dataProcessing.loading && (
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span>Processing data...</span>
                      <span>{dataProcessing.progress}%</span>
    </div>
                    <Progress value={dataProcessing.progress} className="h-2" />
    </div>
                )}

                {dataProcessing.error && (
                  <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                    <p className="text-sm text-red-600">{(dataProcessing.error as Error).message}</p>
    </div>
                )}

                {dataProcessing.data && typeof dataProcessing.data === 'object' ? (
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-3 bg-green-50 rounded-lg">
                      <div className="text-2xl font-bold">
                        {(dataProcessing.data as ProcessedData)?.processedPosts?.length || 0}
                      </div>
                      <div className="text-sm text-muted-foreground">Processed Posts</div>
    </div>
                    <div className="text-center p-3 bg-blue-50 rounded-lg">
                      <div className="text-2xl font-bold">
                        {(dataProcessing.data as ProcessedData)?.totalProcessed || 0}
                      </div>
                      <div className="text-sm text-muted-foreground">Total Processed</div>
    </div>
                  </div>
                ) : null}

                <div className="flex gap-2">
                  <Button
                    onClick={() => dataProcessing.execute({
                      posts: posts || [],
                      filters: { category: 'trending' },
                      sortBy: 'engagement'
                    })}, disabled={dataProcessing.loading || !posts?.length}
                  >
                    {dataProcessing.loading ? (
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <Play className="h-4 w-4 mr-2" />
                    )}
                    Process Data
                  </Button>
                  
                  {dataProcessing.loading && (
                    <Button variant="outline" onClick={dataProcessing.cancel}>
                      <Square className="h-4 w-4 mr-2" />
                      Cancel
                    </Button>
                  )}
                </div>
    </div>
            </CardContent>
    </Card>
        </TabsContent>

        <TabsContent value="analytics" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                Analytics Generation
                <Badge variant={getDemoStatus(analytics).color === 'green' ? 'default' : 'secondary'}>
                  {getDemoStatus(analytics).status}
                </Badge>
    </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  Generate comprehensive analytics from post data using background processing.
                </p>

                {analytics.loading && (
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span>Generating analytics...</span>
                      <span>{analytics.progress}%</span>
    </div>
                    <Progress value={analytics.progress} className="h-2" />
    </div>
                )}

                {analytics.data && (
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="text-center p-3 bg-blue-50 rounded-lg">
                      <div className="text-2xl font-bold">{(analytics.data as AnalyticsData).totalPosts}</div>
                      <div className="text-sm text-muted-foreground">Total Posts</div>
    </div>
                    <div className="text-center p-3 bg-green-50 rounded-lg">
                      <div className="text-2xl font-bold">{(analytics.data as AnalyticsData).totalLikes}</div>
                      <div className="text-sm text-muted-foreground">Total Likes</div>
    </div>
                    <div className="text-center p-3 bg-purple-50 rounded-lg">
                      <div className="text-2xl font-bold">{(analytics.data as AnalyticsData).totalComments}</div>
                      <div className="text-sm text-muted-foreground">Total Comments</div>
    </div>
                    <div className="text-center p-3 bg-orange-50 rounded-lg">
                      <div className="text-2xl font-bold">{Math.round((analytics.data as AnalyticsData).avgEngagement)}</div>
                      <div className="text-sm text-muted-foreground">Avg Engagement</div>
    </div>
                  </div>
                )}

                <Button
                  onClick={() => analytics.execute({ posts: posts || [] })}, disabled={analytics.loading || !posts?.length}
                >
                  {analytics.loading ? (
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <TrendingUp className="h-4 w-4 mr-2" />
                  )}
                  Generate Analytics
                </Button>
    </div>
            </CardContent>
    </Card>
        </TabsContent>

        <TabsContent value="sorting" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                Post Sorting
                <Badge variant={getDemoStatus(sorting).color === 'green' ? 'default' : 'secondary'}>
                  {getDemoStatus(sorting).status}
                </Badge>
    </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  Sort large datasets efficiently using background workers.
                </p>

                {sorting.loading && (
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span>Sorting posts...</span>
                      <span>{sorting.progress}%</span>
    </div>
                    <Progress value={sorting.progress} className="h-2" />
    </div>
                )}

                {sorting.data && (
                  <div className="space-y-2">
                    <div className="text-sm font-medium">Sorted Results (Top 3):</div>
                    {(sorting.data as Array<{ id: string, title: string, content: string, metrics: { likes: number, comments: number, shares: number } }>).slice(0, 3).map((post, index: number) => (
                      <div key={post.id} className="p-2 bg-gray-50 rounded text-sm">
                        <span className="font-medium">#{index + 1}</span> {post.content.slice(0, 50)}...
                        <span className="text-muted-foreground ml-2">
                          ({post.metrics.likes + post.metrics.comments + post.metrics.shares} engagement)
                        </span>
    </div>
                    ))}
                  </div>
                )}

                <Button
                  onClick={() => sorting.execute({
                    posts: posts || [],
                    criteria: 'engagement',
                    direction: 'desc'
                  })}, disabled={sorting.loading || !posts?.length}
                >
                  {sorting.loading ? (
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <ArrowUpDown className="h-4 w-4 mr-2" />
                  )}
                  Sort by Engagement
                </Button>
    </div>
            </CardContent>
    </Card>
        </TabsContent>

        <TabsContent value="filtering" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                Post Filtering
                <Badge variant={getDemoStatus(filtering).color === 'green' ? 'default' : 'secondary'}>
                  {getDemoStatus(filtering).status}
                </Badge>
    </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  Filter posts with complex criteria using background processing.
                </p>

                {filtering.loading && (
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span>Filtering posts...</span>
                      <span>{filtering.progress}%</span>
    </div>
                    <Progress value={filtering.progress} className="h-2" />
    </div>
                )}

                {filtering.data && (
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-3 bg-blue-50 rounded-lg">
                      <div className="text-2xl font-bold">{posts?.length || 0}</div>
                      <div className="text-sm text-muted-foreground">Original Count</div>
    </div>
                    <div className="text-center p-3 bg-green-50 rounded-lg">
                      <div className="text-2xl font-bold">{(filtering.data as any[]).length}</div>
                      <div className="text-sm text-muted-foreground">Filtered Count</div>
    </div>
                  </div>
                )}

                <Button
                  onClick={() => filtering.execute({
                    posts: posts || [],
                    filters: {
                      categories: ['trending', 'recent'],
                      minEngagement: 10
                    }
                  })}, disabled={filtering.loading || !posts?.length}
                >
                  {filtering.loading ? (
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Filter className="h-4 w-4 mr-2" />
                  )}
                  Filter Posts
                </Button>
    </div>
            </CardContent>
    </Card>
        </TabsContent>

        <TabsContent value="cache" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                Cache Optimization
                <Badge variant={getDemoStatus(cacheOptimization).color === 'green' ? 'default' : 'secondary'}>
                  {getDemoStatus(cacheOptimization).status}
                </Badge>
    </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  Optimize cache entries using LRU and other strategies in background workers.
                </p>

                {cacheOptimization.loading && (
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span>Optimizing cache...</span>
                      <span>{cacheOptimization.progress}%</span>
    </div>
                    <Progress value={cacheOptimization.progress} className="h-2" />
    </div>
                )}

                {cacheOptimization.data && (
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="text-center p-3 bg-blue-50 rounded-lg">
                      <div className="text-2xl font-bold">{sampleCacheEntries.length}</div>
                      <div className="text-sm text-muted-foreground">Original Items</div>
    </div>
                    <div className="text-center p-3 bg-green-50 rounded-lg">
                      <div className="text-2xl font-bold">{(cacheOptimization.data as { toKeep: unknown[], toEvict: unknown[], stats: { optimizedSize: number } }).toKeep.length}</div>
                      <div className="text-sm text-muted-foreground">Items Kept</div>
    </div>
                    <div className="text-center p-3 bg-red-50 rounded-lg">
                      <div className="text-2xl font-bold">{(cacheOptimization.data as { toKeep: unknown[], toEvict: unknown[], stats: { optimizedSize: number } }).toEvict.length}</div>
                      <div className="text-sm text-muted-foreground">Items Evicted</div>
    </div>
                    <div className="text-center p-3 bg-purple-50 rounded-lg">
                      <div className="text-2xl font-bold">
                        {(cacheOptimization.data as { toKeep: unknown[], toEvict: unknown[], stats: { optimizedSize: number } }).stats.optimizedSize.toFixed(1)}MB
                      </div>
                      <div className="text-sm text-muted-foreground">Final Size</div>
    </div>
                  </div>
                )}

                <Button
                  onClick={() => cacheOptimization.execute({
                    entries: sampleCacheEntries,
                    maxSize: 25,
                    strategy: 'lru'
                  })}, disabled={cacheOptimization.loading}
                >
                  {cacheOptimization.loading ? (
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Database className="h-4 w-4 mr-2" />
                  )}
                  Optimize Cache
                </Button>
    </div>
            </CardContent>
    </Card>
        </TabsContent>
    </Tabs>
      {/* Worker Performance Monitor */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Worker Performance Monitor
          </CardTitle>
    </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium mb-2">Worker Task Distribution</h4>
              <div className="space-y-2">
                {workerStats.workerTaskCounts.map((count, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <span className="text-sm">Worker {index + 1}</span>
                    <div className="flex items-center gap-2">
                      <Progress value={(count / Math.max(...workerStats.workerTaskCounts, 1)) * 100} className="w-20 h-2" />
                      <span className="text-sm font-mono">{count}</span>
    </div>
                  </div>
                ))}
              </div>
    </div>
            <div>
              <h4 className="font-medium mb-2">System Load</h4>
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm">CPU Cores Available</span>
                  <Badge>{navigator.hardwareConcurrency || 'Unknown'}</Badge>
    </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Workers Utilized</span>
                  <Badge variant="outline">
                    {workerStats.activeWorkers}/{workerStats.totalWorkers}
                  </Badge>
    </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Queue Efficiency</span>
                  <Badge variant={workerStats.queuedTasks === 0 ? 'default' : 'secondary'}>
                    {workerStats.queuedTasks === 0 ? 'Optimal' : 'Backlogged'}
                  </Badge>
    </div>
              </div>
    </div>
          </div>
    </CardContent>
      </Card>
    </div>
  );
};

export default WebWorkerDashboard;