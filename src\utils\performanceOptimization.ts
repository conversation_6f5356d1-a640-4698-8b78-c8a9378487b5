import { useCallback, useRef, useEffect } from 'react';
import * as React from 'react';

/**
 * Performance optimization utilities for React components
 */

// Debounce hook for performance optimization
export function useDebounce<T extends (...args: unknown[]) => any>(
  callback: T; delay: number
): T {
  const timeoutRef = useRef<NodeJS.Timeout>();
  
  return useCallback((...args: Parameters<T>) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    timeoutRef.current = setTimeout(() => {
      callback(...args);
    }, delay);
  }, [callback, delay]) as T;
}

// Throttle hook for performance optimization
export function useThrottle<T extends (...args: unknown[]) => any>(
  callback: T; delay: number
): T {
  const lastCallRef = useRef<number>(0);
  
  return useCallback((...args: Parameters<T>) => {
    const now = Date.now();
    
    if (now - lastCallRef.current >= delay) {
      lastCallRef.current = now;
      callback(...args);
    }
  }, [callback, delay]) as T;
}

// Memoized stable reference hook
export function useStableCallback<T extends (...args: unknown[]) => any>(callback: T): T {
  const callbackRef = useRef<T>(callback);
  
  useEffect(() => {
    callbackRef.current = callback;
  });
  
  return useCallback((...args: Parameters<T>) => {
    return callbackRef.current(...args);
  }, []) as T;
}

// Previous value hook for optimization
export function usePrevious<T>(value: T): T | undefined {
  const ref = useRef<T>();
  
  useEffect(() => {
    ref.current = value;
  });
  
  return ref.current;
}

// Memoized object comparison
export function useDeepMemo<T>(factory: () => T; deps: React.DependencyList): T {
  const ref = useRef<{ deps: React.DependencyList, value: T }>();
  
  if (!ref.current || !areEqual(ref.current.deps, deps)) {
    ref.current = { deps, value: factory() };
  }
  
  return ref.current.value;
}

// Deep equality check for arrays and objects
function areEqual(a: readonly any[], b: readonly any[]): boolean {
  if (a.length !== b.length) return false;
  
  for (let i = 0; i < a.length; i++) {
    if (typeof a[i] === 'object' && typeof b[i] === 'object') {
      if (!deepEqual(a[i], b[i])) return false;
    } else if (a[i] !== b[i]) {
      return false;
    }
  }
  
  return true;
}

function deepEqual(a: unknown, b: unknown): boolean {
  if (a === b) return true;
  
  if (a == null || b == null) return false;
  
  if (typeof a !== 'object' || typeof b !== 'object') return false;
  
  const keysA = Object.keys(a);
  const keysB = Object.keys(b);
  
  if (keysA.length !== keysB.length) return false;
  
  for (const key of keysA) {
    if (!keysB.includes(key)) return false;
    if (!deepEqual(a[key], b[key])) return false;
  }
  
  return true;
}

// Performance measurement utilities
export function measurePerformance<T>(
  name: string, fn: () => T;
  logResults = true
): T {
  const start = performance.now();
  const result = fn();
  const end = performance.now();
  
  if (logResults && process.env.NODE_ENV === 'development') {
    console.log(`⚡ ${name}: ${(end - start).toFixed(2)}ms`);
  }
  
  return result;
}

export async function measureAsyncPerformance<T>(
  name: string, fn: () => Promise<T>;
  logResults = true
): Promise<T> {
  const start = performance.now();
  const result = await fn();
  const end = performance.now();
  
  if (logResults && process.env.NODE_ENV === 'development') {
    console.log(`⚡ ${name}: ${(end - start).toFixed(2)}ms`);
  }
  
  return result;
}

// Lazy loading utilities
export function lazyWithPreload<T extends React.ComponentType<any>>(
  importFn: () => Promise<{ default: T }>
): React.LazyExoticComponent<T> & { preload: () => void } {
  const lazyComponent = React.lazy(importFn) as React.LazyExoticComponent<T> & { preload: () => void };
  lazyComponent.preload = importFn;
  return lazyComponent;
}

// Memory optimization utilities
export function useMemoryOptimization() {
  const cleanupFunctions = useRef<(() => void)[]>([]);
  
  const addCleanup = useCallback((cleanup: () => void) => {
    cleanupFunctions.current.push(cleanup);
  }, []);
  
  useEffect(() => {
    return () => {
      cleanupFunctions.current.forEach(cleanup => cleanup());
      cleanupFunctions.current = [];
    };
  }, []);
  
  return { addCleanup };
}

// Bundle size optimization
export const _optimizeBundle = {
  // Lazy load heavy dependencies
  loadHeavyDependency: async <T>(importFn: () => Promise<T>): Promise<T> => {
    try {
      return await importFn();
    } catch (error) {
      console.error('Failed to load dependency:', error);
      throw error;
    }
  },
  
  // Preload critical resources
  preloadResource: (url: string, type: 'script' | 'style' | 'image' = 'script') => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.href = url;
    link.as = type;
    document.head.appendChild(link);
  },
  
  // Code splitting helper
  splitCode: <T extends React.ComponentType<any>>(importFn: () => Promise<{ default: T }>) => {
    return React.lazy(importFn);
  },

  // Bundle optimization utilities
  optimizeBundle: { // Preload critical resources
    preloadCriticalResources: () => {
      const criticalResources = [
        '/assets/fonts/inter.woff2' }
        '/assets/icons/sprite.svg'
      ];

      criticalResources.forEach(resource => {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.href = resource;
        link.as = resource.includes('.woff') ? 'font' : 'image';
        if (resource.includes('.woff')) {
          link.crossOrigin = 'anonymous';
        }
        document.head.appendChild(link);
      });
    },

    // Optimize images with lazy loading
    optimizeImages: () => {
      const images = document.querySelectorAll('img[data-src]');
      const imageObserver = new IntersectionObserver(_(entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const img = entry.target as HTMLImageElement;
            img.src = img.dataset.src || '';
            img.removeAttribute('data-src');
            imageObserver.unobserve(img);
          }
        });
      });

      images.forEach(img => imageObserver.observe(img));
    },

    // Service worker registration for caching
    registerServiceWorker: async () => {
      if ('serviceWorker' in navigator) {
        try {
          const registration = await navigator.serviceWorker.register('/sw.js');
          console.log('SW registered:', registration);
        } catch (error) {
          console.log('SW registration failed:', error);
        }
      }
    }
  }
};
