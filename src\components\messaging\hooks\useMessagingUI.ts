/**
 * Messaging UI Hook
 * Manages UI state and interactions for messaging components
 */

import React from 'react';
import { useWindowSize } from '@/hooks/useWindowSize';
import { useKeyboardNavigation } from '@/hooks/useKeyboardNavigation';
import { KEYBOARD_SHORTCUTS, SIZE_CONFIG, SizeVariant } from '../constants';
import type { AdvancedMessage, Conversation } from '@/types/messaging';

interface UseMessagingUIOptions {
  enableKeyboardNavigation?: boolean;
  enableMobileOptimizations?: boolean;
  autoDetectMobile?: boolean;
  responsiveBreakpoint?: number;
  enableVirtualization?: boolean;
  itemHeight?: number;
}

interface UseMessagingUIReturn {
  // Layout state
  isMobile: boolean, isTablet: boolean, screenSize: SizeVariant, layout: {
    showSidebar: boolean, sidebarWidth: number, contentWidth: number, orientation: 'portrait' | 'landscape';
  };
  
  // UI state
  selectedMessages: Set<string>, showMessageActions: boolean, activeInput: string | null, scrollToBottom: boolean, showEmojiPicker: boolean, showAttachmentMenu: boolean;
  
  // Actions
  toggleSidebar: () => void; selectMessage: (messageId: string) => void; selectMultipleMessages: (messageIds: string[]) => void; clearSelection: () => void; setShowMessageActions: (show: boolean) => void; focusInput: (inputId?: string) => void; toggleEmojiPicker: () => void; toggleAttachmentMenu: () => void;
  
  // Keyboard navigation
  navigateToMessage: (direction: 'up' | 'down') => void; handleKeyboardShortcut: (event: KeyboardEvent) => boolean;
  
  // Responsive utilities
  getResponsiveSize: (sizes: { sm?: number; md?: number; lg?: number }) => number;
  getResponsiveClasses: (classes: { sm?: string; md?: string; lg?: string }) => string;
  
  // Performance utilities
  shouldVirtualize: boolean, visibleMessageRange: { start: number, end: number };
  updateVisibleRange: (start: number, end: number) => void;
  
  // Accessibility
  announceMessage: (message: string) => void; getMessageIndex: (messageId: string) => number; getTotalMessagesCount: () => number;
}

export const useMessagingUI = (
  messages: AdvancedMessage[],
  conversations: Conversation[],
  options: UseMessagingUIOptions = {}
): UseMessagingUIReturn => {
  const {
    enableKeyboardNavigation = true,
    enableMobileOptimizations = true,
    autoDetectMobile = true,
    responsiveBreakpoint = 768,
    enableVirtualization = true,
    itemHeight = 100
  } = options;

  const { width, height } = useWindowSize();
  const keyboard = useKeyboardNavigation({ enabled: enableKeyboardNavigation });

  // Layout state
  const [showSidebar, setShowSidebar] = React.useState(() => !isMobile);
  const [sidebarWidth, setSidebarWidth] = React.useState(() => isTablet ? 280 : 320);

  // UI state
  const [selectedMessages, setSelectedMessages] = React.useState<Set<string>>(() => new Set());
  const [showMessageActions, setShowMessageActions] = React.useState(false);
  const [activeInput, setActiveInput] = React.useState<string | null>(null);
  const [scrollToBottom, setScrollToBottom] = React.useState(true);
  const [showEmojiPicker, setShowEmojiPicker] = React.useState(false);
  const [showAttachmentMenu, setShowAttachmentMenu] = React.useState(false);
  const [visibleMessageRange, setVisibleMessageRange] = React.useState(() => ({ start: 0, end: Math.min(50, messages.length) }));

  // Refs for accessibility
  const announcementRef = React.useRef<HTMLDivElement | null>(null);
  const lastAnnouncementRef = React.useRef<string>('');

  // Responsive calculations
  const isMobile = React.useMemo(() => {
    if (!autoDetectMobile) return false;
    return width < responsiveBreakpoint;
  }, [width, autoDetectMobile, responsiveBreakpoint]);

  const isTablet = React.useMemo(() => {
    return width >= responsiveBreakpoint && width < 1024;
  }, [width, responsiveBreakpoint]);

  const screenSize: SizeVariant = React.useMemo(() => {
    if (width < 640) return 'sm';
    if (width < 1024) return 'md';
    return 'lg';
  }, [width]);

  const layout = React.useMemo(() => {
    const actualSidebarWidth = isMobile ? 0 : (showSidebar ? sidebarWidth : 0);
    return {
      showSidebar: showSidebar && !isMobile,
      sidebarWidth: actualSidebarWidth,
      contentWidth: width - actualSidebarWidth,
      orientation: width > height ? 'landscape' as const : 'portrait' as const
    };
  }, [isMobile, showSidebar, sidebarWidth, width, height]);

  // Performance optimization - should virtualize
  const shouldVirtualize = React.useMemo(() => {
    return enableVirtualization && messages.length > 100;
  }, [enableVirtualization, messages.length]);

  // Auto-hide sidebar on mobile
  React.useEffect(() => {
    if (isMobile && showSidebar) {
      setShowSidebar(false);
    } else if (!isMobile && !showSidebar) {
      setShowSidebar(true);
    }
  }, [isMobile]);

  // Auto-adjust sidebar width based on screen size
  React.useEffect(() => {
    if (isTablet) {
      setSidebarWidth(280);
    } else if (!isMobile) {
      setSidebarWidth(320);
    }
  }, [isTablet, isMobile]);

  // Actions
  const toggleSidebar = React.useCallback(() => {
    setShowSidebar(prev => !prev);
  }, []);

  const selectMessage = React.useCallback((messageId: string) => {
    setSelectedMessages(prev => {
      const newSet = new Set(prev);
      if (newSet.has(messageId)) {
        newSet.delete(messageId);
      } else {
        newSet.add(messageId);
      }
      return newSet;
    });
  }, []);

  const selectMultipleMessages = React.useCallback((messageIds: string[]) => {
    setSelectedMessages(prev => {
      const newSet = new Set(prev);
      messageIds.forEach(id => newSet.add(id));
      return newSet;
    });
  }, []);

  const clearSelection = React.useCallback(() => {
    setSelectedMessages(new Set());
    setShowMessageActions(false);
  }, []);

  const focusInput = React.useCallback((inputId?: string) => {
    const targetInput = inputId || 'main-message-input';
    setActiveInput(targetInput);
    
    // Focus the actual input element
    const inputElement = document.getElementById(targetInput);
    if (inputElement) {
      inputElement.focus();
    }
  }, []);

  const toggleEmojiPicker = React.useCallback(() => {
    setShowEmojiPicker(prev => !prev);
    // Close other menus
    setShowAttachmentMenu(false);
  }, []);

  const toggleAttachmentMenu = React.useCallback(() => {
    setShowAttachmentMenu(prev => !prev);
    // Close other menus
    setShowEmojiPicker(false);
  }, []);

  // Keyboard navigation
  const navigateToMessage = React.useCallback((direction: 'up' | 'down') => {
    if (messages.length === 0) return;
    
    const currentIndex = selectedMessages.size === 1 
      ? messages.findIndex(m => selectedMessages.has(m.id))
      : -1;
    
    let newIndex: number;
    if (direction === 'up') {
      newIndex = currentIndex > 0 ? currentIndex - 1 : messages.length - 1;
    } else {
      newIndex = currentIndex < messages.length - 1 ? currentIndex + 1 : 0;
    }
    
    const targetMessage = messages[newIndex];
    if (targetMessage) {
      clearSelection();
      selectMessage(targetMessage.id);
      
      // Scroll to message
      const messageElement = document.getElementById(`message-${targetMessage.id}`);
      if (messageElement) {
        messageElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
    }
  }, [messages, selectedMessages, clearSelection, selectMessage]);

  const handleKeyboardShortcut = React.useCallback((event: KeyboardEvent): boolean => {
    if (!enableKeyboardNavigation) return false;
    
    const { key, metaKey, ctrlKey, shiftKey } = event;
    const modifier = metaKey || ctrlKey;
    
    // Global shortcuts
    switch (key) {
      case KEYBOARD_SHORTCUTS.ESCAPE:
        if (showEmojiPicker || showAttachmentMenu || selectedMessages.size > 0) {
          setShowEmojiPicker(false);
          setShowAttachmentMenu(false);
          clearSelection();
          return true;
        }
        break;
        
      case KEYBOARD_SHORTCUTS.ARROW_UP:
        if (!modifier && !shiftKey) {
          navigateToMessage('up');
          return true;
        }
        break;
        
      case KEYBOARD_SHORTCUTS.ARROW_DOWN:
        if (!modifier && !shiftKey) {
          navigateToMessage('down');
          return true;
        }
        break;
        
      case '/':
        if (!modifier && !shiftKey) {
          // Focus search/input
          focusInput();
          return true;
        }
        break;
        
      case 'e':
        if (modifier && !shiftKey) {
          // Toggle emoji picker
          toggleEmojiPicker();
          return true;
        }
        break;
        
      case 'a':
        if (modifier && !shiftKey) {
          // Select all messages
          selectMultipleMessages(messages.map(m => m.id));
          return true;
        }
        break;
    }
    
    return false;
  }, [
    enableKeyboardNavigation,
    showEmojiPicker,
    showAttachmentMenu,
    selectedMessages,
    clearSelection,
    navigateToMessage,
    focusInput,
    toggleEmojiPicker,
    selectMultipleMessages,
    messages
  ]);

  // Set up keyboard listeners
  React.useEffect(() => {
    if (!enableKeyboardNavigation) return;
    
    const handleKeyDown = (event: KeyboardEvent) => {
      // Don't handle shortcuts when typing in inputs
      const target = event.target as HTMLElement;
      if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.contentEditable === 'true') {
        return;
      }
      
      if (handleKeyboardShortcut(event)) {
        event.preventDefault();
        event.stopPropagation();
      }
    };
    
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown'; handleKeyDown);
  }, [enableKeyboardNavigation, handleKeyboardShortcut]);

  // Responsive utilities
  const getResponsiveSize = React.useCallback((sizes: { sm?: number; md?: number; lg?: number }): number => {
    switch (screenSize) {
      case 'sm': return sizes.sm ?? sizes.md ?? sizes.lg;
      case 'md': return sizes.md ?? sizes.lg ?? sizes.sm;
      case 'lg': return sizes.lg ?? sizes.md ?? sizes.sm;
      default: return sizes.md ?? sizes.lg ?? sizes.sm;
    }
  }, [screenSize]);

  const getResponsiveClasses = React.useCallback((classes: { sm?: string; md?: string; lg?: string }) => {
    const sizeConfig = SIZE_CONFIG[screenSize];
    return [
      classes.sm && screenSize === 'sm' ? classes.sm : '',
      classes.md && screenSize === 'md' ? classes.md : '',
      classes.lg && screenSize === 'lg' ? classes.lg : '',
      // Fallback to default size config classes
      !classes[screenSize] ? sizeConfig.text : ''
    ].filter(Boolean).join(' ');
  }, [screenSize]);

  // Virtualization
  const updateVisibleRange = React.useCallback((start: number, end: number) => {
    setVisibleMessageRange({ start, end });
  }, []);

  // Accessibility
  const announceMessage = React.useCallback((message: string) => {
    if (!message || message === lastAnnouncementRef.current) return;
    
    lastAnnouncementRef.current = message;
    
    // Create or update ARIA live region
    if (!announcementRef.current) {
      const liveRegion = document.createElement('div');
      liveRegion.setAttribute('aria-live', 'polite');
      liveRegion.setAttribute('aria-atomic', 'true');
      liveRegion.className = 'sr-only';
      document.body.appendChild(liveRegion);
      announcementRef.current = liveRegion;
    }
    
    // Clear and then set the message to ensure it's announced
    announcementRef.current.textContent = '';
    setTimeout(() => {
      if (announcementRef.current) {
        announcementRef.current.textContent = message;
      }
    }, 100);
  }, []);

  const getMessageIndex = React.useCallback((messageId: string): number => {
    return messages.findIndex(m => m.id === messageId);
  }, [messages]);

  const getTotalMessagesCount = React.useCallback((): number => {
    return messages.length;
  }, [messages.length]);

  // Cleanup
  React.useEffect(() => {
    return () => {
      if (announcementRef.current) {
        document.body.removeChild(announcementRef.current);
      }
    };
  }, []);

  return {
    // Layout state
    isMobile,
    isTablet,
    screenSize,
    layout,
    
    // UI state
    selectedMessages,
    showMessageActions,
    activeInput,
    scrollToBottom,
    showEmojiPicker,
    showAttachmentMenu,
    
    // Actions
    toggleSidebar,
    selectMessage,
    selectMultipleMessages,
    clearSelection,
    setShowMessageActions,
    focusInput,
    toggleEmojiPicker,
    toggleAttachmentMenu,
    
    // Keyboard navigation
    navigateToMessage,
    handleKeyboardShortcut,
    
    // Responsive utilities
    getResponsiveSize,
    getResponsiveClasses,
    
    // Performance utilities
    shouldVirtualize,
    visibleMessageRange,
    updateVisibleRange,
    
    // Accessibility
    announceMessage,
    getMessageIndex,
    getTotalMessagesCount
  };
};

export default useMessagingUI;