import React, { useState, useCallback, useMemo, memo, useRef, useEffect } from 'react';
import { Send, Smile, Paperclip, Phone, Video, MoreVertical, Search, Archive, Star } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import { useImmediateOptimization } from '@/hooks/useImmediateOptimizations';
import { formatTimeAgo } from '@/utils/timeUtils';
import { useDebounce, useStableCallback, useMemoryOptimization } from '@/utils/performanceOptimization';

interface Message {
  id: string, content: string, senderId: string, senderName: string, senderAvatar: string, timestamp: Date, isRead: boolean, type: 'text' | 'image' | 'file';
  attachments?: Array<{url: string, type: string, name: string}>;
}

interface Conversation {
  id: string, title: string, participants: Array<{id: string, name: string, avatar: string, isOnline: boolean}>;
  lastMessage?: Message;
  unreadCount: number, type: 'direct' | 'group';
}

interface OptimizedMessagingInterfaceProps {
  conversations: Conversation[];
  activeConversationId?: string;
  messages: Message[], currentUserId: string, onSendMessage: (conversationId: string, content: string) => void; onSelectConversation: (conversationId: string) => void; onMarkAsRead: (conversationId: string) => void;
  onClose?: () => void;
  className?: string;
}

// OPTIMIZATION 1: Memoized conversation item with deep comparison
const OptimizedConversationItem = memo<{
  conversation: Conversation, isActive: boolean, onSelect: (id: string) => void;
}>(({ conversation, isActive, onSelect }) => {
  const { createOptimizedCallback } = useImmediateOptimization(`ConversationItem-${conversation.id}`);
  
  const handleSelect = createOptimizedCallback(() => {
    onSelect(conversation.id);
  }, [onSelect, conversation.id]);
  
  // OPTIMIZATION: Memoized time display
  const timeDisplay = useMemo(() => 
    conversation.lastMessage ? formatTimeAgo(conversation.lastMessage.timestamp) : '',
    [conversation.lastMessage?.timestamp]
  );
  
  // OPTIMIZATION: Memoized participants display
  const participantsDisplay = useMemo(() => {
    if (conversation.type === 'direct') {
      return conversation.participants[0];
    }
    return {
      name: conversation.title,
      avatar: conversation.participants[0]?.avatar || '',
      isOnline: false
    };
  }, [conversation.participants, conversation.title, conversation.type]);
  
  return (
    <motion.div
      layout
      whileHover={{ scale: 1.02 }}, whileTap={{ scale: 0.98 }}, transition={{ duration: 0.1 }}
    >
      <Card
        className={cn(
          "mb-2 cursor-pointer transition-all duration-200 hover:shadow-md",
          isActive && "ring-2 ring-blue-500 bg-blue-50"
        )} onClick={handleSelect}
      >
        <CardContent className="p-3">
          <div className="flex items-center space-x-3">
            <div className="relative">
              <Avatar className="w-12 h-12">
                <AvatarImage src={participantsDisplay.avatar} alt={participantsDisplay.name} />
                <AvatarFallback>{participantsDisplay.name[0]}</AvatarFallback>
    </Avatar>
              {participantsDisplay.isOnline && (
                <div className="absolute bottom-0 right-0 w-3 h-3 bg-green-500 border-2 border-white rounded-full" />
              )}
            </div>
            
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <h3 className="font-medium text-sm truncate">
                  {participantsDisplay.name}
                </h3>
                <span className="text-xs text-gray-500 flex-shrink-0">
                  {timeDisplay}
                </span>
    </div>
              <div className="flex items-center justify-between mt-1">
                <p className="text-gray-600 text-xs truncate">
                  {conversation.lastMessage?.content || 'No messages yet'}
                </p>
                {conversation.unreadCount > 0 && (
                  <Badge variant="destructive" className="text-xs">
                    {conversation.unreadCount}
                  </Badge>
                )}
              </div>
    </div>
          </div>
    </CardContent>
      </Card>
    </motion.div>
  );
}, (prevProps, nextProps) => {
  return (
    prevProps.conversation.id === nextProps.conversation.id &&
    prevProps.conversation.unreadCount === nextProps.conversation.unreadCount &&
    prevProps.conversation.lastMessage?.id === nextProps.conversation.lastMessage?.id &&
    prevProps.isActive === nextProps.isActive &&
    prevProps.onSelect === nextProps.onSelect
  );
});

OptimizedConversationItem.displayName = 'OptimizedConversationItem';

// OPTIMIZATION 2: Memoized message bubble with optimized rendering
const OptimizedMessageBubble = memo<{
  message: Message, isOwnMessage: boolean, showAvatar: boolean;
}>(({ message, isOwnMessage, showAvatar }) => {
  // OPTIMIZATION: Memoized time display
  const timeDisplay = useMemo(() => 
    formatTimeAgo(message.timestamp),
    [message.timestamp]
  );
  
  return (
    <motion.div
      layout
      initial={{ opacity: 0, y: 10 }}, animate={{ opacity: 1, y: 0 }}, exit={{ opacity: 0, y: -10 }}, transition={{ duration: 0.2 }}, className={cn(
        "flex mb-4",
        isOwnMessage ? "justify-end" : "justify-start"
      )}
    >
      <div className={cn(
        "flex max-w-[70%]",
        isOwnMessage ? "flex-row-reverse" : "flex-row"
      )}>
        {showAvatar && !isOwnMessage && (
          <Avatar className="w-8 h-8 mr-2 flex-shrink-0">
            <AvatarImage src={message.senderAvatar} alt={message.senderName} />
            <AvatarFallback>{message.senderName[0]}</AvatarFallback>
    </Avatar>
        )}
        
        <div className={cn(
          "px-4 py-2 rounded-lg shadow-sm",
          isOwnMessage 
            ? "bg-blue-500 text-white rounded-br-sm" 
            : "bg-gray-100 text-gray-900 rounded-bl-sm"
        )}>
          {!isOwnMessage && showAvatar && (
            <p className="text-xs font-medium mb-1 opacity-70">
              {message.senderName}
            </p>
          )}
          <p className="text-sm">{message.content}</p>
          <p className={cn(
            "text-xs mt-1 opacity-70",
            isOwnMessage ? "text-right" : "text-left"
          )}>
            {timeDisplay}
          </p>
    </div>
      </div>
    </motion.div>
  );
}, (prevProps, nextProps) => {
  return (
    prevProps.message.id === nextProps.message.id &&
    prevProps.message.content === nextProps.message.content &&
    prevProps.isOwnMessage === nextProps.isOwnMessage &&
    prevProps.showAvatar === nextProps.showAvatar
  );
});

OptimizedMessageBubble.displayName = 'OptimizedMessageBubble';

// OPTIMIZATION 3: Main messaging interface component
const OptimizedMessagingInterface: React.FC<OptimizedMessagingInterfaceProps> = memo(({
  conversations,
  activeConversationId,
  messages,
  currentUserId,
  onSendMessage,
  onSelectConversation,
  onMarkAsRead,
  className = ''
}) => {
  // OPTIMIZATION: Performance monitoring
  const { 
    createOptimizedCallback, 
    createMemoizedObject;
    getPerformanceMetrics 
  } = useImmediateOptimization('OptimizedMessagingInterface');
  
  // Local state
  const [messageInput, setMessageInput] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  
  // OPTIMIZATION: Refs for stable DOM references
  const messageInputRef = useRef<HTMLTextAreaElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  
  // OPTIMIZATION: Optimized callbacks
  const optimizedSendMessage = createOptimizedCallback(() => {
    if (messageInput.trim() && activeConversationId) {
      // Log performance metrics in development
      if (process.env.NODE_ENV === 'development') {
        const metrics = getPerformanceMetrics();
        console.log('📊 MessagingInterface Performance:', metrics);
      }, onSendMessage(activeConversationId, messageInput.trim());
      setMessageInput('');
      
      // Focus back to input
      setTimeout(() => {
        messageInputRef.current?.focus();
      }, 50);
    }
  }, [messageInput, activeConversationId, onSendMessage, getPerformanceMetrics]);
  
  const optimizedSelectConversation = createOptimizedCallback((...args: unknown[]) => {
    const conversationId = args[0] as string;
    onSelectConversation(conversationId);
    onMarkAsRead(conversationId);
  }, [onSelectConversation, onMarkAsRead]);
  
  // OPTIMIZATION: Memoized filtered conversations
  const filteredConversations = useMemo(() => {
    if (!searchTerm.trim()) return conversations;
    
    const search = searchTerm.toLowerCase().trim();
    return conversations.filter(conv => 
      conv.title.toLowerCase().includes(search) ||
      conv.participants.some(p => p.name.toLowerCase().includes(search)) ||
      conv.lastMessage?.content.toLowerCase().includes(search)
    );
  }, [conversations, searchTerm]);
  
  // OPTIMIZATION: Memoized active conversation
  const activeConversation = useMemo(() => 
    conversations.find(c => c.id === activeConversationId),
    [conversations, activeConversationId]
  );
  
  // OPTIMIZATION: Memoized processed messages for rendering
  const processedMessages = useMemo(() => {
    return messages.map((message, index) => {
      const prevMessage = messages[index - 1];
      const showAvatar = !prevMessage || 
        prevMessage.senderId !== message.senderId ||
        (message.timestamp.getTime() - prevMessage.timestamp.getTime()) > 300000; // 5 minutes
      
      return {
        ...message,
        isOwnMessage: message.senderId === currentUserId,
        showAvatar
      };
    });
  }, [messages, currentUserId]);
  
  // OPTIMIZATION: Memoized interface configuration
  const interfaceConfig = createMemoizedObject(() => ({
    activeConversationId,
    messageInput,
    searchTerm,
    hasActiveConversation: !!activeConversation
  }), [activeConversationId, messageInput, searchTerm, activeConversation]);
  
  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);
  
  // Handle Enter key for sending messages
  const handleKeyPress = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      optimizedSendMessage();
    }
  }, [optimizedSendMessage]);
  
  // OPTIMIZATION: Performance logging in development
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      const metrics = getPerformanceMetrics();
      console.log('⚙️ MessagingInterface Configuration:', interfaceConfig);
      if (metrics.renderCount > 0 && metrics.renderCount % 10 === 0) {
        console.log('📊 MessagingInterface Performance Metrics:', metrics);
      }
    }
  }, [interfaceConfig, getPerformanceMetrics]);
  
  return (
    <div className={cn("flex h-full bg-white rounded-lg shadow-lg overflow-hidden", className)}>
      {/* Conversations Sidebar */}
      <div className="w-1/3 border-r bg-gray-50">
        <div className="p-4 border-b">
          <h2 className="font-semibold text-lg mb-3">Messages</h2>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <Input
              placeholder="Search conversations..."
              value={searchTerm} onChange={(e) => setSearchTerm(e.target.value)}, className="pl-10"
            />
    </div>
        </div>
        
        <ScrollArea className="h-[calc(100%-120px)]">
          <div className="p-2">
            <AnimatePresence mode="popLayout">
              {filteredConversations.map(conversation => (
                <OptimizedConversationItem
                  key={conversation.id} conversation={conversation}, isActive={conversation.id === activeConversationId} onSelect={optimizedSelectConversation}
                />
              ))}
            </AnimatePresence>
    </div>
        </ScrollArea>
    </div>
      {/* Message Area */}
      <div className="flex-1 flex flex-col">
        {activeConversation ? (
          <>
            {/* Message Header */}
            <div className="p-4 border-b bg-white">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Avatar className="w-10 h-10">
                    <AvatarImage 
                      src={activeConversation.participants[0]?.avatar} alt={activeConversation.title} 
                    />
                    <AvatarFallback>
                      {activeConversation.title[0]}
                    </AvatarFallback>
    </Avatar>
                  <div>
                    <h3 className="font-medium">{activeConversation.title}</h3>
                    <p className="text-sm text-gray-500">
                      {activeConversation.type === 'group' 
                        ? `${activeConversation.participants.length} participants`
                        : activeConversation.participants[0]?.isOnline ? 'Online' : 'Offline'
                      }
                    </p>
    </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Button variant="ghost" size="sm">
                    <Phone className="w-4 h-4" />
    </Button>
                  <Button variant="ghost" size="sm">
                    <Video className="w-4 h-4" />
    </Button>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreVertical className="w-4 h-4" />
    </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem>
                        <Star className="w-4 h-4 mr-2" />
                        Star conversation
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Archive className="w-4 h-4 mr-2" />
                        Archive
                      </DropdownMenuItem>
    </DropdownMenuContent>
                  </DropdownMenu>
    </div>
              </div>
    </div>
            {/* Messages */}
            <ScrollArea ref={scrollAreaRef} className="flex-1 p-4">
              <AnimatePresence mode="popLayout">
                {processedMessages.map(message => (
                  <OptimizedMessageBubble
                    key={message.id} message={message}, isOwnMessage={message.isOwnMessage} showAvatar={message.showAvatar}
                  />
                ))}
              </AnimatePresence>
              <div ref={messagesEndRef} />
    </ScrollArea>
            {/* Message Input */}
            <div className="p-4 border-t bg-gray-50">
              <div className="flex items-end space-x-2">
                <Button variant="ghost" size="sm">
                  <Paperclip className="w-4 h-4" />
    </Button>
                <div className="flex-1">
                  <Textarea
                    ref={messageInputRef} placeholder="Type a message..."
                    value={messageInput} onChange={(e) => setMessageInput(e.target.value)}, onKeyPress={handleKeyPress} className="resize-none min-h-[40px] max-h-32"
                    rows={1}
                  />
    </div>
                <Button variant="ghost" size="sm">
                  <Smile className="w-4 h-4" />
    </Button>
                <Button 
                  onClick={optimizedSendMessage} disabled={!messageInput.trim()}, className="px-4"
                >
                  <Send className="w-4 h-4" />
    </Button>
              </div>
    </div>
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Send className="w-8 h-8 text-gray-400" />
    </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Select a conversation
              </h3>
              <p className="text-gray-500">
                Choose from your existing conversations or start a new one
              </p>
    </div>
          </div>
        )}
      </div>
    </div>
  );
});

OptimizedMessagingInterface.displayName = 'OptimizedMessagingInterface';

export default OptimizedMessagingInterface;
