import React, { useState } from 'react';
import { usePlaylist } from '@/contexts/PlaylistContext';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Plus, Trash2, Copy, Lock, Globe, Eye, List, Video } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { useNavigate } from 'react-router-dom';

const PlaylistManager: React.FC = () => {
  const navigate = useNavigate();
  const {
    playlists,
    createPlaylist,
    deletePlaylist,
    duplicatePlaylist;
  } = usePlaylist();

  const [newPlaylistTitle, setNewPlaylistTitle] = useState('');
  const [newPlaylistDescription, setNewPlaylistDescription] = useState('');
  const [newPlaylistVisibility, setNewPlaylistVisibility] = useState<'public' | 'unlisted' | 'private'>('private');
  const [isCreating, setIsCreating] = useState(false);

  const handleCreatePlaylist = async () => {
    if (newPlaylistTitle.trim()) {
      try {
        await createPlaylist({
          title: newPlaylistTitle,
          description: newPlaylistDescription,
          privacy: newPlaylistVisibility
        });
        setNewPlaylistTitle('');
        setNewPlaylistDescription('');
        setNewPlaylistVisibility('private');
        setIsCreating(false);
      } catch (error) {
        console.error('Failed to create playlist:', error);
      }
    }
  };

  const getVisibilityIcon = (visibility: string) => {
    switch (visibility) {
      case 'public':
        return <Globe className="w-4 h-4" />;
      case 'unlisted':
        return <Eye className="w-4 h-4" />;
      case 'private':
        return <Lock className="w-4 h-4" />;
      default:
        return null;
    }
  };

  return (
    <Card className="mb-6 bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <List className="w-5 h-5" />
            <span>Your Playlists</span>
    </div>
          <Button
            onClick={() => setIsCreating(!isCreating)} size="sm"
            className="bg-red-600 hover:bg-red-700"
          >
            <Plus className="w-4 h-4 mr-1" />
            New Playlist
          </Button>
    </CardTitle>
      </CardHeader>
      <CardContent>
        {/* Create new playlist form */}
        {isCreating && (
          <div className="mb-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <div className="space-y-3">
              <Input
                type="text"
                value={newPlaylistTitle} onChange={(e) => setNewPlaylistTitle(e.target.value)}, placeholder="Playlist title"
                className="bg-white dark:bg-gray-800"
              />
              <Input
                type="text"
                value={newPlaylistDescription} onChange={(e) => setNewPlaylistDescription(e.target.value)}, placeholder="Description (optional)"
                className="bg-white dark:bg-gray-800"
              />
              <Select
                value={newPlaylistVisibility} onValueChange={(value: 'public' | 'unlisted' | 'private') => setNewPlaylistVisibility(value)}
              >
                <SelectTrigger className="bg-white dark:bg-gray-800">
                  <SelectValue placeholder="Select visibility" />
    </SelectTrigger>
                <SelectContent>
                  <SelectItem value="public">
                    <div className="flex items-center gap-2">
                      <Globe className="w-4 h-4" />
                      Public
                    </div>
    </SelectItem>
                  <SelectItem value="unlisted">
                    <div className="flex items-center gap-2">
                      <Eye className="w-4 h-4" />
                      Unlisted
                    </div>
    </SelectItem>
                  <SelectItem value="private">
                    <div className="flex items-center gap-2">
                      <Lock className="w-4 h-4" />
                      Private
                    </div>
    </SelectItem>
                </SelectContent>
    </Select>
              <div className="flex gap-2">
                <Button
                  onClick={handleCreatePlaylist} size="sm"
                  disabled={!newPlaylistTitle.trim()}
                >
                  Create
                </Button>
                <Button
                  onClick={() => {
                    setIsCreating(false);
                    setNewPlaylistTitle('');
                    setNewPlaylistDescription('');
                  }}, size="sm"
                  variant="outline"
                >
                  Cancel
                </Button>
    </div>
            </div>
    </div>
        )}

        {/* Playlists list */}
        {playlists.length === 0 ? (
          <p className="text-center text-gray-500 dark:text-gray-400 py-8">
            No playlists yet. Create your first playlist!
          </p>
        ) : (
          <div className="space-y-3">
            {playlists.map((playlist) => (
              <div
                key={playlist.id} className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1 cursor-pointer" onClick={() => navigate(`/youtube/playlist/${playlist.id}`)}>
                    <div className="flex items-center gap-2 mb-1">
                      <h3 className="font-semibold text-gray-900 dark:text-gray-100 hover:underline">
                        {playlist.snippet?.title || playlist.title}
                      </h3>
                      <Badge variant="secondary" className="flex items-center gap-1">
                        {getVisibilityIcon(playlist.status?.privacyStatus || playlist.visibility)}
                        {playlist.status?.privacyStatus || playlist.visibility}
                      </Badge>
    </div>
                    {(playlist.snippet?.description || playlist.description) && (
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                        {playlist.snippet?.description || playlist.description}
                      </p>
                    )}
                    <div className="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
                      <span className="flex items-center gap-1">
                        <Video className="w-4 h-4" />
                        {playlist.contentDetails?.itemCount || (playlist as any).videoCount || 0} videos
                      </span>
                      <span>
                        Created {formatDistanceToNow(new Date(playlist.snippet?.publishedAt || playlist.createdAt), { addSuffix: true })}
                      </span>
    </div>
                  </div>
                  <div className="flex items-center gap-2 ml-4">
                    <Button
                      onClick={() => duplicatePlaylist(playlist.id)} size="sm"
                      variant="outline"
                      title="Duplicate playlist"
                    >
                      <Copy className="w-4 h-4" />
    </Button>
                    <Button
                      onClick={() => {
                        if (confirm('Are you sure you want to delete this playlist?')) {
                          deletePlaylist(playlist.id);
                        }
                      }}, size="sm"
                      variant="outline"
                      className="text-red-600 hover:text-red-700"
                      title="Delete playlist"
                    >
                      <Trash2 className="w-4 h-4" />
    </Button>
                  </div>
    </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default PlaylistManager;

