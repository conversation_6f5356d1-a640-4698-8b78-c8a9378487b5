#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

function fixJSXCommaIssues(content) {
  let lines = content.split('\n');
  let modified = false;
  
  for (let i = 0; i < lines.length; i++) {
    let line = lines[i];
    
    // Pattern 1: prop={value}, prop2={value2} (comma should not be there)
    if (line.includes('={') && line.includes('},')) {
      const regex = /(\w+)=\{([^}]+)\},\s*(\w+)=/g;
      const matches = [...line.matchAll(regex)];
      
      if (matches.length > 0) {
        let newLine = line;
        matches.reverse().forEach(match => {
          const [fullMatch, prop1, value1, prop2] = match;
          const replacement = `${prop1}={${value1}}\n          ${prop2}=`;
          newLine = newLine.replace(fullMatch, replacement);
        });
        
        if (newLine !== line) {
          lines[i] = newLine;
          modified = true;
        }
      }
    }
    
    // Pattern 2: Multiple props on same line
    const multiPropPattern = /(\w+)=\{([^}]+)\}\s+(\w+)=\{([^}]+)\}\s+(\w+)=/;
    if (multiPropPattern.test(line)) {
      const match = line.match(multiPropPattern);
      if (match) {
        const [fullMatch, prop1, value1, prop2, value2, prop3] = match;
        const indent = line.match(/^\s*/)[0];
        const beforeMatch = line.substring(0, line.indexOf(fullMatch));
        const afterMatch = line.substring(line.indexOf(fullMatch) + fullMatch.length);
        
        lines[i] = beforeMatch + indent + prop1 + '={' + value1 + '}';
        lines.splice(i + 1, 0, indent + '          ' + prop2 + '={' + value2 + '}');
        lines.splice(i + 2, 0, indent + '          ' + prop3 + '=' + afterMatch);
        modified = true;
        i += 2; // Skip the newly inserted lines
      }
    }
    
    // Pattern 3: Two props on same line
    const twoPropPattern = /(\w+)=\{([^}]+)\}\s+(\w+)=/;
    if (twoPropPattern.test(line) && !line.includes('},')) {
      const match = line.match(twoPropPattern);
      if (match) {
        const [fullMatch, prop1, value1, prop2] = match;
        const indent = line.match(/^\s*/)[0];
        const beforeMatch = line.substring(0, line.indexOf(fullMatch));
        const afterMatch = line.substring(line.indexOf(fullMatch) + fullMatch.length);
        
        lines[i] = beforeMatch + indent + prop1 + '={' + value1 + '}';
        lines.splice(i + 1, 0, indent + '          ' + prop2 + '=' + afterMatch);
        modified = true;
        i++; // Skip the newly inserted line
      }
    }
  }
  
  return modified ? lines.join('\n') : content;
}

function fixFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const fixedContent = fixJSXCommaIssues(content);
    
    if (fixedContent !== content) {
      fs.writeFileSync(filePath, fixedContent, 'utf8');
      console.log(`Fixed: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`Error fixing ${filePath}:`, error.message);
    return false;
  }
}

function walkDirectory(dir, extensions = ['.tsx', '.ts', '.jsx', '.js']) {
  const files = [];
  
  function walk(currentDir) {
    try {
      const items = fs.readdirSync(currentDir);
      
      for (const item of items) {
        const fullPath = path.join(currentDir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          walk(fullPath);
        } else if (stat.isFile() && extensions.some(ext => item.endsWith(ext))) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      console.error(`Error reading directory ${currentDir}:`, error.message);
    }
  }
  
  walk(dir);
  return files;
}

function main() {
  const srcDir = path.join(__dirname, '..', 'src');
  const files = walkDirectory(srcDir);
  
  console.log(`Found ${files.length} files to check for JSX comma issues...`);
  
  let fixedCount = 0;
  
  files.forEach(file => {
    if (fixFile(file)) {
      fixedCount++;
    }
  });
  
  console.log(`\nFixed JSX comma issues in ${fixedCount} files out of ${files.length} total files.`);
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}
