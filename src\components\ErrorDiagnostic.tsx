import React from 'react';
import { errorLogger } from '@/utils/errorLogger';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { AlertCircle, RefreshCw, Trash2 } from 'lucide-react';
import type { ErrorLog } from '@/utils/errorLogger';

const ErrorDiagnostic: React.FC = () => {
  const [errors, setErrors] = React.useState<ErrorLog[]>([]);
  const [isVisible, setIsVisible] = React.useState(false);

  // Function to deduplicate errors based on message and category
  const deduplicateErrors = (errorList: ErrorLog[]): ErrorLog[] => {
    const seen = new Set<string>();
    return errorList.filter(error => {
      const key = `${error.category}-${error.message}-${error.context || ''}`;
      if (seen.has(key)) {
        return false;
      }
      seen.add(key);
      return true;
    });
  };

  React.useEffect(() => {
    // Subscribe to error events with deferred setState to avoid setState during render
    const unsubscribe = errorLogger.subscribe((error) => {
      // Use setTimeout to defer setState and avoid "Cannot update during render" warning
      setTimeout(() => {
        setErrors(prev => {
          const newErrors = [...prev, error];
          return deduplicateErrors(newErrors);
        });
        if (error.category === 'ui') {
          setIsVisible(true);
        }
      }, 0);
    });

    // Get existing errors and deduplicate them
    const existingErrors = errorLogger.getErrorQueue();
    setErrors(deduplicateErrors(existingErrors));

    // Check for UI errors
    if (existingErrors.some(e => e.category === 'ui')) {
      setIsVisible(true);
    }

    return unsubscribe;
  }, []);

  const clearErrors = () => {
    errorLogger.clearErrorQueue();
    setErrors([]);
    setIsVisible(false);
  };

  const uiErrors = errors.filter(e => e.category === 'ui');

  if (!isVisible || process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 max-w-md max-h-[80vh] overflow-hidden">
      <Card className="bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 flex flex-col max-h-full">
        <CardHeader className="pb-3 flex-shrink-0">
          <CardTitle className="text-sm flex items-center justify-between">
            <span className="flex items-center gap-2">
              <AlertCircle className="w-4 h-4 text-red-600" />
              UI Error Diagnostic ({uiErrors.length})
            </span>
            <Button
              size="sm"
              variant="ghost"
              onClick={() => setIsVisible(false)} className="h-6 px-2"
            >
              ×
            </Button>
    </CardTitle>
        </CardHeader>
        <CardContent className="space-y-2 overflow-y-auto flex-1 min-h-0">
          {uiErrors.slice(-3).map((error, index) => (
            <div key={error.id || index} className="text-xs space-y-1 p-2 bg-white dark:bg-gray-800 rounded">
              <div className="font-semibold text-red-600 dark:text-red-400">
                {error.message}
              </div>
              {error.context && (
                <div className="text-gray-600 dark:text-gray-400">
                  Context: {error.context}
                </div>
              )}
              {error.stack && (
                <details className="cursor-pointer">
                  <summary className="text-gray-500 hover:text-gray-700">Stack trace</summary>
                  <div className="mt-1 max-h-32 overflow-y-auto">
                    <pre className="text-xs whitespace-pre-wrap break-words bg-gray-100 dark:bg-gray-700 p-2 rounded">
                      {error.stack}
                    </pre>
    </div>
                </details>
              )}
            </div>
          ))}
          <div className="flex gap-2 pt-2 flex-shrink-0">
            <Button
              size="sm"
              variant="outline"
              onClick={() => window.location.reload()} className="flex-1"
            >
              <RefreshCw className="w-3 h-3 mr-1" />
              Refresh
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={clearErrors} className="flex-1"
            >
              <Trash2 className="w-3 h-3 mr-1" />
              Clear
            </Button>
    </div>
        </CardContent>
    </Card>
    </div>
  );
};

export default ErrorDiagnostic;
