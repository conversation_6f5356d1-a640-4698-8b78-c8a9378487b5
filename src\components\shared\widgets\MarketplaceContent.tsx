import React, { memo, useState, useCallback, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Store, Heart, MessageCircle, DollarSign } from 'lucide-react';
import { toast } from 'sonner';
import { useNavigate } from 'react-router-dom';
import { ROUTES } from '@/lib/constants';
import { OptimizedImage } from '@/components/OptimizedImage';

interface MarketplaceListing {
  id: string, title: string, price: number, location: string, image: string, category: string;
  isLiked?: boolean;
}

interface MarketplaceContentProps {
  maxListings?: number;
}

// Mock marketplace listings data
const INITIAL_LISTINGS: MarketplaceListing[] = [
  {
    id: '1',
    title: 'iPhone 14 Pro',
    price: 899,
    location: 'New York, NY',
    image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8ZGVmcz4KICAgIDxsaW5lYXJHcmFkaWVudCBpZD0icGhvbmUiIHgxPSIwJSIgeTE9IjAlIiB4Mj0iMTAwJSIgeTI9IjEwMCUiPgogICAgICA8c3RvcCBvZmZzZXQ9IjAlIiBzdHlsZT0ic3RvcC1jb2xvcjpoc2woMjIwLCA4MCUsIDYwJSk7c3RvcC1vcGFjaXR5OjEiIC8+CiAgICAgIDxzdG9wIG9mZnNldD0iMTAwJSIgc3R5bGU9InN0b3AtY29sb3I6aHNsKDI2MCwgODAlLCA3MCUpO3N0b3Atb3BhY2l0eToxIiAvPgogICAgPC9saW5lYXJHcmFkaWVudD4KICA8L2RlZnM+CiAgPHJlY3Qgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgZmlsbD0idXJsKCNwaG9uZSkiIC8+CiAgPHRleHQgeD0iNTAlIiB5PSI1MCUiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGRvbWluYW50LWJhc2VsaW5lPSJtaWRkbGUiIGZpbGw9IndoaXRlIiBmb250LXNpemU9IjE2Ij7wn5OxPC90ZXh0Pgo8L3N2Zz4=',
    category: 'Electronics'
  },
  {
    id: '2',
    title: 'Vintage Leather Sofa',
    price: 450,
    location: 'Brooklyn, NY',
    image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8ZGVmcz4KICAgIDxsaW5lYXJHcmFkaWVudCBpZD0ic29mYSIgeDE9IjAlIiB5MT0iMCUiIHgyPSIxMDAlIiB5Mj0iMTAwJSI+CiAgICAgIDxzdG9wIG9mZnNldD0iMCUiIHN0eWxlPSJzdG9wLWNvbG9yOmhzbCgzMCwgNzAlLCA1MCUpO3N0b3Atb3BhY2l0eToxIiAvPgogICAgICA8c3RvcCBvZmZzZXQ9IjEwMCUiIHN0eWxlPSJzdG9wLWNvbG9yOmhzbCg2MCwgNzAlLCA2MCUpO3N0b3Atb3BhY2l0eToxIiAvPgogICAgPC9saW5lYXJHcmFkaWVudD4KICA8L2RlZnM+CiAgPHJlY3Qgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgZmlsbD0idXJsKCNzb2ZhKSIgLz4KICA8dGV4dCB4PSI1MCUiIHk9IjUwJSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZG9taW5hbnQtYmFzZWxpbmU9Im1pZGRsZSIgZmlsbD0id2hpdGUiIGZvbnQtc2l6ZT0iMTYiPvCfmoo8L3RleHQ+Cjwvc3ZnPg==',
    category: 'Furniture'
  },
  {
    id: '3',
    title: 'Mountain Bike',
    price: 320,
    location: 'Queens, NY',
    image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8ZGVmcz4KICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iYmlrZSIgeDE9IjAlIiB5MT0iMCUiIHgyPSIxMDAlIiB5Mj0iMTAwJSI+CiAgICAgIDxzdG9wIG9mZnNldD0iMCUiIHN0eWxlPSJzdG9wLWNvbG9yOmhzbCgxMjAsIDcwJSwgNTAlKTtzdG9wLW9wYWNpdHk6MSIgLz4KICAgICAgPHN0b3Agb2Zmc2V0PSIxMDAlIiBzdHlsZT0ic3RvcC1jb2xvcjpoc2woMTgwLCA3MCUsIDYwJSk7c3RvcC1vcGFjaXR5OjEiIC8+CiAgICA8L2xpbmVhckdyYWRpZW50PgogIDwvZGVmcz4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSJ1cmwoI2Jpa2UpIiAvPgogIDx0ZXh0IHg9IjUwJSIgeT0iNTAlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkb21pbmFudC1iYXNlbGluZT0ibWlkZGxlIiBmaWxsPSJ3aGl0ZSIgZm9udC1zaXplPSIxNiI+8J+agDwvdGV4dD4KPC9zdmc+',
    category: 'Sports'
  },
  {
    id: '4',
    title: 'Gaming Setup',
    price: 1200,
    location: 'Manhattan, NY',
    image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8ZGVmcz4KICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZ2FtaW5nIiB4MT0iMCUiIHkxPSIwJSIgeDI9IjEwMCUiIHkyPSIxMDAlIj4KICAgICAgPHN0b3Agb2Zmc2V0PSIwJSIgc3R5bGU9InN0b3AtY29sb3I6aHNsKDI3MCwgODAlLCA2MCUpO3N0b3Atb3BhY2l0eToxIiAvPgogICAgICA8c3RvcCBvZmZzZXQ9IjEwMCUiIHN0eWxlPSJzdG9wLWNvbG9yOmhzbCgzMzAsIDgwJSwgNzAlKTtzdG9wLW9wYWNpdHk6MSIgLz4KICAgIDwvbGluZWFyR3JhZGllbnQ+CiAgPC9kZWZzPgogIDxyZWN0IHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9InVybCgjZ2FtaW5nKSIgLz4KICA8dGV4dCB4PSI1MCUiIHk9IjUwJSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZG9taW5hbnQtYmFzZWxpbmU9Im1pZGRsZSIgZmlsbD0id2hpdGUiIGZvbnQtc2l6ZT0iMTYiPvCfkq4gPC90ZXh0Pgo8L3N2Zz4=',
    category: 'Electronics'
  },
  {
    id: '5',
    title: 'Designer Handbag',
    price: 280,
    location: 'Staten Island, NY',
    image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8ZGVmcz4KICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iYmFnIiB4MT0iMCUiIHkxPSIwJSIgeDI9IjEwMCUiIHkyPSIxMDAlIj4KICAgICAgPHN0b3Agb2Zmc2V0PSIwJSIgc3R5bGU9InN0b3AtY29sb3I6aHNsKDMwMCwgNzAlLCA1MCUpO3N0b3Atb3BhY2l0eToxIiAvPgogICAgICA8c3RvcCBvZmZzZXQ9IjEwMCUiIHN0eWxlPSJzdG9wLWNvbG9yOmhzbCgzMzAsIDcwJSwgNjAlKTtzdG9wLW9wYWNpdHk6MSIgLz4KICAgIDwvbGluZWFyR3JhZGllbnQ+CiAgPC9kZWZzPgogIDxyZWN0IHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9InVybCgjYmFnKSIgLz4KICA8dGV4dCB4PSI1MCUiIHk9IjQ1JSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZG9taW5hbnQtYmFzZWxpbmU9Im1pZGRsZSIgZm9udC1zaXplPSI0MCI+👜PC90ZXh0PgogIDx0ZXh0IHg9IjUwJSIgeT0iNjUlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkb21pbmFudC1iYXNlbGluZT0ibWlkZGxlIiBmaWxsPSJ3aGl0ZSIgZm9udC1zaXplPSIxNCI+SGFuZGJhZzwvdGV4dD4KPC9zdmc+',
    category: 'Fashion'
  },
  {
    id: '6',
    title: 'Coffee Table',
    price: 150,
    location: 'Bronx, NY',
    image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8ZGVmcz4KICAgIDxsaW5lYXJHcmFkaWVudCBpZD0idGFibGUiIHgxPSIwJSIgeTE9IjAlIiB4Mj0iMTAwJSIgeTI9IjEwMCUiPgogICAgICA8c3RvcCBvZmZzZXQ9IjAlIiBzdHlsZT0ic3RvcC1jb2xvcjpoc2woMzAsIDcwJSwgNTAlKTtzdG9wLW9wYWNpdHk6MSIgLz4KICAgICAgPHN0b3Agb2Zmc2V0PSIxMDAlIiBzdHlsZT0ic3RvcC1jb2xvcjpoc2woNjAsIDcwJSwgNjAlKTtzdG9wLW9wYWNpdHk6MSIgLz4KICAgIDwvbGluZWFyR3JhZGllbnQ+CiAgPC9kZWZzPgogIDxyZWN0IHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9InVybCgjdGFibGUpIiAvPgogIDx0ZXh0IHg9IjUwJSIgeT0iNDUlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkb21pbmFudC1iYXNlbGluZT0ibWlkZGxlIiBmb250LXNpemU9IjQwIj7isJM8L3RleHQ+CiAgPHRleHQgeD0iNTAlIiB5PSI2NSUiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGRvbWluYW50LWJhc2VsaW5lPSJtaWRkbGUiIGZpbGw9IndoaXRlIiBmb250LXNpemU9IjE0Ij5UYWJsZTwvdGV4dD4KPC9zdmc+',
    category: 'Furniture'
  }
];

const MarketplaceContent: React.FC<MarketplaceContentProps> = memo(({ maxListings = 4 }) => {
  const navigate = useNavigate();
  
  // Memoize listings to prevent recreation and flickering
  const listings = useMemo(() => {
    return INITIAL_LISTINGS.slice(0, maxListings);
  }, [maxListings]);

  const [likedListings, setLikedListings] = useState<Set<string>>(new Set());

  const handleListingClick = useCallback((listingId: string) => {
    const listing = listings.find(l => l.id === listingId);
    if (listing) {
      toast.success(`Viewing ${listing.title}`, {
        description: `$${listing.price} • ${listing.location}`
      });
      // Navigate to marketplace with the specific listing
      navigate(`${ROUTES.MARKETPLACE}?item=${listingId}`);
    }
  }, [listings, navigate]);

  const handleLikeListing = useCallback((listingId: string, event: React.MouseEvent) => {
    event.stopPropagation();
    try {
      setLikedListings(prev => {
        const newLiked = new Set(prev);
        if (newLiked.has(listingId)) {
          newLiked.delete(listingId);
          toast.info('Removed from favorites');
        } else {
          newLiked.add(listingId);
          toast.success('Added to favorites');
        }
        return newLiked;
      });
    } catch (error) {
      console.error('Error updating liked listings:', error);
      toast.error('Failed to update favorites');
    }
  }, []);

  const handleMessageSeller = useCallback((listingId: string, event: React.MouseEvent) => {
    event.stopPropagation();
    const listing = listings.find(l => l.id === listingId);
    if (listing) {
      toast.info(`Messaging seller about ${listing.title}`);
    }
  }, [listings]);

  const handleViewAllClick = useCallback(() => {
    navigate(ROUTES.MARKETPLACE);
    toast.info('Opening Marketplace');
  }, [navigate]);

  return (
    <Card className="hidden lg:block">
      <CardHeader className="p-2 pb-1">
        <CardTitle className="text-sm font-semibold flex items-center justify-between">
          <div className="flex items-center">
            <Store className="w-4 h-4 mr-2 text-blue-600" />
            <span>Marketplace</span>
    </div>
          <Button
            variant="ghost"
            size="sm"
            className="text-blue-600 text-xs hover:bg-blue-50 transition-colors dark:text-blue-400 dark:hover:bg-blue-900/20"
            onClick={handleViewAllClick}
          >
            View All
          </Button>
    </CardTitle>
      </CardHeader>
      <CardContent className="p-2 pt-0">
        <div className="space-y-2">
          {listings.map((listing) => {
            const isLiked = likedListings.has(listing.id);
            return (
              <div
                key={listing.id} className="flex items-center space-x-3 p-2 bg-gray-50 rounded-lg dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer transition-colors"
                onClick={() => handleListingClick(listing.id)}
              >
                <div className="w-12 h-12 rounded-lg overflow-hidden flex-shrink-0">
                  <OptimizedImage
                    src={listing.image} alt={listing.title}, width={48} height={48}, className="w-full h-full object-cover"
                    placeholder="blur"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjNmNGY2Ii8+CiAgPHRleHQgeD0iNTAlIiB5PSI1MCUiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGRvbWluYW50LWJhc2VsaW5lPSJtaWRkbGUiIGZpbGw9IiM5Y2EzYWYiIGZvbnQtc2l6ZT0iMTQiPkVycm9yPC90ZXh0Pgo8L3N2Zz4=';
                    }}
                  />
    </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                    {listing.title}
                  </p>
                  <div className="flex items-center space-x-2 mt-1">
                    <span className="text-sm font-semibold text-green-600 dark:text-green-400">
                      ${listing.price}
                    </span>
                    <span className="text-xs text-gray-500 dark:text-gray-400 truncate">
                      {listing.location}
                    </span>
    </div>
                  <p className="text-xs text-gray-400 dark:text-gray-500">
                    {listing.category}
                  </p>
    </div>
                <div className="flex flex-col space-y-1">
                  <Button
                    size="icon"
                    variant="ghost"
                    className={`w-6 h-6 hover:bg-red-100 dark:hover:bg-red-900/20 ${
                      isLiked ? 'text-red-500' : 'text-gray-400'
                    }`}, onClick={(e) => handleLikeListing(listing.id; e)}
                  >
                    <Heart className={`w-3 h-3 ${isLiked ? 'fill-current' : ''}`} />
    </Button>
                  <Button
                    size="icon"
                    variant="ghost"
                    className="w-6 h-6 text-gray-400 hover:bg-blue-100 dark:hover:bg-blue-900/20 hover:text-blue-600"
                    onClick={(e) => handleMessageSeller(listing.id; e)}
                  >
                    <MessageCircle className="w-3 h-3" />
    </Button>
                </div>
    </div>
            );
          })}
        </div>
        
        {/* Quick Actions */}
        <div className="mt-3 pt-2 border-t border-gray-200 dark:border-gray-700">
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              className="flex-1 text-xs h-8 dark:border-gray-700 dark:text-gray-200"
              onClick={() => {
                navigate(ROUTES.MARKETPLACE);
                toast.info('Browse all listings');
              }}
            >
              <Store className="w-3 h-3 mr-1" />
              Browse
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="flex-1 text-xs h-8 dark:border-gray-700 dark:text-gray-200"
              onClick={() => {
                navigate(ROUTES.MARKETPLACE);
                toast.info('Create new listing');
              }}
            >
              <DollarSign className="w-3 h-3 mr-1" />
              Sell
            </Button>
    </div>
        </div>
    </CardContent>
    </Card>
  );
});

MarketplaceContent.displayName = 'MarketplaceContent';

export default MarketplaceContent;