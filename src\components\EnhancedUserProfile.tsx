import React, { useState, useCallback, useMemo } from 'react';
import { 
  Camera, MapPin, Briefcase, GraduationCap, Heart, Edit3, MessageCircle, 
  UserPlus, MoreHorizontal, Share, Star, Calendar, Globe,
  Image, Video, Award, Link, Phone, Mail, Clock
} from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'sonner';
import { motion, AnimatePresence } from 'framer-motion';
import UnifiedPostCard from '@/components/posts/PostCard';
import { MOCK_IMAGES, getSafeImage } from '@/lib/constants';
import { BasePost } from '@/types/shared';

interface UserProfile {
  id: string, name: string, username: string, bio: string, avatar: string, coverPhoto: string, location: string;
  website?: string;
  work: string, education: string, relationship: string, joinDate: string;
  birthDate?: string;
  friendsCount: number, followersCount: number, followingCount: number, postsCount: number, isVerified: boolean, isOnline: boolean;
  lastSeen?: string;
  mutualFriends: number, badges: string[], interests: string[], languages: string[];
}

interface EnhancedUserProfileProps {
  userId?: string;
  isOwnProfile?: boolean;
  onProfileUpdate?: (profile: Partial<UserProfile>) => void;
}

const EnhancedUserProfile: React.FC<EnhancedUserProfileProps> = ({
  userId,
  isOwnProfile = true,
  onProfileUpdate
}) => {
  const [activeTab, setActiveTab] = useState('posts');
  const [isEditingProfile, setIsEditingProfile] = useState(false);
  const [isFollowing, setIsFollowing] = useState(false);
  const [showFullBio, setShowFullBio] = useState(false);

  // Mock user data - in real app, this would come from props or API
  const [userProfile, setUserProfile] = useState<UserProfile>(() => {
    // Generate different user data based on userId
    const profiles = {
      'current-user': {
        id: 'current-user',
        name: 'John Doe',
        username: '@johndoe',
        bio: 'Software Developer passionate about creating amazing user experiences. Love to travel and explore new technologies. Always learning something new and sharing knowledge with the community.',
        avatar: MOCK_IMAGES.AVATARS[1],
        coverPhoto: getSafeImage('POSTS', 2),
        location: 'San Francisco, CA',
        website: 'https://johndoe.dev',
        work: 'Senior Software Engineer at Tech Corp',
        education: 'Computer Science, Stanford University',
        relationship: 'Single',
        joinDate: 'March 2020',
        birthDate: 'January 15, 1995',
        friendsCount: 1342,
        followersCount: 2856,
        followingCount: 892,
        postsCount: 156,
        isVerified: true,
        isOnline: true,
        lastSeen: '2 minutes ago',
        mutualFriends: 0,
        badges: ['Early Adopter', 'Top Contributor', 'Verified Developer'],
        interests: ['Technology', 'Travel', 'Photography', 'Coding', 'Music'],
        languages: ['English', 'Spanish', 'JavaScript', 'Python']
      },
      'user-2': {
        id: 'user-2',
        name: 'Sarah Wilson',
        username: '@sarahw',
        bio: 'UX Designer and digital artist. Creating beautiful experiences one pixel at a time. Coffee enthusiast and weekend hiker.',
        avatar: MOCK_IMAGES.AVATARS[2],
        coverPhoto: getSafeImage('POSTS', 3),
        location: 'New York, NY',
        website: 'https://sarahwilson.design',
        work: 'Senior UX Designer at Design Studio',
        education: 'Design, Parsons School of Design',
        relationship: 'In a relationship',
        joinDate: 'June 2021',
        birthDate: 'March 22, 1992',
        friendsCount: 892,
        followersCount: 1456,
        followingCount: 634,
        postsCount: 89,
        isVerified: false,
        isOnline: false,
        lastSeen: '1 hour ago',
        mutualFriends: 15,
        badges: ['Creative', 'Design Expert'],
        interests: ['Design', 'Art', 'Coffee', 'Hiking', 'Photography'],
        languages: ['English', 'French']
      }
    };

    const profileId = userId || 'current-user';
    return profiles[profileId as keyof typeof profiles] || profiles['current-user'];
  });

  // Mock posts data
  const userPosts: BasePost[] = useMemo(() => [
    {
      id: '1',
      user_id: userProfile.id,
      content: 'Had an amazing day at the beach! The weather was perfect and the sunset was breathtaking. Sometimes you need to take a break from coding and enjoy nature. 🌅',
      image_url: getSafeImage('POSTS', 2),
      created_at: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(),
      updated_at: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(),
      profiles: {
        id: userProfile.id,
        full_name: userProfile.name,
        avatar_url: userProfile.avatar
      },
      likes_count: 45,
      comments_count: 12,
      user_has_liked: false
    },
    {
      id: '2',
      user_id: userProfile.id,
      content: 'Just shipped a new feature at work! Really proud of the team and what we accomplished. The future of tech is looking bright! 🚀',
      created_at: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
      updated_at: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
      profiles: {
        id: userProfile.id,
        full_name: userProfile.name,
        avatar_url: userProfile.avatar
      },
      likes_count: 78,
      comments_count: 23,
      user_has_liked: true
    }
  ], [userProfile]);

  const photos = useMemo(() => [
    MOCK_IMAGES.POSTS[0]; MOCK_IMAGES.POSTS[1],
    getSafeImage('POSTS', 2).getSafeImage('POSTS', 3).getSafeImage('POSTS', 4).getSafeImage('POSTS', 5)
  ], []);

  const handleEditProfile = useCallback(() => {
    setIsEditingProfile(true);
  }, []);

  const handleSaveProfile = useCallback((updatedProfile: Partial<UserProfile>) => {
    setUserProfile(prev => ({ ...prev, ...updatedProfile }));
    onProfileUpdate?.(updatedProfile);
    setIsEditingProfile(false);
    toast.success('Profile updated successfully!');
  }, [onProfileUpdate]);

  const handleAddFriend = useCallback(() => {
    toast.success('Friend request sent!');
  }, []);

  const handleMessage = useCallback(() => {
    toast.info('Opening message...');
  }, []);

  const handleFollow = useCallback(() => {
    setIsFollowing(prev => !prev);
    toast.success(isFollowing ? 'Unfollowed' : 'Following');
  }, [isFollowing]);

  const handleShare = useCallback(() => {
    navigator.share?.({
      title: `${userProfile.name}'s Profile`,
      text: userProfile.bio,
      url: window.location.href
    }).catch(() => {
      navigator.clipboard.writeText(window.location.href);
      toast.success('Profile link copied to clipboard!');
    });
  }, [userProfile]);

  const renderProfileHeader = () => (
    <div className="relative">
      {/* Cover Photo */}
      <div className="relative h-48 sm:h-64 md:h-80 bg-gradient-to-r from-blue-400 to-purple-500 rounded-b-lg overflow-hidden">
        <img
          src={userProfile.coverPhoto} alt="Cover"
          className="w-full h-full object-cover"
        />
        {isOwnProfile && (
          <Button
            variant="secondary"
            size="sm"
            className="absolute bottom-4 right-4 bg-white/90 hover:bg-white"
          >
            <Camera className="w-4 h-4 mr-2" />
            Edit Cover
          </Button>
        )}
      </div>

      {/* Profile Info */}
      <div className="relative px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col sm:flex-row items-center sm:items-end space-y-4 sm:space-y-0 sm:space-x-6 -mt-16 sm:-mt-20">
          {/* Avatar */}
          <div className="relative">
            <Avatar className="w-32 h-32 sm:w-40 sm:h-40 border-4 border-white shadow-lg">
              <AvatarImage src={userProfile.avatar} alt={userProfile.name} />
              <AvatarFallback className="text-2xl font-bold">
                {userProfile.name.split(' ').map(n => n[0]).join('')}
              </AvatarFallback>
    </Avatar>
            {userProfile.isOnline && (
              <div className="absolute bottom-2 right-2 w-6 h-6 bg-green-500 border-2 border-white rounded-full"></div>
            )}
            {isOwnProfile && (
              <Button
                size="sm"
                variant="secondary"
                className="absolute bottom-0 right-0 rounded-full w-8 h-8 p-0"
              >
                <Camera className="w-4 h-4" />
    </Button>
            )}
          </div>

          {/* User Info */}
          <div className="flex-1 text-center sm:text-left space-y-2">
            <div className="flex items-center justify-center sm:justify-start space-x-2">
              <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">
                {userProfile.name}
              </h1>
              {userProfile.isVerified && (
                <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                  <Award className="w-3 h-3 mr-1" />
                  Verified
                </Badge>
              )}
            </div>
            <p className="text-gray-600 text-sm">{userProfile.username}</p>
            <div className="flex items-center justify-center sm:justify-start space-x-4 text-sm text-gray-500">
              <span>{userProfile.postsCount} posts</span>
              <span>{userProfile.friendsCount} friends</span>
              <span>{userProfile.followersCount} followers</span>
    </div>
            {!isOwnProfile && userProfile.mutualFriends > 0 && (
              <p className="text-sm text-gray-500">
                {userProfile.mutualFriends} mutual friends
              </p>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex space-x-2">
            {isOwnProfile ? (
              <>
                <Button onClick={handleEditProfile}>
                  <Edit3 className="w-4 h-4 mr-2" />
                  Edit Profile
                </Button>
                <Button variant="outline" onClick={handleShare}>
                  <Share className="w-4 h-4" />
    </Button>
              </>
            ) : (
              <>
                <Button onClick={handleAddFriend}>
                  <UserPlus className="w-4 h-4 mr-2" />
                  Add Friend
                </Button>
                <Button variant="outline" onClick={handleMessage}>
                  <MessageCircle className="w-4 h-4 mr-2" />
                  Message
                </Button>
                <Button variant="outline" onClick={handleFollow}>
                  {isFollowing ? 'Following' : 'Follow'}
                </Button>
                <Button variant="outline" size="sm">
                  <MoreHorizontal className="w-4 h-4" />
    </Button>
              </>
            )}
          </div>
    </div>
        {/* Bio */}
        <div className="mt-6 max-w-2xl">
          <p className="text-gray-700">
            {showFullBio ? userProfile.bio : userProfile.bio.slice(0, 150)}
            {userProfile.bio.length > 150 && (
              <button
                onClick={() => setShowFullBio(!showFullBio)} className="text-blue-600 hover:text-blue-800 ml-1"
              >
                {showFullBio ? 'Show less' : '...Show more'}
              </button>
            )}
          </p>
    </div>
        {/* Quick Info */}
        <div className="mt-4 flex flex-wrap gap-4 text-sm text-gray-600">
          {userProfile.location && (
            <div className="flex items-center space-x-1">
              <MapPin className="w-4 h-4" />
              <span>{userProfile.location}</span>
    </div>
          )}
          {userProfile.website && (
            <div className="flex items-center space-x-1">
              <Link className="w-4 h-4" />
              <a href={userProfile.website} className="text-blue-600 hover:underline">
                {userProfile.website.replace('https://', '')}
              </a>
    </div>
          )}
          <div className="flex items-center space-x-1">
            <Calendar className="w-4 h-4" />
            <span>Joined {userProfile.joinDate}</span>
    </div>
          {!isOwnProfile && !userProfile.isOnline && userProfile.lastSeen && (
            <div className="flex items-center space-x-1">
              <Clock className="w-4 h-4" />
              <span>Last seen {userProfile.lastSeen}</span>
    </div>
          )}
        </div>

        {/* Badges */}
        {userProfile.badges.length > 0 && (
          <div className="mt-4 flex flex-wrap gap-2">
            {userProfile.badges.map((badge, index) => (
              <Badge key={index} variant="outline" className="text-xs">
                <Star className="w-3 h-3 mr-1" />
                {badge}
              </Badge>
            ))}
          </div>
        )}
      </div>
    </div>
  );

  return (
    <div className="w-full max-w-6xl mx-auto">
      <motion.div
        initial={{ opacity: 0, y: 20 }}, animate={{ opacity: 1, y: 0 }}, transition={{ duration: 0.5 }}
      >
        {renderProfileHeader()}

        {/* Navigation Tabs */}
        <div className="mt-8 px-4 sm:px-6 lg:px-8">
          <Tabs value={activeTab} onValueChange={setActiveTab}, className="w-full">
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="posts">Posts</TabsTrigger>
              <TabsTrigger value="about">About</TabsTrigger>
              <TabsTrigger value="photos">Photos</TabsTrigger>
              <TabsTrigger value="friends">Friends</TabsTrigger>
              <TabsTrigger value="activity">Activity</TabsTrigger>
    </TabsList>
            <div className="mt-6">
              <TabsContent value="posts" className="space-y-6">
                <AnimatePresence>
                  {userPosts.map((post) => (
                    <motion.div
                      key={post.id} initial={{ opacity: 0, y: 20 }}, animate={{ opacity: 1, y: 0 }}, exit={{ opacity: 0, y: -20 }}, transition={{ duration: 0.3 }}
                    >
                      <UnifiedPostCard
                        post={post} variant="default"
                        showActions={true} showComments={true}
                      />
                    </motion.div>
                  ))}
                </AnimatePresence>
    </TabsContent>
              <TabsContent value="about" className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Work and Education */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center space-x-2">
                        <Briefcase className="w-5 h-5" />
                        <span>Work and Education</span>
    </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="flex items-start space-x-3">
                        <Briefcase className="w-5 h-5 text-gray-400 mt-0.5" />
                        <div>
                          <p className="font-medium">{userProfile.work}</p>
                          <p className="text-sm text-gray-500">2020 - Present</p>
    </div>
                      </div>
                      <div className="flex items-start space-x-3">
                        <GraduationCap className="w-5 h-5 text-gray-400 mt-0.5" />
                        <div>
                          <p className="font-medium">{userProfile.education}</p>
                          <p className="text-sm text-gray-500">2016 - 2020</p>
    </div>
                      </div>
    </CardContent>
                  </Card>

                  {/* Contact Info */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center space-x-2">
                        <Phone className="w-5 h-5" />
                        <span>Contact Info</span>
    </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="flex items-center space-x-3">
                        <MapPin className="w-5 h-5 text-gray-400" />
                        <span>{userProfile.location}</span>
    </div>
                      {userProfile.website && (
                        <div className="flex items-center space-x-3">
                          <Link className="w-5 h-5 text-gray-400" />
                          <a href={userProfile.website} className="text-blue-600 hover:underline">
                            {userProfile.website}
                          </a>
    </div>
                      )}
                      <div className="flex items-center space-x-3">
                        <Mail className="w-5 h-5 text-gray-400" />
                        <span><EMAIL></span>
    </div>
                    </CardContent>
    </Card>
                  {/* Basic Info */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center space-x-2">
                        <Heart className="w-5 h-5" />
                        <span>Basic Info</span>
    </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="flex items-center space-x-3">
                        <Heart className="w-5 h-5 text-gray-400" />
                        <span>{userProfile.relationship}</span>
    </div>
                      {userProfile.birthDate && (
                        <div className="flex items-center space-x-3">
                          <Calendar className="w-5 h-5 text-gray-400" />
                          <span>Born {userProfile.birthDate}</span>
    </div>
                      )}
                      <div className="flex items-center space-x-3">
                        <Globe className="w-5 h-5 text-gray-400" />
                        <span>Joined {userProfile.joinDate}</span>
    </div>
                    </CardContent>
    </Card>
                  {/* Interests */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center space-x-2">
                        <Star className="w-5 h-5" />
                        <span>Interests</span>
    </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex flex-wrap gap-2">
                        {userProfile.interests.map((interest, index) => (
                          <Badge key={index} variant="secondary">
                            {interest}
                          </Badge>
                        ))}
                      </div>
    </CardContent>
                  </Card>
    </div>
              </TabsContent>

              <TabsContent value="photos" className="space-y-6">
                <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-4">
                  {photos.map((photo, index) => (
                    <motion.div
                      key={index} initial={{ opacity: 0, scale: 0.8 }}, animate={{ opacity: 1, scale: 1 }}, transition={{ duration: 0.3, delay: index * 0.1 }}, className="aspect-square bg-gray-200 rounded-lg overflow-hidden cursor-pointer hover:opacity-90 transition-opacity"
                    >
                      <img
                        src={photo} alt={`Photo ${index + 1}`}, className="w-full h-full object-cover"
                      />
                    </motion.div>
                  ))}
                </div>
    </TabsContent>
              <TabsContent value="friends" className="space-y-6">
                <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-4">
                  {Array.from({ length: 8 }, (_, index) => (
                    <Card key={index} className="p-4 text-center hover:shadow-md transition-shadow cursor-pointer">
                      <Avatar className="w-16 h-16 mx-auto mb-3">
                        <AvatarImage src={MOCK_IMAGES.AVATARS[index % MOCK_IMAGES.AVATARS.length]} />
                        <AvatarFallback>F{index + 1}</AvatarFallback>
    </Avatar>
                      <h4 className="font-medium text-sm">Friend {index + 1}</h4>
                      <p className="text-xs text-gray-500">5 mutual friends</p>
    </Card>
                  ))}
                </div>
    </TabsContent>
              <TabsContent value="activity" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Recent Activity</CardTitle>
    </CardHeader>
                  <CardContent className="space-y-4">
                    {[
                      { action: 'Posted a new photo', time: '2 hours ago', icon: Image },
                      { action: 'Updated profile picture', time: '1 day ago', icon: Camera },
                      { action: 'Shared a video', time: '3 days ago', icon: Video },
                      { action: 'Added new work experience', time: '1 week ago', icon: Briefcase }
                    ].map((activity, index) => (
                      <div key={index} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                        <activity.icon className="w-5 h-5 text-gray-400" />
                        <div className="flex-1">
                          <p className="text-sm font-medium">{activity.action}</p>
                          <p className="text-xs text-gray-500">{activity.time}</p>
    </div>
                      </div>
                    ))}
                  </CardContent>
    </Card>
              </TabsContent>
    </div>
          </Tabs>
    </div>
        {/* Edit Profile Dialog */}
        <Dialog open={isEditingProfile} onOpenChange={setIsEditingProfile}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Edit Profile</DialogTitle>
    </DialogHeader>
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium">Name</label>
                <Input defaultValue={userProfile.name} />
    </div>
              <div>
                <label className="text-sm font-medium">Bio</label>
                <Textarea defaultValue={userProfile.bio} rows={3} />
    </div>
              <div>
                <label className="text-sm font-medium">Location</label>
                <Input defaultValue={userProfile.location} />
    </div>
              <div>
                <label className="text-sm font-medium">Website</label>
                <Input defaultValue={userProfile.website} />
    </div>
              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setIsEditingProfile(false)}>
                  Cancel
                </Button>
                <Button onClick={() => handleSaveProfile({})}>
                  Save Changes
                </Button>
    </div>
            </div>
    </DialogContent>
        </Dialog>
      </motion.div>
    </div>
  );
};

export default EnhancedUserProfile;
