/**
 * Error Notification Component
 * User-friendly error notifications for messaging
 */

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  AlertTriangle, 
  Wifi, 
  WifiOff, 
  RefreshCw, 
  X, 
  Clock,
  Shield,
  Zap
} from 'lucide-react';
import { MessagingError, ConnectionStatus } from '@/types/messaging';
import { cn } from '@/lib/utils';

interface ErrorNotificationProps {
  error: MessagingError | null, connectionStatus: ConnectionStatus;
  onRetry?: () => void;
  onDismiss?: () => void;
  className?: string;
  position?: 'top' | 'bottom' | 'inline';
  showDetails?: boolean;
}

const errorIcons = {
  NETWORK_ERROR: WifiOff,
  CONNECTION_LOST: WifiOff,
  RATE_LIMITED: Clock,
  UNAUTHORIZED: Shield,
  VALIDATION_ERROR: AlertTriangle,
  SERVER_ERROR: Zap,
  default: AlertTriangle
};

const errorColors = {
  NETWORK_ERROR: 'border-orange-200 bg-orange-50 text-orange-800 dark:bg-orange-900/20 dark:text-orange-300',
  CONNECTION_LOST: 'border-red-200 bg-red-50 text-red-800 dark:bg-red-900/20 dark:text-red-300',
  RATE_LIMITED: 'border-yellow-200 bg-yellow-50 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300',
  UNAUTHORIZED: 'border-purple-200 bg-purple-50 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300',
  VALIDATION_ERROR: 'border-blue-200 bg-blue-50 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300',
  SERVER_ERROR: 'border-red-200 bg-red-50 text-red-800 dark:bg-red-900/20 dark:text-red-300',
  default: 'border-gray-200 bg-gray-50 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300'
};

const connectionStatusMessages = {
  connected: { message: 'Connected', icon: Wifi, color: 'text-green-600' },
  connecting: { message: 'Connecting...', icon: RefreshCw, color: 'text-yellow-600' },
  disconnected: { message: 'Disconnected', icon: WifiOff, color: 'text-red-600' }
};

export const ErrorNotification: React.FC<ErrorNotificationProps> = ({
  error,
  connectionStatus,
  onRetry,
  onDismiss,
  className,
  position = 'top',
  showDetails = false
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [autoHideTimer, setAutoHideTimer] = useState<NodeJS.Timeout | null>(null);

  // Show notification when error occurs
  useEffect(() => {
    if (error || connectionStatus === 'disconnected') {
      setIsVisible(true);
      
      // Auto-hide for non-critical errors
      if (error && error.recoverable && !autoHideTimer) {
        const timer = setTimeout(() => {
          setIsVisible(false);
          onDismiss?.();
        }, 8000);
        setAutoHideTimer(timer);
      }
    } else {
      setIsVisible(false);
    }

    return () => {
      if (autoHideTimer) {
        clearTimeout(autoHideTimer);
        setAutoHideTimer(null);
      }
    };
  }, [error, connectionStatus, onDismiss, autoHideTimer]);

  const handleDismiss = () => {
    setIsVisible(false);
    onDismiss?.();
    if (autoHideTimer) {
      clearTimeout(autoHideTimer);
      setAutoHideTimer(null);
    }
  };

  const getErrorIcon = (errorCode: string) => {
    return errorIcons[errorCode as keyof typeof errorIcons] || errorIcons.default;
  };

  const getErrorColor = (errorCode: string) => {
    return errorColors[errorCode as keyof typeof errorColors] || errorColors.default;
  };

  const getPositionClasses = () => {
    switch (position) {
      case 'top':
        return 'fixed top-4 left-1/2 transform -translate-x-1/2 z-50 max-w-md';
      case 'bottom':
        return 'fixed bottom-4 left-1/2 transform -translate-x-1/2 z-50 max-w-md';
      case 'inline':
        return 'relative w-full';
      default:
        return 'relative w-full';
    }
  };

  // Connection status notification
  if (!error && connectionStatus !== 'connected') {
    const statusInfo = connectionStatusMessages[connectionStatus];
    const StatusIcon = statusInfo.icon;

    return (
      <AnimatePresence>
        {isVisible && (
          <motion.div
            initial={{ opacity: 0, y: position === 'bottom' ? 50 : -50 }}, animate={{ opacity: 1, y: 0 }}, exit={{ opacity: 0, y: position === 'bottom' ? 50 : -50 }}, className={cn(getPositionClasses(), className)}
          >
            <Alert className="border-yellow-200 bg-yellow-50 dark:bg-yellow-900/20">
              <StatusIcon className={cn("h-4 w-4", statusInfo.color, connectionStatus === 'connecting' && "animate-spin")} />
              <AlertTitle className="flex items-center justify-between">
                <span>Connection Status</span>
                <Badge variant="outline" className={statusInfo.color}>
                  {statusInfo.message}
                </Badge>
    </AlertTitle>
              <AlertDescription>
                {connectionStatus === 'connecting' 
                  ? 'Attempting to reconnect to messaging service...'
                  : 'Lost connection to messaging service. Some features may not work.'
                }
              </AlertDescription>
              {onRetry && connectionStatus === 'disconnected' && (
                <div className="mt-3 flex gap-2">
                  <Button size="sm" onClick={onRetry} variant="outline">
                    <RefreshCw className="w-3 h-3 mr-1" />
                    Retry Connection
                  </Button>
    </div>
              )}
            </Alert>
          </motion.div>
        )}
      </AnimatePresence>
    );
  }

  // Error notification
  if (!error) return null;

  const ErrorIcon = getErrorIcon(error.code);
  const errorColorClass = getErrorColor(error.code);

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, y: position === 'bottom' ? 50 : -50 }}, animate={{ opacity: 1, y: 0 }}, exit={{ opacity: 0, y: position === 'bottom' ? 50 : -50 }}, className={cn(getPositionClasses(), className)}
        >
          <Alert className={errorColorClass}>
            <ErrorIcon className="h-4 w-4" />
            <AlertTitle className="flex items-center justify-between">
              <span>Messaging Error</span>
              <div className="flex items-center gap-2">
                {error.recoverable && (
                  <Badge variant="outline" className="text-xs">
                    Recoverable
                  </Badge>
                )}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleDismiss} className="h-6 w-6 p-0 hover:bg-black/10"
                >
                  <X className="h-3 w-3" />
    </Button>
              </div>
    </AlertTitle>
            <AlertDescription className="space-y-2">
              <p>{error.message}</p>
              
              {showDetails && (
                <details className="text-xs opacity-75">
                  <summary className="cursor-pointer hover:opacity-100">
                    Technical Details
                  </summary>
                  <div className="mt-2 space-y-1">
                    <p><strong>Code:</strong> {error.code}</p>
                    <p><strong>Time:</strong> {new Date(error.timestamp).toLocaleString()}</p>
                    {error.details && (
                      <p><strong>Details:</strong> {JSON.stringify(error.details, null, 2)}</p>
                    )}
                  </div>
    </details>
              )}
              
              {(onRetry && error.recoverable) && (
                <div className="flex gap-2 mt-3">
                  <Button size="sm" onClick={onRetry} variant="outline">
                    <RefreshCw className="w-3 h-3 mr-1" />
                    Retry
                  </Button>
                  {!error.recoverable && (
                    <Button size="sm" onClick={() => window.location.reload()} variant="outline">
                      Refresh Page
                    </Button>
                  )}
                </div>
              )}
            </AlertDescription>
    </Alert>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default ErrorNotification;