import React, { useState, useRef, useEffect, useMemo, memo } from 'react';
import { Search, X, User, FileText } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui';
import { motion, AnimatePresence } from 'framer-motion';
import { Message, User as UserType } from '@/types/enhanced-messaging';
import { useMessaging } from '@/hooks/useMessaging';

interface OptimizedMessageSearchProps {
  isOpen: boolean, onClose: () => void; currentUserId: string;
  conversationId?: string;
}

const OptimizedMessageSearch: React.FC<OptimizedMessageSearchProps> = memo(({
  isOpen,
  onClose,
  currentUserId,
  conversationId
}) => {
  // Mock implementation for now
  const conversations: unknown[] = [];
  const searchResults: unknown[] = [];
  const searchMessages = (query: string) => {
    console.log('Searching messages:', query);
  };
  const clearSearch = () => {
    console.log('Clearing search');
  };

  const [searchQuery, setSearchQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [selectedUser, setSelectedUser] = useState<string>('');
  const [selectedType, setSelectedType] = useState<'all' | 'text' | 'file' | 'image'>('all');
  
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Get users from conversations
  const users = useMemo(() => 
    conversations
      .map(conv => conv.user)
      .filter(Boolean) as UserType[]
  , [conversations]);

  // Focus input when opened
  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isOpen]);

  // Perform search
  const performSearch = async () => {
    if (!searchQuery.trim()) return;
    
    setIsSearching(true);
    try {
      await searchMessages(searchQuery, {
        conversationId,
        senderId: selectedUser || undefined,
        type: selectedType === 'all' ? undefined : selectedType as Message['type']
      });
    } finally {
      setIsSearching(false);
    }
  };

  // Handle clear
  const handleClear = () => {
    setSearchQuery('');
    setSelectedUser('');
    setSelectedType('all');
    clearSearch();
  };

  // Handle close
  const handleClose = () => {
    handleClear();
    onClose();
  };

  if (!isOpen) return null;

  return (
    <motion.div
      initial={{ opacity: 0 }}, animate={{ opacity: 1 }}, exit={{ opacity: 0 }}, className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4"
      onClick={handleClose}
    >
      <motion.div
        initial={{ scale: 0.9, y: 20 }}, animate={{ scale: 1, y: 0 }}, exit={{ scale: 0.9, y: 20 }}, className="bg-white dark:bg-gray-900 rounded-xl shadow-2xl w-full max-w-2xl max-h-[80vh] flex flex-col"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold">Search Messages</h2>
            <Button variant="ghost" size="sm" onClick={handleClose}>
              <X className="w-4 h-4" />
    </Button>
          </div>
          
          {/* Search Input */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <Input
              ref={searchInputRef} value={searchQuery}, onChange={(e) => setSearchQuery(e.target.value)} placeholder="Search messages..."
              className="pl-10"
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  performSearch();
                }
              }}
            />
    </div>
          {/* Filters */}
          <div className="flex flex-wrap gap-2 mt-3">
            <select
              value={selectedUser} onChange={(e) => setSelectedUser(e.target.value)}, className="px-3 py-1 text-sm border border-gray-200 dark:border-gray-700 rounded-md bg-white dark:bg-gray-800"
            >
              <option value="">All Users</option>
              {users.map((user) => (
                <option key={user.id} value={user.id}>
                  {user.name}
                </option>
              ))}
            </select>
            
            <select
              value={selectedType} onChange={(e) => setSelectedType(e.target.value as 'all' | 'text' | 'file' | 'image')}, className="px-3 py-1 text-sm border border-gray-200 dark:border-gray-700 rounded-md bg-white dark:bg-gray-800"
            >
              <option value="all">All Types</option>
              <option value="text">Text</option>
              <option value="file">Files</option>
              <option value="image">Images</option>
    </select>
            <Button size="sm" onClick={performSearch} disabled={!searchQuery.trim() || isSearching}>
              {isSearching ? 'Searching...' : 'Search'}
            </Button>
            
            {(searchQuery || selectedUser || selectedType !== 'all') && (
              <Button variant="outline" size="sm" onClick={handleClear}>
                Clear
              </Button>
            )}
          </div>
    </div>
        {/* Results */}
        <ScrollArea className="flex-1 p-4">
          {searchResults.length === 0 && searchQuery && !isSearching ? (
            <div className="text-center py-8">
              <Search className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No messages found for "{searchQuery}"</p>
    </div>
          ) : searchResults.length === 0 && !searchQuery ? (
            <div className="text-center py-8">
              <Search className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">Enter a search term to find messages</p>
    </div>
          ) : (
            <div className="space-y-3">
              <AnimatePresence>
                {searchResults.map((result, index) => {
                  const conversation = conversations.find(c => c.id === result.conversationId);
                  const sender = conversation?.user;
                  
                  return (
                    <motion.div
                      key={`${result.conversationId}-${index}`}, initial={{ opacity: 0, y: 10 }}, animate={{ opacity: 1, y: 0 }}, exit={{ opacity: 0, y: -10 }}, className="p-3 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer transition-colors"
                      onClick={() => {
                        // Handle result click - could navigate to conversation
                        handleClose();
                      }}
                    >
                      <div className="flex items-start gap-3">
                        <Avatar className="w-8 h-8">
                          <AvatarImage src={sender?.avatar} />
                          <AvatarFallback className="text-xs">
                            {sender?.name?.[0]}
                          </AvatarFallback>
    </Avatar>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="font-medium text-sm">
                              {sender?.name || 'Unknown User'}
                            </span>
                            <span className="text-xs text-gray-500">
                              in {conversation?.name || 'Unknown Chat'}
                            </span>
                            <span className="text-xs text-gray-400">
                              {new Date(result.message.timestamp).toLocaleDateString()}
                            </span>
    </div>
                          <p className="text-sm text-gray-600 dark:text-gray-300 break-words">
                            {result.message.content}
                          </p>
                          
                          {result.message.type !== 'text' && (
                            <Badge variant="secondary" className="mt-1">
                              {result.message.type}
                            </Badge>
                          )}
                        </div>
                        
                        <div className="flex-shrink-0">
                          {result.message.type === 'file' && <FileText className="w-4 h-4 text-gray-400" />}
                          {result.message.type === 'text' && <User className="w-4 h-4 text-gray-400" />}
                        </div>
    </div>
                    </motion.div>
                  );
                })}
              </AnimatePresence>
    </div>
          )}
        </ScrollArea>
      </motion.div>
    </motion.div>
  );
});

OptimizedMessageSearch.displayName = 'OptimizedMessageSearch';

export default OptimizedMessageSearch;
