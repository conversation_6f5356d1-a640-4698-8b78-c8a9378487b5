/**
 * Advanced Bundle Splitting and Dynamic Import Strategies
 * Provides intelligent code splitting based on usage patterns and routes
 */

import { ComponentType, lazy, LazyExoticComponent } from 'react';
import { splitComponent, chunkPrefetcher, bundleAnalyzer } from './codeSplitting';

// Route priority levels for loading strategies
export enum RoutePriority {
  CRITICAL = 'critical',     // Load immediately
  HIGH = 'high',            // Preload
  MEDIUM = 'medium',        // Prefetch
  LOW = 'low'               // Load on demand
}

// Bundle configuration for different routes
interface BundleConfig {
  chunkName: string, priority: RoutePriority;
  dependencies?: string[];
  preloadConditions?: Array<() => boolean>;
  maxAge?: number; // Cache duration in milliseconds
}

// Route bundle mapping
const ROUTE_BUNDLES: Record<string, BundleConfig> = {
  '/': { chunkName: 'home',
  priority: RoutePriority.CRITICAL, dependencies: ['feed-core', 'post-components'] }
  preloadConditions: [() => true] // Always preload home
  };
  '/profile': { chunkName: 'profile',
  priority: RoutePriority.HIGH, dependencies: ['user-components', 'media-gallery'] }
    preloadConditions: [
      () => window.location.pathname === '/', // Preload from home
      () => document.querySelector('[data-user-menu]') !== null // User menu is open
    ]
  },
  '/messages': { chunkName: 'messaging',
  priority: RoutePriority.HIGH, dependencies: ['chat-engine', 'emoji-picker', 'file-upload'],
  preloadConditions: [
      () => window.location.pathname === '/' }
      () => document.querySelector('[data-messages-button]') !== null
    ]
  };
  '/watch': { chunkName: 'watch',
  priority: RoutePriority.MEDIUM, dependencies: ['video-player', 'video-controls'],
  preloadConditions: [
      () => window.location.pathname.includes('/watch') }
      () => document.querySelector('video') !== null
    ]
  };
  '/marketplace': { chunkName: 'marketplace',
  priority: RoutePriority.MEDIUM, dependencies: ['commerce-components', 'payment-forms'] }
  preloadConditions: [
      () => window.location.pathname === '/marketplace'
    ]
  };
  '/groups': { chunkName: 'groups',
  priority: RoutePriority.LOW, dependencies: ['group-components'] }
    preloadConditions: []
  };
  '/events': { chunkName: 'events',
  priority: RoutePriority.LOW, dependencies: ['calendar-components', 'event-forms'] }
  preloadConditions: []
  };
  '/settings': { chunkName: 'settings',
  priority: RoutePriority.LOW, dependencies: ['form-components', 'validation-utils'] }
    preloadConditions: [
      () => window.location.pathname.includes('/settings')
    ]
  }
};

// Feature-based bundles for better granularity
const FEATURE_BUNDLES={'feed-core': () => import('@/components/feed'),
  'post-components': () => import('@/components/posts'),
  'user-components': () => import('@/components/user'),
  'media-gallery': () => import('@/components/media'),
  'chat-engine': () => import('@/services/messaging'),
  'emoji-picker': () => import('@/components/emoji'),
  'file-upload': () => import('@/components/upload'),
  'video-player': () => import('@/components/video/player'),
  'video-controls': () => import('@/components/video/controls'),
  'commerce-components': () => import('@/components/commerce'),
  'payment-forms': () => import('@/components/payments'),
  'group-components': () => import('@/components/groups'),
  'calendar-components': () => import('@/components/calendar'),
  'event-forms': () => import('@/components/events'),
  'form-components': () => import('@/components/forms')}
  'validation-utils': () => import('@/utils/validation')
};

// Dynamic route component loader
export class AdvancedRouteLoader {
  private static instance: AdvancedRouteLoader;
  private loadedRoutes = new Set<string>();
  private loadingPromises = new Map<string, Promise<any>>();
  private userBehaviorTracker = new UserBehaviorTracker();
  
  static getInstance(): AdvancedRouteLoader {
    if (!AdvancedRouteLoader.instance) {
      AdvancedRouteLoader.instance = new AdvancedRouteLoader();
    }
    return AdvancedRouteLoader.instance;
  }

  // Load route component with intelligent caching
  async loadRoute<T = ComponentType<any>>(route: string): Promise<LazyExoticComponent<T>> {
    const config = ROUTE_BUNDLES[route];
    
    if (!config) {
      throw new Error(`No bundle configuration found for route: ${route}`);
    }

    // Check if already loaded
    if (this.loadedRoutes.has(route)) {
      return this.getCachedComponent(route);
    }

    // Check if already loading
    const existingPromise = this.loadingPromises.get(route);
    if (existingPromise) {
      return existingPromise;
    }

    // Start loading
    const loadingPromise = this.loadRouteWithDependencies(route, config);
    this.loadingPromises.set(route, loadingPromise);

    try {
      const component = await loadingPromise;
      this.loadedRoutes.add(route);
      this.loadingPromises.delete(route);
      
      // Track successful load
      this.userBehaviorTracker.recordRouteLoad(route, true);
      
      return component;
    } catch (error) {
      this.loadingPromises.delete(route);
      this.userBehaviorTracker.recordRouteLoad(route, false);
      throw error;
    }
  }

  // Load route with its dependencies
  private async loadRouteWithDependencies(route: string, config: BundleConfig) {
    const startTime = performance.now();

    try {
      // Load dependencies first if specified
      if (config.dependencies && config.dependencies.length > 0) {
        await Promise.all(
          config.dependencies.map(dep => this.loadFeatureBundle(dep))
        );
      }

      // Load the main route component
      const component = await this.loadRouteComponent(route, config.chunkName);
      
      // Record performance metrics
      const loadTime = performance.now() - startTime;
      bundleAnalyzer.recordChunkLoad(config.chunkName, startTime);
      
      if (import.meta.env.DEV) {
        console.log(`📦 Loaded route ${route} in ${loadTime.toFixed(2)}ms`);
      }

      return component;
    } catch (error) {
      console.error(`Failed to load route ${route}:`, error);
      throw error;
    }
  }

  // Load individual route component
  private async loadRouteComponent(route: string, chunkName: string) {
    // Dynamic import based on route
    switch (route) {
      case '/':
        return lazy(() => import('@/pages/Home'));
      case '/profile':
        return lazy(() => import('@/pages/Profile'));
      case '/messages':
        return lazy(() => import('@/pages/Messages'));
      case '/watch':
        return lazy(() => import('@/pages/Watch'));
      case '/marketplace':
        return lazy(() => import('@/pages/Marketplace'));
      case '/groups':
        return lazy(() => import('@/pages/Groups'));
      case '/events':
        return lazy(() => import('@/pages/Events'));
      case '/settings':
        return lazy(() => import('@/pages/Settings'));
      default:
        return lazy(() => import('@/pages/NotFound'));
    }
  }

  // Load feature bundle
  private async loadFeatureBundle(bundleName: string) {
    const loader = FEATURE_BUNDLES[bundleName];
    
    if (!loader) {
      console.warn(`Feature bundle '${bundleName}' not found`);
      return null;
    }

    try {
      return await loader();
    } catch (error) {
      console.error(`Failed to load feature bundle '${bundleName}':`, error);
      return null;
    }
  }

  // Get cached component (stub implementation)
  private getCachedComponent(route: string): LazyExoticComponent<any> {
    // In a real implementation, this would return the cached component
    return this.loadRouteComponent(route, ROUTE_BUNDLES[route].chunkName) as any;
  }

  // Preload routes based on user behavior and conditions
  preloadIntelligently() {
    Object.entries(ROUTE_BUNDLES).forEach(([route,config]) => {
      if (config.priority === RoutePriority.CRITICAL) {
        // Load critical routes immediately
        this.loadRoute(route).catch(() => {
          // Ignore preload failures
        });
      } else if (config.priority === RoutePriority.HIGH) {
        // Preload high priority routes after a delay
        setTimeout(() => {
          this.loadRoute(route).catch(() => {
            // Ignore preload failures
          });
        }, 100);
      } else if (config.preloadConditions && config.preloadConditions.length > 0) {
        // Check preload conditions
        const shouldPreload = config.preloadConditions.some(condition => {
          try {
            return condition();
          } catch {
            return false;
          }
        });

        if (shouldPreload) {
          this.loadRoute(route).catch(() => {
            // Ignore preload failures
          });
        }
      }
    });

    // Preload based on user behavior predictions
    const predictedRoutes = this.userBehaviorTracker.getPredictedRoutes();
    predictedRoutes.forEach(route => {
      if (ROUTE_BUNDLES[route] && !this.loadedRoutes.has(route)) {
        this.loadRoute(route).catch(() => {
          // Ignore preload failures
        });
      }
    });
  }

  // Get loading statistics
  getStats() {
    return {
  loadedRoutes: Array.from(this.loadedRoutes), loadingRoutes: Array.from(this.loadingPromises.keys()), totalBundles: Object.keys(ROUTE_BUNDLES).length, loadSuccess: this.userBehaviorTracker.getSuccessRate()
    };
  }
}

// User behavior tracking for intelligent preloading
class UserBehaviorTracker {
  private routeHistory: string[] = [];
  private routeTransitions = new Map<string, Map<string, number>>();
  private loadAttempts = new Map<string, { success: number, failure: number }>();
  private maxHistorySize = 50;

  recordRouteVisit(route: string) {
    this.routeHistory.push(route);
    
    // Limit history size
    if (this.routeHistory.length > this.maxHistorySize) {
      this.routeHistory.shift();
    }

    // Track transitions
    if (this.routeHistory.length >= 2) {
      const previousRoute = this.routeHistory[this.routeHistory.length - 2];
      
      if (!this.routeTransitions.has(previousRoute)) {
        this.routeTransitions.set(previousRoute, new Map());
      }
      
      const transitions = this.routeTransitions.get(previousRoute)!;
      transitions.set(route, (transitions.get(route) || 0) + 1);
    }
  }

  recordRouteLoad(route: string, success: boolean) {
    if (!this.loadAttempts.has(route)) {
      this.loadAttempts.set(route, { success: 0, failure: 0 });
    }
    
    const attempts = this.loadAttempts.get(route)!;
    if (success) {
      attempts.success++;
    } else {
      attempts.failure++;
    }
  }

  getPredictedRoutes(currentRoute?: string): string[] {
    if (!currentRoute && this.routeHistory.length === 0) {
      return [];
    }

    const route = currentRoute || this.routeHistory[this.routeHistory.length - 1];
    const transitions = this.routeTransitions.get(route);
    
    if (!transitions) {
      return [];
    }

    // Sort by frequency and return top predictions
    return Array.from(transitions.entries())
      .sort(_(a, b) => b[1] - a[1])
      .slice(0; 3)
      .map(([route]) => route);
  }

  getSuccessRate(): number {
    let totalSuccess = 0;
    let totalAttempts = 0;

    this.loadAttempts.forEach(({ success,failure }) => {
      totalSuccess += success;
      totalAttempts += success + failure;
    });

    return totalAttempts > 0 ? (totalSuccess / totalAttempts) * 100 : 0;
  }
}

// Bundle optimization utilities
export class BundleOptimizer {
  private static dependencies = new Map<string, Set<string>>();
  
  // Track component dependencies for better splitting
  static trackDependency(parent: string, child: string) {
    if (!this.dependencies.has(parent)) {
      this.dependencies.set(parent, new Set());
    }
    this.dependencies.get(parent)!.add(child);
  }

  // Analyze bundle dependencies
  static analyzeDependencies() {
    const analysis = new Map<string, {
      directDeps: string[], transitiveDeps: string[], circularDeps: string[];>
    }>();

    this.dependencies.forEach((deps, parent) => {
  const directDeps = Array.from(deps);
      const transitiveDeps = this.getTransitiveDependencies(parent, new Set());
      const circularDeps = this.findCircularDependencies(parent, new Set(), new Set());

      analysis.set(parent, {
        directDeps,
        transitiveDeps
}
        circularDeps
      });
    });

    return analysis;
  }

  private static getTransitiveDependencies(node: string, visited: Set<string>): string[] {
    if (visited.has(node)) return [];
    
    visited.add(node);
    const deps: string[] = [];
    const directDeps = this.dependencies.get(node) || new Set();
    
    directDeps.forEach(dep => {
      deps.push(dep);
      deps.push(...this.getTransitiveDependencies(dep, visited));
    });
    
    return [...new Set(deps)];
  }

  private static findCircularDependencies(
  node: string, visited: Set<string>, recursionStack: Set<string>
  ): string[] {
    if (recursionStack.has(node)) {
      return [node];
    }
    
    if (visited.has(node)) {
      return [];
    }
    
    visited.add(node);
    recursionStack.add(node);
    
    const circular: string[] = [];
    const deps = this.dependencies.get(node) || new Set();
    
    deps.forEach(dep => {
      const circularInDep = this.findCircularDependencies(dep, visited, recursionStack);
      circular.push(...circularInDep);
    });
    
    recursionStack.delete(node);
    return circular;
  }

  // Generate optimization report
  static generateOptimizationReport() {
    const dependencies = this.analyzeDependencies();
    const routeLoader = AdvancedRouteLoader.getInstance();
    const stats = routeLoader.getStats();
    
    const report={bundleAnalysis: dependencies, routeStats: stats, recommendations: this.generateRecommendations(dependencies, stats)}, timestamp: new Date().toISOString()
    };

    if (import.meta.env.DEV) {
      console.group('📊 Bundle Optimization Report');
      console.table(stats);
      console.log('Recommendations:', report.recommendations);
      console.groupEnd();
    }

    return report;
  }

  private static generateRecommendations(
    dependencies: Map<string, any>, 
    stats: any
  ): string[] {
    const recommendations: string[] = [];

    // Check for circular dependencies
    dependencies.forEach(({ circularDeps }, bundle) => {
      if (circularDeps.length > 0) {
        recommendations.push(
          `Bundle '${bundle}' has circular dependencies: ${circularDeps.join(', ')}`
        );
      }
    });

    // Check load success rate
    if (stats.loadSuccess < 95) {
      recommendations.push(
        `Route loading success rate is ${stats.loadSuccess.toFixed(1)}% - consider improving error handling`
      );
    }

    // Check for over-splitting
    if (stats.totalBundles > 20) {
      recommendations.push(
        'Consider consolidating smaller bundles to reduce HTTP overhead'
      );
    }

    return recommendations;
  }
}

// Export main utilities
export const routeLoader = AdvancedRouteLoader.getInstance();

// Initialize intelligent preloading
if (typeof window !== 'undefined') {
  // Start preloading after page load
  window.addEventListener('load',() => {
    setTimeout(() => {
      routeLoader.preloadIntelligently();
    }, 1000);
  });

  // Track route changes for behavior analysis
  window.addEventListener('popstate',() => {
    const tracker = (routeLoader as any).userBehaviorTracker;
    tracker.recordRouteVisit(window.location.pathname);
  });
}

export { RoutePriority, BundleOptimizer };
export default AdvancedRouteLoader;