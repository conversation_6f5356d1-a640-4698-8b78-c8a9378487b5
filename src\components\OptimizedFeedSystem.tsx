import React, { useState, useMemo, memo, useRef, useEffect } from 'react';
import { Heart, MessageCircle, Share2, Bookmark, MoreHorizontal, Eye, TrendingUp, Clock, Search } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { useImmediateOptimization } from '@/hooks/useImmediateOptimizations';
import { formatTimeAgo } from '@/utils/timeUtils';
import { FixedSizeList as List } from 'react-window';

interface FeedPost {
  id: string, author: {
    id: string, name: string, username: string, avatar: string, isVerified: boolean;
  };
  content: string, mediaUrls: string[], timestamp: Date, metrics: {
    likes: number, comments: number, shares: number, views: number;
  };
  userInteractions: {
    hasLiked: boolean, hasShared: boolean, hasBookmarked: boolean;
  };
  tags: string[], isPromoted: boolean, category: 'trending' | 'recent' | 'following' | 'recommended';
}

interface OptimizedFeedSystemProps {
  posts: FeedPost[], isLoading: boolean, hasMore: boolean, onLoadMore: () => void; onLike: (postId: string) => void; onUnlike: (postId: string) => void; onComment: (postId: string) => void; onShare: (postId: string) => void; onBookmark: (postId: string) => void; onUserClick: (userId: string) => void;
  className?: string;
}

// OPTIMIZATION 1: Memoized feed post component with comprehensive optimization
const OptimizedFeedPost = memo<{
  post: FeedPost, index: number, onLike: (postId: string) => void; onUnlike: (postId: string) => void; onComment: (postId: string) => void; onShare: (postId: string) => void; onBookmark: (postId: string) => void; onUserClick: (userId: string) => void;
  style?: React.CSSProperties;
}>(({ post, index, onLike, onUnlike, onComment, onShare, onBookmark, onUserClick, style }) => {
  const { createOptimizedCallback } = useImmediateOptimization(`FeedPost-${post.id}`);
  
  // OPTIMIZATION: Optimized interaction callbacks
  const handleLike = createOptimizedCallback(() => {
    if (post.userInteractions.hasLiked) {
      onUnlike(post.id);
    } else {
      onLike(post.id);
    }
  }, [post.id, post.userInteractions.hasLiked, onLike, onUnlike]);
  
  const handleComment = createOptimizedCallback(() => {
    onComment(post.id);
  }, [onComment, post.id]);
  
  const handleShare = createOptimizedCallback(() => {
    onShare(post.id);
  }, [onShare, post.id]);
  
  const handleBookmark = createOptimizedCallback(() => {
    onBookmark(post.id);
  }, [onBookmark, post.id]);
  
  const handleUserClick = createOptimizedCallback(() => {
    onUserClick(post.author.id);
  }, [onUserClick, post.author.id]);
  
  // OPTIMIZATION: Memoized timestamp formatting
  const timeDisplay = useMemo(() => 
    formatTimeAgo(post.timestamp),
    [post.timestamp]
  );
  
  // OPTIMIZATION: Memoized metrics formatting
  const formattedMetrics = useMemo(() => ({
    likes: post.metrics.likes >= 1000 
      ? `${(post.metrics.likes / 1000).toFixed(1)}k` 
      : post.metrics.likes.toString(),
    comments: post.metrics.comments >= 1000 
      ? `${(post.metrics.comments / 1000).toFixed(1)}k` 
      : post.metrics.comments.toString(),
    shares: post.metrics.shares >= 1000 
      ? `${(post.metrics.shares / 1000).toFixed(1)}k` 
      : post.metrics.shares.toString(),
    views: post.metrics.views >= 1000000
      ? `${(post.metrics.views / 1000000).toFixed(1)}M`
      : post.metrics.views >= 1000 
        ? `${(post.metrics.views / 1000).toFixed(1)}k` 
        : post.metrics.views.toString()
  }), [post.metrics]);
  
  // OPTIMIZATION: Memoized interaction states
  const interactionStyles = useMemo(() => ({
    like: post.userInteractions.hasLiked 
      ? 'text-red-500 fill-red-500' 
      : 'text-gray-500 hover:text-red-500',
    bookmark: post.userInteractions.hasBookmarked 
      ? 'text-blue-500 fill-blue-500' 
      : 'text-gray-500 hover:text-blue-500'
  }), [post.userInteractions]);
  
  return (
    <div style={style} className="px-4">
      <motion.div
        layout
        initial={{ opacity: 0, y: 20 }}, animate={{ opacity: 1, y: 0 }}, exit={{ opacity: 0, y: -20 }}, transition={{ duration: 0.3, delay: index * 0.05 }}
      >
        <Card className="mb-4 hover:shadow-md transition-shadow duration-200">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <Avatar className="w-10 h-10 cursor-pointer" onClick={handleUserClick}>
                  <AvatarImage src={post.author.avatar} alt={post.author.name} />
                  <AvatarFallback>
                    {post.author.name.split(' ').map(n => n[0]).join('')}
                  </AvatarFallback>
    </Avatar>
                <div>
                  <div className="flex items-center space-x-2">
                    <span 
                      className="font-medium text-gray-900 cursor-pointer hover:underline"
                      onClick={handleUserClick}
                    >
                      {post.author.name}
                    </span>
                    {post.author.isVerified && (
                      <Badge variant="secondary" className="bg-blue-100 text-blue-800 text-xs px-1 py-0">
                        ✓
                      </Badge>
                    )}
                    {post.isPromoted && (
                      <Badge variant="outline" className="text-xs">
                        <TrendingUp className="w-3 h-3 mr-1" />
                        Promoted
                      </Badge>
                    )}
                  </div>
                  <div className="flex items-center space-x-2 text-sm text-gray-500">
                    <span>@{post.author.username}</span>
                    <span>•</span>
                    <span>{timeDisplay}</span>
                    <span>•</span>
                    <div className="flex items-center space-x-1">
                      <Eye className="w-3 h-3" />
                      <span>{formattedMetrics.views}</span>
    </div>
                  </div>
    </div>
              </div>
              
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                    <MoreHorizontal className="w-4 h-4" />
    </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem>Hide this post</DropdownMenuItem>
                  <DropdownMenuItem>Unfollow @{post.author.username}</DropdownMenuItem>
                  <DropdownMenuItem>Report post</DropdownMenuItem>
    </DropdownMenuContent>
              </DropdownMenu>
    </div>
          </CardHeader>
          
          <CardContent className="pt-0">
            {/* Post Content */}
            <div className="mb-4">
              <p className="text-gray-900 whitespace-pre-wrap break-words">
                {post.content}
              </p>
              
              {/* Tags */}
              {post.tags.length > 0 && (
                <div className="flex flex-wrap gap-2 mt-3">
                  {post.tags.map(tag => (
                    <Badge key={tag} variant="outline" className="text-xs">
                      #{tag}
                    </Badge>
                  ))}
                </div>
              )}
            </div>
            
            {/* Media */}
            {post.mediaUrls.length > 0 && (
              <div className="mb-4">
                {post.mediaUrls.length === 1 ? (
                  <div className="rounded-lg overflow-hidden">
                    <img
                      src={post.mediaUrls[0]} alt="Post media"
                      className="w-full h-auto max-h-96 object-cover"
                      loading="lazy"
                    />
    </div>
                ) : (
                  <div className="grid grid-cols-2 gap-2 rounded-lg overflow-hidden">
                    {post.mediaUrls.slice(0, 4).map((url, idx) => (
                      <div key={idx} className="relative">
                        <img
                          src={url} alt={`Post media ${idx + 1}`}, className="w-full h-32 object-cover"
                          loading="lazy"
                        />
                        {idx === 3 && post.mediaUrls.length > 4 && (
                          <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                            <span className="text-white font-medium">
                              +{post.mediaUrls.length - 4} more
                            </span>
    </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}
            
            {/* Interaction Stats */}
            <div className="flex items-center justify-between text-sm text-gray-500 mb-3 pb-3 border-b">
              <div className="flex items-center space-x-4">
                <span>{formattedMetrics.likes} likes</span>
                <span>{formattedMetrics.comments} comments</span>
                <span>{formattedMetrics.shares} shares</span>
    </div>
              <Badge 
                variant={post.category === 'trending' ? 'default' : 'outline'} className="text-xs"
              >
                {post.category}
              </Badge>
    </div>
            {/* Action Buttons */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-6">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleLike} className={cn("h-8 px-2", interactionStyles.like)}
                >
                  <Heart className="w-4 h-4 mr-1" />
                  <span className="text-sm">{formattedMetrics.likes}</span>
    </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleComment} className="h-8 px-2 text-gray-500 hover:text-blue-500"
                >
                  <MessageCircle className="w-4 h-4 mr-1" />
                  <span className="text-sm">{formattedMetrics.comments}</span>
    </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleShare} className="h-8 px-2 text-gray-500 hover:text-green-500"
                >
                  <Share2 className="w-4 h-4 mr-1" />
                  <span className="text-sm">{formattedMetrics.shares}</span>
    </Button>
              </div>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={handleBookmark} className={cn("h-8 w-8 p-0", interactionStyles.bookmark)}
              >
                <Bookmark className="w-4 h-4" />
    </Button>
            </div>
    </CardContent>
        </Card>
      </motion.div>
    </div>
  );
}, (prevProps, nextProps) => {
  // OPTIMIZATION: Deep comparison for post data
  const postEqual = (
    prevProps.post.id === nextProps.post.id &&
    prevProps.post.metrics.likes === nextProps.post.metrics.likes &&
    prevProps.post.metrics.comments === nextProps.post.metrics.comments &&
    prevProps.post.metrics.shares === nextProps.post.metrics.shares &&
    prevProps.post.metrics.views === nextProps.post.metrics.views &&
    prevProps.post.userInteractions.hasLiked === nextProps.post.userInteractions.hasLiked &&
    prevProps.post.userInteractions.hasShared === nextProps.post.userInteractions.hasShared &&
    prevProps.post.userInteractions.hasBookmarked === nextProps.post.userInteractions.hasBookmarked
  );
  
  const callbacksEqual = (
    prevProps.onLike === nextProps.onLike &&
    prevProps.onUnlike === nextProps.onUnlike &&
    prevProps.onComment === nextProps.onComment &&
    prevProps.onShare === nextProps.onShare &&
    prevProps.onBookmark === nextProps.onBookmark &&
    prevProps.onUserClick === nextProps.onUserClick
  );
  
  return postEqual && callbacksEqual && prevProps.index === nextProps.index;
});

OptimizedFeedPost.displayName = 'OptimizedFeedPost';

// OPTIMIZATION 2: Virtualized feed row component
const FeedRow = memo<{
  index: number, style: React.CSSProperties, data: {
    posts: FeedPost[], onLike: (postId: string) => void; onUnlike: (postId: string) => void; onComment: (postId: string) => void; onShare: (postId: string) => void; onBookmark: (postId: string) => void; onUserClick: (userId: string) => void;
  };
}>(({ index, style, data }) => {
  const post = data.posts[index];
  
  if (!post) {
    return (
      <div style={style} className="px-4">
        <Card className="mb-4 animate-pulse">
          <CardContent className="p-4">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-10 h-10 bg-gray-300 rounded-full"></div>
              <div className="space-y-2">
                <div className="h-4 bg-gray-300 rounded w-32"></div>
                <div className="h-3 bg-gray-300 rounded w-24"></div>
    </div>
            </div>
            <div className="space-y-2">
              <div className="h-4 bg-gray-300 rounded w-full"></div>
              <div className="h-4 bg-gray-300 rounded w-3/4"></div>
    </div>
          </CardContent>
    </Card>
      </div>
    );
  }
  
  return (
    <OptimizedFeedPost
      post={post} index={index}, style={style} onLike={data.onLike}, onUnlike={data.onUnlike} onComment={data.onComment}, onShare={data.onShare} onBookmark={data.onBookmark}, onUserClick={data.onUserClick}
    />
  );
});

FeedRow.displayName = 'FeedRow';

// OPTIMIZATION 3: Main feed system with virtualization and comprehensive optimization
const OptimizedFeedSystem: React.FC<OptimizedFeedSystemProps> = memo(({
  posts,
  isLoading,
  hasMore,
  onLoadMore,
  onLike,
  onUnlike,
  onComment,
  onShare,
  onBookmark,
  onUserClick,
  className = ''
}) => {
  // OPTIMIZATION: Performance monitoring
  const { 
    createOptimizedCallback;
    getPerformanceMetrics 
  } = useImmediateOptimization('OptimizedFeedSystem');
  
  // Local state
  const [filterCategory, setFilterCategory] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const listRef = useRef<List>(null);
  
  // OPTIMIZATION: Filter and search logic
  const filteredPosts = useMemo(() => {
    let filtered = posts;
    
    // Category filter
    if (filterCategory !== 'all') {
      filtered = filtered.filter(post => post.category === filterCategory);
    }
    
    // Search filter
    if (searchTerm.trim()) {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter(post => 
        post.content.toLowerCase().includes(searchLower) ||
        post.author.name.toLowerCase().includes(searchLower) ||
        post.author.username.toLowerCase().includes(searchLower) ||
        post.tags.some(tag => tag.toLowerCase().includes(searchLower))
      );
    }
    
    return filtered;
  }, [posts, filterCategory, searchTerm]);
  
  // OPTIMIZATION: Memoized virtualization data
  const virtualizationData = useMemo(() => ({
    posts: filteredPosts,
    onLike,
    onUnlike,
    onComment,
    onShare,
    onBookmark,
    onUserClick
  }), [filteredPosts, onLike, onUnlike, onComment, onShare, onBookmark, onUserClick]);
  
  // OPTIMIZATION: Optimized search callback
  const optimizedSearch = createOptimizedCallback((...args: unknown[]) => {
    const value = args[0] as string;
    setSearchTerm(value);
  }, []);

  // OPTIMIZATION: Optimized filter callback
  const optimizedFilter = createOptimizedCallback((...args: unknown[]) => {
    const category = args[0] as string;
    setFilterCategory(category);
  }, []);
  
  // OPTIMIZATION: Optimized load more with intersection observer
  const loadMoreRef = useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    if (!loadMoreRef.current || !hasMore || isLoading) return;
    
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          onLoadMore();
        }
      },
      { threshold: 0.1 }
    );
    
    observer.observe(loadMoreRef.current);
    
    return () => observer.disconnect();
  }, [hasMore, isLoading, onLoadMore]);
  
  // OPTIMIZATION: Performance logging in development
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      const metrics = getPerformanceMetrics();
      if (metrics.renderCount > 0 && metrics.renderCount % 10 === 0) {
        console.log('📊 FeedSystem Performance Metrics:', {
          ...metrics,
          postsCount: posts.length,
          filteredCount: filteredPosts.length,
          filterCategory,
          searchTerm: searchTerm ? `"${searchTerm}"` : 'none'
        });
      }
    }
  }, [getPerformanceMetrics, posts.length, filteredPosts.length, filterCategory, searchTerm]);
  
  return (
    <div className={cn("w-full max-w-2xl mx-auto", className)}>
      {/* Feed Controls */}
      <div className="sticky top-0 z-10 bg-white border-b mb-4 p-4">
        <div className="space-y-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search posts, users, or hashtags..."
              value={searchTerm} onChange={(e) => optimizedSearch(e.target.value)}, className="pl-10"
            />
    </div>
          {/* Filter Categories */}
          <div className="flex items-center space-x-2 overflow-x-auto pb-2">
            <Button
              variant={filterCategory === 'all' ? 'default' : 'outline'} size="sm"
              onClick={() => optimizedFilter('all')}
            >
              All
            </Button>
            <Button
              variant={filterCategory === 'trending' ? 'default' : 'outline'} size="sm"
              onClick={() => optimizedFilter('trending')}
            >
              <TrendingUp className="w-4 h-4 mr-1" />
              Trending
            </Button>
            <Button
              variant={filterCategory === 'recent' ? 'default' : 'outline'} size="sm"
              onClick={() => optimizedFilter('recent')}
            >
              <Clock className="w-4 h-4 mr-1" />
              Recent
            </Button>
            <Button
              variant={filterCategory === 'following' ? 'default' : 'outline'} size="sm"
              onClick={() => optimizedFilter('following')}
            >
              Following
            </Button>
            <Button
              variant={filterCategory === 'recommended' ? 'default' : 'outline'} size="sm"
              onClick={() => optimizedFilter('recommended')}
            >
              Recommended
            </Button>
    </div>
          {/* Results Count */}
          {(searchTerm || filterCategory !== 'all') && (
            <div className="text-sm text-gray-500">
              Showing {filteredPosts.length} of {posts.length} posts
              {searchTerm && (
                <span> for "{searchTerm}"</span>
              )}
            </div>
          )}
        </div>
    </div>
      {/* Virtualized Feed */}
      <div className="relative">
        {filteredPosts.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-gray-500 mb-2">No posts found</div>
            {searchTerm && (
              <Button variant="outline" onClick={() => optimizedSearch('')}>
                Clear search
              </Button>
            )}
          </div>
        ) : (
          <List
            ref={listRef} height={800}, width={window.innerWidth || 800} itemCount={filteredPosts.length + (hasMore ? 1 : 0)}, itemSize={400} // Average post height
            itemData={virtualizationData} overscanCount={2}
          >
            {FeedRow}
          </List>
        )}
        
        {/* Load More Trigger */}
        {hasMore && !isLoading && (
          <div ref={loadMoreRef} className="h-20 flex items-center justify-center">
            <div className="animate-pulse text-gray-500">Loading more posts...</div>
    </div>
        )}
        
        {/* Loading State */}
        {isLoading && (
          <div className="px-4">
            {Array.from({ length: 3 }).map((_, idx) => (
              <Card key={idx} className="mb-4 animate-pulse">
                <CardContent className="p-4">
                  <div className="flex items-center space-x-3 mb-4">
                    <div className="w-10 h-10 bg-gray-300 rounded-full"></div>
                    <div className="space-y-2">
                      <div className="h-4 bg-gray-300 rounded w-32"></div>
                      <div className="h-3 bg-gray-300 rounded w-24"></div>
    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="h-4 bg-gray-300 rounded w-full"></div>
                    <div className="h-4 bg-gray-300 rounded w-3/4"></div>
    </div>
                </CardContent>
    </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  );
});

OptimizedFeedSystem.displayName = 'OptimizedFeedSystem';

export default OptimizedFeedSystem;
