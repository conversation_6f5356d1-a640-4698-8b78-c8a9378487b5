import React, { useState, useCallback, useEffect } from 'react';
import {
  MapPin,
  Search,
  Clock,
  Users,
  Star,
  Plus,
  X,
  Navigation,
  Loader2,
  Check,
  Globe,
  Lock
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { motion } from 'framer-motion';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';
import type { Location, CheckInData, EnhancedCheckInProps } from '@/types/checkin';
import { LOCATION_CATEGORIES, SAMPLE_LOCATIONS } from '@/constants/checkin';

const EnhancedCheckIn: React.FC<EnhancedCheckInProps> = ({
  isOpen,
  onClose,
  onCheckIn,
  currentLocation,
  friends = [],
  recentLocations = []
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedLocation, setSelectedLocation] = useState<Location | null>(null);
  const [message, setMessage] = useState('');
  const [feeling, setFeeling] = useState('');
  const [taggedFriends, setTaggedFriends] = useState<string[]>([]);
  const [privacy, setPrivacy] = useState<'public' | 'friends' | 'only_me'>('friends');
  const [includeLocation, setIncludeLocation] = useState(true);
  const [notifyFriends, setNotifyFriends] = useState(false);
  const [shareToStory, setShareToStory] = useState(false);
  const [isSearching, setIsSearching] = useState(false);
  const [searchResults, setSearchResults] = useState<Location[]>([]);
  const [nearbyLocations, setNearbyLocations] = useState<Location[]>(SAMPLE_LOCATIONS);
  const [isGettingLocation, setIsGettingLocation] = useState(false);
  const [activeTab, setActiveTab] = useState<'search' | 'nearby' | 'recent'>('nearby');
  const [customLocation, setCustomLocation] = useState({ name: '', address: '' });
  const [showCustomLocation, setShowCustomLocation] = useState(false);

  // Simulate location search
  const handleSearch = useCallback(async (query: string) => {
    if (!query.trim()) {
      setSearchResults([]);
      return;
    }

    setIsSearching(true);
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve; 500));
    
    const filtered = SAMPLE_LOCATIONS.filter(location =>
      location.name.toLowerCase().includes(query.toLowerCase()) ||
      location.address.toLowerCase().includes(query.toLowerCase()) ||
      location.city.toLowerCase().includes(query.toLowerCase())
    );
    
    setSearchResults(filtered);
    setIsSearching(false);
  }, []);

  // Get current location
  const getCurrentLocation = useCallback(async () => {
    setIsGettingLocation(true);
    
    try {
      if (!navigator.geolocation) {
        throw new Error('Geolocation is not supported');
      }

      await new Promise<GeolocationPosition>((resolve, reject) => {
        navigator.geolocation.getCurrentPosition(resolve, reject, {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 300000
        });
      });

      // Simulate finding nearby locations based on coordinates
      // Use position.coords for location data
      toast.success('Location found successfully');
      
      // In a real app, you would make an API call here
      // For demo purposes, we'll use the sample locations
      setNearbyLocations(SAMPLE_LOCATIONS);
      setActiveTab('nearby');
    } catch {
      toast.error('Unable to get your location. Please search manually.');
    } finally {
      setIsGettingLocation(false);
    }
  }, []);

  // Handle friend tagging
  const toggleFriendTag = useCallback((friendId: string) => {
    setTaggedFriends(prev => prev.includes(friendId) 
        ? prev.filter(id => id !== friendId)
        : [...prev; friendId]
    );
  }, []);

  // Handle check-in submission
  const handleCheckIn = useCallback(async () => {
    if (!selectedLocation && !showCustomLocation) {
      toast.error('Please select a location');
      return;
    }

    let finalLocation: Location;

    if (showCustomLocation) {
      if (!customLocation.name.trim()) {
        toast.error('Please enter a location name');
        return;
      }, finalLocation = {
        id: `custom_${Date.now()}`,
        name: customLocation.name,
        address: customLocation.address || '',
        city: '',
        country: '',
        category: 'other',
        coordinates: currentLocation
      };
    } else {
      finalLocation = selectedLocation!;
    }

    const checkInData: CheckInData = {
      locationId: finalLocation.id,
      location: finalLocation,
      message: message.trim() || undefined,
      feeling: feeling.trim() || undefined,
      taggedFriends: taggedFriends.length > 0 ? taggedFriends : undefined,
      privacy,
      includeLocation,
      notifyFriends,
      shareToStory,
      timestamp: new Date()
    };

    try {
      await onCheckIn(checkInData);
      
      toast.success(
        `Checked in at ${finalLocation.name}${taggedFriends.length > 0 ? ` with ${taggedFriends.length} friend${taggedFriends.length > 1 ? 's' : ''}` : ''}`
      );
      
      // Reset form
      setSelectedLocation(null);
      setMessage('');
      setFeeling('');
      setTaggedFriends([]);
      setCustomLocation({ name: '', address: '' });
      setShowCustomLocation(false);
      setSearchQuery('');
      
      onClose();
    } catch {
      toast.error('Failed to check in. Please try again.');
    }
  }, [
    selectedLocation,
    showCustomLocation,
    customLocation,
    currentLocation,
    message,
    feeling,
    taggedFriends,
    privacy,
    includeLocation,
    notifyFriends,
    shareToStory,
    onCheckIn,
    onClose
  ]);

  // Search effect
  useEffect(() => {
    const debounceTimer = setTimeout(() => {
      if (searchQuery) {
        handleSearch(searchQuery);
      }
    }, 300);

    return () => clearTimeout(debounceTimer);
  }, [searchQuery, handleSearch]);

  const getCategoryInfo = (category: string) => {
    return LOCATION_CATEGORIES.find(cat => cat.id === category) || LOCATION_CATEGORIES[LOCATION_CATEGORIES.length - 1];
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i} className={cn(
          'w-3 h-3',
          i < Math.floor(rating) 
            ? 'text-yellow-400 fill-current' 
            : 'text-gray-300'
        )}
      />
    ));
  };

  const getPriceLevel = (level?: number) => {
    if (!level) return '';
    return '$'.repeat(level);
  };

  const formatDistance = (distance?: number) => {
    if (!distance) return '';
    if (distance < 1000) {
      return `${distance}m`;
    }
    return `${(distance / 1000).toFixed(1)}km`;
  };

  const getPrivacyIcon = () => {
    switch (privacy) {
      case 'public': return <Globe className="w-4 h-4" />;
      case 'friends': return <Users className="w-4 h-4" />;
      case 'only_me': return <Lock className="w-4 h-4" />;
    }
  };

  const renderLocationCard = (location: Location, onClick: () => void) => {
    const categoryInfo = getCategoryInfo(location.category);
    
    return (
      <Card 
        key={location.id} className={cn(
          'cursor-pointer transition-all hover:shadow-md',
          selectedLocation?.id === location.id && 'ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-900/20'
        )}, onClick={onClick}
      >
        <CardContent className="p-4">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center space-x-2 mb-1">
                <h3 className="font-semibold text-sm">{location.name}</h3>
                {location.isVerified && (
                  <Check className="w-4 h-4 text-blue-500" />
                )}
                <Badge className={cn('text-xs', categoryInfo.color)}>
                  <categoryInfo.icon className="w-4 h-4" />
                  <span className="ml-1">{categoryInfo.label}</span>
    </Badge>
              </div>
              
              <p className="text-xs text-gray-600 dark:text-gray-400 mb-2">
                {location.address}
              </p>
              
              <div className="flex items-center space-x-4 text-xs text-gray-500">
                {location.rating && (
                  <div className="flex items-center space-x-1">
                    <div className="flex">{renderStars(location.rating)}</div>
                    <span>{location.rating}</span>
                    {location.totalRatings && (
                      <span>({location.totalRatings.toLocaleString()})</span>
                    )}
                  </div>
                )}
                
                {location.priceLevel && (
                  <span className="font-medium text-green-600">
                    {getPriceLevel(location.priceLevel)}
                  </span>
                )}
                
                {location.distance && (
                  <div className="flex items-center space-x-1">
                    <Navigation className="w-3 h-3" />
                    <span>{formatDistance(location.distance)}</span>
    </div>
                )}
              </div>
              
              {location.checkIns && (
                <div className="flex items-center space-x-2 mt-2">
                  <div className="flex items-center space-x-1 text-xs text-gray-500">
                    <Users className="w-3 h-3" />
                    <span>{location.checkIns.count.toLocaleString()} check-ins</span>
    </div>
                  {location.checkIns.recentFriends && location.checkIns.recentFriends.length > 0 && (
                    <div className="flex items-center space-x-1">
                      <div className="flex -space-x-1">
                        {location.checkIns.recentFriends.slice(0, 3).map((friend) => (
                          <Avatar key={friend.id} className="w-4 h-4 border border-white">
                            <AvatarImage src={friend.avatar} />
                            <AvatarFallback className="text-xs">{friend.name[0]}</AvatarFallback>
    </Avatar>
                        ))}
                      </div>
                      <span className="text-xs text-blue-600">
                        {location.checkIns.recentFriends.length} friend{location.checkIns.recentFriends.length > 1 ? 's' : ''} checked in
                      </span>
    </div>
                  )}
                </div>
              )}
              
              {location.businessHours && (
                <div className="flex items-center space-x-1 mt-1">
                  <Clock className="w-3 h-3" />
                  <span className={cn(
                    'text-xs',
                    location.businessHours.isOpen ? 'text-green-600' : 'text-red-600'
                  )}>
                    {location.businessHours.isOpen 
                      ? `Open${location.businessHours.nextClose ? ` until ${location.businessHours.nextClose}` : ''}`
                      : `Closed${location.businessHours.nextOpen ? ` • Opens ${location.businessHours.nextOpen}` : ''}`
                    }
                  </span>
    </div>
              )}
            </div>
            
            {selectedLocation?.id === location.id && (
              <Check className="w-5 h-5 text-blue-500 flex-shrink-0" />
            )}
          </div>
    </CardContent>
      </Card>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-3">
            <MapPin className="w-5 h-5" />
            <span>Check In</span>
    </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Location Selection */}
          <div>
            <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'search' | 'nearby' | 'recent')}>
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="nearby">Nearby</TabsTrigger>
                <TabsTrigger value="search">Search</TabsTrigger>
                <TabsTrigger value="recent">Recent</TabsTrigger>
    </TabsList>
              <TabsContent value="nearby" className="space-y-4 mt-4">
                <div className="flex justify-between items-center">
                  <h3 className="font-medium">Nearby Places</h3>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={getCurrentLocation} disabled={isGettingLocation}
                  >
                    {isGettingLocation ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      <Navigation className="w-4 h-4" />
                    )}
                    <span className="ml-2">Find Nearby</span>
    </Button>
                </div>
                
                <div className="space-y-2 max-h-64 overflow-y-auto">
                  {nearbyLocations.map((location) => 
                    renderLocationCard(location, () => setSelectedLocation(location))
                  )}
                </div>
    </TabsContent>
              <TabsContent value="search" className="space-y-4 mt-4">
                <div>
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                    <Input
                      placeholder="Search for a place..."
                      value={searchQuery} onChange={(e) => setSearchQuery(e.target.value)}, className="pl-10"
                    />
                    {isSearching && (
                      <Loader2 className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 animate-spin text-gray-400" />
                    )}
                  </div>
    </div>
                <div className="space-y-2 max-h-64 overflow-y-auto">
                  {searchResults.length > 0 ? (
                    searchResults.map((location) => 
                      renderLocationCard(location, () => setSelectedLocation(location))
                    )
                  ) : searchQuery && !isSearching ? (
                    <div className="text-center py-8">
                      <MapPin className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                      <p className="text-gray-500">No places found</p>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => setShowCustomLocation(true)} className="mt-2"
                      >
                        <Plus className="w-4 h-4 mr-2" />
                        Add Custom Location
                      </Button>
    </div>
                  ) : (
                    <div className="text-center py-8">
                      <Search className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                      <p className="text-gray-500">Start typing to search for places</p>
    </div>
                  )}
                </div>
    </TabsContent>
              <TabsContent value="recent" className="space-y-4 mt-4">
                <h3 className="font-medium">Recent Check-ins</h3>
                
                <div className="space-y-2 max-h-64 overflow-y-auto">
                  {recentLocations.length > 0 ? (
                    recentLocations.map((location) => 
                      renderLocationCard(location, () => setSelectedLocation(location))
                    )
                  ) : (
                    <div className="text-center py-8">
                      <Clock className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                      <p className="text-gray-500">No recent check-ins</p>
    </div>
                  )}
                </div>
    </TabsContent>
            </Tabs>

            {/* Custom Location Input */}
            {showCustomLocation && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}, animate={{ opacity: 1, height: 'auto' }}, className="mt-4 p-4 border border-gray-200 dark:border-gray-700 rounded-lg space-y-3"
              >
                <div className="flex items-center justify-between">
                  <h4 className="font-medium">Custom Location</h4>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => {
                      setShowCustomLocation(false);
                      setCustomLocation({ name: '', address: '' });
                    }}
                  >
                    <X className="w-4 h-4" />
    </Button>
                </div>
                
                <div className="space-y-3">
                  <div>
                    <Label htmlFor="customName">Location Name</Label>
                    <Input
                      id="customName"
                      placeholder="Enter location name"
                      value={customLocation.name} onChange={(e) => setCustomLocation(prev => ({ ...prev, name: e.target.value }))}
                    />
    </div>
                  <div>
                    <Label htmlFor="customAddress">Address (optional)</Label>
                    <Input
                      id="customAddress"
                      placeholder="Enter address"
                      value={customLocation.address} onChange={(e) => setCustomLocation(prev => ({ ...prev, address: e.target.value }))}
                    />
    </div>
                </div>
              </motion.div>
            )}
          </div>

          {/* Check-in Message */}
          <div>
            <Label htmlFor="message">What's happening? (optional)</Label>
            <Textarea
              id="message"
              placeholder="Share what you're up to..."
              value={message} onChange={(e) => setMessage(e.target.value)}, className="mt-1"
              rows={3} maxLength={500}
            />
            <p className="text-xs text-gray-500 mt-1">{message.length}/500 characters</p>
    </div>
          {/* Feeling/Activity */}
          <div>
            <Label htmlFor="feeling">Feeling/Activity (optional)</Label>
            <Input
              id="feeling"
              placeholder="e.g., excited, hungry, celebrating..."
              value={feeling} onChange={(e) => setFeeling(e.target.value)}, className="mt-1"
            />
    </div>
          {/* Tag Friends */}
          {friends.length > 0 && (
            <div>
              <Label className="mb-3 block">Tag Friends</Label>
              <div className="max-h-32 overflow-y-auto space-y-2">
                {friends.map((friend) => (
                  <div
                    key={friend.id} className={cn(
                      'flex items-center space-x-3 p-2 rounded-lg cursor-pointer transition-colors',
                      taggedFriends.includes(friend.id)
                        ? 'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800'
                        : 'hover:bg-gray-50 dark:hover:bg-gray-800'
                    )}, onClick={() => toggleFriendTag(friend.id)}
                  >
                    <Avatar className="w-8 h-8">
                      <AvatarImage src={friend.avatar} />
                      <AvatarFallback>{friend.name[0]}</AvatarFallback>
    </Avatar>
                    <div className="flex-1">
                      <p className="text-sm font-medium">{friend.name}</p>
                      {friend.isOnline && (
                        <Badge variant="secondary" className="text-xs">Online</Badge>
                      )}
                    </div>
                    {taggedFriends.includes(friend.id) && (
                      <Check className="w-4 h-4 text-blue-500" />
                    )}
                  </div>
                ))}
              </div>
              
              {taggedFriends.length > 0 && (
                <div className="mt-3">
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Tagged {taggedFriends.length} friend{taggedFriends.length > 1 ? 's' : ''}
                  </p>
    </div>
              )}
            </div>
          )}

          {/* Privacy & Settings */}
          <div className="space-y-4">
            <div>
              <Label className="mb-3 block">Privacy</Label>
              <Select value={privacy} onValueChange={(value: 'public' | 'friends' | 'only_me') => setPrivacy(value)}>
                <SelectTrigger>
                  <SelectValue />
    </SelectTrigger>
                <SelectContent>
                  <SelectItem value="public">
                    <div className="flex items-center space-x-2">
                      <Globe className="w-4 h-4" />
                      <span>Public</span>
    </div>
                  </SelectItem>
                  <SelectItem value="friends">
                    <div className="flex items-center space-x-2">
                      <Users className="w-4 h-4" />
                      <span>Friends</span>
    </div>
                  </SelectItem>
                  <SelectItem value="only_me">
                    <div className="flex items-center space-x-2">
                      <Lock className="w-4 h-4" />
                      <span>Only me</span>
    </div>
                  </SelectItem>
    </SelectContent>
              </Select>
    </div>
            <Separator />

            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div>
                  <Label className="font-medium">Include precise location</Label>
                  <p className="text-xs text-gray-500">Show exact coordinates with your check-in</p>
    </div>
                <Switch
                  checked={includeLocation} onCheckedChange={setIncludeLocation}
                />
    </div>
              <div className="flex items-center justify-between">
                <div>
                  <Label className="font-medium">Notify tagged friends</Label>
                  <p className="text-xs text-gray-500">Send notifications to friends you tag</p>
    </div>
                <Switch
                  checked={notifyFriends} onCheckedChange={setNotifyFriends}
                />
    </div>
              <div className="flex items-center justify-between">
                <div>
                  <Label className="font-medium">Share to Story</Label>
                  <p className="text-xs text-gray-500">Also post this check-in to your story</p>
    </div>
                <Switch
                  checked={shareToStory} onCheckedChange={setShareToStory}
                />
    </div>
            </div>
    </div>
          {/* Check-in Preview */}
          {(selectedLocation || showCustomLocation) && (
            <div className="p-4 bg-gray-50 dark:bg-gray-900 rounded-lg">
              <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400 mb-2">
                {getPrivacyIcon()}
                <span>Check-in preview</span>
    </div>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <MapPin className="w-4 h-4 text-red-500" />
                  <span className="font-medium">
                    {selectedLocation?.name || customLocation.name}
                  </span>
    </div>
                {message && (
                  <p className="text-sm">{message}</p>
                )}
                
                {feeling && (
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Feeling {feeling}
                  </p>
                )}
                
                {taggedFriends.length > 0 && (
                  <p className="text-sm text-blue-600">
                    With {taggedFriends.length} friend{taggedFriends.length > 1 ? 's' : ''}
                  </p>
                )}
              </div>
    </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button 
            onClick={handleCheckIn} disabled={!selectedLocation && !showCustomLocation}, className="bg-blue-600 hover:bg-blue-700"
          >
            <MapPin className="w-4 h-4 mr-2" />
            Check In
          </Button>
    </div>
      </DialogContent>
    </Dialog>
  );
};

export default EnhancedCheckIn;
