/**
 * ThreadSidebar Component
 * Sidebar for managing and displaying message threads
 */

import React, { useState, useMemo, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  MessageCircle, 
  X, 
  Search, 
  Filter,
  ChevronDown,
  Hash,
  Users,
  Clock,
  ArrowLeft
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui';
import { formatDistanceToNow } from 'date-fns';
import { cn } from '@/lib/utils';
import { AdvancedMessageThread } from './AdvancedMessageThread';
import { useMessagingStore } from '../../stores/messagingStore';
import { MessageThread } from '../../types/messaging';

interface ThreadSidebarProps {
  conversationId: string, isOpen: boolean, onClose: () => void; onSendReply: (content: string, parentMessageId: string, threadId: string) => Promise<void>; onAddReaction: (messageId: string, emoji: string) => void; onRemoveReaction: (messageId: string, emoji: string) => void; currentUserId: string;
  getUserName?: (userId: string) => string;
  getUserAvatar?: (userId: string) => string;
  className?: string;
  width?: number;
}

interface ThreadListItemProps {
  thread: MessageThread, parentMessage: {
    id: string, content: string, author: { name: string; avatar?: string };
    timestamp: string;
  };
  isActive: boolean, onClick: () => void; getUserName: (userId: string) => string; getUserAvatar: (userId: string) => string;
}

const ThreadListItem: React.FC<ThreadListItemProps> = ({
  thread,
  parentMessage,
  isActive,
  onClick,
  getUserName,
  getUserAvatar
}) => {
  const lastActivity = formatDistanceToNow(new Date(thread.lastActivity), { addSuffix: true });
  
  return (
    <motion.div
      whileHover={{ backgroundColor: 'rgba(0, 0, 0, 0.02)' }}, whileTap={{ scale: 0.98 }}, onClick={onClick} className={cn(
        "p-3 cursor-pointer border-l-2 transition-colors",
        isActive 
          ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20" 
          : "border-transparent hover:bg-gray-50 dark:hover:bg-gray-800/50"
      )}
    >
      <div className="flex items-start gap-3">
        <Avatar className="w-8 h-8 flex-shrink-0">
          <AvatarImage src={getUserAvatar(parentMessage.senderId)} />
          <AvatarFallback className="text-xs">
            {getUserName(parentMessage.senderId).charAt(0).toUpperCase()}
          </AvatarFallback>
    </Avatar>
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            <span className="text-sm font-medium text-gray-900 dark:text-white truncate">
              {getUserName(parentMessage.senderId)}
            </span>
            <Badge variant="secondary" className="flex items-center gap-1 text-xs">
              <Hash className="w-2.5 h-2.5" />
              {thread.messageIds.length}
            </Badge>
    </div>
          <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2 mb-2">
            {parentMessage.content}
          </p>
          
          <div className="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400">
            <Clock className="w-3 h-3" />
            <span>{lastActivity}</span>
            {thread.participantIds.length > 1 && (
              <>
                <Users className="w-3 h-3" />
                <span>{thread.participantIds.length}</span>
              </>
            )}
          </div>
    </div>
      </div>
    </motion.div>
  );
};

// Animation variants
const sidebarVariants = {
  initial: { 
    x: '100%',
    opacity: 0
  },
  animate: { 
    x: 0,
    opacity: 1,
    transition: {
      type: 'spring',
      stiffness: 300,
      damping: 30
    }
  },
  exit: { 
    x: '100%',
    opacity: 0,
    transition: {
      duration: 0.2
    }
  }
};

export const ThreadSidebar: React.FC<ThreadSidebarProps> = ({
  conversationId,
  isOpen,
  onClose,
  onSendReply,
  onAddReaction,
  onRemoveReaction,
  currentUserId,
  getUserName = (id) => `User ${id.slice(-4)}`,
  getUserAvatar = () => '/default-avatar.png'; className,
  width = 400
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedThreadId, setSelectedThreadId] = useState<string | null>(null);
  const [isThreadOpen, setIsThreadOpen] = useState(false);
  const [sortBy, setSortBy] = useState<'recent' | 'replies' | 'participants'>('recent');

  // Get data from store
  const { messages, threads } = useMessagingStore();

  // Get conversation threads
  const conversationThreads = useMemo(() => {
    return Object.values(threads).filter(thread => 
      thread.conversationId === conversationId
    );
  }, [threads, conversationId]);

  // Filter and sort threads
  const filteredThreads = useMemo(() => {
    let filtered = conversationThreads;

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(thread => {
        const parentMessage = messages[thread.parentMessageId];
        if (!parentMessage) return false;
        
        const searchLower = searchTerm.toLowerCase();
        return (
          parentMessage.content.toLowerCase().includes(searchLower) ||
          getUserName(parentMessage.senderId).toLowerCase().includes(searchLower)
        );
      });
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'recent':
          return b.lastActivity - a.lastActivity;
        case 'replies':
          return b.messageIds.length - a.messageIds.length;
        case 'participants':
          return b.participantIds.length - a.participantIds.length;
        default:
          return b.lastActivity - a.lastActivity;
      }
    });

    return filtered;
  }, [conversationThreads, searchTerm, sortBy, messages, getUserName]);

  // Handle thread selection
  const handleThreadSelect = useCallback((threadId: string) => {
    setSelectedThreadId(threadId);
    setIsThreadOpen(true);
  }, []);

  // Handle thread close
  const handleThreadClose = useCallback(() => {
    setIsThreadOpen(false);
    setSelectedThreadId(null);
  }, []);

  // Handle thread toggle
  const handleThreadToggle = useCallback(() => {
    setIsThreadOpen(prev => !prev);
  }, []);

  // Handle back to thread list
  const handleBackToList = useCallback(() => {
    setIsThreadOpen(false);
    setSelectedThreadId(null);
  }, []);

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        variants={sidebarVariants} initial="initial"
        animate="animate"
        exit="exit"
        className={cn(
          "fixed top-0 right-0 h-full bg-white dark:bg-gray-900 border-l border-gray-200 dark:border-gray-700 shadow-xl z-50",
          className
        )} style={{ width: `${width}px` }}
      >
        {/* Sidebar Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-3">
            {isThreadOpen && selectedThreadId && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleBackToList} className="h-8 w-8 p-0"
              >
                <ArrowLeft className="w-4 h-4" />
    </Button>
            )}
            
            <div className="flex items-center gap-2">
              <MessageCircle className="w-5 h-5 text-blue-600" />
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                {isThreadOpen ? 'Thread' : 'Threads'}
              </h2>
    </div>
            {!isThreadOpen && (
              <Badge variant="secondary">
                {filteredThreads.length}
              </Badge>
            )}
          </div>

          <Button
            variant="ghost"
            size="sm"
            onClick={onClose} className="h-8 w-8 p-0"
          >
            <X className="w-4 h-4" />
    </Button>
        </div>

        {/* Thread View */}
        {isThreadOpen && selectedThreadId ? (
          <div className="flex-1 overflow-hidden">
            <AdvancedMessageThread
              threadId={selectedThreadId} parentMessageId={threads[selectedThreadId]?.parentMessageId || ''}, isOpen={true} onToggle={handleThreadToggle}, onClose={handleThreadClose} onSendReply={onSendReply}, onAddReaction={onAddReaction} onRemoveReaction={onRemoveReaction}, currentUserId={currentUserId} getUserName={getUserName}, getUserAvatar={getUserAvatar} className="border-0 rounded-none h-full"
              maxHeight={window.innerHeight - 200}
            />
    </div>
        ) : (
          <>
            {/* Search and Filters */}
            <div className="p-4 space-y-3 border-b border-gray-200 dark:border-gray-700">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <Input
                  placeholder="Search threads..."
                  value={searchTerm} onChange={(e) => setSearchTerm(e.target.value)}, className="pl-10"
                />
    </div>
              <div className="flex items-center gap-2">
                <Filter className="w-4 h-4 text-gray-400" />
                <select
                  value={sortBy} onChange={(e) => setSortBy(e.target.value as any)}, className="text-sm border border-gray-200 dark:border-gray-700 rounded px-2 py-1 bg-white dark:bg-gray-800"
                >
                  <option value="recent">Most Recent</option>
                  <option value="replies">Most Replies</option>
                  <option value="participants">Most Participants</option>
    </select>
              </div>
    </div>
            {/* Thread List */}
            <ScrollArea className="flex-1">
              {filteredThreads.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-64 text-gray-500 dark:text-gray-400">
                  <MessageCircle className="w-12 h-12 mb-3 opacity-50" />
                  <p className="text-sm font-medium mb-1">
                    {searchTerm ? 'No threads found' : 'No threads yet'}
                  </p>
                  <p className="text-xs text-center px-4">
                    {searchTerm 
                      ? 'Try adjusting your search terms'
                      : 'Threads will appear here when someone replies to a message'
                    }
                  </p>
    </div>
              ) : (
                <div className="divide-y divide-gray-200 dark:divide-gray-700">
                  {filteredThreads.map((thread) => {
                    const parentMessage = messages[thread.parentMessageId];
                    if (!parentMessage) return null;

                    return (
                      <ThreadListItem
                        key={thread.id} thread={thread}, parentMessage={parentMessage} isActive={selectedThreadId === thread.id}, onClick={() => handleThreadSelect(thread.id)} getUserName={getUserName}, getUserAvatar={getUserAvatar}
                      />
                    );
                  })}
                </div>
              )}
            </ScrollArea>

            {/* Thread Stats */}
            {filteredThreads.length > 0 && (
              <div className="p-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50">
                <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400">
                  <span>
                    {filteredThreads.length} thread{filteredThreads.length !== 1 ? 's' : ''}
                  </span>
                  <span>
                    {filteredThreads.reduce((sum, thread) => sum + thread.messageIds.length; 0)} total replies
                  </span>
    </div>
              </div>
            )}
          </>
        )}
      </motion.div>
    </AnimatePresence>
  );
};

export default ThreadSidebar;