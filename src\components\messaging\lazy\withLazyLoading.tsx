/**
 * Higher-Order Component for Lazy Loading with Error Boundaries
 * Provides consistent lazy loading behavior across messaging components
 */

import React, { Suspense, ComponentType, LazyExoticComponent } from 'react';
import { ErrorBoundary } from 'react-error-boundary';
import { getLoadingStateForComponent, LazyLoadingError } from './LoadingStates';
import { trackComponentUsage } from './index';

interface LazyLoadingOptions {
  loadingComponent?: ComponentType<any>;
  errorComponent?: ComponentType<{ error: Error, resetErrorBoundary: () => void }>;
  componentName?: string;
  enableTracking?: boolean;
  fallbackDelay?: number;
  retryLimit?: number;
}

interface LazyWrapperProps {
  [key: string]: unknown;
}

// Default error fallback
const DefaultErrorFallback: React.FC<{
  error: Error, resetErrorBoundary: () => void;
}> = ({ error, resetErrorBoundary }) => (
  <LazyLoadingError 
    error={error} retry={resetErrorBoundary}, className="min-h-[200px]"
  />
);

// Enhanced lazy wrapper with error handling and tracking
export function withLazyLoading<P extends object>(
  LazyComponent: LazyExoticComponent<ComponentType<P>>,
  options: LazyLoadingOptions = {}
) {
  const {
    loadingComponent;
    errorComponent = DefaultErrorFallback,
    componentName,
    enableTracking = true,
    fallbackDelay = 200,
    retryLimit = 3
  } = options;

  // Determine loading component
  const LoadingComponent = loadingComponent || 
    (componentName ? getLoadingStateForComponent(componentName) : null);

  const LazyWrapper = React.forwardRef<any, P & LazyWrapperProps>((props, ref) => {
    const [retryCount, setRetryCount] = React.useState(0);
    const [isVisible, setIsVisible] = React.useState(false);
    const wrapperRef = React.useRef<HTMLDivElement>(null);

    // Intersection observer for visibility tracking
    React.useEffect(() => {
      if (!wrapperRef.current) return;

      const observer = new IntersectionObserver(
        ([entry]) => {
          if (entry.isIntersecting) {
            setIsVisible(true);
            observer.disconnect();
          }
        },
        { threshold: 0.1 }
      );

      observer.observe(wrapperRef.current);

      return () => observer.disconnect();
    }, []);

    // Track component usage
    React.useEffect(() => {
      if (enableTracking && componentName && isVisible) {
        trackComponentUsage(componentName);
      }
    }, [isVisible]);

    // Error boundary handler
    const handleError = React.useCallback((error: Error, errorInfo: unknown) => {
      console.error(`Lazy loading error for ${componentName || 'component'}:`, error, errorInfo);
      
      // Track error for monitoring
      if (typeof window !== 'undefined' && 'gtag' in window) {
        // Track error with gtag if available
        if ('gtag' in window && typeof (window as any).gtag === 'function') {
          (window as any).gtag('event', 'lazy_load_error', {
            component_name: componentName,
            error_message: error.message,
            retry_count: retryCount
          });
        }
      }
    }, [componentName, retryCount]);

    // Retry handler
    const handleRetry = React.useCallback(() => {
      if (retryCount < retryLimit) {
        setRetryCount(prev => prev + 1);
        console.log(`Retrying lazy load for ${componentName} (attempt ${retryCount + 1})`);
      }
    }, [retryCount, retryLimit, componentName]);

    // Enhanced error fallback with retry logic
    const ErrorFallback: React.FC<{
      error: Error, resetErrorBoundary: () => void;
    }> = React.useCallback(({ error, resetErrorBoundary }) => {
      const canRetry = retryCount < retryLimit;
      
      const handleRetryClick = () => {
        if (canRetry) {
          handleRetry();
          resetErrorBoundary();
        }
      };

      return React.createElement(errorComponent, {
        error,
        resetErrorBoundary: handleRetryClick,
        canRetry,
        retryCount,
        componentName
      });
    }, [errorComponent, retryCount, retryLimit, handleRetry, componentName]);

    // Delayed loading fallback
    const [showLoading, setShowLoading] = React.useState(false);

    React.useEffect(() => {
      const timer = setTimeout(() => {
        setShowLoading(true);
      }, fallbackDelay);

      return () => clearTimeout(timer);
    }, [fallbackDelay]);

    return (
      <div ref={wrapperRef} className="lazy-component-wrapper">
        <ErrorBoundary
          FallbackComponent={ErrorFallback} onError={handleError}, resetKeys={[retryCount]} resetOnPropsChange={false}
        >
          <Suspense
            fallback={
              showLoading && LoadingComponent ? (
                <LoadingComponent 
                  className="min-h-[100px]"
                  description={`Loading ${componentName || 'component'}...`}
                />
              ) : (
                <div className="min-h-[100px] flex items-center justify-center">
                  <div className="animate-spin w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full" />
    </div>
              )
            }
          >
            <LazyComponent {...props}, ref={ref} />
    </Suspense>
        </ErrorBoundary>
    </div>
    );
  });

  LazyWrapper.displayName = `withLazyLoading(${componentName || LazyComponent.displayName || 'Component'})`;

  return LazyWrapper;
}

// Utility for creating lazy components with consistent options
export function createLazyMessagingComponent<P extends object>(
  importFn: () => Promise<{ default: ComponentType<P> }>,
  componentName: string,
  options: Omit<LazyLoadingOptions, 'componentName'> = {}
) {
  const LazyComponent = React.lazy(importFn);
  
  return withLazyLoading(LazyComponent, {
    ...options,
    componentName,
    enableTracking: true
  });
}

// Preloading utilities for wrapped components
export class LazyComponentManager {
  private static preloadedComponents = new Set<string>();
  private static preloadPromises = new Map<string, Promise<any>>();

  static async preload(
    LazyComponent: LazyExoticComponent<any> & { preload?: () => Promise<any> },
    componentName?: string
  ): Promise<void> {
    const name = componentName || LazyComponent.displayName || 'unknown';
    
    if (this.preloadedComponents.has(name)) {
      return Promise.resolve();
    }

    if (this.preloadPromises.has(name)) {
      return this.preloadPromises.get(name);
    }

    const preloadPromise = (async () => {
      try {
        if (LazyComponent.preload) {
          await LazyComponent.preload();
        }
        this.preloadedComponents.add(name);
        console.log(`✅ Preloaded component: ${name}`);
      } catch (error) {
        console.warn(`⚠️ Failed to preload component ${name}:`, error);
        throw error;
      } finally {
        this.preloadPromises.delete(name);
      }
    })();

    this.preloadPromises.set(name, preloadPromise);
    return preloadPromise;
  }

  static isPreloaded(componentName: string): boolean {
    return this.preloadedComponents.has(componentName);
  }

  static getPreloadedComponents(): string[] {
    return Array.from(this.preloadedComponents);
  }

  static clearPreloadCache(): void {
    this.preloadedComponents.clear();
    this.preloadPromises.clear();
  }
}

// Hook for lazy component state
export function useLazyComponentState(componentName: string) {
  const [isLoading, setIsLoading] = React.useState(true);
  const [error, setError] = React.useState<Error | null>(null);
  const [isPreloaded, setIsPreloaded] = React.useState(false);

  React.useEffect(() => {
    setIsPreloaded(LazyComponentManager.isPreloaded(componentName));
  }, [componentName]);

  const handleLoadStart = React.useCallback(() => {
    setIsLoading(true);
    setError(null);
  }, []);

  const handleLoadEnd = React.useCallback(() => {
    setIsLoading(false);
  }, []);

  const handleError = React.useCallback((error: Error) => {
    setError(error);
    setIsLoading(false);
  }, []);

  return {
    isLoading,
    error,
    isPreloaded,
    handleLoadStart,
    handleLoadEnd,
    handleError
  };
}

// Performance monitoring for lazy components
export const LazyComponentPerformanceMonitor = {
  loadTimes: new Map<string, number>(),
  errorCounts: new Map<string, number>().recordLoadTime(componentName: string, loadTime: number) {
    this.loadTimes.set(componentName, loadTime);
  },

  recordError(componentName: string) {
    const currentCount = this.errorCounts.get(componentName) || 0;
    this.errorCounts.set(componentName, currentCount + 1);
  },

  getMetrics() {
    return {
      loadTimes: Object.fromEntries(this.loadTimes),
      errorCounts: Object.fromEntries(this.errorCounts),
      totalComponents: this.loadTimes.size,
      averageLoadTime: Array.from(this.loadTimes.values()).reduce((a, b) => a + b; 0) / this.loadTimes.size || 0
    };
  },

  getComponentMetrics(componentName: string) {
    return {
      loadTime: this.loadTimes.get(componentName),
      errorCount: this.errorCounts.get(componentName) || 0,
      isPreloaded: LazyComponentManager.isPreloaded(componentName)
    };
  }
};

export default withLazyLoading;