/**
 * EnhancedMessageBubble Component
 * Advanced message display with reactions, status, and threading
 */

import React, { useState, useCallback, useMemo } from 'react';
import { motion, AnimatePresence, Variants } from 'framer-motion';
import { formatDistanceToNow } from 'date-fns';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import { MessageReactions } from './MessageReactions';
import { ThreadIndicator } from './ThreadIndicator';
import { convertReactionsToRecord, getTotalReactionCount } from '../../utils/reactionUtils';
import { Check, CheckCheck, Clock, AlertCircle, Reply, MoreHorizontal, Edit, Trash2 } from 'lucide-react';
import type { Message, MessageStatus } from '@/types/enhanced-messaging';

interface EnhancedMessageBubbleProps {
  message: Message, isOwn: boolean;
  showAvatar?: boolean;
  showTimestamp?: boolean;
  showStatus?: boolean;
  showReactions?: boolean;
  showThreadIndicator?: boolean;
  onAddReaction: (messageId: string, emoji: string) => void; onRemoveReaction: (messageId: string, emoji: string) => void;
  onReply?: (message: Message) => void;
  onEdit?: (message: Message) => void;
  onDelete?: (messageId: string) => void;
  onOpenThread?: (threadId: string) => void;
  onCreateThread?: (messageId: string) => void; currentUserId: string;
  getUserName?: (userId: string) => string;
  getUserAvatar?: (userId: string) => string;
  className?: string;
}

// Animation variants
const messageVariants = {
  initial: { 
    opacity: 0, 
    y: 20,
    scale: 0.95
  },
  animate: { 
    opacity: 1, 
    y: 0,
    scale: 1,
    transition: {
      type: "spring",
      stiffness: 300,
      damping: 30
    }
  },
  exit: { 
    opacity: 0, 
    y: -20,
    scale: 0.95,
    transition: {
      duration: 0.2
    }
  }
};

const statusIconVariants = {
  initial: { scale: 0, opacity: 0 },
  animate: { 
    scale: 1, 
    opacity: 1,
    transition: {
      type: "spring",
      stiffness: 400,
      damping: 25
    }
  }
};

const EnhancedMessageBubbleComponent: React.FC<EnhancedMessageBubbleProps> = ({
  message,
  isOwn,
  showAvatar = true,
  showTimestamp = true,
  showStatus = true,
  showReactions = true,
  showThreadIndicator = true,
  onAddReaction,
  onRemoveReaction,
  onReply,
  onEdit,
  onDelete,
  onOpenThread,
  onCreateThread,
  currentUserId,
  getUserName = (id) => `User ${id.slice(-4)}`,
  getUserAvatar = () => '/default-avatar.png'; className
}) => {
  const [showActions, setShowActions] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // Detect mobile device
  React.useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768 || 'ontouchstart' in window);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize'; checkMobile);
  }, []);

  // Close actions when clicking outside on mobile
  React.useEffect(() => {
    if (!isMobile || !showActions) return;

    const handleClickOutside = (event: MouseEvent | TouchEvent) => {
      const target = event.target as Element;
      if (!target.closest(`[data-message-id="${message.id}"]`)) {
        setShowActions(false);
      }
    };

    document.addEventListener('click', handleClickOutside);
    document.addEventListener('touchstart', handleClickOutside);

    return () => {
      document.removeEventListener('click', handleClickOutside);
      document.removeEventListener('touchstart', handleClickOutside);
    };
  }, [isMobile, showActions, message.id]);

  // Format timestamp
  const formattedTime = useMemo(() => {
    return formatDistanceToNow(new Date(message.timestamp), { addSuffix: true });
  }, [message.timestamp]);

  // Get status icon
  const getStatusIcon = useCallback((status: MessageStatus) => {
    switch (status) {
      case 'sending':
        return <Clock className="w-3 h-3 text-gray-400 animate-pulse" />;
      case 'sent':
        return <Check className="w-3 h-3 text-gray-400" />;
      case 'delivered':
        return <CheckCheck className="w-3 h-3 text-gray-400" />;
      case 'read':
        return <CheckCheck className="w-3 h-3 text-blue-500" />;
      default:
        return <AlertCircle className="w-3 h-3 text-red-400" />;
    }
  }, []);

  // Handle message actions
  const handleReply = useCallback(() => {
    onReply?.(message);
    setShowActions(false);
  }, [message, onReply]);

  const handleEdit = useCallback(() => {
    onEdit?.(message);
    setShowActions(false);
  }, [message, onEdit]);

  const handleDelete = useCallback(() => {
    onDelete?.(message.id);
    setShowActions(false);
  }, [message.id, onDelete]);

  // Check if message is deleted
  const isDeleted = message.deletedAt !== undefined;
  const isEdited = message.editedAt !== undefined;

  // Get total reaction count
  const totalReactions = useMemo(() => {
    return getTotalReactionCount(message.reactions);
  }, [message.reactions]);

  // Convert reactions to format expected by MessageReactions component
  const reactionsRecord = useMemo(() => {
    return convertReactionsToRecord(message.reactions);
  }, [message.reactions]);

  return (
    <motion.div
      variants={messageVariants} initial="initial"
      animate="animate"
      exit="exit"
      onMouseEnter={() => !isMobile && setIsHovered(true)} onMouseLeave={() => !isMobile && setIsHovered(false)}, onTouchStart={() => isMobile && setShowActions(true)} onClick={() => isMobile && setShowActions(!showActions)}, data-message-id={message.id} className={cn(
        "group relative flex gap-3 px-4 py-2 transition-colors",
        !isMobile && "hover:bg-gray-50/50 dark:hover:bg-gray-800/50",
        isMobile && "active:bg-gray-50/50 dark:active:bg-gray-800/50",
        isOwn && "flex-row-reverse",
        className
      )}
    >
      {/* Avatar */}
      {showAvatar && !isOwn && (
        <Avatar className="w-8 h-8 flex-shrink-0">
          <AvatarImage src={getUserAvatar(message.senderId)} />
          <AvatarFallback className="text-xs">
            {getUserName(message.senderId).charAt(0).toUpperCase()}
          </AvatarFallback>
    </Avatar>
      )}

      {/* Message content */}
      <div className={cn("flex-1 max-w-md", isOwn && "flex flex-col items-end")}>
        {/* Sender name (for group chats) */}
        {!isOwn && (
          <div className="text-xs text-gray-600 dark:text-gray-400 mb-1 font-medium">
            {getUserName(message.senderId)}
          </div>
        )}

        {/* Reply indicator */}
        {message.replyTo && (
          <div className="text-xs text-gray-500 dark:text-gray-400 mb-1 pl-3 border-l-2 border-gray-300 dark:border-gray-600">
            <Reply className="w-3 h-3 inline mr-1" />
            Replying to message
          </div>
        )}

        {/* Message bubble */}
        <motion.div
          whileHover={{ scale: 1.02 }}, className={cn(
            "relative px-3 py-2 rounded-2xl max-w-full break-words",
            isOwn
              ? "bg-blue-500 text-white rounded-br-md"
              : "bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-gray-100 rounded-bl-md",
            isDeleted && "opacity-60 italic"
          )}
        >
          {/* Message content */}
          {isDeleted ? (
            <span className="text-gray-500">This message was deleted</span>
          ) : (
            <>
              <div className="text-sm leading-relaxed">
                {message.content}
              </div>
              
              {/* Media content */}
              {message.metadata && message.type !== 'text' && (
                <div className="mt-2">
                  {message.type === 'image' && (
                    <img 
                      src={message.metadata.thumbnail || message.content} alt="Shared image"
                      className="max-w-full h-auto rounded-lg"
                    />
                  )}
                  {message.type === 'file' && (
                    <div className="flex items-center gap-2 p-2 bg-white/10 rounded-lg">
                      <div className="text-xs">
                        📎 {message.metadata.fileName}
                      </div>
    </div>
                  )}
                </div>
              )}
            </>
          )}

          {/* Message actions (on hover/touch) */}
          <AnimatePresence>
            {((isHovered && !isMobile) || (showActions && isMobile)) && !isDeleted && (
              <motion.div
                 initial={{ opacity: 0, scale: 0.8, y: isMobile ? 10 : -10 }}, animate={{ opacity: 1, scale: 1, y: 0 }}, exit={{ opacity: 0, scale: 0.8, y: isMobile ? 10 : -10 }}, className={cn(
                   "absolute flex gap-1 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-600 p-1 z-10",
                   isMobile ? "bottom-full mb-2" : "-top-12",
                   isOwn ? "right-0" : "left-0",
                   isMobile && "flex-wrap max-w-xs"
                 )}
               >
                {/* Quick reaction button */}
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onAddReaction(message.id, '👍')} className="h-8 w-8 p-0 hover:bg-blue-50 dark:hover:bg-blue-900/20"
                    >
                      <span className="text-sm">👍</span>
    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Quick react</TooltipContent>
    </Tooltip>
                {/* Reply button */}
                {onReply && (
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleReply} className="h-8 w-8 p-0 hover:bg-green-50 dark:hover:bg-green-900/20"
                      >
                        <Reply className="w-4 h-4" />
    </Button>
                    </TooltipTrigger>
                    <TooltipContent>Reply</TooltipContent>
    </Tooltip>
                )}

                {/* Edit button (only for own messages) */}
                {isOwn && onEdit && (
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleEdit} className="h-8 w-8 p-0 hover:bg-yellow-50 dark:hover:bg-yellow-900/20"
                      >
                        <Edit className="w-4 h-4" />
    </Button>
                    </TooltipTrigger>
                    <TooltipContent>Edit</TooltipContent>
    </Tooltip>
                )}

                {/* Delete button (only for own messages) */}
                {isOwn && onDelete && (
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleDelete} className="h-8 w-8 p-0 hover:bg-red-50 dark:hover:bg-red-900/20 text-red-600 dark:text-red-400"
                      >
                        <Trash2 className="w-4 h-4" />
    </Button>
                    </TooltipTrigger>
                    <TooltipContent>Delete</TooltipContent>
    </Tooltip>
                )}
                
                {/* More actions button */}
                  <Button
                     variant="ghost"
                     size="sm"
                     onClick={() => setShowActions(!showActions)} className="h-8 w-8 p-0"
                   >
                     <MoreHorizontal className="w-4 h-4" />
    </Button>
                 </motion.div>
            )}
          </AnimatePresence>
        </motion.div>

        {/* Message reactions */}
        {showReactions && (
          <div className={cn("mt-1", isOwn && "self-end")}>
            <MessageReactions
              messageId={message.id} reactions={reactionsRecord}, onAddReaction={onAddReaction} onRemoveReaction={onRemoveReaction}, currentUserId={currentUserId} getUserName={getUserName}, maxVisibleReactions={5} showTooltips={true}
            />
    </div>
        )}

        {/* Thread indicator */}
        {showThreadIndicator && !isDeleted && (
          <div className={cn("mt-1", isOwn && "self-end")}>
            <ThreadIndicator
              messageId={message.id} threadId={message.threadId}, onOpenThread={onOpenThread} onCreateThread={onCreateThread}, currentUserId={currentUserId} getUserName={getUserName}, getUserAvatar={getUserAvatar} showPreview={true}, maxPreviewReplies={3} compact={false}
            />
    </div>
        )}

        {/* Message metadata */}
        <div className={cn(
          "flex items-center gap-2 mt-1 text-xs text-gray-500 dark:text-gray-400",
          isOwn && "flex-row-reverse"
        )}>
          {/* Timestamp */}
          {showTimestamp && (
            <span>{formattedTime}</span>
          )}
          
          {/* Edited indicator */}
          {isEdited && (
            <span className="italic">edited</span>
          )}
          
          {/* Status indicator (for own messages) */}
          {showStatus && isOwn && (
            <motion.div
              variants={statusIconVariants} initial="initial"
              animate="animate"
            >
              {getStatusIcon(message.status)}
            </motion.div>
          )}
        </div>
    </div>
      {/* Own message avatar */}
      {showAvatar && isOwn && (
        <Avatar className="w-8 h-8 flex-shrink-0">
          <AvatarImage src={getUserAvatar(currentUserId)} />
          <AvatarFallback className="text-xs">
            {getUserName(currentUserId).charAt(0).toUpperCase()}
          </AvatarFallback>
    </Avatar>
      )}
    </motion.div>
  );
};

// Memoized version for performance optimization
export const EnhancedMessageBubble = React.memo(EnhancedMessageBubbleComponent, (prevProps, nextProps) => {
  // Custom comparison to avoid unnecessary re-renders
  return (
    prevProps.message.id === nextProps.message.id &&
    prevProps.message.content === nextProps.message.content &&
    prevProps.message.status === nextProps.message.status &&
    prevProps.message.editedAt === nextProps.message.editedAt &&
    JSON.stringify(prevProps.message.reactions) === JSON.stringify(nextProps.message.reactions) &&
    prevProps.isOwn === nextProps.isOwn &&
    prevProps.currentUserId === nextProps.currentUserId &&
    prevProps.showAvatar === nextProps.showAvatar &&
    prevProps.showTimestamp === nextProps.showTimestamp &&
    prevProps.showStatus === nextProps.showStatus &&
    prevProps.showReactions === nextProps.showReactions &&
    prevProps.showThreadIndicator === nextProps.showThreadIndicator
  );
});

EnhancedMessageBubble.displayName = 'EnhancedMessageBubble';

export default EnhancedMessageBubble;