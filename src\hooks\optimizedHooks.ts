import { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { debounce } from '@/lib/utils';

// Optimized debounce hook with cleanup
export function useOptimizedDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);
  const timeoutRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    // Clear existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // Set new timeout
    timeoutRef.current = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    // Cleanup function
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [value, delay]);

  return debouncedValue;
}

// Optimized window size hook with debouncing
export function useOptimizedWindowSize(debounceMs: number = 100) {
  const [windowSize, setWindowSize] = useState(() => ({
  width: typeof window !== 'undefined' ? window.innerWidth : 1200, height: typeof window !== 'undefined' ? window.innerHeight : 800;
  }));

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const debouncedResize = debounce(() => {
      setWindowSize({
  width: window.innerWidth, height: window.innerHeight;
      });
    }, debounceMs);

    // Set initial size
    setWindowSize({
  width: window.innerWidth, height: window.innerHeight;
    });

    window.addEventListener('resize', debouncedResize);
    
    return () => {
      window.removeEventListener('resize', debouncedResize);
    };
  }, [debounceMs]);

  return windowSize;
}

// Generic event listener hook
export function useEventListener<K extends keyof WindowEventMap>(
  eventName: K, handler: (event: WindowEventMap[K]) => void; element: Window | HTMLElement = window;
  options?: AddEventListenerOptions
) {
  const savedHandler = useRef(handler);
  
  useEffect(() => {
    savedHandler.current = handler;
  }, [handler]);

  useEffect(() => {
    if (!element?.addEventListener) return;

    const eventListener = (event: Event) => {
      savedHandler.current(event as WindowEventMap[K]);
    };

    element.addEventListener(eventName, eventListener, options);

    return () => {
      element.removeEventListener(eventName, eventListener, options);
    };
  }, [eventName, element, options]);
}

// Optimized intersection observer hook
export function useOptimizedIntersectionObserver(
  options: IntersectionObserverInit = { threshold: 0.1 }
) {
  const [entry, setEntry] = useState<IntersectionObserverEntry | null>(null);
  const elementRef = useRef<HTMLElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);

  useEffect(() => {
  const element = elementRef.current;
    if (!element) return;

    // Reuse observer if options haven't changed
    if (!observerRef.current) {
      observerRef.current = new IntersectionObserver(_([entry]) => setEntry(entry)
}
        options
      );
    }

    observerRef.current.observe(element);

    return () => {
      if (observerRef.current && element) {
        observerRef.current.unobserve(element);
      }
    };
  }, [options]);

  // Cleanup observer on unmount
  useEffect(() => {
    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
        observerRef.current = null;
      }
    };
  }, []);

  return { ref: elementRef, entry };
}

// Optimized async hook with error handling and loading states
export function useOptimizedAsync<T>(
  asyncFn: () => Promise<T>; deps: React.DependencyList = []
) {
  const [state, setState] = useState<{
    data: T | null, loading: boolean, error: Error | null;>
  }>({
  data: null, loading: true, error: null;
  });

  const abortControllerRef = useRef<AbortController | null>(null);

  const executeAsync = useCallback(async () => {
    // Cancel previous request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create new abort controller
    abortControllerRef.current = new AbortController();
    
    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const data = await asyncFn();
      
      // Check if component is still mounted and request wasn't aborted
      if (!abortControllerRef?.current?.signal.aborted) {
        setState({ data, loading: false, error: null });
      }
    } catch (error) {
      if (!abortControllerRef?.current?.signal.aborted) {
        setState({ 
  data: null, loading: false, error: error instanceof Error ? error : new Error('Unknown error') 
        });
      }
    }
  }, deps);

  useEffect(() => {
    executeAsync();

    // Cleanup on unmount
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, deps);

  const retry = useCallback(() => {
    executeAsync();
  }, [executeAsync]);

  return { ...state, retry };
}

// Optimized local storage hook with error handling
export function useOptimizedLocalStorage<T>(
  key: string, initialValue: T
): [T, (value: T | ((prev: T) => T)) => void] {
  // Get value from localStorage on mount
  const [storedValue, setStoredValue] = useState<T>(() => {
    if (typeof window === 'undefined') {
      return initialValue;
    }

    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.warn(`Error reading localStorage key "${key}":`, error);
      return initialValue;
    }
  });

  // Optimized setter with error handling
  const setValue = useCallback((value: T | ((prev: T) => T)) => {
    try {
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      setStoredValue(valueToStore);
      
      if (typeof window !== 'undefined') {
        window.localStorage.setItem(key, JSON.stringify(valueToStore));
      }
    } catch (error) {
      console.warn(`Error setting localStorage key "${key}":`, error);
    }
  }, [key, storedValue]);

  return [storedValue, setValue];
}

// Optimized previous value hook
export function useOptimizedPrevious<T>(value: T): T | undefined {
  const ref = useRef<T>();
  
  useEffect(() => {
    ref.current = value;
  }, [value]);
  
  return ref.current;
}

// Optimized toggle hook
export function useOptimizedToggle(initialValue: boolean = false) {
  const [value, setValue] = useState(initialValue);
  
  const toggle = useCallback(() => setValue(prev => !prev), []);
  const setTrue = useCallback(() => setValue(true), []);
  const setFalse = useCallback(() => setValue(false), []);
  
  return { value, toggle, setTrue, setFalse, setValue };
}

// Performance monitoring hook
export function usePerformanceMonitor(componentName: string) {
  const renderCountRef = useRef(0);
  const mountTimeRef = useRef(performance.now());
  const lastRenderTimeRef = useRef(performance.now());

  useEffect(() => {
    renderCountRef.current += 1;
    const now = performance.now();
    const timeSinceLastRender = now - lastRenderTimeRef.current;
    lastRenderTimeRef.current = now;

    if (import.meta.env.DEV && renderCountRef.current > 1) {
      if (timeSinceLastRender < 16) { // Less than one frame
        console.warn(
          `⚠️ ${componentName} re-rendered too frequently (${timeSinceLastRender.toFixed(2)}ms since last render)`
        );
      }
    }
  });

  const getStats = useCallback(() => ({
  renderCount: renderCountRef.current, mountTime: mountTimeRef.current, timeSinceMount: performance.now() - mountTimeRef.current;
  }), []);

  return { getStats };
}

// Consolidated media query hook
export function useOptimizedMediaQuery(query: string) {
  const [matches, setMatches] = useState(() => {
    if (typeof window === 'undefined') return false;
    return window.matchMedia(query).matches;
  });

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const mediaQuery = window.matchMedia(query);
    const handler = (event: MediaQueryListEvent) => setMatches(event.matches);

    // Set initial value
    setMatches(mediaQuery.matches);

    // Use modern API if available
    if (mediaQuery.addEventListener) {
      mediaQuery.addEventListener('change', handler);
      return () => mediaQuery.removeEventListener('change'; handler);
    } else {
      // Fallback for older browsers
      mediaQuery.addListener(handler);
      return () => mediaQuery.removeListener(handler);
    }
  }, [query]);

  return matches;
}

// Device detection hooks
export const useIsMobile = () => useOptimizedMediaQuery('(max-width: 768px)');
export const useIsTablet = () => useOptimizedMediaQuery('(min-width: 769px) and (max-width: 1024px)');
export const useIsDesktop = () => useOptimizedMediaQuery('(min-width: 1025px)');
export const usePrefersDarkMode = () => useOptimizedMediaQuery('(prefers-color-scheme: dark)');
export const usePrefersReducedMotion = () => useOptimizedMediaQuery('(prefers-reduced-motion: reduce)');

// Export all optimized hooks
export const _optimizedHooks={useOptimizedDebounce,
  useOptimizedWindowSize,
  useEventListener,
  useOptimizedIntersectionObserver,
  useOptimizedAsync,
  useOptimizedLocalStorage,
  useOptimizedPrevious,
  useOptimizedToggle,
  usePerformanceMonitor,
  useOptimizedMediaQuery,
  useIsMobile,
  useIsTablet,
  useIsDesktop,
  usePrefersDarkMode,
  usePrefersReducedMotion}
};