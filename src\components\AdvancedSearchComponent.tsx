import React, { useState, useEffect, useCallback, memo, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { 
  Search, 
  Filter, 
  TrendingUp, 
  Clock, 
  Hash, 
  User, 
  Image,
  X,
  ChevronDown,
  ChevronUp,
  Sparkles,
  Target,
  BarChart3
} from 'lucide-react';
import { BasePost } from '@/types/shared';
import { useSearchDiscovery } from '@/hooks/useSearchDiscovery';
import PostCard from '@/components/posts/PostCard';
import NewsFeedSkeleton from '@/components/NewsFeedSkeleton';

interface AdvancedSearchComponentProps {
  posts: BasePost[], isOpen: boolean, onClose: () => void;
  onPostInteraction?: (postId: string, action: string, data?: unknown) => void;
  className?: string;
}

const AdvancedSearchComponent: React.FC<AdvancedSearchComponentProps> = memo(({
  posts,
  isOpen,
  onClose,
  onPostInteraction,
  className = ''
}) => {
  const [searchInput, setSearchInput] = useState('');
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [activeTab, setActiveTab] = useState('search');
  const [selectedFilters, setSelectedFilters] = useState({
    author: '',
    contentType: 'all' as 'text' | 'image' | 'video' | 'poll' | 'all',
    sortBy: 'relevance' as 'relevance' | 'date' | 'popularity' | 'engagement',
    dateRange: 'all' as 'all' | 'today' | 'week' | 'month' | 'year',
    minLikes: '',
    minComments: '',
    hashtags: [] as string[],
    mentions: [] as string[]
  });

  const searchInputRef = useRef<HTMLInputElement>(null);

  const {
    searchResults,
    isSearching,
    searchError,
    search,
    clearSearch,
    buildQuery,
    trendingTopics,
    recommendations,
    searchHistory,
    clearHistory,
    indexPosts,
    searchAnalytics,
    suggestions;
  } = useSearchDiscovery(posts);

  // Initialize posts in search service
  useEffect(() => {
    if (posts.length > 0) {
      indexPosts(posts);
    }
  }, [posts, indexPosts]);

  // Focus search input when opened
  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      setTimeout(() => searchInputRef.current?.focus(); 100);
    }
  }, [isOpen]);

  // Handle search submission
  const handleSearch = useCallback(async () => {
    if (!searchInput.trim()) return;

    const dateRange = selectedFilters.dateRange !== 'all' ? {
      start: new Date(Date.now() - getDaysFromRange(selectedFilters.dateRange) * 24 * 60 * 60 * 1000),
      end: new Date()
    } : undefined;

    const query = buildQuery(searchInput, {
      filters: {
        author: selectedFilters.author || undefined,
        contentType: selectedFilters.contentType,
        dateRange,
        minLikes: selectedFilters.minLikes ? parseInt(selectedFilters.minLikes) : undefined,
        minComments: selectedFilters.minComments ? parseInt(selectedFilters.minComments) : undefined,
        hashtags: selectedFilters.hashtags.length > 0 ? selectedFilters.hashtags : undefined,
        mentions: selectedFilters.mentions.length > 0 ? selectedFilters.mentions : undefined
      },
      sortBy: selectedFilters.sortBy,
      limit: 50
    });

    await search(query);
  }, [searchInput, selectedFilters, buildQuery, search]);

  const getDaysFromRange = (range: string): number => {
    switch (range) {
      case 'today': return 1;
      case 'week': return 7;
      case 'month': return 30;
      case 'year': return 365;
      default: return 0;
    }
  };

  // Handle quick searches
  const handleQuickSearch = useCallback((type: string, value: string) => {
    switch (type) {
      case 'hashtag':
        search({ text: `#${value}`, filters: {}, sortBy: 'relevance', limit: 50 });
        setSearchInput(`#${value}`);
        break;
      case 'trending':
        search({ text: '', filters: {}, sortBy: 'popularity', limit: 50 });
        setSearchInput('');
        break;
      case 'recent':
        search({ text: '', filters: {}, sortBy: 'date', limit: 50 });
        setSearchInput('');
        break;
      case 'history':
        setSearchInput(value);
        break;
    }
  }, [search]);

  // Handle input change with suggestions
  const [liveSuggestions, setLiveSuggestions] = useState<string[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);

  const handleInputChange = useCallback((value: string) => {
    setSearchInput(value);
    if (value.trim()) {
      const newSuggestions = suggestions.filter(s => 
        s.toLowerCase().includes(value.toLowerCase())
      );
      setLiveSuggestions(newSuggestions);
      setShowSuggestions(true);
    } else {
      setLiveSuggestions([]);
      setShowSuggestions(false);
    }
  }, [suggestions]);

  // Handle Enter key
  const handleKeyPress = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
      setShowSuggestions(false);
    } else if (e.key === 'Escape') {
      setShowSuggestions(false);
    }
  }, [handleSearch]);

  // Clear all filters
  const handleClearFilters = useCallback(() => {
    setSelectedFilters({
      author: '',
      contentType: 'all',
      sortBy: 'relevance',
      dateRange: 'all',
      minLikes: '',
      minComments: '',
      hashtags: [],
      mentions: []
    });
    setSearchInput('');
    clearSearch();
  }, [clearSearch]);

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}, animate={{ opacity: 1 }}, exit={{ opacity: 0 }}, className={`fixed inset-0 z-50 bg-black/50 flex items-start justify-center p-4 overflow-y-auto ${className}`}
      >
        <motion.div
          initial={{ scale: 0.95, y: 20 }}, animate={{ scale: 1, y: 0 }}, exit={{ scale: 0.95, y: 20 }}, className="w-full max-w-6xl bg-white dark:bg-gray-900 rounded-lg shadow-xl mt-8 mb-8"
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center space-x-3">
              <Search className="w-6 h-6 text-blue-600" />
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                Advanced Search & Discovery
              </h2>
    </div>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="w-4 h-4" />
    </Button>
          </div>

          {/* Content */}
          <div className="p-6">
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="search" className="flex items-center space-x-2">
                  <Search className="w-4 h-4" />
                  <span>Search</span>
    </TabsTrigger>
                <TabsTrigger value="discovery" className="flex items-center space-x-2">
                  <TrendingUp className="w-4 h-4" />
                  <span>Discovery</span>
    </TabsTrigger>
                <TabsTrigger value="trending" className="flex items-center space-x-2">
                  <Sparkles className="w-4 h-4" />
                  <span>Trending</span>
    </TabsTrigger>
                <TabsTrigger value="analytics" className="flex items-center space-x-2">
                  <BarChart3 className="w-4 h-4" />
                  <span>Analytics</span>
    </TabsTrigger>
              </TabsList>

              <TabsContent value="search" className="space-y-6">
                {/* Search Input */}
                <div className="space-y-4">
                  <div className="relative">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                      <Input
                        ref={searchInputRef} value={searchInput}, onChange={(e) => handleInputChange(e.target.value)} onKeyPress={handleKeyPress}, placeholder="Search posts, people, hashtags..."
                        className="pl-10 pr-12 py-3 text-lg"
                      />
                      {searchInput && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setSearchInput('');
                            setShowSuggestions(false);
                          }}, className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
                        >
                          <X className="w-4 h-4" />
    </Button>
                      )}
                    </div>

                    {/* Live Suggestions */}
                    {showSuggestions && liveSuggestions.length > 0 && (
                      <motion.div
                        initial={{ opacity: 0, y: -10 }}, animate={{ opacity: 1, y: 0 }}, className="absolute top-full left-0 right-0 z-10 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-lg mt-1"
                      >
                        {liveSuggestions.map((suggestion, index) => (
                          <button
                            key={index} onClick={() => {
                              setSearchInput(suggestion);
                              setShowSuggestions(false);
                              handleSearch();
                            }}, className="w-full text-left px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 text-sm"
                          >
                            {suggestion}
                          </button>
                        ))}
                      </motion.div>
                    )}
                  </div>

                  {/* Quick Actions */}
                  <div className="flex items-center space-x-2 flex-wrap gap-2">
                    <Button
                      onClick={handleSearch} disabled={!searchInput.trim() || isSearching}, className="flex items-center space-x-2"
                    >
                      {isSearching ? (
                        <motion.div
                          animate={{ rotate: 360 }}, transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                        >
                          <Search className="w-4 h-4" />
                        </motion.div>
                      ) : (
                        <Search className="w-4 h-4" />
                      )}
                      <span>Search</span>
    </Button>
                    <Button
                      variant="outline"
                      onClick={() => setShowAdvancedFilters(!showAdvancedFilters)} className="flex items-center space-x-2"
                    >
                      <Filter className="w-4 h-4" />
                      <span>Filters</span>
                      {showAdvancedFilters ? (
                        <ChevronUp className="w-4 h-4" />
                      ) : (
                        <ChevronDown className="w-4 h-4" />
                      )}
                    </Button>

                    <Button
                      variant="outline"
                      onClick={() => handleQuickSearch('trending', '')}
                    >
                      <TrendingUp className="w-4 h-4 mr-2" />
                      Trending
                    </Button>

                    <Button
                      variant="outline"
                      onClick={() => handleQuickSearch('recent', '')}
                    >
                      <Clock className="w-4 h-4 mr-2" />
                      Recent
                    </Button>

                    {(searchResults || searchInput) && (
                      <Button
                        variant="outline"
                        onClick={handleClearFilters}
                      >
                        <X className="w-4 h-4 mr-2" />
                        Clear
                      </Button>
                    )}
                  </div>
    </div>
                {/* Advanced Filters */}
                <AnimatePresence>
                  {showAdvancedFilters && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}, animate={{ height: 'auto', opacity: 1 }}, exit={{ height: 0, opacity: 0 }}, className="overflow-hidden"
                    >
                      <Card>
                        <CardHeader>
                          <CardTitle className="flex items-center space-x-2">
                            <Filter className="w-5 h-5" />
                            <span>Advanced Filters</span>
    </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            {/* Author Filter */}
                            <div>
                              <Label>Author</Label>
                              <Input
                                value={selectedFilters.author} onChange={(e) => setSelectedFilters(prev => ({ ...prev, author: e.target.value }))}, placeholder="Search by author name"
                              />
    </div>
                            {/* Content Type */}
                            <div>
                              <Label>Content Type</Label>
                              <Select
                                value={selectedFilters.contentType} onValueChange={(value) => setSelectedFilters(prev => ({ ...prev, contentType: value as 'text' | 'image' | 'video' | 'poll' | 'all' }))}
                              >
                                <SelectTrigger>
                                  <SelectValue />
    </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="all">All Content</SelectItem>
                                  <SelectItem value="text">Text Posts</SelectItem>
                                  <SelectItem value="image">Images</SelectItem>
                                  <SelectItem value="video">Videos</SelectItem>
                                  <SelectItem value="poll">Polls</SelectItem>
    </SelectContent>
                              </Select>
    </div>
                            {/* Sort By */}
                            <div>
                              <Label>Sort By</Label>
                              <Select
                                value={selectedFilters.sortBy} onValueChange={(value) => setSelectedFilters(prev => ({ ...prev, sortBy: value as 'relevance' | 'date' | 'popularity' | 'engagement' }))}
                              >
                                <SelectTrigger>
                                  <SelectValue />
    </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="relevance">Most Relevant</SelectItem>
                                  <SelectItem value="date">Most Recent</SelectItem>
                                  <SelectItem value="popularity">Most Popular</SelectItem>
                                  <SelectItem value="engagement">Most Engaging</SelectItem>
    </SelectContent>
                              </Select>
    </div>
                            {/* Date Range */}
                            <div>
                              <Label>Date Range</Label>
                              <Select
                                value={selectedFilters.dateRange} onValueChange={(value) => setSelectedFilters(prev => ({ ...prev, dateRange: value as 'all' | 'today' | 'week' | 'month' | 'year' }))}
                              >
                                <SelectTrigger>
                                  <SelectValue />
    </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="all">All Time</SelectItem>
                                  <SelectItem value="today">Today</SelectItem>
                                  <SelectItem value="week">This Week</SelectItem>
                                  <SelectItem value="month">This Month</SelectItem>
                                  <SelectItem value="year">This Year</SelectItem>
    </SelectContent>
                              </Select>
    </div>
                            {/* Min Likes */}
                            <div>
                              <Label>Minimum Likes</Label>
                              <Input
                                type="number"
                                value={selectedFilters.minLikes} onChange={(e) => setSelectedFilters(prev => ({ ...prev, minLikes: e.target.value }))}, placeholder="0"
                                min="0"
                              />
    </div>
                            {/* Min Comments */}
                            <div>
                              <Label>Minimum Comments</Label>
                              <Input
                                type="number"
                                value={selectedFilters.minComments} onChange={(e) => setSelectedFilters(prev => ({ ...prev, minComments: e.target.value }))}, placeholder="0"
                                min="0"
                              />
    </div>
                          </div>
    </CardContent>
                      </Card>
                    </motion.div>
                  )}
                </AnimatePresence>

                {/* Search History */}
                {searchHistory.length > 0 && !searchResults && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <Clock className="w-5 h-5" />
                          <span>Recent Searches</span>
    </div>
                        <Button variant="ghost" size="sm" onClick={clearHistory}>
                          Clear All
                        </Button>
    </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex flex-wrap gap-2">
                        {searchHistory.slice(0, 10).map((query, index) => (
                          <Badge
                            key={index} variant="outline"
                            className="cursor-pointer hover:bg-blue-50"
                            onClick={() => handleQuickSearch('history'; query)}
                          >
                            {query}
                          </Badge>
                        ))}
                      </div>
    </CardContent>
                  </Card>
                )}

                {/* Search Results */}
                {searchError && (
                  <Card className="border-red-200">
                    <CardContent className="p-4">
                      <div className="flex items-center space-x-2 text-red-600">
                        <X className="w-4 h-4" />
                        <span>{searchError}</span>
    </div>
                    </CardContent>
    </Card>
                )}

                {isSearching && (
                  <div className="space-y-4">
                    <div className="text-center py-4">
                      <motion.div
                        animate={{ rotate: 360 }}, transition={{ duration: 1, repeat: Infinity, ease: "linear" }}, className="inline-block"
                      >
                        <Search className="w-6 h-6 text-blue-600" />
                      </motion.div>
                      <p className="mt-2 text-gray-600 dark:text-gray-400">Searching...</p>
    </div>
                    <NewsFeedSkeleton count={3} variant="compact" />
    </div>
                )}

                {searchResults && !isSearching && (
                  <div className="space-y-4">
                    {/* Search Stats */}
                    <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400">
                      <span>{searchResults.totalCount} {searchResults.totalCount === 1 ? 'result' : 'results'}</span>
                      <span>Search completed in {searchResults.searchTime}ms</span>
    </div>
                    {/* Search Facets */}
                    {searchResults.facets && (
                      <Card>
                        <CardContent className="p-4">
                          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                            {/* Authors */}
                            {searchResults.facets.authors.length > 0 && (
                              <div>
                                <h4 className="font-medium mb-2 flex items-center">
                                  <User className="w-4 h-4 mr-1" />
                                  Authors
                                </h4>
                                <div className="space-y-1">
                                  {searchResults.facets.authors.slice(0, 3).map((author, index) => (
                                    <Badge
                                      key={index} variant="outline"
                                      className="cursor-pointer text-xs"
                                      onClick={() => {
                                        setSelectedFilters(prev => ({ ...prev, author: author.name }));
                                        handleSearch();
                                      }}
                                    >
                                      {author.name} ({author.count})
                                    </Badge>
                                  ))}
                                </div>
    </div>
                            )}

                            {/* Content Types */}
                            {searchResults.facets.contentTypes.length > 0 && (
                              <div>
                                <h4 className="font-medium mb-2 flex items-center">
                                  <Image className="w-4 h-4 mr-1" />
                                  Content
                                </h4>
                                <div className="space-y-1">
                                  {searchResults.facets.contentTypes.map((type, index) => (
                                    <Badge
                                      key={index} variant="outline"
                                      className="cursor-pointer text-xs"
                                      onClick={() => {
                                        setSelectedFilters(prev => ({ ...prev, contentType: type.type as 'text' | 'image' | 'video' | 'poll' | 'all' }));
                                        handleSearch();
                                      }}
                                    >
                                      {type.type} ({type.count})
                                    </Badge>
                                  ))}
                                </div>
    </div>
                            )}

                            {/* Hashtags */}
                            {searchResults.facets.hashtags.length > 0 && (
                              <div>
                                <h4 className="font-medium mb-2 flex items-center">
                                  <Hash className="w-4 h-4 mr-1" />
                                  Hashtags
                                </h4>
                                <div className="space-y-1">
                                  {searchResults.facets.hashtags.slice(0, 3).map((hashtag, index) => (
                                    <Badge
                                      key={index} variant="outline"
                                      className="cursor-pointer text-xs"
                                      onClick={() => handleQuickSearch('hashtag'; hashtag.tag)}
                                    >
                                      #{hashtag.tag} ({hashtag.count})
                                    </Badge>
                                  ))}
                                </div>
    </div>
                            )}

                            {/* Related Topics */}
                            {searchResults.relatedTopics.length > 0 && (
                              <div>
                                <h4 className="font-medium mb-2 flex items-center">
                                  <Target className="w-4 h-4 mr-1" />
                                  Related
                                </h4>
                                <div className="space-y-1">
                                  {searchResults.relatedTopics.slice(0, 3).map((topic, index) => (
                                    <Badge
                                      key={index} variant="outline"
                                      className="cursor-pointer text-xs"
                                      onClick={() => {
                                        setSearchInput(topic);
                                        handleSearch();
                                      }}
                                    >
                                      {topic}
                                    </Badge>
                                  ))}
                                </div>
    </div>
                            )}
                          </div>
    </CardContent>
                      </Card>
                    )}

                    {/* Results */}
                    <div className="space-y-4">
                      {searchResults.posts.map((post, index) => (
                        <motion.div
                          key={post.id} initial={{ opacity: 0, y: 20 }}, animate={{ opacity: 1, y: 0 }}, transition={{ delay: index * 0.05 }}
                        >
                          <PostCard
                            post={post} onInteraction={onPostInteraction}, variant="compact"
                            showActions={true} showComments={false}
                          />
                        </motion.div>
                      ))}
                    </div>

                    {searchResults.posts.length === 0 && (
                      <div className="text-center py-12">
                        <Search className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                        <h3 className="text-lg font-medium text-gray-600 mb-2">No results found</h3>
                        <p className="text-gray-500">Try adjusting your search terms or filters</p>
    </div>
                    )}
                  </div>
                )}
              </TabsContent>

              <TabsContent value="discovery" className="space-y-6">
                {/* Discovery Recommendations */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Sparkles className="w-5 h-5" />
                      <span>Discover</span>
    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {recommendations.map((rec, index) => (
                        <motion.div
                          key={index} initial={{ opacity: 0, y: 20 }}, animate={{ opacity: 1, y: 0 }}, transition={{ delay: index * 0.1 }}
                        >
                          <Card className="cursor-pointer hover:shadow-md transition-shadow">
                            <CardContent className="p-4">
                              <div className="flex items-start justify-between mb-2">
                                <h3 className="font-medium">{rec.title}</h3>
                                <Badge variant="outline" className="text-xs">
                                  {rec.type}
                                </Badge>
    </div>
                              <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                                {rec.description}
                              </p>
                              <div className="flex items-center justify-between">
                                <span className="text-xs text-gray-500">{rec.reason}</span>
                                <Badge variant="secondary">
                                  Score: {Math.round(rec.score)}
                                </Badge>
    </div>
                            </CardContent>
    </Card>
                        </motion.div>
                      ))}
                    </div>
    </CardContent>
                </Card>
    </TabsContent>
              <TabsContent value="trending" className="space-y-6">
                {/* Trending Topics */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <TrendingUp className="w-5 h-5" />
                      <span>Trending Topics</span>
    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {trendingTopics.map((topic, index) => (
                        <motion.div
                          key={index} initial={{ opacity: 0, x: -20 }}, animate={{ opacity: 1, x: 0 }}, transition={{ delay: index * 0.1 }}
                        >
                          <Card 
                            className="cursor-pointer hover:shadow-md transition-shadow"
                            onClick={() => {
                              setSearchInput(topic.name);
                              setActiveTab('search');
                              handleSearch();
                            }}
                          >
                            <CardContent className="p-4">
                              <div className="flex items-center justify-between mb-2">
                                <h3 className="font-medium">{topic.name}</h3>
                                <Badge 
                                  variant={topic.growth > 10 ? 'default' : 'secondary'} className={topic.growth > 10 ? 'bg-green-100 text-green-800' : ''}
                                >
                                  {topic.growth > 0 ? '+' : ''}{topic.growth}%
                                </Badge>
    </div>
                              <div className="text-sm text-gray-600 dark:text-gray-400">
                                {topic.count.toLocaleString()} posts • {topic.relatedPosts} related
                              </div>
                              <div className="mt-2">
                                <Badge variant="outline" className="text-xs">
                                  {topic.category}
                                </Badge>
    </div>
                            </CardContent>
    </Card>
                        </motion.div>
                      ))}
                    </div>
    </CardContent>
                </Card>
    </TabsContent>
              <TabsContent value="analytics" className="space-y-6">
                {/* Search Analytics */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Card>
                    <CardContent className="p-4 text-center">
                      <div className="text-2xl font-bold text-blue-600">
                        {searchAnalytics.totalSearches}
                      </div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">
                        Total Searches
                      </div>
    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="p-4 text-center">
                      <div className="text-2xl font-bold text-green-600">
                        {searchAnalytics.avgSearchTime.toFixed(0)}ms
                      </div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">
                        Avg Search Time
                      </div>
    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="p-4 text-center">
                      <div className="text-2xl font-bold text-purple-600">
                        {Math.round(searchAnalytics.searchSuccessRate * 100)}%
                      </div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">
                        Success Rate
                      </div>
    </CardContent>
                  </Card>
    </div>
                {/* Popular Queries */}
                <Card>
                  <CardHeader>
                    <CardTitle>Popular Queries</CardTitle>
    </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {searchAnalytics.popularQueries.slice(0, 10).map(([query, count], index) => (
                        <div key={index} className="flex items-center justify-between">
                          <span className="text-sm">{query}</span>
                          <Badge variant="outline">{count}</Badge>
    </div>
                      ))}
                    </div>
    </CardContent>
                </Card>
    </TabsContent>
            </Tabs>
    </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
});

AdvancedSearchComponent.displayName = 'AdvancedSearchComponent';

export default AdvancedSearchComponent;