// Type declarations for Avatar components to fix Radix UI type issues
import React from 'react';

declare module '@radix-ui/react-avatar' {
  export interface ImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {
    src?: string;
    alt?: string;
    loading?: 'lazy' | 'eager';
  }
  
  export interface FallbackProps extends React.HTMLAttributes<HTMLSpanElement> {
    children?: React.ReactNode;
  }
  
  export interface RootProps extends React.HTMLAttributes<HTMLDivElement> {
    children?: React.ReactNode;
  }
}

// Extend the UI Avatar components with proper typing
declare module '@/components/ui/avatar' {
  export const Avatar: React.ForwardRefExoticComponent<>
    React.HTMLAttributes<HTMLDivElement> & React.RefAttributes<HTMLDivElement>
  >;

  export const AvatarImage: React.ForwardRefExoticComponent<>
    React.ImgHTMLAttributes<HTMLImageElement> & React.RefAttributes<HTMLImageElement>
  >;

  export const AvatarFallback: React.ForwardRefExoticComponent<>
    React.HTMLAttributes<HTMLSpanElement> & React.RefAttributes<HTMLSpanElement>
  >;
}
