import { useState, useCallback, useMemo, useRef } from 'react';
import { toast } from 'sonner';
import { 
  StoryFormState, 
  DEFAULT_FORM_STATE, 
  validateStoryForm, 
  getDurationInHours,
  StoryType,
  PrivacyLevel,
  DurationOption
} from './types';

/**
 * Custom hook for managing Story Creator state and operations
 * Optimized for performance with memoization and stable references
 */
export const useStoryCreator = (onCreateStory: (storyData: unknown) => void) => {
  // State management
  const [formState, setFormState] = useState<StoryFormState>(DEFAULT_FORM_STATE);
  const [selectedMusicTrack, setSelectedMusicTrack] = useState('');
  
  // Refs for file inputs to avoid re-creating
  const fileInputRef = useRef<HTMLInputElement>(null);
  const videoInputRef = useRef<HTMLInputElement>(null);

  // Memoized update function to prevent unnecessary re-renders
  const updateFormState = useCallback((updates: Partial<StoryFormState>) => {
    setFormState(prev => ({ ...prev, ...updates }));
  }, []);

  // Reset form with stable reference
  const resetForm = useCallback(() => {
    setFormState(DEFAULT_FORM_STATE);
    setSelectedMusicTrack('');
    
    // Clear file inputs
    if (fileInputRef.current) fileInputRef.current.value = '';
    if (videoInputRef.current) videoInputRef.current.value = '';
  }, []);

  // Media handling with optimized callbacks
  const mediaHandlers = useMemo(() => ({
    handleImageSelect: (imageUrl: string) => {
      updateFormState({ selectedImage: imageUrl });
    },
    
    handleVideoSelect: (videoUrl: string, thumbnailUrl: string) => {
  updateFormState({ 
        videoUrl
}
        selectedImage: thumbnailUrl 
      });
    },
  handleRemoveMedia: () => {
      updateFormState({ 
        selectedImage: '', videoUrl: '' 
      });
    },
    
    handleUploadStart: () => {
      updateFormState({ isUploading: true });
    },
    
    handleUploadEnd: () => {
      updateFormState({ isUploading: false });
    }
  }), [updateFormState]);

  // Text styling handlers with memoization
  const textHandlers = useMemo(() => ({
    handleBackgroundChange: (background: string) => {
      updateFormState({ background });
    },
    
    handleTextColorChange: (textColor: string) => {
      updateFormState({ textColor });
    },
    
    handleFontSizeChange: (fontSize: number[]) => {
      updateFormState({ fontSize: fontSize[0] });
    },
    
    handleTextAlignmentChange: (textAlignment: string) => {
      updateFormState({ textAlignment });
    }
  }), [updateFormState]);

  // Interactive features handlers
  const interactiveHandlers = useMemo(() => ({
  handleUpdateMusic: (music: Partial<StoryFormState['music']>) => {
      setFormState(prev => ({
        ...prev, music: { ...prev.music, ...music }
      }));
    },
  handleUpdatePoll: (poll: Partial<StoryFormState['poll']>) => {
      setFormState(prev => ({
        ...prev, poll: { ...prev.poll, ...poll }
      }));
    },
  handleUpdateCountdown: (countdown: Partial<StoryFormState['countdown']>) => {
      setFormState(prev => ({
        ...prev, countdown: { ...prev.countdown, ...countdown }
      }));
    },
    
    handleUpdateQuestion: (question: string) => {
      updateFormState({ question });
    },
    
    handleUpdateARFilters: (arFilters: string[]) => {
      updateFormState({ arFilters });
    },
  handleUpdateStickers: (stickers: Partial<StoryFormState['stickers']>) => {
      setFormState(prev => ({
        ...prev, stickers: { ...prev.stickers, ...stickers }
      }));
    }
  }), [updateFormState]);

  // Story creation with validation
  const handleCreateStory = useCallback(() => {
    const validationError = validateStoryForm(formState);
    if (validationError) {
      toast.error(validationError);
      return;
    }

    try {
      const storyData={type: formState.type, content: formState.content, background: formState.background, textColor: formState.textColor, fontSize: formState.fontSize, textAlignment: formState.textAlignment, media: formState.type === 'video' ? formState.videoUrl : formState.selectedImage, privacy: formState.privacy, duration: getDurationInHours(formState.duration, formState.customHours)}, timestamp: new Date().toISOString(), music: (formState.music.title || formState.music.artist) ? formState.music : undefined, polls: formState.poll.question ? formState.poll : undefined, countdown: formState.countdown.enabled && formState.countdown.endTime ? 
          { endTime: formState.countdown.endTime } : undefined;
  arFilters: formState.arFilters.length > 0 ? formState.arFilters : undefined, stickers: formState.stickers.enabled && formState.stickers.selected.length > 0 ? 
          formState.stickers.selected : undefined, question: formState.question || undefined;
      };

      onCreateStory(storyData);
      resetForm();
      toast.success('Story created successfully!');
    } catch (error) {
      console.error('Error creating story:', error);
      toast.error('Failed to create story. Please try again.');
    }
  }, [formState, onCreateStory, resetForm]);

  // Type-specific handlers
  const handleTypeChange = useCallback((type: StoryType) => {
    updateFormState({ type });
  }, [updateFormState]);

  const handlePrivacyChange = useCallback((privacy: PrivacyLevel) => {
    updateFormState({ privacy });
  }, [updateFormState]);

  const handleDurationChange = useCallback((duration: DurationOption) => {
    updateFormState({ duration });
  }, [updateFormState]);

  const handleContentChange = useCallback((content: string) => {
    updateFormState({ content });
  }, [updateFormState]);

  const handleCustomHoursChange = useCallback((customHours: number) => {
    updateFormState({ customHours });
  }, [updateFormState]);

  // Memoized privacy options for performance
  const privacyOptions = useMemo(() => [{ value: 'public', label: 'Public', color: 'text-green-500' },
    { value: 'friends', label: 'Friends', color: 'text-blue-500' },
    { value: 'close-friends', label: 'Close Friends', color: 'text-purple-500' }]
  ], []);

  // Return all handlers and state
  return {
    // State
    formState,
    selectedMusicTrack,
    setSelectedMusicTrack,
    
    // Refs
    fileInputRef,
    videoInputRef,
    
    // Core handlers
    updateFormState,
    resetForm,
    handleCreateStory,
    
    // Type-specific handlers
    handleTypeChange,
    handlePrivacyChange,
    handleDurationChange,
    handleContentChange,
    handleCustomHoursChange,
    
    // Grouped handlers
    mediaHandlers,
    textHandlers,
    interactiveHandlers,
    
    // Options
    privacyOptions,
    
    // Validation
  isValid: !validateStoryForm(formState), validationError: validateStoryForm(formState)
  };
};

export default useStoryCreator;
