import React, { 
  memo, 
  useMemo, 
  useCallback, 
  useEffect, 
  useState, 
  useRef,
  Suspense,
  lazy
} from 'react';
import { ErrorBoundary } from 'react-error-boundary';
import { 
  Zap, 
  TrendingUp, 
  Clock, 
  BarChart3, 
  Activity, 
  AlertCircle,
  CheckCircle,
  RefreshCw,
  Monitor,
  Cpu,
  HardDrive,
  Wifi
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

// Lazy load heavy components
const PerformanceChart = lazy(() => import('./PerformanceChart'));
const MemoryAnalyzer = lazy(() => import('./MemoryAnalyzer'));
const NetworkMonitor = lazy(() => import('./NetworkMonitor'));

// Performance metrics interface
interface PerformanceMetrics {
  fcp: number; // First Contentful Paint
  lcp: number; // Largest Contentful Paint
  fid: number; // First Input Delay
  cls: number; // Cumulative Layout Shift
  ttfb: number; // Time to First Byte
  memoryUsage: number, bundleSize: number, renderTime: number, networkLatency: number;
}

// Layout shift entry interface
interface LayoutShiftEntry extends PerformanceEntry {
  value: number, hadRecentInput: boolean;
}

// Performance memory interface
interface PerformanceMemoryInfo {
  usedJSHeapSize: number, totalJSHeapSize: number, jsHeapSizeLimit: number;
}

interface PerformanceWithMemory extends Performance {
  memory: PerformanceMemoryInfo;
}

// Performance thresholds
const PERFORMANCE_THRESHOLDS = {
  fcp: { good: 1800, poor: 3000 },
  lcp: { good: 2500, poor: 4000 },
  fid: { good: 100, poor: 300 },
  cls: { good: 0.1, poor: 0.25 },
  ttfb: { good: 800, poor: 1800 },
  memoryUsage: { good: 50, poor: 80 },
  bundleSize: { good: 250, poor: 500 },
  renderTime: { good: 16, poor: 50 },
  networkLatency: { good: 100, poor: 300 }
};

// Performance optimization suggestions
const OPTIMIZATION_SUGGESTIONS = {
  fcp: [
    'Optimize critical rendering path',
    'Minimize render-blocking resources',
    'Use resource hints (preload, prefetch)',
    'Optimize web fonts loading'
  ],
  lcp: [
    'Optimize largest content element',
    'Use responsive images with proper sizing',
    'Implement lazy loading for below-fold content',
    'Optimize server response times'
  ],
  fid: [
    'Break up long-running JavaScript tasks',
    'Use web workers for heavy computations',
    'Implement code splitting',
    'Optimize third-party scripts'
  ],
  cls: [
    'Set explicit dimensions for images and videos',
    'Reserve space for dynamic content',
    'Avoid inserting content above existing content',
    'Use CSS transforms instead of layout-triggering properties'
  ],
  memoryUsage: [
    'Implement virtual scrolling for large lists',
    'Clean up event listeners and subscriptions',
    'Use React.memo for expensive components',
    'Optimize image sizes and formats'
  ],
  bundleSize: [
    'Implement code splitting',
    'Use dynamic imports for large dependencies',
    'Tree shake unused code',
    'Optimize and compress assets'
  ]
};

// Performance monitor hook
const usePerformanceMonitor = () => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    fcp: 0,
    lcp: 0,
    fid: 0,
    cls: 0,
    ttfb: 0,
    memoryUsage: 0,
    bundleSize: 0,
    renderTime: 0,
    networkLatency: 0
  });
  
  const [isMonitoring, setIsMonitoring] = useState(false);
  const observerRef = useRef<PerformanceObserver | null>(null);
  
  const startMonitoring = useCallback(() => {
    if (!('PerformanceObserver' in window)) {
      toast.error('Performance monitoring not supported');
      return;
    }
    
    setIsMonitoring(true);
    
    // Monitor Core Web Vitals
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      
      entries.forEach((entry) => {
        switch (entry.entryType) {
          case 'paint':
            if (entry.name === 'first-contentful-paint') {
              setMetrics(prev => ({ ...prev, fcp: entry.startTime }));
            }
            break;
          case 'largest-contentful-paint':
            setMetrics(prev => ({ ...prev, lcp: entry.startTime }));
            break;
          case 'first-input': {
            const fidEntry = entry as PerformanceEventTiming;
            setMetrics(prev => ({ 
              ...prev, 
              fid: fidEntry.processingStart - fidEntry.startTime 
            }));
            break;
          }
          case 'layout-shift': {
            const layoutShiftEntry = entry as LayoutShiftEntry;
            if (!layoutShiftEntry.hadRecentInput) {
              setMetrics(prev => ({ 
                ...prev, 
                cls: prev.cls + layoutShiftEntry.value 
              }));
            }
            break;
          }
        }
      });
    });
    
    observer.observe({ entryTypes: ['paint', 'largest-contentful-paint', 'first-input', 'layout-shift'] });
    observerRef.current = observer;
    
    // Monitor memory usage
    if ('memory' in performance) {
      const updateMemoryUsage = () => {
        const memory = (performance as PerformanceWithMemory).memory;
        const usagePercent = (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100;
        setMetrics(prev => ({ ...prev, memoryUsage: usagePercent }));
      };
      
      updateMemoryUsage();
      const memoryInterval = setInterval(updateMemoryUsage, 5000);
      
      return () => clearInterval(memoryInterval);
    }
  }, []);
  
  const stopMonitoring = useCallback(() => {
    setIsMonitoring(false);
    if (observerRef.current) {
      observerRef.current.disconnect();
      observerRef.current = null;
    }
  }, []);
  
  useEffect(() => {
    return () => {
      stopMonitoring();
    };
  }, [stopMonitoring]);
  
  return {
    metrics,
    isMonitoring,
    startMonitoring,
    stopMonitoring
  };
};

// Performance score calculator
const calculatePerformanceScore = (metrics: PerformanceMetrics): number => {
  let score = 100;
  
  // FCP scoring
  if (metrics.fcp > PERFORMANCE_THRESHOLDS.fcp.poor) score -= 20;
  else if (metrics.fcp > PERFORMANCE_THRESHOLDS.fcp.good) score -= 10;
  
  // LCP scoring
  if (metrics.lcp > PERFORMANCE_THRESHOLDS.lcp.poor) score -= 25;
  else if (metrics.lcp > PERFORMANCE_THRESHOLDS.lcp.good) score -= 12;
  
  // FID scoring
  if (metrics.fid > PERFORMANCE_THRESHOLDS.fid.poor) score -= 20;
  else if (metrics.fid > PERFORMANCE_THRESHOLDS.fid.good) score -= 10;
  
  // CLS scoring
  if (metrics.cls > PERFORMANCE_THRESHOLDS.cls.poor) score -= 20;
  else if (metrics.cls > PERFORMANCE_THRESHOLDS.cls.good) score -= 10;
  
  // Memory usage scoring
  if (metrics.memoryUsage > PERFORMANCE_THRESHOLDS.memoryUsage.poor) score -= 15;
  else if (metrics.memoryUsage > PERFORMANCE_THRESHOLDS.memoryUsage.good) score -= 8;
  
  return Math.max(0, score);
};

// Performance metric card component
const MetricCard = memo(({ 
  title, 
  value, 
  threshold, 
  unit = 'ms',
  icon: Icon 
}: {
  title: string, value: number, threshold: { good: number, poor: number };
  unit?: string;
  icon: React.ComponentType<{ className?: string }>;
}) => {
  const status = useMemo(() => {
    if (value <= threshold.good) return 'good';
    if (value <= threshold.poor) return 'needs-improvement';
    return 'poor';
  }, [value, threshold]);
  
  const statusColors = {
    good: 'text-green-600 bg-green-50 border-green-200',
    'needs-improvement': 'text-yellow-600 bg-yellow-50 border-yellow-200',
    poor: 'text-red-600 bg-red-50 border-red-200'
  };
  
  const statusIcons = {
    good: CheckCircle,
    'needs-improvement': AlertCircle,
    poor: AlertCircle
  };
  
  const StatusIcon = statusIcons[status];
  
  return (
    <Card className={cn('transition-all duration-200', statusColors[status])}>
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Icon className="w-5 h-5" />
            <span className="font-medium text-sm">{title}</span>
    </div>
          <StatusIcon className="w-4 h-4" />
    </div>
        <div className="mt-2">
          <div className="text-2xl font-bold">
            {value.toFixed(unit === 'ms' ? 0 : 2)}{unit}
          </div>
          <div className="text-xs opacity-75">
            Good: ≤{threshold.good}{unit} | Poor: &gt;{threshold.poor}{unit}
          </div>
    </div>
      </CardContent>
    </Card>
  );
});

MetricCard.displayName = 'MetricCard';

// Optimization suggestions component
const OptimizationSuggestions = memo(({ metrics }: { metrics: PerformanceMetrics }) => {
  const suggestions = useMemo(() => {
    const issues: Array<{ metric: string, suggestions: string[] }> = [];
    
    if (metrics.fcp > PERFORMANCE_THRESHOLDS.fcp.good) {
      issues.push({ metric: 'First Contentful Paint', suggestions: OPTIMIZATION_SUGGESTIONS.fcp });
    }
    
    if (metrics.lcp > PERFORMANCE_THRESHOLDS.lcp.good) {
      issues.push({ metric: 'Largest Contentful Paint', suggestions: OPTIMIZATION_SUGGESTIONS.lcp });
    }
    
    if (metrics.fid > PERFORMANCE_THRESHOLDS.fid.good) {
      issues.push({ metric: 'First Input Delay', suggestions: OPTIMIZATION_SUGGESTIONS.fid });
    }
    
    if (metrics.cls > PERFORMANCE_THRESHOLDS.cls.good) {
      issues.push({ metric: 'Cumulative Layout Shift', suggestions: OPTIMIZATION_SUGGESTIONS.cls });
    }
    
    if (metrics.memoryUsage > PERFORMANCE_THRESHOLDS.memoryUsage.good) {
      issues.push({ metric: 'Memory Usage', suggestions: OPTIMIZATION_SUGGESTIONS.memoryUsage });
    }
    
    if (metrics.bundleSize > PERFORMANCE_THRESHOLDS.bundleSize.good) {
      issues.push({ metric: 'Bundle Size', suggestions: OPTIMIZATION_SUGGESTIONS.bundleSize });
    }
    
    return issues;
  }, [metrics]);
  
  if (suggestions.length === 0) {
    return (
      <Card className="bg-green-50 border-green-200">
        <CardContent className="p-6 text-center">
          <CheckCircle className="w-12 h-12 text-green-600 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-green-800 mb-2">
            Excellent Performance!
          </h3>
          <p className="text-green-700">
            All performance metrics are within optimal ranges.
          </p>
    </CardContent>
      </Card>
    );
  }
  
  return (
    <div className="space-y-4">
      {suggestions.map((issue, index) => (
        <Card key={index} className="border-orange-200 bg-orange-50">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg text-orange-800 flex items-center gap-2">
              <AlertCircle className="w-5 h-5" />
              {issue.metric} Optimization
            </CardTitle>
    </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {issue.suggestions.map((suggestion, suggestionIndex) => (
                <li key={suggestionIndex} className="flex items-start gap-2 text-orange-700">
                  <div className="w-1.5 h-1.5 bg-orange-500 rounded-full mt-2 flex-shrink-0" />
                  <span className="text-sm">{suggestion}</span>
    </li>
              ))}
            </ul>
    </CardContent>
        </Card>
      ))}
    </div>
  );
});

OptimizationSuggestions.displayName = 'OptimizationSuggestions';

// Main component
const AdvancedPerformanceOptimizer = memo(() => {
  const { metrics, isMonitoring, startMonitoring, stopMonitoring } = usePerformanceMonitor();
  const [activeTab, setActiveTab] = useState('overview');
  
  const performanceScore = useMemo(() => calculatePerformanceScore(metrics), [metrics]);
  
  const handleToggleMonitoring = useCallback(() => {
    if (isMonitoring) {
      stopMonitoring();
      toast.success('Performance monitoring stopped');
    } else {
      startMonitoring();
      toast.success('Performance monitoring started');
    }
  }, [isMonitoring, startMonitoring, stopMonitoring]);
  
  return (
    <div className="w-full space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Performance Optimizer
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Monitor and optimize your application's performance
          </p>
    </div>
        <div className="flex items-center gap-3">
          <Badge 
            variant={isMonitoring ? "default" : "secondary"} className="flex items-center gap-1"
          >
            <Activity className="w-3 h-3" />
            {isMonitoring ? 'Monitoring' : 'Stopped'}
          </Badge>
          
          <Button
            onClick={handleToggleMonitoring} variant={isMonitoring ? "outline" : "default"}, className="flex items-center gap-2"
          >
            {isMonitoring ? (
              <>
                <RefreshCw className="w-4 h-4" />
                Stop Monitoring
              </>
            ) : (
              <>
                <Zap className="w-4 h-4" />
                Start Monitoring
              </>
            )}
          </Button>
    </div>
      </div>
      
      {/* Performance Score */}
      <Card className="bg-gradient-to-r from-blue-500 to-purple-600 text-white">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold mb-2">Performance Score</h2>
              <p className="text-blue-100">
                Overall application performance rating
              </p>
    </div>
            <div className="text-right">
              <div className="text-4xl font-bold">{performanceScore}</div>
              <div className="text-blue-100">/ 100</div>
    </div>
          </div>
          <div className="mt-4">
            <Progress value={performanceScore} className="bg-blue-400" />
    </div>
        </CardContent>
    </Card>
      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="metrics">Metrics</TabsTrigger>
          <TabsTrigger value="suggestions">Suggestions</TabsTrigger>
          <TabsTrigger value="tools">Tools</TabsTrigger>
    </TabsList>
        <TabsContent value="overview" className="space-y-6">
          {/* Core Web Vitals */}
          <div>
            <h3 className="text-xl font-semibold mb-4">Core Web Vitals</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <MetricCard
                title="First Contentful Paint"
                value={metrics.fcp} threshold={PERFORMANCE_THRESHOLDS.fcp}, icon={Clock}
              />
              <MetricCard
                title="Largest Contentful Paint"
                value={metrics.lcp} threshold={PERFORMANCE_THRESHOLDS.lcp}, icon={Monitor}
              />
              <MetricCard
                title="First Input Delay"
                value={metrics.fid} threshold={PERFORMANCE_THRESHOLDS.fid}, icon={Zap}
              />
              <MetricCard
                title="Cumulative Layout Shift"
                value={metrics.cls} threshold={PERFORMANCE_THRESHOLDS.cls}, unit=""
                icon={BarChart3}
              />
    </div>
          </div>
          
          {/* Additional Metrics */}
          <div>
            <h3 className="text-xl font-semibold mb-4">Additional Metrics</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <MetricCard
                title="Memory Usage"
                value={metrics.memoryUsage} threshold={PERFORMANCE_THRESHOLDS.memoryUsage}, unit="%"
                icon={Cpu}
              />
              <MetricCard
                title="Bundle Size"
                value={metrics.bundleSize} threshold={PERFORMANCE_THRESHOLDS.bundleSize}, unit="KB"
                icon={HardDrive}
              />
              <MetricCard
                title="Network Latency"
                value={metrics.networkLatency} threshold={PERFORMANCE_THRESHOLDS.networkLatency}, icon={Wifi}
              />
              <MetricCard
                title="Render Time"
                value={metrics.renderTime} threshold={PERFORMANCE_THRESHOLDS.renderTime}, icon={TrendingUp}
              />
    </div>
          </div>
    </TabsContent>
        <TabsContent value="metrics" className="space-y-6">
          <Suspense fallback={<div className="h-64 bg-gray-100 dark:bg-gray-800 rounded-lg animate-pulse" />}>
            <ErrorBoundary fallback={
              <div className="h-64 flex items-center justify-center text-muted-foreground">
                <div className="text-center">
                  <AlertCircle className="w-8 h-8 mx-auto mb-2" />
                  <p>Unable to load performance chart</p>
    </div>
              </div>
            }>
              <PerformanceChart
                data={Object.entries(metrics).map(([key, value]) => ({ name: key, value }))}, title="Performance Metrics"
              />
    </ErrorBoundary>
          </Suspense>
    </TabsContent>
        <TabsContent value="suggestions" className="space-y-6">
          <OptimizationSuggestions metrics={metrics} />
    </TabsContent>
        <TabsContent value="tools" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Suspense fallback={<div className="h-48 bg-gray-100 dark:bg-gray-800 rounded-lg animate-pulse" />}>
              <ErrorBoundary fallback={
                <div className="h-48 flex items-center justify-center text-muted-foreground">
                  <div className="text-center">
                    <AlertCircle className="w-6 h-6 mx-auto mb-2" />
                    <p>Unable to load memory analyzer</p>
    </div>
                </div>
              }>
                <MemoryAnalyzer memoryUsage={metrics.memoryUsage} />
    </ErrorBoundary>
            </Suspense>

            <Suspense fallback={<div className="h-48 bg-gray-100 dark:bg-gray-800 rounded-lg animate-pulse" />}>
              <ErrorBoundary fallback={
                <div className="h-48 flex items-center justify-center text-muted-foreground">
                  <div className="text-center">
                    <AlertCircle className="w-6 h-6 mx-auto mb-2" />
                    <p>Unable to load network monitor</p>
    </div>
                </div>
              }>
                <NetworkMonitor
                  networkLatency={metrics.networkLatency} connectionType="4g"
                />
    </ErrorBoundary>
            </Suspense>
    </div>
        </TabsContent>
    </Tabs>
    </div>
  );
});

AdvancedPerformanceOptimizer.displayName = 'AdvancedPerformanceOptimizer';

export default AdvancedPerformanceOptimizer;