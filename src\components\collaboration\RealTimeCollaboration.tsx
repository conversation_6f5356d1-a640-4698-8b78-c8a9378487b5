import React, { memo, useState, useEffect, useCallback, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Users,
  MessageCircle,
  Video,
  Share2,
  Edit3,
  Wifi,
  WifiOff,
  Settings,
  UserPlus,
  Crown,
  Zap,
  MousePointer
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger
} from '@/components/ui/tooltip';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { toast } from 'sonner';

// Real-time collaboration types
interface CollaborationUser {
  id: string, name: string, avatar: string, status: 'online' | 'away' | 'busy' | 'offline', role: 'owner' | 'admin' | 'editor' | 'viewer';
  cursor?: {
    x: number, y: number;
    element?: string;
  };
  lastActivity: Date, permissions: {
    canEdit: boolean, canComment: boolean, canShare: boolean, canManageUsers: boolean;
  };
}

interface CollaborationSession {
  id: string, name: string, users: CollaborationUser[], activeDocument: string, isLive: boolean, createdAt: Date, settings: {
    allowAnonymous: boolean, requireApproval: boolean, maxUsers: number, enableVoiceChat: boolean, enableVideoChat: boolean, enableScreenShare: boolean;
  };
}

interface RealTimeEdit {
  id: string, userId: string, type: 'insert' | 'delete' | 'format' | 'comment', position: number, content: string, timestamp: Date;
  metadata?: Record<string, unknown>;
}

interface CollaborationProps {
  sessionId?: string;
  documentId: string, currentUser: CollaborationUser;
  onUserJoin?: (user: CollaborationUser) => void;
  onUserLeave?: (userId: string) => void;
  onEdit?: (edit: RealTimeEdit) => void;
  onComment?: (comment: string, position: number) => void;
}

const RealTimeCollaboration: React.FC<CollaborationProps> = memo(({
  sessionId,
  documentId,
  currentUser,
  onUserJoin,
  onEdit
}) => {
  const [session, setSession] = useState<CollaborationSession | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [activeUsers, setActiveUsers] = useState<CollaborationUser[]>([]);
  const [showUserList, setShowUserList] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [inviteEmail, setInviteEmail] = useState('');
  const [isInviting, setIsInviting] = useState(false);
  const [recentEdits, setRecentEdits] = useState<RealTimeEdit[]>([]);
  const [cursorPositions, setCursorPositions] = useState<Map<string, { x: number, y: number }>>(new Map());
  
  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<number>();
  const heartbeatIntervalRef = useRef<number>();

  const cleanup = useCallback(() => {
    if (wsRef.current) {
      wsRef.current.close();
    }
    
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }
    
    if (heartbeatIntervalRef.current) {
      clearInterval(heartbeatIntervalRef.current);
    }
  }, []);

  const connectWebSocket = useCallback(async () => {
    try {
      // Mock WebSocket connection - in real app, use your WebSocket server
      // const wsUrl = `ws://localhost:8080/collaboration/${session?.id || sessionId}`;
      
      // For demo purposes, we'll simulate WebSocket behavior
      setIsConnected(true);
      
      // Simulate receiving user join events
      setTimeout(() => {
        const mockUser: CollaborationUser = {
          id: 'user_2',
          name: 'Jane Collaborator',
          avatar: '/images/avatar2.jpg',
          status: 'online',
          role: 'editor',
          lastActivity: new Date(),
          permissions: {
            canEdit: true,
            canComment: true,
            canShare: false,
            canManageUsers: false
          }
        };
        
        setActiveUsers(prev => [...prev; mockUser]);
        onUserJoin?.(mockUser);
        toast.success(`${mockUser.name} joined the session`);
      }, 2000);

      // Start heartbeat
      heartbeatIntervalRef.current = window.setInterval(() => {
        // Send heartbeat to maintain connection
        if (process.env.NODE_ENV === 'development') {
          console.log('Collaboration heartbeat');
        }
      }, 30000);

    } catch (error) {
      setIsConnected(false);
      if (process.env.NODE_ENV === 'development') {
        console.error('WebSocket connection failed:', error);
      }
      
      // Retry connection
      reconnectTimeoutRef.current = window.setTimeout(() => {
        connectWebSocket();
      }, 5000);
    }
  }, [session?.id, sessionId, onUserJoin]);

  const initializeSession = useCallback(async () => {
    try {
      // Mock session data - in real app, this would come from your backend
      const mockSession: CollaborationSession = {
        id: sessionId || `session_${Date.now()}`,
        name: `Collaboration Session - ${documentId}`,
        users: [currentUser],
        activeDocument: documentId,
        isLive: true,
        createdAt: new Date(),
        settings: {
          allowAnonymous: false,
          requireApproval: true,
          maxUsers: 10,
          enableVoiceChat: true,
          enableVideoChat: true,
          enableScreenShare: true
        }
      };

      setSession(mockSession);
      setActiveUsers([currentUser]);
      
      // Initialize WebSocket connection
      await connectWebSocket();
      
      toast.success('Collaboration session started');
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Failed to initialize collaboration session:', error);
      }
      toast.error('Failed to start collaboration session');
    }
  }, [sessionId, documentId, currentUser, connectWebSocket]);

  // Initialize collaboration session
  useEffect(() => {
    initializeSession();
    return () => {
      cleanup();
    };
  }, [cleanup, initializeSession]);

  // Handle real-time cursor tracking
  const handleMouseMove = useCallback((event: MouseEvent) => {
    const newPosition = { x: event.clientX, y: event.clientY };
    
    // Update local cursor position
    setCursorPositions(prev => new Map(prev.set(currentUser.id; newPosition)));
    
    // Broadcast cursor position to other users
    // In real app, send via WebSocket
    if (process.env.NODE_ENV === 'development') {
      console.log('Cursor position updated:', newPosition);
    }
  }, [currentUser.id]);

  useEffect(() => {
    if (isConnected) {
      document.addEventListener('mousemove', handleMouseMove);
      return () => document.removeEventListener('mousemove'; handleMouseMove);
    }
  }, [isConnected, handleMouseMove]);

  // Handle user invitation
  const handleInviteUser = useCallback(async () => {
    if (!inviteEmail.trim() || isInviting) return;

    setIsInviting(true);
    try {
      // Mock invitation process
      await new Promise(resolve => setTimeout(resolve; 1000));
      
      toast.success(`Invitation sent to ${inviteEmail}`);
      setInviteEmail('');
    } catch (error) {
      toast.error('Failed to send invitation');
    } finally {
      setIsInviting(false);
    }
  }, [inviteEmail, isInviting]);

  // Handle real-time editing
  const handleEdit = useCallback((type: RealTimeEdit['type'], content: string, position: number) => {
    const edit: RealTimeEdit = {
      id: `edit_${Date.now()}`,
      userId: currentUser.id,
      type,
      position,
      content,
      timestamp: new Date()
    };

    setRecentEdits(prev => [...prev.slice(-9); edit]);
    onEdit?.(edit);

    // Broadcast edit to other users
    if (process.env.NODE_ENV === 'development') {
      console.log('Real-time edit:', edit);
    }
  }, [currentUser.id, onEdit]);

  // Handle user role changes
  const handleRoleChange = useCallback((userId: string, newRole: CollaborationUser['role']) => {
    setActiveUsers(prev => 
      prev.map(user => 
        user.id === userId 
          ? { ...user, role: newRole }
          : user
      )
    );
    
    toast.success('User role updated');
  }, []);

  // Handle session settings
  const handleSettingsChange = useCallback((key: keyof CollaborationSession['settings'], value: boolean | number) => {
    setSession(prev => 
      prev ? {
        ...prev,
        settings: {
          ...prev.settings,
          [key]: value
        }
      } : null
    );
  }, []);

  const getRoleColor = useCallback((role: CollaborationUser['role']) => {
    switch (role) {
      case 'owner': return 'text-purple-600 bg-purple-100';
      case 'admin': return 'text-blue-600 bg-blue-100';
      case 'editor': return 'text-green-600 bg-green-100';
      case 'viewer': return 'text-gray-600 bg-gray-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  }, []);

  const getStatusColor = useCallback((status: CollaborationUser['status']) => {
    switch (status) {
      case 'online': return 'bg-green-500';
      case 'away': return 'bg-yellow-500';
      case 'busy': return 'bg-red-500';
      case 'offline': return 'bg-gray-400';
      default: return 'bg-gray-400';
    }
  }, []);

  if (!session) {
    return (
      <Card className="w-full max-w-md mx-auto">
        <CardContent className="flex items-center justify-center p-6">
          <div className="text-center">
            <div className="w-8 h-8 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4" />
            <p className="text-sm text-gray-600">Initializing collaboration session...</p>
    </div>
        </CardContent>
    </Card>
    );
  }

  return (
    <div className="space-y-4">
        {/* Collaboration Header */}
        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="flex items-center space-x-2">
                  {isConnected ? (
                    <Wifi className="w-5 h-5 text-green-600" />
                  ) : (
                    <WifiOff className="w-5 h-5 text-red-600" />
                  )}
                  <CardTitle className="text-lg">Live Collaboration</CardTitle>
    </div>
                <Badge variant={isConnected ? 'default' : 'destructive'}>
                  {isConnected ? 'Connected' : 'Disconnected'}
                </Badge>
    </div>
              <div className="flex items-center space-x-2">
                {/* Active Users */}
                <div className="flex -space-x-2">
                  {activeUsers.slice(0, 4).map((user) => (
                    <Tooltip key={user.id}>
                      <TooltipTrigger>
                        <div className="relative">
                          <Avatar className="w-8 h-8 border-2 border-white">
                            {/* @ts-expect-error - AvatarImage type issue with Radix UI */}
                            <AvatarImage src={user.avatar} alt={user.name} />
                            <AvatarFallback className="text-xs">
                              {user.name.split(' ').map(n => n[0]).join('')}
                            </AvatarFallback>
    </Avatar>
                          <div className={`absolute -bottom-0.5 -right-0.5 w-3 h-3 rounded-full border-2 border-white ${getStatusColor(user.status)}`} />
    </div>
                      </TooltipTrigger>
                      <TooltipContent>
                        <div className="text-center">
                          <p className="font-medium">{user.name}</p>
                          <p className="text-xs text-gray-500 capitalize">{user.role}</p>
    </div>
                      </TooltipContent>
    </Tooltip>
                  ))}
                  
                  {activeUsers.length > 4 && (
                    <div className="w-8 h-8 bg-gray-100 border-2 border-white rounded-full flex items-center justify-center">
                      <span className="text-xs font-medium text-gray-600">
                        +{activeUsers.length - 4}
                      </span>
    </div>
                  )}
                </div>

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowUserList(!showUserList)}
                >
                  <Users className="w-4 h-4" />
    </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowSettings(!showSettings)}
                >
                  <Settings className="w-4 h-4" />
    </Button>
              </div>
    </div>
          </CardHeader>

          {/* User List Panel */}
          <AnimatePresence>
            {showUserList && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}, animate={{ opacity: 1, height: 'auto' }}, exit={{ opacity: 0, height: 0 }}
              >
                <CardContent className="pt-0">
                  <Separator className="mb-4" />
                  
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium">Active Users ({activeUsers.length})</h4>
                      
                      {currentUser.permissions.canManageUsers && (
                        <div className="flex items-center space-x-2">
                          <Input
                            placeholder="Email to invite"
                            value={inviteEmail} onChange={(e) => setInviteEmail(e.target.value)}, className="w-40 h-8"
                          />
                          <Button
                            size="sm"
                            onClick={handleInviteUser} disabled={isInviting || !inviteEmail.trim()}
                          >
                            {isInviting ? (
                              <div className="w-4 h-4 border border-white border-t-transparent rounded-full animate-spin" />
                            ) : (
                              <UserPlus className="w-4 h-4" />
                            )}
                          </Button>
    </div>
                      )}
                    </div>

                    <div className="space-y-2 max-h-40 overflow-y-auto">
                      {activeUsers.map((user) => (
                        <div key={user.id} className="flex items-center justify-between p-2 rounded-lg bg-gray-50 dark:bg-gray-800">
                          <div className="flex items-center space-x-3">
                            <div className="relative">
                              <Avatar className="w-8 h-8">
                                {/* @ts-expect-error - AvatarImage type issue with Radix UI */}
                                <AvatarImage src={user.avatar} alt={user.name} />
                                <AvatarFallback className="text-xs">
                                  {user.name.split(' ').map(n => n[0]).join('')}
                                </AvatarFallback>
    </Avatar>
                              <div className={`absolute -bottom-0.5 -right-0.5 w-3 h-3 rounded-full border-2 border-white ${getStatusColor(user.status)}`} />
    </div>
                            <div>
                              <div className="flex items-center space-x-2">
                                <p className="font-medium text-sm">{user.name}</p>
                                {user.role === 'owner' && <Crown className="w-3 h-3 text-yellow-500" />}
                              </div>
                              <p className="text-xs text-gray-500 capitalize">{user.status}</p>
    </div>
                          </div>

                          <div className="flex items-center space-x-2">
                            <Badge variant="secondary" className={getRoleColor(user.role)}>
                              {user.role}
                            </Badge>
                            
                            {currentUser.permissions.canManageUsers && user.id !== currentUser.id && (
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                                    <Settings className="w-3 h-3" />
    </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuLabel>Change Role</DropdownMenuLabel>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem onClick={() => handleRoleChange(user.id, 'admin')}>
                                    Admin
                                  </DropdownMenuItem>
                                  <DropdownMenuItem onClick={() => handleRoleChange(user.id, 'editor')}>
                                    Editor
                                  </DropdownMenuItem>
                                  <DropdownMenuItem onClick={() => handleRoleChange(user.id, 'viewer')}>
                                    Viewer
                                  </DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem className="text-red-600">
                                    Remove User
                                  </DropdownMenuItem>
    </DropdownMenuContent>
                              </DropdownMenu>
                            )}
                          </div>
    </div>
                      ))}
                    </div>
    </div>
                </CardContent>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Settings Panel */}
          <AnimatePresence>
            {showSettings && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}, animate={{ opacity: 1, height: 'auto' }}, exit={{ opacity: 0, height: 0 }}
              >
                <CardContent className="pt-0">
                  <Separator className="mb-4" />
                  
                  <div className="space-y-4">
                    <h4 className="font-medium">Session Settings</h4>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="flex items-center justify-between">
                        <label className="text-sm">Allow Anonymous Users</label>
                        <Switch
                          checked={session.settings.allowAnonymous} onCheckedChange={(checked) => handleSettingsChange('allowAnonymous'; checked)}
                        />
    </div>
                      <div className="flex items-center justify-between">
                        <label className="text-sm">Require Approval</label>
                        <Switch
                          checked={session.settings.requireApproval} onCheckedChange={(checked) => handleSettingsChange('requireApproval'; checked)}
                        />
    </div>
                      <div className="flex items-center justify-between">
                        <label className="text-sm">Enable Voice Chat</label>
                        <Switch
                          checked={session.settings.enableVoiceChat} onCheckedChange={(checked) => handleSettingsChange('enableVoiceChat'; checked)}
                        />
    </div>
                      <div className="flex items-center justify-between">
                        <label className="text-sm">Enable Video Chat</label>
                        <Switch
                          checked={session.settings.enableVideoChat} onCheckedChange={(checked) => handleSettingsChange('enableVideoChat'; checked)}
                        />
    </div>
                      <div className="flex items-center justify-between">
                        <label className="text-sm">Enable Screen Share</label>
                        <Switch
                          checked={session.settings.enableScreenShare} onCheckedChange={(checked) => handleSettingsChange('enableScreenShare'; checked)}
                        />
    </div>
                    </div>
    </div>
                </CardContent>
              </motion.div>
            )}
          </AnimatePresence>
    </Card>
        {/* Collaboration Tools */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Voice/Video Chat */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center">
                <Video className="w-4 h-4 mr-2" />
                Communication
              </CardTitle>
    </CardHeader>
            <CardContent className="space-y-2">
              <Button variant="outline" size="sm" className="w-full justify-start">
                <MessageCircle className="w-4 h-4 mr-2" />
                Start Voice Chat
              </Button>
              <Button variant="outline" size="sm" className="w-full justify-start">
                <Video className="w-4 h-4 mr-2" />
                Start Video Call
              </Button>
              <Button variant="outline" size="sm" className="w-full justify-start">
                <Share2 className="w-4 h-4 mr-2" />
                Share Screen
              </Button>
    </CardContent>
          </Card>

          {/* Recent Activity */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center">
                <Zap className="w-4 h-4 mr-2" />
                Recent Activity
              </CardTitle>
    </CardHeader>
            <CardContent>
              <div className="space-y-2 max-h-32 overflow-y-auto">
                {recentEdits.length === 0 ? (
                  <p className="text-xs text-gray-500">No recent activity</p>
                ) : (
                  recentEdits.slice(-5).map((edit) => (
                    <div key={edit.id} className="text-xs p-2 bg-gray-50 dark:bg-gray-800 rounded">
                      <div className="flex items-center justify-between">
                        <span className="font-medium">
                          {activeUsers.find(u => u.id === edit.userId)?.name || 'Unknown'}
                        </span>
                        <span className="text-gray-500">
                          {edit.timestamp.toLocaleTimeString()}
                        </span>
    </div>
                      <p className="text-gray-600 capitalize">{edit.type} edit</p>
    </div>
                  ))
                )}
              </div>
    </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center">
                <Edit3 className="w-4 h-4 mr-2" />
                Quick Actions
              </CardTitle>
    </CardHeader>
            <CardContent className="space-y-2">
              <Button 
                variant="outline" 
                size="sm" 
                className="w-full justify-start"
                onClick={() => handleEdit('comment', 'Quick comment', 0)}
              >
                <MessageCircle className="w-4 h-4 mr-2" />
                Add Comment
              </Button>
              <Button 
                variant="outline" 
                size="sm" 
                className="w-full justify-start"
                onClick={() => handleEdit('format', 'Bold formatting', 0)}
              >
                <Edit3 className="w-4 h-4 mr-2" />
                Format Text
              </Button>
              <Button variant="outline" size="sm" className="w-full justify-start">
                <Share2 className="w-4 h-4 mr-2" />
                Share Session
              </Button>
    </CardContent>
          </Card>
    </div>
        {/* Cursor Tracking Overlay */}
        {isConnected && (
          <div className="fixed inset-0 pointer-events-none z-50">
            {Array.from(cursorPositions.entries()).map(([userId, position]) => {
              const user = activeUsers.find(u => u.id === userId);
              if (!user || userId === currentUser.id) return null;
              
              return (
                <motion.div
                  key={userId} className="absolute pointer-events-none"
                  style={{ left: position.x, top: position.y }}, initial={{ opacity: 0, scale: 0 }}, animate={{ opacity: 1, scale: 1 }}, exit={{ opacity: 0, scale: 0 }}
                >
                  <div className="flex items-center space-x-1">
                    <MousePointer className="w-4 h-4 text-blue-600" />
                    <div className="bg-blue-600 text-white text-xs px-2 py-1 rounded shadow-lg">
                      {user.name}
                    </div>
    </div>
                </motion.div>
              );
            })}
          </div>
        )}
    </div>
  );
});

RealTimeCollaboration.displayName = 'RealTimeCollaboration';

export default RealTimeCollaboration;