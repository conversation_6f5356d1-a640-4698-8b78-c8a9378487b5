import React, { useState, use<PERSON><PERSON>back, useMemo } from 'react';
import { motion } from 'framer-motion';
import { 
  Search, 
  Clock, 
  Zap, 
  Target, 
  Settings,
  ChevronDown,
  X,
  Hash,
  Users,
  FileText,
  Image,
  TrendingUp
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Collapsible, 
  CollapsibleContent, 
  CollapsibleTrigger 
} from '@/components/ui/collapsible';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui';
import {
  useFuzzySearch,
  usePostSearch,
  useUserSearch,
  useGroupSearch,
  useGlobalSearch
} from '@/hooks/useFuzzySearch';
import { SearchSuggestion } from '@/types/search';

interface SearchResultItem {
  id: string;
  name?: string;
  username?: string;
  profession?: string;
  avatar?: string;
  bio?: string;
  location?: string;
  followers?: number;
  description?: string;
  tags?: string[];
  members?: number;
  author?: {
    name?: string;
    avatar?: string;
  };
  content?: string;
  [key: string]: unknown;
}

interface SearchResult {
  item: SearchResultItem, score: number;
  matches?: unknown[];
}

interface FuzzySearchDemoProps {
  posts?: unknown[];
  className?: string;
}

const FuzzySearchDemo: React.FC<FuzzySearchDemoProps> = ({ 
  posts = [], 
  className = '' 
}) => {
  const [activeTab, setActiveTab] = useState('posts');
  const [searchQuery, setSearchQuery] = useState('');
  const [showAdvancedSettings, setShowAdvancedSettings] = useState(false);
  const [searchSettings, setSearchSettings] = useState({
    threshold: 0.4,
    maxDistance: 100,
    includeMatches: true,
    useExtendedSearch: false,
    enableSuggestions: true,
    ignoreCase: true
  });

  // Generate sample data for demonstration
  const sampleUsers = useMemo(() => [
    {
      id: '1',
      name: 'Sarah Johnson',
      username: 'sarah_dev',
      bio: 'Senior React Developer passionate about performance optimization',
      location: 'San Francisco, CA',
      profession: 'Software Engineer',
      followers: 2500,
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=sarah'
    },
    {
      id: '2',
      name: 'Mike Chen',
      username: 'mikechen_ui',
      bio: 'UI/UX Designer creating beautiful digital experiences',
      location: 'New York, NY',
      profession: 'UX Designer',
      followers: 1800,
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=mike'
    },
    {
      id: '3',
      name: 'Emily Rodriguez',
      username: 'emily_codes',
      bio: 'Full-stack developer | JavaScript enthusiast | Open source contributor',
      location: 'Austin, TX',
      profession: 'Full Stack Developer',
      followers: 3200,
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=emily'
    },
    {
      id: '4',
      name: 'David Kim',
      username: 'davidk_mobile',
      bio: 'Mobile app developer specializing in React Native and Flutter',
      location: 'Seattle, WA',
      profession: 'Mobile Developer',
      followers: 1500,
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=david'
    }
  ], []);

  const sampleGroups = useMemo(() => [
    {
      id: '1',
      name: 'React Developers Hub',
      description: 'A community for React developers to share knowledge, tips, and best practices',
      category: 'Technology',
      members: 15000,
      tags: ['react', 'javascript', 'frontend', 'development'],
      isPublic: true
    },
    {
      id: '2',
      name: 'UI/UX Design Masters',
      description: 'Professional designers sharing latest trends and design methodologies',
      category: 'Design',
      members: 8500,
      tags: ['design', 'ui', 'ux', 'prototyping'],
      isPublic: true
    },
    {
      id: '3',
      name: 'Full Stack Developers',
      description: 'End-to-end development discussions, from frontend to backend and DevOps',
      category: 'Technology',
      members: 12000,
      tags: ['fullstack', 'backend', 'frontend', 'devops'],
      isPublic: false
    }
  ], []);

  const sampleMedia = useMemo(() => [
    {
      id: '1',
      title: 'React Performance Optimization Tutorial',
      description: 'Learn how to optimize React applications for better performance',
      tags: ['react', 'performance', 'tutorial'],
      author: 'Sarah Johnson',
      category: 'Education',
      type: 'video',
      duration: 1200
    },
    {
      id: '2',
      title: 'Modern CSS Grid Layout Examples',
      description: 'Beautiful examples of CSS Grid layouts for modern web design',
      tags: ['css', 'grid', 'layout', 'design'],
      author: 'Mike Chen',
      category: 'Design',
      type: 'image',
      fileSize: 2500
    }
  ], []);

  // Initialize search hooks
  const postSearch = usePostSearch(posts);
  const userSearch = useUserSearch(sampleUsers);
  const groupSearch = useGroupSearch(sampleGroups);
  const mediaSearch = useFuzzySearch(sampleMedia, {
    searchKeys: ['title', 'description', 'tags', 'author', 'category'],
    threshold: searchSettings.threshold,
    maxResults: 20,
    enableSuggestions: searchSettings.enableSuggestions,
    useExtendedSearch: searchSettings.useExtendedSearch
  });

  const globalSearch = useGlobalSearch(posts, sampleUsers, sampleGroups, sampleMedia);

  // Handle search input change
  const handleSearchChange = useCallback((value: string) => {
    setSearchQuery(value);
    
    switch (activeTab) {
      case 'posts':
        postSearch.search(value);
        break;
      case 'users':
        userSearch.search(value);
        break;
      case 'groups':
        groupSearch.search(value);
        break;
      case 'media':
        mediaSearch.search(value);
        break;
      case 'global':
        globalSearch.searchAll(value);
        break;
    }
  }, [activeTab, postSearch, userSearch, groupSearch, mediaSearch, globalSearch]);

  // Clear search
  const handleClear = useCallback(() => {
    setSearchQuery('');
    postSearch.clear();
    userSearch.clear();
    groupSearch.clear();
    mediaSearch.clear();
    globalSearch.clearAll();
  }, [postSearch, userSearch, groupSearch, mediaSearch, globalSearch]);

  // Get current search state
  const getCurrentSearch = () => {
    switch (activeTab) {
      case 'posts': return postSearch;
      case 'users': return userSearch;
      case 'groups': return groupSearch;
      case 'media': return mediaSearch;
      case 'global': return globalSearch.getAggregatedResults();
      default: return postSearch;
    }
  };

  const currentSearch = getCurrentSearch();

  // Format search time
  const formatSearchTime = (time: number) => {
    return time < 1 ? '<1ms' : `${time.toFixed(1)}ms`;
  };

  // Render search suggestions
  const renderSuggestions = (suggestions: SearchSuggestion[]) => {
    if (!suggestions || suggestions.length === 0) return null;

    return (
      <Card className="absolute top-full left-0 right-0 z-50 mt-1 max-h-60 overflow-y-auto">
        <CardContent className="p-2">
          {suggestions.map((suggestion, index) => (
            <motion.div
              key={suggestion.id || index} initial={{ opacity: 0, y: -5 }}, animate={{ opacity: 1, y: 0 }}, transition={{ delay: index * 0.05 }}, className="flex items-center gap-2 p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded cursor-pointer"
              onClick={() => handleSearchChange(suggestion.text)}
            >
              {suggestion.type === 'hashtag' && <Hash className="h-4 w-4 text-blue-500" />}
              {suggestion.type === 'user' && <Users className="h-4 w-4 text-green-500" />}
              {suggestion.type === 'query' && <Search className="h-4 w-4 text-gray-500" />}
              <span className="text-sm">{suggestion.text}</span>
              {suggestion.popularity && (
                <Badge variant="outline" className="text-xs ml-auto">
                  {suggestion.popularity}%
                </Badge>
              )}
            </motion.div>
          ))}
        </CardContent>
    </Card>
    );
  };

  // Render post results  
  const renderPostResults = (results: SearchResult[]) => (
    <div className="grid grid-cols-1 gap-4">
      {results.map((result, index) => (
        <motion.div
          key={result.item.id} initial={{ opacity: 0, y: 20 }}, animate={{ opacity: 1, y: 0 }}, transition={{ delay: index * 0.05 }}
        >
          <Card className="hover:shadow-md transition-shadow">
            <CardContent className="p-4">
              <div className="flex items-start gap-3 mb-3">
                <Avatar className="h-10 w-10">
                  <AvatarImage src={result.item.author?.avatar} />
                  <AvatarFallback>{result.item.author?.name?.slice(0, 2)}</AvatarFallback>
    </Avatar>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="font-medium">{result.item.author?.name}</span>
                    <Badge variant="secondary" className="text-xs">
                      {(result.score * 100).toFixed(0)}%
                    </Badge>
    </div>
                  <p className="text-sm text-gray-600 mb-3">
                    {result.item.content?.slice(0, 200) || ''}...
                  </p>
    </div>
              </div>
    </CardContent>
          </Card>
        </motion.div>
      ))}
    </div>
  );

  // Render user results
  const renderUserResults = (results: SearchResult[]) => (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      {results.map((result, index) => (
        <motion.div
          key={result.item.id} initial={{ opacity: 0, scale: 0.95 }}, animate={{ opacity: 1, scale: 1 }}, transition={{ delay: index * 0.05 }}
        >
          <Card className="hover:shadow-md transition-shadow">
            <CardContent className="p-4">
              <div className="flex items-center gap-3 mb-3">
                <Avatar className="h-12 w-12">
                  <AvatarImage src={result.item.avatar} />
                  <AvatarFallback>{result.item.name?.slice(0, 2) || 'U'}</AvatarFallback>
    </Avatar>
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <h3 className="font-medium">{result.item.name}</h3>
                    <Badge variant="outline" className="text-xs">
                      {(result.score * 100).toFixed(0)}%
                    </Badge>
    </div>
                  <p className="text-sm text-gray-600">@{result.item.username}</p>
                  <p className="text-xs text-gray-500">{result.item.profession}</p>
    </div>
              </div>
              <p className="text-sm text-gray-700 mb-2">{result.item.bio}</p>
              <div className="flex justify-between items-center text-xs text-gray-500">
                <span>{result.item.location}</span>
                <span>{result.item.followers?.toLocaleString() || 0} followers</span>
    </div>
            </CardContent>
    </Card>
        </motion.div>
      ))}
    </div>
  );

  // Render group results
  const renderGroupResults = (results: SearchResult[]) => (
    <div className="space-y-3">
      {results.map((result, index) => (
        <motion.div
          key={result.item.id} initial={{ opacity: 0, x: -20 }}, animate={{ opacity: 1, x: 0 }}, transition={{ delay: index * 0.05 }}
        >
          <Card className="hover:shadow-md transition-shadow">
            <CardContent className="p-4">
              <div className="flex justify-between items-start mb-2">
                <h3 className="font-medium">{result.item.name}</h3>
                <Badge variant="outline" className="text-xs">
                  {(result.score * 100).toFixed(0)}%
                </Badge>
    </div>
              <p className="text-sm text-gray-600 mb-3">{result.item.description}</p>
              <div className="flex justify-between items-center">
                <div className="flex flex-wrap gap-1">
                  {result.item.tags?.slice(0, 3).map((tag: string, i: number) => (
                    <Badge key={i} variant="secondary" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>
                <div className="text-xs text-gray-500">
                  {result.item.members?.toLocaleString() || 0} members
                </div>
    </div>
            </CardContent>
    </Card>
        </motion.div>
      ))}
    </div>
  );

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            Fuzzy Search with Advanced Matching
          </CardTitle>
    </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Search Input */}
            <div className="relative">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search posts, users, groups, or media... Try 'react dev' or 'ui design'"
                  value={searchQuery} onChange={(e) => handleSearchChange(e.target.value)}, className="pl-10 pr-10"
                />
                {searchQuery && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0"
                    onClick={handleClear}
                  >
                    <X className="h-4 w-4" />
    </Button>
                )}
              </div>
              
              {/* Search Suggestions */}
              {activeTab !== 'global' && 'suggestions' in currentSearch && 
               currentSearch.suggestions && currentSearch.suggestions.length > 0 && (
                renderSuggestions(currentSearch.suggestions)
              )}
            </div>

            {/* Search Stats */}
            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-1">
                  <Target className="h-4 w-4" />
                  <span>
                    {typeof currentSearch.totalResults === 'number' 
                      ? currentSearch.totalResults 
                      : 0} results
                  </span>
    </div>
                {'searchTime' in currentSearch && (
                  <div className="flex items-center gap-1">
                    <Clock className="h-4 w-4" />
                    <span>{formatSearchTime(currentSearch.searchTime)}</span>
    </div>
                )}
                {('isSearching' in currentSearch && currentSearch.isSearching) && (
                  <div className="flex items-center gap-1">
                    <Zap className="h-4 w-4 animate-pulse" />
                    <span>Searching...</span>
    </div>
                )}
              </div>
              
              <Collapsible open={showAdvancedSettings} onOpenChange={setShowAdvancedSettings}>
                <CollapsibleTrigger asChild>
                  <Button variant="outline" size="sm">
                    <Settings className="h-4 w-4 mr-2" />
                    Advanced
                    <ChevronDown className="h-4 w-4 ml-2" />
    </Button>
                </CollapsibleTrigger>
    </Collapsible>
            </div>

            {/* Advanced Settings */}
            <Collapsible open={showAdvancedSettings} onOpenChange={setShowAdvancedSettings}>
              <CollapsibleContent>
                <Card className="p-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-3">
                      <div>
                        <Label htmlFor="threshold">Fuzzy Threshold: {searchSettings.threshold}</Label>
                        <Slider
                          id="threshold"
                          min={0.1} max={1}, step={0.1} value={[searchSettings.threshold]}, onValueChange={([value]) => 
                            setSearchSettings(prev => ({ ...prev, threshold: value }))
                          }, className="mt-2"
                        />
    </div>
                      <div>
                        <Label htmlFor="maxDistance">Max Distance: {searchSettings.maxDistance}</Label>
                        <Slider
                          id="maxDistance"
                          min={1} max={200}, step={10} value={[searchSettings.maxDistance]}, onValueChange={([value]) => 
                            setSearchSettings(prev => ({ ...prev, maxDistance: value }))
                          }, className="mt-2"
                        />
    </div>
                    </div>
                    
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <Label htmlFor="includeMatches">Include Matches</Label>
                        <Switch
                          id="includeMatches"
                          checked={searchSettings.includeMatches} onCheckedChange={(checked) => 
                            setSearchSettings(prev => ({ ...prev, includeMatches: checked }))
                          }
                        />
    </div>
                      <div className="flex items-center justify-between">
                        <Label htmlFor="useExtendedSearch">Extended Search</Label>
                        <Switch
                          id="useExtendedSearch"
                          checked={searchSettings.useExtendedSearch} onCheckedChange={(checked) => 
                            setSearchSettings(prev => ({ ...prev, useExtendedSearch: checked }))
                          }
                        />
    </div>
                      <div className="flex items-center justify-between">
                        <Label htmlFor="enableSuggestions">Enable Suggestions</Label>
                        <Switch
                          id="enableSuggestions"
                          checked={searchSettings.enableSuggestions} onCheckedChange={(checked) => 
                            setSearchSettings(prev => ({ ...prev, enableSuggestions: checked }))
                          }
                        />
    </div>
                    </div>
    </div>
                </Card>
    </CollapsibleContent>
            </Collapsible>
    </div>
        </CardContent>
    </Card>
      {/* Search Results */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="posts">
            <FileText className="h-4 w-4 mr-2" />
            Posts
          </TabsTrigger>
          <TabsTrigger value="users">
            <Users className="h-4 w-4 mr-2" />
            Users
          </TabsTrigger>
          <TabsTrigger value="groups">
            <Users className="h-4 w-4 mr-2" />
            Groups
          </TabsTrigger>
          <TabsTrigger value="media">
            <Image className="h-4 w-4 mr-2" />
            Media
          </TabsTrigger>
          <TabsTrigger value="global">
            <TrendingUp className="h-4 w-4 mr-2" />
            Global
          </TabsTrigger>
    </TabsList>
        <TabsContent value="posts" className="mt-6">
          {postSearch.results.length > 0 ? (
            renderPostResults(postSearch.results)
          ) : (
            <Card>
              <CardContent className="p-8 text-center">
                <FileText className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                <p className="text-gray-600">
                  {postSearch.hasSearched ? 'No posts found' : 'Search for posts to see results'}
                </p>
    </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="users" className="mt-6">
          {userSearch.results.length > 0 ? (
            renderUserResults(userSearch.results)
          ) : (
            <Card>
              <CardContent className="p-8 text-center">
                <Users className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                <p className="text-gray-600">
                  {userSearch.hasSearched ? 'No users found' : 'Search for users to see results'}
                </p>
    </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="groups" className="mt-6">
          {groupSearch.results.length > 0 ? (
            renderGroupResults(groupSearch.results)
          ) : (
            <Card>
              <CardContent className="p-8 text-center">
                <Users className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                <p className="text-gray-600">
                  {groupSearch.hasSearched ? 'No groups found' : 'Search for groups to see results'}
                </p>
    </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="media" className="mt-6">
          {mediaSearch.results.length > 0 ? (
            <div className="space-y-3">
              {mediaSearch.results.map((result, index) => (
                <motion.div
                  key={result.item.id} initial={{ opacity: 0, y: 20 }}, animate={{ opacity: 1, y: 0 }}, transition={{ delay: index * 0.05 }}
                >
                  <Card className="hover:shadow-md transition-shadow">
                    <CardContent className="p-4">
                      <div className="flex justify-between items-start mb-2">
                        <h3 className="font-medium">{result.item.title}</h3>
                        <Badge variant="outline" className="text-xs">
                          {(result.score * 100).toFixed(0)}%
                        </Badge>
    </div>
                      <p className="text-sm text-gray-600 mb-3">{result.item.description}</p>
                      <div className="flex justify-between items-center">
                        <div className="flex items-center gap-2">
                          <Badge variant="secondary" className="text-xs">
                            {result.item.type}
                          </Badge>
                          <span className="text-xs text-gray-500">by {result.item.author}</span>
    </div>
                        <div className="flex gap-1">
                          {result.item.tags?.slice(0, 2).map((tag: string, i: number) => (
                            <Badge key={i} variant="outline" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                        </div>
    </div>
                    </CardContent>
    </Card>
                </motion.div>
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="p-8 text-center">
                <Image className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                <p className="text-gray-600">
                  {mediaSearch.hasSearched ? 'No media found' : 'Search for media to see results'}
                </p>
    </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="global" className="mt-6">
          <div className="space-y-6">
            {globalSearch.getAggregatedResults().posts.length > 0 && (
              <div>
                <h3 className="text-lg font-semibold mb-3">Posts</h3>
                {renderPostResults(globalSearch.getAggregatedResults().posts.slice(0, 3))}
              </div>
            )}
            
            {globalSearch.getAggregatedResults().users.length > 0 && (
              <div>
                <h3 className="text-lg font-semibold mb-3">Users</h3>
                {renderUserResults(globalSearch.getAggregatedResults().users.slice(0, 4))}
              </div>
            )}
            
            {globalSearch.getAggregatedResults().groups.length > 0 && (
              <div>
                <h3 className="text-lg font-semibold mb-3">Groups</h3>
                {renderGroupResults(globalSearch.getAggregatedResults().groups.slice(0, 2))}
              </div>
            )}

            {globalSearch.getAggregatedResults().totalResults === 0 && (
              <Card>
                <CardContent className="p-8 text-center">
                  <TrendingUp className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                  <p className="text-gray-600">
                    {globalSearch.getAggregatedResults().hasSearched 
                      ? 'No results found across all categories' 
                      : 'Search to see results from all categories'
                    }
                  </p>
    </CardContent>
              </Card>
            )}
          </div>
    </TabsContent>
      </Tabs>
    </div>
  );
};

export default FuzzySearchDemo;