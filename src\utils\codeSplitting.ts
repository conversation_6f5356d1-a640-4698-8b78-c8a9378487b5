/**
 * Advanced code splitting utilities for optimal bundle management
 */

import React, { ComponentType, lazy } from 'react';

// Track loaded chunks
const loadedChunks = new Set<string>();
const pendingChunks = new Map<string, Promise<unknown>>();

// Preload link elements cache
const preloadLinks = new Map<string, HTMLLinkElement>();

/**
 * Prefetch a chunk without executing it
 */
export function prefetchChunk(chunkName: string, href: string) {
  if (loadedChunks.has(chunkName) || preloadLinks.has(chunkName)) {
    return;
  }

  const link = document.createElement('link');
  link.rel = 'prefetch';
  link.href = href;
  link.as = 'script';
  
  document.head.appendChild(link);
  preloadLinks.set(chunkName, link);
}

/**
 * Preload a chunk (higher priority than prefetch)
 */
export function preloadChunk(chunkName: string, href: string) {
  if (loadedChunks.has(chunkName)) {
    return;
  }

  // Remove prefetch if exists
  const prefetchLink = preloadLinks.get(chunkName);
  if (prefetchLink && prefetchLink.rel === 'prefetch') {
    prefetchLink.remove();
    preloadLinks.delete(chunkName);
  }

  const link = document.createElement('link');
  link.rel = 'preload';
  link.href = href;
  link.as = 'script';
  link.crossOrigin = 'anonymous';
  
  document.head.appendChild(link);
  preloadLinks.set(chunkName, link);
}

/**
 * Dynamic import with chunk tracking
 */
export async function importChunk<T = unknown>(
  chunkName: string,
  importFn: () => Promise<T>
): Promise<T> {
  // Check if already loaded
  if (loadedChunks.has(chunkName)) {
    return importFn();
  }

  // Check if already loading
  const pending = pendingChunks.get(chunkName);
  if (pending) {
    return pending as Promise<T>;
  }

  // Start loading
  const promise = importFn()
    .then((module) => {
      loadedChunks.add(chunkName);
      pendingChunks.delete(chunkName);
      
      // Clean up preload/prefetch links
      const link = preloadLinks.get(chunkName);
      if (link) {
        link.remove();
        preloadLinks.delete(chunkName);
      }
      
      return module;
    })
    .catch((error) => {
      pendingChunks.delete(chunkName);
      throw error;
    });

  pendingChunks.set(chunkName, promise);
  return promise;
}

/**
 * Route-based code splitting configuration
 */
export const routeChunkMap: Record<string, {
  chunk: string;
  dependencies?: string[];
  prefetch?: string[];
}> = {
  '/': {
    chunk: 'home',
    prefetch: ['profile', 'messages']
  },
  '/profile': {
    chunk: 'profile',
    dependencies: ['user-utils'],
    prefetch: ['settings']
  },
  '/messages': {
    chunk: 'messages',
    dependencies: ['chat-engine', 'emoji-picker'],
    prefetch: ['video-call']
  },
  '/watch': {
    chunk: 'watch',
    dependencies: ['video-player'],
    prefetch: ['video-recommendations']
  },
  '/marketplace': {
    chunk: 'marketplace',
    dependencies: ['payment-utils'],
    prefetch: ['product-details']
  },
  '/groups': {
    chunk: 'groups',
    prefetch: ['group-admin']
  }
};

/**
 * Intelligent chunk prefetching based on user behavior
 */
export class ChunkPrefetcher {
  private userRouteHistory: string[] = [];
  private routePredictions = new Map<string, Map<string, number>>();
  
  recordRouteTransition(from: string, to: string) {
    this.userRouteHistory.push(to);
    
    // Update predictions
    if (!this.routePredictions.has(from)) {
      this.routePredictions.set(from, new Map());
    }
    
    const predictions = this.routePredictions.get(from)!;
    predictions.set(to, (predictions.get(to) || 0) + 1);
    
    // Limit history size
    if (this.userRouteHistory.length > 50) {
      this.userRouteHistory.shift();
    }
  }
  
  getPredictedRoutes(currentRoute: string, threshold = 0.3): string[] {
    const predictions = this.routePredictions.get(currentRoute);
    if (!predictions) return [];
    
    const total = Array.from(predictions.values()).reduce((sum, count) => sum + count, 0);
    
    return Array.from(predictions.entries())
      .filter(([_, count]) => count / total >= threshold)
      .sort((a, b) => b[1] - a[1])
      .map(([route]) => route);
  }
  
  prefetchPredictedChunks(currentRoute: string) {
    const predicted = this.getPredictedRoutes(currentRoute);
    
    predicted.forEach((route) => {
      const config = routeChunkMap[route];
      if (config) {
        // Prefetch route chunk
        prefetchChunk(config.chunk, `/assets/${config.chunk}.js`);
        
        // Prefetch dependencies
        config?.dependencies?.forEach((dep) => {
          prefetchChunk(dep, `/assets/${dep}.js`);
        });
      }
    });
  }
}

export const _chunkPrefetcher = new ChunkPrefetcher();

/**
 * Component-level code splitting with advanced options
 */
export function splitComponent<T extends ComponentType<unknown>>(
  importFn: () => Promise<{ default: T }>,
  options: {
    chunkName: string;
    fallback?: React.ComponentType;
    preload?: boolean;
    prefetch?: boolean;
    ssr?: boolean;
  }
): T {
  const { chunkName, preload = false, prefetch = false, ssr = false } = options;

  // Handle SSR
  if (ssr && typeof window === 'undefined') {
    // Return a placeholder component for SSR
    return (() => null) as unknown as T;
  }

  // Preload/prefetch if requested
  if (typeof window !== 'undefined') {
    const chunkUrl = `/assets/${chunkName}.js`;
    
    if (preload) {
      preloadChunk(chunkName, chunkUrl);
    } else if (prefetch) {
      prefetchChunk(chunkName, chunkUrl);
    }
  }

  // Create lazy component with chunk tracking
  return lazy(async () => {
    const module = await importChunk(chunkName, importFn);
    return module;
  }) as unknown as T;
}

/**
 * Bundle analyzer integration
 */
export class BundleAnalyzer {
  private chunkSizes = new Map<string, number>();
  private loadTimes = new Map<string, number>();
  
  recordChunkLoad(chunkName: string, startTime: number, size?: number) {
    const loadTime = performance.now() - startTime;
    this.loadTimes.set(chunkName, loadTime);
    
    if (size) {
      this.chunkSizes.set(chunkName, size);
    }
  }
  
  getMetrics() {
    const metrics = {
      totalChunks: loadedChunks.size,
      totalSize: Array.from(this.chunkSizes.values()).reduce((sum, size) => sum + size, 0),
      averageLoadTime: this.calculateAverageLoadTime(),
      chunkDetails: this.getChunkDetails()
    };
    
    return metrics;
  }
  
  private calculateAverageLoadTime(): number {
    const times = Array.from(this.loadTimes.values());
    if (times.length === 0) return 0;
    return times.reduce((sum, time) => sum + time, 0) / times.length;
  }
  
  private getChunkDetails() {
    return Array.from(loadedChunks).map((chunk) => ({
      name: chunk,
      size: this.chunkSizes.get(chunk) || 0,
      loadTime: this.loadTimes.get(chunk) || 0
    }));
  }
  
  generateReport() {
    const metrics = this.getMetrics();
    console.group('📊 Bundle Analysis Report');
    console.log('Total Chunks Loaded:', metrics.totalChunks);
    console.log('Total Size:', `${(metrics.totalSize / 1024).toFixed(2)} KB`);
    console.log('Average Load Time:', `${metrics.averageLoadTime.toFixed(2)} ms`);
    console.table(metrics.chunkDetails);
    console.groupEnd();
  }
}

export const _bundleAnalyzer = new BundleAnalyzer();

/**
 * Resource hints for better performance
 */
export function addResourceHints() {
  // DNS prefetch for CDNs
  const dnsPrefetchDomains = [
    'https://fonts.googleapis.com',
    'https://fonts.gstatic.com',
    'https://cdn.jsdelivr.net'
  ];
  
  dnsPrefetchDomains.forEach((domain) => {
    const link = document.createElement('link');
    link.rel = 'dns-prefetch';
    link.href = domain;
    document.head.appendChild(link);
  });
  
  // Preconnect to critical origins
  const preconnectOrigins = [
    { href: 'https://fonts.googleapis.com', crossOrigin: 'anonymous' },
    { href: 'https://fonts.gstatic.com', crossOrigin: 'anonymous' }
  ];
  
  preconnectOrigins.forEach(({ href, crossOrigin }) => {
    const link = document.createElement('link');
    link.rel = 'preconnect';
    link.href = href;
    if (crossOrigin) {
      link.crossOrigin = crossOrigin;
    }
    document.head.appendChild(link);
  });
}

/**
 * Module federation utilities for micro-frontends
 */
export async function loadRemoteModule<T = unknown>(
  remoteName: string,
  moduleName: string
): Promise<T> {
  // Access global module federation container with proper typing
  const container = (window as any)[remoteName];
  
  if (!container) {
    throw new Error(`Remote container ${remoteName} not found`);
  }
  
  // Initialize the container
  // Initialize container with webpack share scopes
  await container.init((globalThis as any).__webpack_share_scopes__?.default);
  
  // Get the module factory
  const factory = await container.get(moduleName);
  
  // Execute the factory to get the module
  const Module = factory();
  
  return Module;
}
