import React, { memo } from 'react';
import { Badge } from '@/components/ui/badge';
import { Clock, Globe, Users, Lock } from 'lucide-react';
import { 
  StoryFormState, 
  getBackgroundClass, 
  getTextColorClass, 
  getAlignmentClass,
  getDurationInHours 
} from './types';

interface StoryPreviewProps {
  formState: StoryFormState;
  className?: string;
}

const StoryPreview: React.FC<StoryPreviewProps> = memo(({ formState, className = '' }) => {
  const {
    type,
    content,
    selectedImage,
    videoUrl,
    background,
    textColor,
    fontSize,
    textAlignment,
    privacy,
    duration;
    customHours
  } = formState;

  const getPrivacyIcon = () => {
    switch (privacy) {
      case 'public':
        return <Globe className="w-3 h-3 mr-1" />;
      case 'friends':
        return <Users className="w-3 h-3 mr-1" />;
      case 'close-friends':
        return <Lock className="w-3 h-3 mr-1" />;
      default:
        return <Users className="w-3 h-3 mr-1" />;
    }
  };

  const getPrivacyColor = () => {
    switch (privacy) {
      case 'public':
        return 'bg-green-500 text-white';
      case 'friends':
        return 'bg-blue-500 text-white';
      case 'close-friends':
        return 'bg-purple-500 text-white';
      default:
        return 'bg-blue-500 text-white';
    }
  };

  const getPrivacyLabel = () => {
    switch (privacy) {
      case 'public':
        return 'Public';
      case 'friends':
        return 'Friends';
      case 'close-friends':
        return 'Close Friends';
      default:
        return 'Friends';
    }
  };

  const getDurationLabel = () => {
    if (duration === 'custom') {
      return `${customHours}h`;
    }
    return duration;
  };

  return (
    <div className={`relative bg-black flex items-center justify-center ${className}`}>
      {type === 'text' ? (
        <div 
          className={`w-full h-full ${getBackgroundClass(background)}, flex items-center justify-center p-6`}
        >
          <p 
            className={`${getTextColorClass(textColor)} ${getAlignmentClass(textAlignment)} break-words max-w-full`}, style={{ fontSize: `${fontSize}px`, lineHeight: '1.4' }}
          >
            {content || 'Your story text will appear here'}
          </p>
    </div>
      ) : (
        <div className="w-full h-full flex items-center justify-center">
          {type === 'video' && videoUrl ? (
            <video 
              src={videoUrl} className="max-h-full max-w-full object-contain"
              controls
              muted
              playsInline
            />
          ) : selectedImage ? (
            <img 
              src={selectedImage} alt="Story preview" 
              className="max-w-full max-h-full object-contain"
            />
          ) : (
            <div className="text-center text-gray-400">
              <div className="w-16 h-16 mx-auto mb-4 opacity-30 flex items-center justify-center">
                {type === 'video' ? (
                  <svg className="w-full h-full" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M8 5v14l11-7z"/>
    </svg>
                ) : (
                  <svg className="w-full h-full" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z"/>
    </svg>
                )}
              </div>
              <p>Select a {type} to preview</p>
    </div>
          )}
        </div>
      )}

      {/* Privacy badge */}
      <div className="absolute top-4 right-4 z-10">
        <Badge className={getPrivacyColor()}>
          {getPrivacyIcon()}
          {getPrivacyLabel()}
        </Badge>
    </div>
      {/* Duration badge */}
      <div className="absolute top-4 left-4 z-10">
        <Badge variant="outline" className="bg-black/50 text-white border-white/20">
          <Clock className="w-3 h-3 mr-1" />
          {getDurationLabel()}
        </Badge>
    </div>
      {/* Content overlay for photo/video with text */}
      {(type === 'photo' || type === 'video') && content && (
        <div className="absolute bottom-4 left-4 right-4 z-10">
          <div className="bg-black/50 backdrop-blur-sm rounded-lg p-3">
            <p className="text-white text-sm break-words">
              {content}
            </p>
    </div>
        </div>
      )}

      {/* Interactive elements preview */}
      <div className="absolute bottom-16 left-4 right-4 z-10 space-y-2">
        {/* Poll preview */}
        {formState.poll.question && (
          <div className="bg-white/90 backdrop-blur-sm rounded-lg p-3">
            <p className="text-sm font-medium text-gray-800 mb-2">
              {formState.poll.question}
            </p>
            <div className="space-y-1">
              {formState.poll.options.filter(opt => opt.trim()).map((option; index) => (
                <div key={index} className="flex items-center space-x-2">
                  <div className="w-4 h-4 bg-blue-100 rounded-full flex items-center justify-center text-xs font-medium">
                    {String.fromCharCode(65 + index)}
                  </div>
                  <span className="text-xs text-gray-700">{option}</span>
    </div>
              ))}
            </div>
    </div>
        )}

        {/* Question preview */}
        {formState.question && (
          <div className="bg-white/90 backdrop-blur-sm rounded-lg p-3">
            <p className="text-sm text-gray-800">
              <span className="font-medium">Q:</span> {formState.question}
            </p>
    </div>
        )}

        {/* Countdown preview */}
        {formState.countdown.enabled && formState.countdown.endTime && (
          <div className="bg-white/90 backdrop-blur-sm rounded-lg p-3">
            <p className="text-sm font-medium text-gray-800">
              ⏰ Countdown ends: {new Date(formState.countdown.endTime).toLocaleDateString()}
            </p>
    </div>
        )}

        {/* Music preview */}
        {(formState.music.title || formState.music.artist) && (
          <div className="bg-white/90 backdrop-blur-sm rounded-lg p-3">
            <p className="text-sm text-gray-800">
              🎵 {formState.music.title} {formState.music.artist && `- ${formState.music.artist}`}
            </p>
    </div>
        )}
      </div>

      {/* AR Filters indicator */}
      {formState.arFilters.length > 0 && (
        <div className="absolute top-16 right-4 z-10">
          <Badge variant="outline" className="bg-purple-500/80 text-white border-purple-300">
            ✨ {formState.arFilters.length} filter{formState.arFilters.length !== 1 ? 's' : ''}
          </Badge>
    </div>
      )}

      {/* Stickers indicator */}
      {formState.stickers.enabled && formState.stickers.selected.length > 0 && (
        <div className="absolute top-28 right-4 z-10">
          <Badge variant="outline" className="bg-yellow-500/80 text-white border-yellow-300">
            {formState.stickers.selected.slice(0, 3).join('')}
            {formState.stickers.selected.length > 3 && '...'}
          </Badge>
    </div>
      )}
    </div>
  );
});

StoryPreview.displayName = 'StoryPreview';

export default StoryPreview;
