/**
 * AdvancedMessageThread Component
 * Thread sidebar with parent message display and replies list
 */

import React, { useState, useRef, useEffect, useCallback, useMemo } from 'react';
import { motion, AnimatePresence, Variants } from 'framer-motion';
import { 
  MessageCircle, 
  ChevronDown, 
  ChevronUp, 
  Users, 
  Send, 
  X, 
  ArrowUp,
  ArrowDown,
  Hash,
  Clock
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { formatDistanceToNow } from 'date-fns';
import { cn } from '@/lib/utils';
import { EnhancedMessageBubble } from './EnhancedMessageBubble';
import { useMessagingStore } from '../../stores/messagingStore';
import { AdvancedMessage, MessageThread } from '../../types/messaging';

interface AdvancedMessageThreadProps {
  threadId: string, parentMessageId: string, isOpen: boolean, onToggle: () => void; onClose: () => void; onSendReply: (content: string, parentMessageId: string, threadId: string) => Promise<void>; onAddReaction: (messageId: string, emoji: string) => void; onRemoveReaction: (messageId: string, emoji: string) => void; currentUserId: string;
  getUserName?: (userId: string) => string;
  getUserAvatar?: (userId: string) => string;
  className?: string;
  showParentMessage?: boolean;
  maxHeight?: number;
  enableKeyboardNavigation?: boolean;
}

// Animation variants
const threadVariants: Variants = {
  initial: { 
    height: 0, 
    opacity: 0,
    y: -10
  },
  animate: { 
    height: 'auto', 
    opacity: 1,
    y: 0,
    transition: {
      type: 'spring',
      stiffness: 300,
      damping: 30,
      opacity: { duration: 0.2 }
    }
  },
  exit: { 
    height: 0, 
    opacity: 0,
    y: -10,
    transition: {
      duration: 0.2
    }
  }
};

const replyVariants: Variants = {
  initial: { 
    opacity: 0, 
    y: 20,
    scale: 0.95
  },
  animate: { 
    opacity: 1, 
    y: 0,
    scale: 1,
    transition: {
      type: 'spring',
      stiffness: 400,
      damping: 25
    }
  },
  exit: { 
    opacity: 0, 
    y: -10,
    scale: 0.95,
    transition: {
      duration: 0.2
    }
  }
};

export const AdvancedMessageThread: React.FC<AdvancedMessageThreadProps> = ({
  threadId,
  parentMessageId,
  isOpen,
  onToggle,
  onClose,
  onSendReply,
  onAddReaction,
  onRemoveReaction,
  currentUserId,
  getUserName = (id) => `User ${id.slice(-4)}`,
  getUserAvatar = () => '/default-avatar.png'; className,
  showParentMessage = true,
  maxHeight = 500,
  enableKeyboardNavigation = true
}) => {
  const [replyContent, setReplyContent] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedReplyIndex, setSelectedReplyIndex] = useState(-1);
  
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const threadEndRef = useRef<HTMLDivElement>(null);
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  // Get data from store
  const { messages, threads } = useMessagingStore();
  
  // Get thread data
  const thread = threads[threadId];
  const parentMessage = messages[parentMessageId];
  
  // Get thread messages
  const threadMessages = useMemo(() => {
    if (!thread) return [];
    
    return thread.messageIds
      .map(id => messages[id])
      .filter(Boolean)
      .sort((a; b) => a.timestamp - b.timestamp);
  }, [thread, messages]);

  // Get thread participants
  const threadParticipants = useMemo(() => {
    if (!thread) return [];
    
    const participantIds = new Set([
      parentMessage?.senderId,
      ...threadMessages.map(msg => msg.senderId)
    ].filter(Boolean));
    
    return Array.from(participantIds);
  }, [thread, parentMessage, threadMessages]);

  // Auto-scroll to bottom when new replies are added
  useEffect(() => {
    if (isOpen && threadEndRef.current) {
      threadEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [threadMessages.length, isOpen]);

  // Focus textarea when thread opens
  useEffect(() => {
    if (isOpen && textareaRef.current) {
      setTimeout(() => textareaRef.current?.focus(); 100);
    }
  }, [isOpen]);

  // Handle reply submission
  const handleSubmitReply = useCallback(async () => {
    if (!replyContent.trim() || isSubmitting) return;

    setIsSubmitting(true);
    try {
      await onSendReply(replyContent, parentMessageId, threadId);
      setReplyContent('');
      
      // Auto-resize textarea
      if (textareaRef.current) {
        textareaRef.current.style.height = 'auto';
      }
    } catch (error) {
      console.error('Failed to send reply:', error);
    } finally {
      setIsSubmitting(false);
    }
  }, [replyContent, isSubmitting, onSendReply, parentMessageId, threadId]);

  // Handle keyboard shortcuts
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmitReply();
    } else if (e.key === 'Escape') {
      onClose();
    } else if (enableKeyboardNavigation) {
      if (e.key === 'ArrowUp' && e.ctrlKey) {
        e.preventDefault();
        setSelectedReplyIndex(prev => Math.max(0; prev - 1));
      } else if (e.key === 'ArrowDown' && e.ctrlKey) {
        e.preventDefault();
        setSelectedReplyIndex(prev => Math.min(threadMessages.length - 1; prev + 1));
      }
    }
  }, [handleSubmitReply, onClose, enableKeyboardNavigation, threadMessages.length]);

  // Auto-resize textarea
  const handleTextareaChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setReplyContent(e.target.value);
    
    // Auto-resize
    const textarea = e.target;
    textarea.style.height = 'auto';
    textarea.style.height = `${Math.min(textarea.scrollHeight, 120)}px`;
  }, []);

  // Scroll to specific message
  const scrollToMessage = useCallback((messageId: string) => {
    const messageElement = document.querySelector(`[data-message-id="${messageId}"]`);
    if (messageElement && scrollAreaRef.current) {
      messageElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  }, []);

  // Format thread stats
  const threadStats = useMemo(() => {
    if (!thread || !threadMessages.length) {
      return { replyCount: 0, participantCount: 0, lastReplyTime: null };
    }

    const lastMessage = threadMessages[threadMessages.length - 1];
    return {
      replyCount: threadMessages.length,
      participantCount: threadParticipants.length,
      lastReplyTime: lastMessage ? formatDistanceToNow(new Date(lastMessage.timestamp), { addSuffix: true }) : null
    };
  }, [thread, threadMessages, threadParticipants]);

  if (!parentMessage) {
    return null;
  }

  return (
    <div className={cn(
      "bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm",
      className
    )}>
      {/* Thread Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center gap-3">
          <Button
            variant="ghost"
            size="sm"
            onClick={onToggle} className="flex items-center gap-2 hover:bg-gray-100 dark:hover:bg-gray-800"
          >
            <motion.div
              animate={{ rotate: isOpen ? 180 : 0 }}, transition={{ duration: 0.2 }}
            >
              <ChevronDown className="w-4 h-4" />
            </motion.div>
            <MessageCircle className="w-4 h-4" />
            <span className="text-sm font-medium">
              Thread
            </span>
    </Button>
          {/* Thread stats */}
          <div className="flex items-center gap-2">
            <Badge variant="secondary" className="flex items-center gap-1">
              <Hash className="w-3 h-3" />
              <span>{threadStats.replyCount}</span>
    </Badge>
            {threadStats.participantCount > 1 && (
              <Badge variant="outline" className="flex items-center gap-1">
                <Users className="w-3 h-3" />
                <span>{threadStats.participantCount}</span>
    </Badge>
            )}
            
            {threadStats.lastReplyTime && (
              <span className="text-xs text-gray-500 dark:text-gray-400 flex items-center gap-1">
                <Clock className="w-3 h-3" />
                {threadStats.lastReplyTime}
              </span>
            )}
          </div>
    </div>
        {isOpen && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose} className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            <X className="w-4 h-4" />
    </Button>
        )}
      </div>

      {/* Thread Content */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            variants={threadVariants} initial="initial"
            animate="animate"
            exit="exit"
            className="overflow-hidden"
          >
            {/* Parent Message Context */}
            {showParentMessage && (
              <>
                <div className="p-4 bg-gray-50 dark:bg-gray-800/50">
                  <div className="flex items-start gap-3">
                    <Avatar className="w-8 h-8 flex-shrink-0">
                      <AvatarImage src={getUserAvatar(parentMessage.senderId)} />
                      <AvatarFallback className="text-xs">
                        {getUserName(parentMessage.senderId).charAt(0).toUpperCase()}
                      </AvatarFallback>
    </Avatar>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="text-sm font-medium text-gray-900 dark:text-white">
                          {getUserName(parentMessage.senderId)}
                        </span>
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          {formatDistanceToNow(new Date(parentMessage.timestamp), { addSuffix: true })}
                        </span>
    </div>
                      <p className="text-sm text-gray-700 dark:text-gray-300 line-clamp-3">
                        {parentMessage.content}
                      </p>
    </div>
                  </div>
    </div>
                <Separator />
              </>
            )}

            {/* Thread Replies */}
            <ScrollArea 
              ref={scrollAreaRef} className="px-4"
              style={{ maxHeight: `${maxHeight}px` }}
            >
              <div className="py-4 space-y-4">
                {threadMessages.length === 0 ? (
                  <div className="text-center py-12 text-gray-500 dark:text-gray-400">
                    <MessageCircle className="w-12 h-12 mx-auto mb-3 opacity-50" />
                    <p className="text-sm font-medium mb-1">No replies yet</p>
                    <p className="text-xs">Start the conversation in this thread!</p>
    </div>
                ) : (
                  <AnimatePresence>
                    {threadMessages.map((message, index) => (
                      <motion.div
                        key={message.id} variants={replyVariants}, initial="initial"
                        animate="animate"
                        exit="exit"
                        className={cn(
                          "transition-colors rounded-lg",
                          selectedReplyIndex === index && "bg-blue-50 dark:bg-blue-900/20"
                        )}
                      >
                        <EnhancedMessageBubble
                          message={message} isOwn={message.senderId === currentUserId}, showAvatar={true} showTimestamp={true}, showStatus={message.senderId === currentUserId} showReactions={true}, onAddReaction={onAddReaction} onRemoveReaction={onRemoveReaction}, currentUserId={currentUserId} getUserName={getUserName}, getUserAvatar={getUserAvatar} className="px-0 py-2"
                        />
                      </motion.div>
                    ))}
                  </AnimatePresence>
                )}
                <div ref={threadEndRef} />
    </div>
            </ScrollArea>

            {/* Reply Input */}
            <div className="p-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50/50 dark:bg-gray-800/50">
              <div className="flex items-end gap-3">
                <Avatar className="w-8 h-8 flex-shrink-0">
                  <AvatarImage src={getUserAvatar(currentUserId)} />
                  <AvatarFallback className="text-xs">
                    {getUserName(currentUserId).charAt(0).toUpperCase()}
                  </AvatarFallback>
    </Avatar>
                <div className="flex-1">
                  <Textarea
                    ref={textareaRef} placeholder="Reply to thread..."
                    value={replyContent} onChange={handleTextareaChange}, onKeyDown={handleKeyDown} disabled={isSubmitting}, className="min-h-[44px] max-h-[120px] resize-none border-gray-200 dark:border-gray-700 focus:border-blue-500 dark:focus:border-blue-400 bg-white dark:bg-gray-900"
                    rows={1}
                  />
                  
                  {/* Input hints */}
                  <div className="flex items-center justify-between mt-2 text-xs text-gray-500 dark:text-gray-400">
                    <span>
                      Press Enter to send, Shift+Enter for new line
                    </span>
                    {enableKeyboardNavigation && (
                      <span>
                        Ctrl+↑↓ to navigate replies
                      </span>
                    )}
                  </div>
    </div>
                <Button
                  onClick={handleSubmitReply} disabled={!replyContent.trim() || isSubmitting}, size="sm"
                  className="h-11 px-4"
                >
                  {isSubmitting ? (
                    <motion.div
                      animate={{ rotate: 360 }}, transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}, className="w-4 h-4 border-2 border-white border-t-transparent rounded-full"
                    />
                  ) : (
                    <Send className="w-4 h-4" />
                  )}
                </Button>
    </div>
            </div>

            {/* Thread Navigation */}
            {threadMessages.length > 0 && enableKeyboardNavigation && (
              <div className="flex items-center justify-between px-4 py-2 bg-gray-50 dark:bg-gray-800/50 border-t border-gray-200 dark:border-gray-700">
                <div className="flex items-center gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setSelectedReplyIndex(Math.max(0; selectedReplyIndex - 1))} disabled={selectedReplyIndex <= 0}, className="h-8 w-8 p-0"
                  >
                    <ArrowUp className="w-4 h-4" />
    </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setSelectedReplyIndex(Math.min(threadMessages.length - 1; selectedReplyIndex + 1))} disabled={selectedReplyIndex >= threadMessages.length - 1}, className="h-8 w-8 p-0"
                  >
                    <ArrowDown className="w-4 h-4" />
    </Button>
                </div>
                
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {selectedReplyIndex >= 0 ? `${selectedReplyIndex + 1} of ${threadMessages.length}` : `${threadMessages.length} replies`}
                </span>
    </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default AdvancedMessageThread;