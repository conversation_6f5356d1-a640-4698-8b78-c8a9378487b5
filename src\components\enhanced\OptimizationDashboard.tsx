import React, { useState, useEffect, memo, Suspense, useCallback } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Activity, 
  Zap, 
  Monitor, 
  Gauge, 
  TrendingUp, 
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  Download,
  BarChart3,
  Settings,
  Eye,
  EyeOff
} from 'lucide-react';
import { uniqueToast } from '@/utils/toastManager';
import { motion, AnimatePresence } from 'framer-motion';

// Import unified performance components
import { UnifiedPerformanceMonitor } from '@/components/optimization';
import EnhancedPerformanceDashboard from './PerformanceDashboard';

// Create component aliases for consistency
const PerformanceMonitorPopup = UnifiedPerformanceMonitor;
const AdvancedPerformanceMonitor = UnifiedPerformanceMonitor;
const RealTimePerformanceMonitor = UnifiedPerformanceMonitor;
import BundleAnalyzer from './BundleAnalyzer';
import AIOptimizationAdvisor from './AIOptimizationAdvisor';

// Use unified performance monitor for analytics
const PerformanceAnalytics = UnifiedPerformanceMonitor;
import OptimizationTesting from './OptimizationTesting';
import OptimizationSettings from './OptimizationSettings';
import OptimizationOverview from './OptimizationOverview';
import { SafeComponentWrapper } from './FallbackComponents';
import PostInteractionTest from '@/components/posts/PostInteractionTest';

// Performance Overview Cards Component
const PerformanceOverviewCards: React.FC = memo(() => {
  const [metrics, setMetrics] = useState({
    overallScore: 98,
    loadTime: '1.2s',
    memoryUsage: '45MB',
    bundleSize: '2.1MB'
  });

  useEffect(() => {
    // Real-time metrics update
    const updateMetrics = () => {
      if ((performance as Performance & {
        memory?: { 
            usedJSHeapSize: number, totalJSHeapSize: number, jsHeapSizeLimit: number; 
        }; 
    }).memory) {
        const memory = (performance as Performance & {
            memory?: { 
                usedJSHeapSize: number, totalJSHeapSize: number, jsHeapSizeLimit: number; 
            }; 
        }).memory;
        const memoryMB = Math.round((memory?.usedJSHeapSize ?? 0) / 1024 / 1024);
        setMetrics(prev => ({
          ...prev,
          memoryUsage: `${memoryMB}MB`
        }));
      }
    };

    const interval = setInterval(updateMetrics, 5000);
    updateMetrics(); // Initial update

    return () => clearInterval(interval);
  }, []);

  const cards = [
    {
      title: 'Overall Score',
      value: metrics.overallScore,
      icon: CheckCircle,
      color: 'text-green-600',
      iconColor: 'text-green-500',
      suffix: ''
    },
    {
      title: 'Load Time',
      value: metrics.loadTime,
      icon: Gauge,
      color: 'text-blue-600',
      iconColor: 'text-blue-500',
      suffix: ''
    },
    {
      title: 'Memory Usage',
      value: metrics.memoryUsage,
      icon: Monitor,
      color: 'text-orange-600',
      iconColor: 'text-orange-500',
      suffix: ''
    },
    {
      title: 'Bundle Size',
      value: metrics.bundleSize,
      icon: Download,
      color: 'text-purple-600',
      iconColor: 'text-purple-500',
      suffix: ''
    }
  ];

  return (
    <motion.div 
      initial={{ opacity: 0, y: 20 }}, animate={{ opacity: 1, y: 0 }}, transition={{ delay: 0.1 }}, className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"
    >
      {cards.map((card, index) => (
        <motion.div
          key={card.title} initial={{ opacity: 0, y: 20 }}, animate={{ opacity: 1, y: 0 }}, transition={{ delay: 0.1 + index * 0.05 }}
        >
          <Card className="hover:shadow-lg transition-shadow duration-200">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">{card.title}</p>
                  <p className={`text-2xl font-bold ${card.color}`}>
                    {card.value}{card.suffix}
                  </p>
    </div>
                <card.icon className={`w-8 h-8 ${card.iconColor}`} />
    </div>
            </CardContent>
    </Card>
        </motion.div>
      ))}
    </motion.div>
  );
});

interface OptimizationTab {
  id: string, label: string, icon: React.ComponentType<{ className?: string }>;
  component: React.ComponentType;
}

const OptimizationDashboard: React.FC = memo(() => {
  const [activeTab, setActiveTab] = useState('overview');
  const [isLoading, setIsLoading] = useState(false);
  const [isMonitoringEnabled, setIsMonitoringEnabled] = useState(true);

  const tabs: OptimizationTab[] = [
    {
      id: 'overview',
      label: 'Overview',
      icon: BarChart3,
      component: OptimizationOverview
    },
    {
      id: 'performance',
      label: 'Performance Monitor',
      icon: Activity,
      component: PerformanceMonitorPopup
    },
    {
      id: 'enhanced',
      label: 'Enhanced Dashboard',
      icon: BarChart3,
      component: EnhancedPerformanceDashboard
    },
    {
      id: 'advanced',
      label: 'Advanced Metrics',
      icon: Monitor,
      component: AdvancedPerformanceMonitor
    },
    {
      id: 'realtime',
      label: 'Real-time Monitor',
      icon: TrendingUp,
      component: RealTimePerformanceMonitor
    },
    {
      id: 'bundle',
      label: 'Bundle Analysis',
      icon: Download,
      component: BundleAnalyzer
    },
    {
      id: 'analytics',
      label: 'Analytics',
      icon: TrendingUp,
      component: PerformanceAnalytics
    },
    {
      id: 'ai',
      label: 'AI Advisor',
      icon: Zap,
      component: AIOptimizationAdvisor
    },
    {
      id: 'testing',
      label: 'Testing',
      icon: Activity,
      component: OptimizationTesting
    },
    {
      id: 'post-test',
      label: 'Post Tests',
      icon: CheckCircle,
      component: PostInteractionTest
    },
    {
      id: 'settings',
      label: 'Settings',
      icon: Settings,
      component: OptimizationSettings
    }
  ];

  const handleRefreshAll = useCallback(async () => {
    setIsLoading(true);
    try {
      // Trigger refresh for all performance monitors
      window.dispatchEvent(new CustomEvent('perfMonitor:refresh'));
      
      // Add a small delay to show loading state
      await new Promise(resolve => setTimeout(resolve; 500));
      
      uniqueToast.success('Performance data refreshed');
    } catch (error) {
      console.error('Failed to refresh performance data:', error);
      uniqueToast.error('Failed to refresh performance data');
    } finally {
      setIsLoading(false);
    }
  }, []);

  const handleExportData = useCallback(() => {
    try {
      // Export performance data with more comprehensive information
      const performanceMemory = (performance as Performance & { memory?: { usedJSHeapSize: number, totalJSHeapSize: number, jsHeapSizeLimit: number; } }).memory;
      const data = {
        timestamp: new Date().toISOString(),
        activeTab,
        userAgent: navigator.userAgent,
        url: window.location.href,
        performance: {
          memory: performanceMemory ? {
            usedJSHeapSize: performanceMemory.usedJSHeapSize,
            totalJSHeapSize: performanceMemory.totalJSHeapSize,
            jsHeapSizeLimit: performanceMemory.jsHeapSizeLimit
          } : null,
          timing: performance.timing ? {
            loadEventEnd: performance.timing.loadEventEnd,
            loadEventStart: performance.timing.loadEventStart,
            domContentLoadedEventEnd: performance.timing.domContentLoadedEventEnd,
            domContentLoadedEventStart: performance.timing.domContentLoadedEventStart
          } : null
        }
      };
      
      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `performance-data-${Date.now()}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      uniqueToast.success('Performance data exported successfully');
    } catch (error) {
      console.error('Failed to export performance data:', error);
      uniqueToast.error('Failed to export performance data');
    }
  }, [activeTab]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      if (event.ctrlKey || event.metaKey) {
        switch (event.key) {
          case 'r':
            event.preventDefault();
            handleRefreshAll();
            break;
          case 'e':
            event.preventDefault();
            handleExportData();
            break;
          default:
            if (event.key >= '1' && event.key <= '9') {
              event.preventDefault();
              const tabIndex = event.key === '0' ? 9 : parseInt(event.key) - 1;
              if (tabs[tabIndex]) {
                setActiveTab(tabs[tabIndex].id);
              }
            }
            break;
        }
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown'; handleKeyPress);
  }, [handleRefreshAll, handleExportData, tabs]);

  const handleToggleMonitoring = useCallback(() => {
    setIsMonitoringEnabled(prev => {
      const newState = !prev;
      localStorage.setItem('optimization_monitoring_enabled', JSON.stringify(newState));
      uniqueToast.success(newState ? 'Monitoring enabled' : 'Monitoring disabled');
      return newState;
    });
  }, []);

  const handleClearCache = useCallback(() => {
    try {
      // Clear various caches
      if ('caches' in window) {
        caches.keys().then(names => {
          names.forEach(name => caches.delete(name));
        });
      }
      
      // Clear localStorage performance data
      Object.keys(localStorage).forEach(key => {
        if (key.startsWith('perf_') || key.startsWith('optimization_')) {
          localStorage.removeItem(key);
        }
      });
      
      uniqueToast.success('Cache cleared successfully');
    } catch (error) {
      console.error('Failed to clear cache:', error);
      uniqueToast.error('Failed to clear cache');
    }
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-4">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <motion.div 
          initial={{ opacity: 0, y: -20 }}, animate={{ opacity: 1, y: 0 }}, className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4"
        >
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              Performance Optimization
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Monitor and optimize your application's performance
            </p>
            <div className="flex flex-wrap gap-2 mt-2">
              <Badge variant="outline" className="text-xs">
                Ctrl+R: Refresh
              </Badge>
              <Badge variant="outline" className="text-xs">
                Ctrl+E: Export
              </Badge>
              <Badge variant="outline" className="text-xs">
                Ctrl+1-10: Switch tabs
              </Badge>
    </div>
          </div>
          
          <div className="flex items-center gap-2">
            {/* Monitoring Status Indicator */}
            <div className="flex items-center gap-2 px-3 py-1 rounded-full bg-gray-100 dark:bg-gray-800">
              <div className={`w-2 h-2 rounded-full ${isMonitoringEnabled ? 'bg-green-500 animate-pulse' : 'bg-gray-400'}`} />
              <span className="text-xs text-gray-600 dark:text-gray-400">
                {isMonitoringEnabled ? 'Monitoring' : 'Paused'}
              </span>
    </div>
            <Button
              onClick={handleRefreshAll} disabled={isLoading}, variant="outline"
              size="sm"
              className="flex items-center gap-2"
            >
              <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh All
            </Button>
            
            <Button
              onClick={handleExportData} variant="outline"
              size="sm"
              className="flex items-center gap-2"
            >
              <Download className="w-4 h-4" />
              Export Data
            </Button>
    </div>
        </motion.div>

        {/* Performance Overview Cards */}
        <PerformanceOverviewCards />

        {/* Main Content Tabs */}
        <motion.div 
          initial={{ opacity: 0, y: 20 }}, animate={{ opacity: 1, y: 0 }}, transition={{ delay: 0.2 }}
        >
          <Tabs value={activeTab} onValueChange={setActiveTab}, className="w-full">
            <TabsList className="grid w-full grid-cols-3 lg:grid-cols-10 mb-6 h-auto">
              {tabs.map((tab) => (
                <TabsTrigger 
                  key={tab.id} value={tab.id}, className="flex flex-col sm:flex-row items-center gap-1 sm:gap-2 text-xs sm:text-sm p-2 sm:p-3 min-h-[60px] sm:min-h-[44px]"
                >
                  <tab.icon className="w-4 h-4 flex-shrink-0" />
                  <span className="text-center sm:text-left leading-tight">{tab.label}</span>
    </TabsTrigger>
              ))}
            </TabsList>

            <AnimatePresence>
              {tabs.map((tab) => (
                <TabsContent key={tab.id} value={tab.id}, className="mt-0">
                  <motion.div
                    initial={{ opacity: 0, x: 20 }}, animate={{ opacity: 1, x: 0 }}, exit={{ opacity: 0, x: -20 }}, transition={{ duration: 0.2 }}
                  >
                    <Suspense 
                      fallback={
                        <Card>
                          <CardContent className="p-8">
                            <div className="flex items-center justify-center">
                              <RefreshCw className="w-6 h-6 animate-spin text-blue-500" />
                              <span className="ml-2 text-gray-600 dark:text-gray-400">
                                Loading {tab.label}...
                              </span>
    </div>
                          </CardContent>
    </Card>
                      }
                    >
                      <SafeComponentWrapper fallbackName={tab.label}>
                        <tab.component />
    </SafeComponentWrapper>
                    </Suspense>
                  </motion.div>
    </TabsContent>
              ))}
            </AnimatePresence>
    </Tabs>
        </motion.div>

        {/* Quick Actions */}
        <motion.div 
          initial={{ opacity: 0, y: 20 }}, animate={{ opacity: 1, y: 0 }}, transition={{ delay: 0.3 }}, className="grid grid-cols-1 md:grid-cols-3 gap-4"
        >
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg flex items-center gap-2">
                <TrendingUp className="w-5 h-5 text-green-500" />
                Performance Tips
              </CardTitle>
    </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                <li>• Enable lazy loading for images</li>
                <li>• Use React.memo for expensive components</li>
                <li>• Implement virtual scrolling for large lists</li>
                <li>• Optimize bundle size with code splitting</li>
    </ul>
            </CardContent>
    </Card>
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg flex items-center gap-2">
                <AlertTriangle className="w-5 h-5 text-orange-500" />
                Current Issues
              </CardTitle>
    </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Badge variant="outline" className="text-orange-600 border-orange-200">
                  No critical issues detected
                </Badge>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Your application is performing well!
                </p>
    </div>
            </CardContent>
    </Card>
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg flex items-center gap-2">
                <Settings className="w-5 h-5 text-blue-500" />
                Quick Settings
              </CardTitle>
    </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="w-full justify-start"
                  onClick={handleToggleMonitoring}
                >
                  {isMonitoringEnabled ? (
                    <EyeOff className="w-4 h-4 mr-2" />
                  ) : (
                    <Eye className="w-4 h-4 mr-2" />
                  )}
                  {isMonitoringEnabled ? 'Disable' : 'Enable'} Monitoring
                </Button>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="w-full justify-start"
                  onClick={handleClearCache}
                >
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Clear Cache
                </Button>
    </div>
            </CardContent>
    </Card>
        </motion.div>
    </div>
    </div>
  );
});

OptimizationDashboard.displayName = 'OptimizationDashboard';

export default OptimizationDashboard;