import React from 'react';
import { AlertTriangle, Shield, Clock, X } from 'lucide-react';

export interface SecurityAlertProps {
  type: 'error' | 'warning' | 'info' | 'rate-limit', title: string, message: string;
  details?: string[];
  onDismiss?: () => void;
  retryAfter?: number;
  className?: string;
}

/**
 * Security alert component for displaying validation errors and warnings
 */
export const SecurityAlert: React.FC<SecurityAlertProps> = ({
  type,
  title,
  message,
  details = [],
  onDismiss,
  retryAfter,
  className = ''
}) => {
  const getAlertStyles = () => {
    switch (type) {
      case 'error':
        return 'bg-red-50 border-red-200 text-red-800';
      case 'warning':
        return 'bg-yellow-50 border-yellow-200 text-yellow-800';
      case 'rate-limit':
        return 'bg-orange-50 border-orange-200 text-orange-800';
      case 'info':
      default:
        return 'bg-blue-50 border-blue-200 text-blue-800';
    }
  };

  const getIcon = () => {
    switch (type) {
      case 'error':
        return <AlertTriangle className="w-5 h-5 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="w-5 h-5 text-yellow-500" />;
      case 'rate-limit':
        return <Clock className="w-5 h-5 text-orange-500" />;
      case 'info':
      default:
        return <Shield className="w-5 h-5 text-blue-500" />;
    }
  };

  const formatRetryTime = (milliseconds: number) => {
    const seconds = Math.ceil(milliseconds / 1000);
    if (seconds < 60) {
      return `${seconds} second${seconds !== 1 ? 's' : ''}`;
    }
    const minutes = Math.ceil(seconds / 60);
    return `${minutes} minute${minutes !== 1 ? 's' : ''}`;
  };

  return (
    <div className={`border rounded-lg p-4 ${getAlertStyles()} ${className}`}>
      <div className="flex items-start">
        <div className="flex-shrink-0">
          {getIcon()}
        </div>
        
        <div className="ml-3 flex-1">
          <div className="flex items-center justify-between">
            <h3 className="text-sm font-medium">
              {title}
            </h3>
            {onDismiss && (
              <button
                onClick={onDismiss} className="ml-2 flex-shrink-0 p-1 rounded-md hover:bg-black hover:bg-opacity-10 transition-colors"
                aria-label="Dismiss alert"
              >
                <X className="w-4 h-4" />
    </button>
            )}
          </div>
          
          <div className="mt-1 text-sm">
            <p>{message}</p>
            
            {retryAfter && retryAfter > 0 && (
              <p className="mt-2 font-medium">
                Try again in {formatRetryTime(retryAfter)}
              </p>
            )}
            
            {details.length > 0 && (
              <div className="mt-2">
                <p className="font-medium mb-1">Details:</p>
                <ul className="list-disc list-inside space-y-1">
                  {details.map((detail, index) => (
                    <li key={index} className="text-xs">
                      {detail}
                    </li>
                  ))}
                </ul>
    </div>
            )}
          </div>
    </div>
      </div>
    </div>
  );
};

export default SecurityAlert;