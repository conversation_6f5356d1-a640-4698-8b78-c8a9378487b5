import React, { useState } from 'react';
import { Video } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { LazyGoLiveModal } from './lazy/LazyModals';

const LiveStreamButton = () => {
  const [isGoLiveModalOpen, setIsGoLiveModalOpen] = useState(false);

  const handleOpenGoLive = () => {
    setIsGoLiveModalOpen(true);
  };

  return (
    <>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button 
            variant="ghost" 
            size="sm" 
            className="relative p-2 rounded-full hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors h-10 w-10 group"
            onClick={handleOpenGoLive}, aria-label="Go Live"
          >
            <Video className="w-5 h-5 text-red-600 dark:text-red-400 group-hover:scale-110 transition-transform" />
            <span className="absolute top-0 right-0 w-2 h-2 bg-red-600 rounded-full animate-pulse" />
    </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>Go Live</p>
    </TooltipContent>
      </Tooltip>

      <LazyGoLiveModal 
        isOpen={isGoLiveModalOpen} onClose={() => setIsGoLiveModalOpen(false)}, onStreamStarted={(streamData) => {
          console.log('Stream started:', streamData);
          setIsGoLiveModalOpen(false);
        }}
      />
    </>
  );
};

export default LiveStreamButton;