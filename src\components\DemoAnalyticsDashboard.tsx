import React, { memo, useMemo, useState, useEffect } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  BarChart3, 
  Activity, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  HardDrive, 
  Cpu, 
  Zap,
  TrendingUp,
  TrendingDown,
  Info
} from 'lucide-react';
import { useDemoPerformanceMonitor } from '@/hooks/useDemoPerformanceMonitor';

interface DemoAnalyticsDashboardProps {
  demoMode: string, activeTab: string, dataSize: number;
  onOptimizationSuggestion?: (suggestion: string) => void;
}

// Memoized metric card component
const MetricCard = memo(({ 
  title, 
  value, 
  unit, 
  icon, 
  status, 
  trend,
  description 
}: {
  title: string, value: number | string;
  unit?: string;
  icon: React.ReactNode, status: 'good' | 'warning' | 'error';
  trend?: 'up' | 'down' | 'stable';
  description?: string;
}) => {
  const statusColors = {
    good: 'text-green-600',
    warning: 'text-yellow-600',
    error: 'text-red-600'
  };

  const trendIcons = {
    up: <TrendingUp className="h-3 w-3 text-green-500" />
        down: <TrendingDown className="h-3 w-3 text-red-500" />
        stable: <div className="h-3 w-3" />
  };

  return (
    <Card>
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {icon}
            <span className="text-sm font-medium">{title}</span>
    </div>
          {trend && trendIcons[trend]}
        </div>
        <div className="mt-2">
          <div className={`text-2xl font-bold ${statusColors[status]}`}>
            {value}{unit && <span className="text-sm text-muted-foreground ml-1">{unit}</span>}
          </div>
          {description && (
            <p className="text-xs text-muted-foreground mt-1">{description}</p>
          )}
        </div>
    </CardContent>
    </Card>
  );
});

// Memoized alert component
interface Alert {
  id: string, type: 'error' | 'warning' | 'info', message: string, timestamp: string | number; // Allow both string and number
  severity: 'low' | 'medium' | 'high' | 'critical';
  resolved?: boolean;
}

const AlertCard = memo(({ alert, onResolve }: { alert: Alert, onResolve: () => void }) => {
  const alertIcons: { [key: string]: React.ReactElement } = {
    error: <AlertTriangle className="h-4 w-4 text-red-500" />
        warning: <AlertTriangle className="h-4 w-4 text-yellow-500" />
        info: <Info className="h-4 w-4 text-blue-500" />
  };

  return (
    <div className={`flex items-start gap-3 p-3 rounded-lg border ${
      alert.resolved ? 'bg-muted/50' : 'bg-background'
    }`}>
      {alertIcons[alert.type as keyof typeof alertIcons]}
      <div className="flex-1">
        <p className="text-sm font-medium">{alert.message}</p>
        <p className="text-xs text-muted-foreground">
          {new Date(typeof alert.timestamp === 'number' ? alert.timestamp : alert.timestamp).toLocaleTimeString()}
        </p>
    </div>
      {!alert.resolved && (
        <Button size="sm" variant="ghost" onClick={onResolve}>
          <CheckCircle className="h-3 w-3" />
    </Button>
      )}
    </div>
  );
});

const DemoAnalyticsDashboard: React.FC<DemoAnalyticsDashboardProps> = memo(({
  demoMode,
  activeTab,
  dataSize,
  onOptimizationSuggestion
}) => {
  const {
    metrics,
    alerts,
    isMonitoring,
    startMonitoring,
    stopMonitoring,
    resolveAlert,
    clearAlerts,
    getPerformanceScore;
    getInsights
  } = useDemoPerformanceMonitor();

  const [sessionStats, setSessionStats] = useState({
    startTime: Date.now(),
    tabSwitches: 0,
    interactions: 0,
    errors: 0
  });

  // Track tab switches
  useEffect(() => {
    setSessionStats(prev => ({ ...prev, tabSwitches: prev.tabSwitches + 1 }));
  }, [activeTab]);

  // Calculate session duration
  const sessionDuration = useMemo(() => {
    const duration = Date.now() - sessionStats.startTime;
    const minutes = Math.floor(duration / 60000);
    const seconds = Math.floor((duration % 60000) / 1000);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }, [sessionStats.startTime]);

  // Performance insights
  const insights = useMemo(() => getInsights(), [getInsights]);

  // Performance score
  const performanceScore = useMemo(() => getPerformanceScore(), [getPerformanceScore]);

  // Metric status calculation
  const getMetricStatus = (metric: keyof typeof metrics, threshold: number): 'good' | 'warning' | 'error' => {
    const value = metrics[metric] as number;
    if (metric === 'fps') {
      return value >= threshold ? 'good' : value >= threshold * 0.8 ? 'warning' : 'error';
    }
    return value <= threshold ? 'good' : value <= threshold * 1.5 ? 'warning' : 'error';
  };

  // Start monitoring on mount
  useEffect(() => {
    startMonitoring();
    return () => stopMonitoring();
  }, [startMonitoring, stopMonitoring]);

  return (
    <div className="space-y-6">
      {/* Header with overall performance score */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Demo Performance Analytics
            </CardTitle>
            <div className="flex items-center gap-4">
              <Badge variant={isMonitoring ? "default" : "secondary"}>
                {isMonitoring ? "Monitoring Active" : "Monitoring Stopped"}
              </Badge>
              <div className="text-right">
                <div className="text-2xl font-bold">{performanceScore}%</div>
                <div className="text-xs text-muted-foreground">Performance Score</div>
    </div>
            </div>
    </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-lg font-semibold">{demoMode}</div>
              <div className="text-xs text-muted-foreground">Current Mode</div>
    </div>
            <div className="text-center">
              <div className="text-lg font-semibold">{activeTab}</div>
              <div className="text-xs text-muted-foreground">Active Tab</div>
    </div>
            <div className="text-center">
              <div className="text-lg font-semibold">{dataSize.toLocaleString()}</div>
              <div className="text-xs text-muted-foreground">Data Items</div>
    </div>
            <div className="text-center">
              <div className="text-lg font-semibold">{sessionDuration}</div>
              <div className="text-xs text-muted-foreground">Session Time</div>
    </div>
          </div>
    </CardContent>
      </Card>

      <Tabs defaultValue="metrics" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="metrics">Performance</TabsTrigger>
          <TabsTrigger value="alerts">
            Alerts {alerts.filter(a => !a.resolved).length > 0 && (
              <Badge variant="destructive" className="ml-2 text-xs">
                {alerts.filter(a => !a.resolved).length}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="insights">Insights</TabsTrigger>
          <TabsTrigger value="session">Session</TabsTrigger>
    </TabsList>
        <TabsContent value="metrics" className="mt-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <MetricCard
              title="Render Time"
              value={metrics.renderTime.toFixed(1)} unit="ms"
              icon={<Clock className="h-4 w-4" />} status={getMetricStatus('renderTime', 16.67)}, trend={metrics.renderTime < 16.67 ? 'up' : 'down'} description="Time to render components"
            />
            <MetricCard
              title="Memory Usage"
              value={metrics.memoryUsage.toFixed(1)} unit="MB"
              icon={<HardDrive className="h-4 w-4" />} status={getMetricStatus('memoryUsage', 100)}, trend={metrics.memoryUsage < 50 ? 'stable' : 'up'} description="Current JS heap size"
            />
            <MetricCard
              title="FPS"
              value={metrics.fps} icon={<Zap className="h-4 w-4" />}, status={getMetricStatus('fps', 30)} trend={metrics.fps >= 60 ? 'up' : metrics.fps >= 30 ? 'stable' : 'down'}, description="Frames per second"
            />
            <MetricCard
              title="Interaction"
              value={metrics.interactionLatency.toFixed(1)} unit="ms"
              icon={<Cpu className="h-4 w-4" />} status={metrics.interactionLatency < 100 ? 'good' : 'warning'}, trend={metrics.interactionLatency < 50 ? 'up' : 'down'} description="Response latency"
            />
    </div>
          {/* Performance Score Progress */}
          <Card className="mt-4">
            <CardHeader>
              <CardTitle className="text-lg">Overall Performance</CardTitle>
    </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>Performance Score</span>
                    <span className="font-medium">{performanceScore}%</span>
    </div>
                  <Progress 
                    value={performanceScore} className="h-2"
                  />
    </div>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-muted-foreground">Component Load Time: </span>
                    <span className="font-medium">{metrics.componentLoadTime.toFixed(1)}ms</span>
    </div>
                  <div>
                    <span className="text-muted-foreground">Error Count: </span>
                    <span className="font-medium">{metrics.errorCount}</span>
    </div>
                </div>
    </div>
            </CardContent>
    </Card>
        </TabsContent>

        <TabsContent value="alerts" className="mt-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Performance Alerts</CardTitle>
                <Button variant="outline" size="sm" onClick={clearAlerts}>
                  Clear All
                </Button>
    </div>
            </CardHeader>
            <CardContent>
              {alerts.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <CheckCircle className="h-8 w-8 mx-auto mb-2" />
                  <p>No alerts at this time</p>
    </div>
              ) : (
                <div className="space-y-3">
                  {alerts.map((alert) => (
                    <AlertCard
                      key={alert.id} alert={alert}, onResolve={() => resolveAlert(alert.id)}
                    />
                  ))}
                </div>
              )}
            </CardContent>
    </Card>
        </TabsContent>

        <TabsContent value="insights" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Performance Insights</CardTitle>
    </CardHeader>
            <CardContent>
              {insights.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <BarChart3 className="h-8 w-8 mx-auto mb-2" />
                  <p>All metrics are performing well</p>
    </div>
              ) : (
                <div className="space-y-4">
                  {insights.map((insight, index) => (
                    <div key={index} className="flex items-start gap-3 p-3 rounded-lg border">
                      <Info className="h-4 w-4 text-blue-500 mt-0.5" />
                      <div className="flex-1">
                        <p className="text-sm font-medium">{insight.message}</p>
                        <Badge variant="outline" className="mt-2 text-xs">
                          {insight.priority} priority
                        </Badge>
    </div>
                      {onOptimizationSuggestion && (
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => onOptimizationSuggestion(insight.message)}
                        >
                          Apply
                        </Button>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
    </Card>
        </TabsContent>

        <TabsContent value="session" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Session Statistics</CardTitle>
    </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold">{sessionDuration}</div>
                  <div className="text-sm text-muted-foreground">Session Duration</div>
    </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">{sessionStats.tabSwitches}</div>
                  <div className="text-sm text-muted-foreground">Tab Switches</div>
    </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">{sessionStats.interactions}</div>
                  <div className="text-sm text-muted-foreground">Interactions</div>
    </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">{sessionStats.errors}</div>
                  <div className="text-sm text-muted-foreground">Errors</div>
    </div>
              </div>
    </CardContent>
          </Card>
    </TabsContent>
      </Tabs>
    </div>
  );
});

DemoAnalyticsDashboard.displayName = 'DemoAnalyticsDashboard';

export default DemoAnalyticsDashboard;