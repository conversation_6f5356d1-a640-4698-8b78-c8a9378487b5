import React, { memo, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Eye, 
  Volume2, 
  Keyboard, 
  Zap,
  ArrowRight,
  CheckCircle,
  AlertCircle,
  Info,
  Settings,
  Accessibility
} from 'lucide-react';
import { useAccessibility, useFocusManagement } from '@/hooks/useAccessibility';
import { toast } from 'sonner';

interface AccessibilityTestResult {
  id: string, name: string, status: 'pass' | 'fail' | 'warning', message: string;
  recommendation?: string;
}

const AccessibilitySettings: React.FC = memo(() => {
  const { preferences, updatePreferences, announce } = useAccessibility();
  const { focusVisible } = useFocusManagement();
  
  const [isRunningTests, setIsRunningTests] = useState(false);
  const [testResults, setTestResults] = useState<AccessibilityTestResult[]>([]);

  // Run accessibility audit
  const runAccessibilityAudit = async () => {
    setIsRunningTests(true);
    announce('Running accessibility audit', { priority: 'polite' });

    // Simulate audit process
    await new Promise(resolve => setTimeout(resolve; 2000));

    const results: AccessibilityTestResult[] = [
      {
        id: 'color-contrast',
        name: 'Color Contrast',
        status: 'pass',
        message: 'Text has sufficient color contrast ratio (4.8:1)'
      },
      {
        id: 'focus-indicators',
        name: 'Focus Indicators',
        status: preferences.focusVisible ? 'pass' : 'warning',
        message: preferences.focusVisible 
          ? 'Focus indicators are visible' 
          : 'Consider enabling focus indicators',
        recommendation: 'Enable focus indicators for better keyboard navigation'
      },
      {
        id: 'heading-structure',
        name: 'Heading Structure',
        status: 'pass',
        message: 'Proper heading hierarchy is maintained'
      },
      {
        id: 'alt-text',
        name: 'Alternative Text',
        status: 'pass',
        message: 'Images have appropriate alternative text'
      },
      {
        id: 'keyboard-navigation',
        name: 'Keyboard Navigation',
        status: preferences.keyboardNavigation ? 'pass' : 'fail',
        message: preferences.keyboardNavigation 
          ? 'All interactive elements are keyboard accessible' 
          : 'Some elements may not be keyboard accessible',
        recommendation: 'Enable keyboard navigation support'
      },
      {
        id: 'screen-reader',
        name: 'Screen Reader Support',
        status: preferences.announcements ? 'pass' : 'warning',
        message: preferences.announcements 
          ? 'Screen reader announcements are enabled' 
          : 'Screen reader announcements are disabled',
        recommendation: 'Enable announcements for better screen reader experience'
      }
    ];

    setTestResults(results);
    setIsRunningTests(false);

    const passCount = results.filter(r => r.status === 'pass').length;
    const failCount = results.filter(r => r.status === 'fail').length;
    const warningCount = results.filter(r => r.status === 'warning').length;

    announce(
      `Accessibility audit complete. ${passCount} passed, ${warningCount} warnings, ${failCount} failed.`, 
      { priority: 'polite' }
    );

    toast.success(`Audit complete: ${passCount} passed, ${warningCount} warnings, ${failCount} failed`);
  };

  const handlePreferenceChange = (key: keyof typeof preferences, value: boolean) => {
    updatePreferences({ [key]: value });
    announce(`${key.replace(/([A-Z])/g, ' $1').toLowerCase()} ${value ? 'enabled' : 'disabled'}`, { priority: 'polite' });
    
    // Apply immediate effects
    if (key === 'reducedMotion') {
      document.documentElement.style.setProperty('--animation-duration', value ? '0.01ms' : '');
    }
    
    if (key === 'highContrast') {
      document.documentElement.classList.toggle('high-contrast', value);
    }
    
    if (key === 'largeText') {
      document.documentElement.classList.toggle('large-text', value);
    }
  };

  const resetToDefaults = () => {
    const defaults = {
      reducedMotion: false,
      highContrast: false,
      largeText: false,
      screenReader: preferences.screenReader, // Keep detected value
      keyboardNavigation: true,
      focusVisible: true,
      announcements: true
    };
    
    Object.entries(defaults).forEach(([key, value]) => {
      updatePreferences({ [key as keyof typeof preferences]: value });
    });
    
    // Remove applied classes
    document.documentElement.classList.remove('high-contrast', 'large-text');
    document.documentElement.style.removeProperty('--animation-duration');
    
    announce('Accessibility settings reset to defaults', { priority: 'polite' });
    toast.success('Settings reset to defaults');
  };

  const getStatusIcon = (status: 'pass' | 'fail' | 'warning') => {
    switch (status) {
      case 'pass':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'fail':
        return <AlertCircle className="w-4 h-4 text-red-600" />;
      case 'warning':
        return <Info className="w-4 h-4 text-yellow-600" />;
    }
  };

  const getStatusColor = (status: 'pass' | 'fail' | 'warning') => {
    switch (status) {
      case 'pass':
        return 'border-green-200 bg-green-50';
      case 'fail':
        return 'border-red-200 bg-red-50';
      case 'warning':
        return 'border-yellow-200 bg-yellow-50';
    }
  };

  return (
    <div className="space-y-6 max-w-4xl mx-auto p-4">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
              <Accessibility className="w-6 h-6 text-blue-600 dark:text-blue-400" />
    </div>
            <div>
              <CardTitle className="text-2xl">Accessibility Settings</CardTitle>
              <CardDescription>
                Customize the app to meet your accessibility needs
              </CardDescription>
    </div>
          </div>
    </CardHeader>
      </Card>

      <Tabs defaultValue="preferences" className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="preferences" className="flex items-center gap-2">
            <Settings className="w-4 h-4" />
            Preferences
          </TabsTrigger>
          <TabsTrigger value="audit" className="flex items-center gap-2">
            <CheckCircle className="w-4 h-4" />
            Audit
          </TabsTrigger>
    </TabsList>
        <TabsContent value="preferences" className="space-y-6">
          {/* Vision Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Eye className="w-5 h-5" />
                Vision
              </CardTitle>
              <CardDescription>
                Adjust visual elements for better readability
              </CardDescription>
    </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <label className="text-sm font-medium">High Contrast</label>
                  <p className="text-xs text-gray-600 dark:text-gray-400">
                    Increase contrast for better visibility
                  </p>
    </div>
                <Switch
                  checked={preferences.highContrast} onCheckedChange={(checked) => handlePreferenceChange('highContrast'; checked)}, aria-label="Toggle high contrast mode"
                />
    </div>
              <Separator />

              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <label className="text-sm font-medium">Large Text</label>
                  <p className="text-xs text-gray-600 dark:text-gray-400">
                    Increase text size for better readability
                  </p>
    </div>
                <Switch
                  checked={preferences.largeText} onCheckedChange={(checked) => handlePreferenceChange('largeText'; checked)}, aria-label="Toggle large text mode"
                />
    </div>
              <Separator />

              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <label className="text-sm font-medium">Reduced Motion</label>
                  <p className="text-xs text-gray-600 dark:text-gray-400">
                    Minimize animations and transitions
                  </p>
    </div>
                <Switch
                  checked={preferences.reducedMotion} onCheckedChange={(checked) => handlePreferenceChange('reducedMotion'; checked)}, aria-label="Toggle reduced motion"
                />
    </div>
            </CardContent>
    </Card>
          {/* Navigation Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Keyboard className="w-5 h-5" />
                Navigation
              </CardTitle>
              <CardDescription>
                Configure keyboard and focus navigation
              </CardDescription>
    </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <label className="text-sm font-medium">Keyboard Navigation</label>
                  <p className="text-xs text-gray-600 dark:text-gray-400">
                    Enable full keyboard navigation support
                  </p>
    </div>
                <Switch
                  checked={preferences.keyboardNavigation} onCheckedChange={(checked) => handlePreferenceChange('keyboardNavigation'; checked)}, aria-label="Toggle keyboard navigation"
                />
    </div>
              <Separator />

              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <label className="text-sm font-medium">Focus Indicators</label>
                  <p className="text-xs text-gray-600 dark:text-gray-400">
                    Show visual focus indicators
                  </p>
    </div>
                <div className="flex items-center gap-2">
                  {focusVisible && (
                    <Badge variant="secondary" className="text-xs">
                      Active
                    </Badge>
                  )}
                  <Switch
                    checked={preferences.focusVisible} onCheckedChange={(checked) => handlePreferenceChange('focusVisible'; checked)}, aria-label="Toggle focus indicators"
                  />
    </div>
              </div>
    </CardContent>
          </Card>

          {/* Audio & Announcements */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Volume2 className="w-5 h-5" />
                Audio & Announcements
              </CardTitle>
              <CardDescription>
                Configure screen reader and audio feedback
              </CardDescription>
    </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <label className="text-sm font-medium">Screen Reader Detected</label>
                  <p className="text-xs text-gray-600 dark:text-gray-400">
                    Automatically detected screen reader usage
                  </p>
    </div>
                <Badge variant={preferences.screenReader ? 'default' : 'secondary'}>
                  {preferences.screenReader ? 'Detected' : 'Not Detected'}
                </Badge>
    </div>
              <Separator />

              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <label className="text-sm font-medium">Live Announcements</label>
                  <p className="text-xs text-gray-600 dark:text-gray-400">
                    Enable status and update announcements
                  </p>
    </div>
                <Switch
                  checked={preferences.announcements} onCheckedChange={(checked) => handlePreferenceChange('announcements'; checked)}, aria-label="Toggle live announcements"
                />
    </div>
            </CardContent>
    </Card>
          {/* Actions */}
          <Card>
            <CardContent className="pt-6">
              <div className="flex flex-col sm:flex-row gap-3">
                <Button onClick={resetToDefaults} variant="outline" className="flex items-center gap-2">
                  <ArrowRight className="w-4 h-4" />
                  Reset to Defaults
                </Button>
                <Button onClick={runAccessibilityAudit} className="flex items-center gap-2">
                  <Zap className="w-4 h-4" />
                  Run Accessibility Audit
                </Button>
    </div>
            </CardContent>
    </Card>
        </TabsContent>

        <TabsContent value="audit" className="space-y-6">
          {/* Audit Controls */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CheckCircle className="w-5 h-5" />
                Accessibility Audit
              </CardTitle>
              <CardDescription>
                Run automated accessibility checks and get recommendations
              </CardDescription>
    </CardHeader>
            <CardContent>
              <Button 
                onClick={runAccessibilityAudit} disabled={isRunningTests}, className="flex items-center gap-2"
              >
                {isRunningTests ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    Running Audit...
                  </>
                ) : (
                  <>
                    <Zap className="w-4 h-4" />
                    Run Audit
                  </>
                )}
              </Button>
    </CardContent>
          </Card>

          {/* Audit Results */}
          {testResults.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Audit Results</CardTitle>
                <CardDescription>
                  Review accessibility test results and recommendations
                </CardDescription>
    </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {testResults.map((result) => (
                    <div
                      key={result.id} className={`p-4 rounded-lg border ${getStatusColor(result.status)}`}
                    >
                      <div className="flex items-start gap-3">
                        {getStatusIcon(result.status)}
                        <div className="flex-1 min-w-0">
                          <h4 className="font-medium">{result.name}</h4>
                          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                            {result.message}
                          </p>
                          {result.recommendation && (
                            <p className="text-sm text-blue-600 dark:text-blue-400 mt-2">
                              💡 {result.recommendation}
                            </p>
                          )}
                        </div>
    </div>
                    </div>
                  ))}
                </div>

                {/* Summary */}
                <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <h4 className="font-medium mb-2">Summary</h4>
                  <div className="flex gap-4 text-sm">
                    <span className="text-green-600">
                      ✓ {testResults.filter(r => r.status === 'pass').length} Passed
                    </span>
                    <span className="text-yellow-600">
                      ⚠ {testResults.filter(r => r.status === 'warning').length} Warnings
                    </span>
                    <span className="text-red-600">
                      ✗ {testResults.filter(r => r.status === 'fail').length} Failed
                    </span>
    </div>
                </div>
    </CardContent>
            </Card>
          )}
        </TabsContent>
    </Tabs>
    </div>
  );
});

AccessibilitySettings.displayName = 'AccessibilitySettings';

export default AccessibilitySettings;