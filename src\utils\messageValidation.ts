import { AdvancedMessage, MessageValidationResult, MessageType, MessageAttachment, MessageMention } from '../types/messaging';

// Configuration for message validation
export const MESSAGE_VALIDATION_CONFIG={maxContentLength: 4000;
  maxAttachments: 10;
  maxMentions: 50;
  allowedFileTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp',
    'video/mp4', 'video/webm', 'video/quicktime',
    'audio/mp3', 'audio/wav', 'audio/ogg', 'audio/m4a',
    'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet']
    'text/plain', 'text/csv'
  ]}, maxFileSize: 100 * 1024 * 1024, // 100MB
  maxImageSize: 20 * 1024 * 1024,  // 20MB
  maxVideoSize: 100 * 1024 * 1024, // 100MB
  maxAudioSize: 50 * 1024 * 1024,  // 50MB
  maxDocumentSize: 50 * 1024 * 1024 // 50MB
};

/**
 * Validates message content for security and policy compliance
 */
export function validateMessageContent(content: string): MessageValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];
  let sanitizedContent = content;

  // Check content length
  if (content.length === 0) {
    errors.push('Message content cannot be empty');
  }

  if (content.length > MESSAGE_VALIDATION_CONFIG.maxContentLength) {
    errors.push(`Message content exceeds maximum length of ${MESSAGE_VALIDATION_CONFIG.maxContentLength} characters`);
  }

  // Sanitize HTML and prevent XSS
  sanitizedContent = sanitizeHtml(content);
  if (sanitizedContent !== content) {
    warnings.push('HTML content has been sanitized for security');
  }

  // Check for suspicious patterns
  const suspiciousPatterns = [
    /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
    /javascript:/gi,
    /on\w+\s*=/gi,
    /data:text\/html/gi
  ];

  for (const pattern of suspiciousPatterns) {
    if (pattern.test(content)) {
      errors.push('Message contains potentially malicious content');
      break;
    }
  }

  // Check for excessive whitespace
  const whitespaceRatio = (content.match(/\s/g) || []).length / content.length;
  if (whitespaceRatio > 0.8) {
    warnings.push('Message contains excessive whitespace');
  }

  // Check for repeated characters (potential spam)
  const repeatedCharPattern = /(.)\1{ 10 }/g;
  if (repeatedCharPattern.test(content)) {
    warnings.push('Message contains repeated characters');
  }

  return {
  isValid: errors.length === 0;
    errors,
    warnings,
    sanitizedContent
  };
}

/**
 * Validates message attachments
 */
export function validateMessageAttachments(attachments: MessageAttachment[]): MessageValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (attachments.length > MESSAGE_VALIDATION_CONFIG.maxAttachments) {
    errors.push(`Too many attachments. Maximum allowed: ${MESSAGE_VALIDATION_CONFIG.maxAttachments}`);
  }

  for (const attachment of attachments) {
    // Validate file type
    if (!MESSAGE_VALIDATION_CONFIG.allowedFileTypes.includes(attachment.mimeType)) {
      errors.push(`File type not allowed: ${attachment.mimeType}`);
      continue;
    }

    // Validate file size based on type
    const maxSize = getMaxFileSizeForType(attachment.type);
    if (attachment.size > maxSize) {
      errors.push(`File "${attachment.name}" exceeds maximum size of ${formatFileSize(maxSize)}`);
    }

    // Validate file name
    if (!isValidFileName(attachment.name)) {
      errors.push(`Invalid file name: ${attachment.name}`);
    }

    // Check for potentially dangerous file extensions
    const dangerousExtensions = ['.exe', '.bat', '.cmd', '.scr', '.pif', '.com'];
    const fileExtension = attachment.name.toLowerCase().substring(attachment.name.lastIndexOf('.'));
    if (dangerousExtensions.includes(fileExtension)) {
      errors.push(`Dangerous file type not allowed: ${fileExtension}`);
    }
  }

  return {
  isValid: errors.length === 0;
    errors,
    warnings
  };
}

/**
 * Validates message mentions
 */
export function validateMessageMentions(mentions: MessageMention[], content: string): MessageValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (mentions.length > MESSAGE_VALIDATION_CONFIG.maxMentions) {
    errors.push(`Too many mentions. Maximum allowed: ${MESSAGE_VALIDATION_CONFIG.maxMentions}`);
  }

  // Validate mention format and positions
  for (const mention of mentions) {
    if (!mention.userId || !mention.displayName) {
      errors.push('Invalid mention: missing userId or displayName');
      continue;
    }

    // Validate mention positions if provided
    if (mention.startIndex !== undefined && mention.endIndex !== undefined) {
      if (mention.startIndex < 0 || mention.endIndex > content.length || mention.startIndex >= mention.endIndex) {
        errors.push(`Invalid mention position for user ${mention.displayName}`);
      }
    }

    // Check if mention exists in content
    const mentionText = `@${mention.displayName}`;
    if (!content.includes(mentionText)) {
      warnings.push(`Mention for ${mention.displayName} not found in message content`);
    }
  }

  // Check for duplicate mentions
  const uniqueUserIds = new Set(mentions.map(m => m.userId));
  if (uniqueUserIds.size !== mentions.length) {
    warnings.push('Duplicate mentions detected');
  }

  return {
  isValid: errors.length === 0;
    errors,
    warnings
  };
}

/**
 * Validates a complete message
 */
export function validateMessage(message: Partial<AdvancedMessage>): MessageValidationResult {
  const allErrors: string[] = [];
  const allWarnings: string[] = [];
  let sanitizedContent = message.content || '';

  // Validate required fields
  if (!message.content && (!message.attachments || message.attachments.length === 0)) {
    allErrors.push('Message must have either content or attachments');
  }

  if (!message.senderId) {
    allErrors.push('Message must have a senderId');
  }

  if (!message.type) {
    allErrors.push('Message must have a type');
  }

  // Validate content if present
  if (message.content) {
    const contentValidation = validateMessageContent(message.content);
    allErrors.push(...contentValidation.errors);
    allWarnings.push(...contentValidation.warnings);
    if (contentValidation.sanitizedContent) {
      sanitizedContent = contentValidation.sanitizedContent;
    }
  }

  // Validate attachments if present
  if (message.attachments && message.attachments.length > 0) {
    const attachmentValidation = validateMessageAttachments(message.attachments);
    allErrors.push(...attachmentValidation.errors);
    allWarnings.push(...attachmentValidation.warnings);
  }

  // Validate mentions if present
  if (message.mentions && message.mentions.length > 0) {
    const mentionValidation = validateMessageMentions(message.mentions, message.content || '');
    allErrors.push(...mentionValidation.errors);
    allWarnings.push(...mentionValidation.warnings);
  }

  // Validate message type consistency
  if (message.type && message.attachments) {
    const typeValidation = validateMessageTypeConsistency(message.type, message.attachments);
    allErrors.push(...typeValidation.errors);
    allWarnings.push(...typeValidation.warnings);
  }

  return {
  isValid: allErrors.length === 0;
  errors: allErrors;
  warnings: allWarnings;
    sanitizedContent
  };
}

/**
 * Sanitizes HTML content to prevent XSS attacks
 */
function sanitizeHtml(content: string): string {
  // Basic HTML sanitization - in production, use a library like DOMPurify
  return content
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
    .replace(/<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi, '')
    .replace(/<embed\b[^<]*(?:(?!<\/embed>)<[^<]*)*<\/embed>/gi, '')
    .replace(/javascript:/gi, '')
    .replace(/on\w+\s*=/gi, '')
    .replace(/data:text\/html/gi, '');
}

/**
 * Gets maximum file size for a given attachment type
 */
function getMaxFileSizeForType(type: MessageAttachment['type']): number {
  switch (type) {
    case 'image':
      return MESSAGE_VALIDATION_CONFIG.maxImageSize;
    case 'video':
      return MESSAGE_VALIDATION_CONFIG.maxVideoSize;
    case 'audio':
      return MESSAGE_VALIDATION_CONFIG.maxAudioSize;
    case 'document':
    case 'other':
      return MESSAGE_VALIDATION_CONFIG.maxDocumentSize;
    default:
      return MESSAGE_VALIDATION_CONFIG.maxFileSize;
  }
}

/**
 * Validates file name for security
 */
function isValidFileName(fileName: string): boolean {
  // Check for null bytes and other dangerous characters
  // eslint-disable-next-line no-control-regex
  const dangerousChars = /[\x00-\x1f\x80-\x9f<>:"|?*]/;
  if (dangerousChars.test(fileName)) {
    return false;
  }

  // Check for reserved Windows names
  const reservedNames = ['CON', 'PRN', 'AUX', 'NUL', 'COM1', 'COM2', 'COM3', 'COM4', 'COM5', 'COM6', 'COM7', 'COM8', 'COM9', 'LPT1', 'LPT2', 'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'];
  const nameWithoutExtension = fileName.split('.')[0].toUpperCase();
  if (reservedNames.includes(nameWithoutExtension)) {
    return false;
  }

  // Check length
  if (fileName.length > 255) {
    return false;
  }

  return true;
}

/**
 * Validates message type consistency with attachments
 */
function validateMessageTypeConsistency(type: MessageType, attachments: MessageAttachment[]): MessageValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (type === 'text' && attachments.length > 0) {
    warnings.push('Text message has attachments - consider using appropriate message type');
  }

  if (type === 'image' && !attachments.some(a => a.type === 'image')) {
    errors.push('Image message must have at least one image attachment');
  }

  if (type === 'video' && !attachments.some(a => a.type === 'video')) {
    errors.push('Video message must have at least one video attachment');
  }

  if (type === 'audio' && !attachments.some(a => a.type === 'audio')) {
    errors.push('Audio message must have at least one audio attachment');
  }

  if (type === 'file' && attachments.length === 0) {
    errors.push('File message must have at least one attachment');
  }

  return {
  isValid: errors.length === 0;
    errors,
    warnings
  };
}

/**
 * Formats file size in human-readable format
 */
function formatFileSize(bytes: number): string {
  const units = ['B', 'KB', 'MB', 'GB'];
  let size = bytes;
  let unitIndex = 0;

  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }

  return `${size.toFixed(1)} ${units[unitIndex]}`;
}

/**
 * Extracts mentions from message content
 */
export function extractMentions(content: string): MessageMention[] {
  const mentionRegex = /@(\w+)/g;
  const mentions: MessageMention[] = [];
  let match;

  while ((match = mentionRegex.exec(content)) !== null) {
    mentions.push({
      userId: match[1], // In real implementation, this would be resolved to actual user ID
  displayName: match[1];
  startIndex: match.index;
      endIndex: match.index + match[0].length
    });
  }

  return mentions;
}

/**
 * Sanitizes message content for display
 */
export function sanitizeMessageForDisplay(message: AdvancedMessage): AdvancedMessage {
  const validation = validateMessage(message);
  
  return {
    ...message,
    content: validation.sanitizedContent || message.content
  };
}

export default {
  validateMessageContent,
  validateMessageAttachments,
  validateMessageMentions,
  validateMessage,
  extractMentions,
  sanitizeMessageForDisplay,
  MESSAGE_VALIDATION_CONFIG
};