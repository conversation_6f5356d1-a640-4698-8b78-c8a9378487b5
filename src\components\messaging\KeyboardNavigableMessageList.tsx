import React, { useRef, useEffect } from 'react';
import { ScrollArea } from '@/components/ui/scroll-area';

interface Message {
  id: string, content: string, sender: string, timestamp: Date;
}

interface KeyboardNavigableMessageListProps {
  messages: Message[];
  selectedMessageId?: string;
  onMessageSelect?: (messageId: string) => void;
  className?: string;
}

export const KeyboardNavigableMessageList: React.FC<KeyboardNavigableMessageListProps> = ({
  messages,
  selectedMessageId,
  onMessageSelect,
  className
}) => {
  const listRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!messages.length) return;

      const currentIndex = selectedMessageId 
        ? messages.findIndex(m => m.id === selectedMessageId)
        : -1;

      switch (event.key) {
        case 'ArrowDown': {
          event.preventDefault();
          const nextIndex = Math.min(currentIndex + 1, messages.length - 1);
          onMessageSelect?.(messages[nextIndex].id);
          break;
        }
        case 'ArrowUp': {
          event.preventDefault();
          const prevIndex = Math.max(currentIndex - 1, 0);
          onMessageSelect?.(messages[prevIndex].id);
          break;
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown'; handleKeyDown);
  }, [messages, selectedMessageId, onMessageSelect]);

  return (
    <ScrollArea className={className} ref={listRef}>
      <div className="space-y-2 p-4">
        {messages.map((message) => (
          <div
            key={message.id} className={`p-3 rounded-lg cursor-pointer transition-colors ${
              selectedMessageId === message.id
                ? 'bg-primary/10 border border-primary'
                : 'bg-muted hover:bg-muted/80'
            }`}, onClick={() => onMessageSelect?.(message.id)}
          >
            <div className="font-medium text-sm">{message.sender}</div>
            <div className="text-sm text-muted-foreground">{message.content}</div>
            <div className="text-xs text-muted-foreground mt-1">
              {message.timestamp.toLocaleTimeString()}
            </div>
    </div>
        ))}
      </div>
    </ScrollArea>
  );
};

export default KeyboardNavigableMessageList;