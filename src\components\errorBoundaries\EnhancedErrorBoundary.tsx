import React, { Component, ErrorInfo, ReactNode } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>gle, Refresh<PERSON>w, Bug, Home, ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface ErrorBoundaryState {
  hasError: boolean, error: Error | null, errorInfo: ErrorInfo | null, errorId: string, retryCount: number;
}

interface EnhancedErrorBoundaryProps {
  children: ReactNode;
  fallback?: React.ComponentType<ErrorFallbackProps>;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  level?: 'app' | 'page' | 'component' | 'feature';
  componentName?: string;
  enableRetry?: boolean;
  maxRetries?: number;
  showErrorDetails?: boolean;
  enableErrorReporting?: boolean;
}

interface ErrorFallbackProps {
  error: Error, errorInfo: ErrorInfo, resetError: () => void; retryCount: number, level: string;
  componentName?: string;
  errorId: string;
}

// Enhanced error logging service
class ErrorLogger {
  static log(error: Error, errorInfo: ErrorInfo, context: { level: string;
    componentName?: string;
    errorId: string, retryCount: number; }) {
    const errorData = {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      ...context
    };

    // Log to console in development
    if (import.meta.env.DEV) {
      console.group(`🚨 Error Boundary: ${context.level}`);
      console.error('Error:', error);
      console.error('Error Info:', errorInfo);
      console.error('Context:', context);
      console.groupEnd();
    }

    // In production, send to error reporting service
    if (import.meta.env.PROD) {
      // Replace with your error reporting service
      // Example: Sentry, LogRocket, etc.
      try {
        // window.errorReportingService?.captureException(error, errorData);
      } catch (reportingError) {
        console.error('Failed to report error:', reportingError);
      }
    }

    // Store in localStorage for debugging
    try {
      const existingErrors = JSON.parse(localStorage.getItem('app_errors') || '[]');
      existingErrors.push(errorData);
      
      // Keep only last 10 errors
      if (existingErrors.length > 10) {
        existingErrors.splice(0, existingErrors.length - 10);
      }
      
      localStorage.setItem('app_errors', JSON.stringify(existingErrors));
    } catch (storageError) {
      console.warn('Failed to store error in localStorage:', storageError);
    }
  }
}

// Default error fallback component
const DefaultErrorFallback: React.FC<ErrorFallbackProps> = ({
  error,
  resetError,
  retryCount,
  level,
  componentName,
  errorId
}) => {
  const getErrorTitle = () => {
    switch (level) {
      case 'app':
        return 'Application Error';
      case 'page':
        return 'Page Error';
      case 'feature':
        return 'Feature Error';
      default:
        return 'Component Error';
    }
  };

  const getErrorDescription = () => {
    if (componentName) {
      return `An error occurred in the ${componentName} ${level}.`;
    }
    return `An unexpected error occurred in this ${level}.`;
  };

  const getSeverityColor = () => {
    switch (level) {
      case 'app':
        return 'destructive';
      case 'page':
        return 'destructive';
      case 'feature':
        return 'secondary';
      default:
        return 'outline';
    }
  };

  return (
    <div className="flex items-center justify-center min-h-[200px] p-4">
      <Card className="w-full max-w-md border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20">
        <CardHeader className="text-center">
          <div className="flex items-center justify-center mb-2">
            <AlertTriangle className="w-12 h-12 text-red-500" />
    </div>
          <CardTitle className="text-red-700 dark:text-red-400">
            {getErrorTitle()}
          </CardTitle>
          <div className="flex items-center justify-center gap-2 mt-2">
            <Badge variant={getSeverityColor() as any} className="text-xs">
              {level.toUpperCase()}
            </Badge>
            {retryCount > 0 && (
              <Badge variant="outline" className="text-xs">
                Retry {retryCount}
              </Badge>
            )}
          </div>
    </CardHeader>
        <CardContent className="text-center space-y-4">
          <p className="text-red-600 dark:text-red-300 text-sm">
            {getErrorDescription()}
          </p>
          
          {process.env.NODE_ENV === 'development' && (
            <details className="text-left">
              <summary className="text-xs text-gray-500 cursor-pointer hover:text-gray-700">
                Error Details (Dev Mode)
              </summary>
              <div className="mt-2 p-2 bg-gray-100 dark:bg-gray-800 rounded text-xs font-mono">
                <div className="text-red-600 dark:text-red-400 mb-1">
                  {error.message}
                </div>
                <div className="text-gray-600 dark:text-gray-400 text-xs">
                  ID: {errorId}
                </div>
    </div>
            </details>
          )}
          
          <div className="flex gap-2 justify-center">
            <Button
              onClick={resetError} size="sm"
              variant="outline"
              className="border-red-300 text-red-700 hover:bg-red-100 dark:border-red-600 dark:text-red-400 dark:hover:bg-red-900/30"
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Try Again
            </Button>
            
            {level === 'app' && (
              <Button
                onClick={() => window.location.href = '/'} size="sm"
                variant="outline"
              >
                <Home className="w-4 h-4 mr-2" />
                Go Home
              </Button>
            )}
            
            {level === 'page' && (
              <Button
                onClick={() => window.history.back()} size="sm"
                variant="outline"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Go Back
              </Button>
            )}
          </div>
    </CardContent>
      </Card>
    </div>
  );
};

class EnhancedErrorBoundary extends Component<EnhancedErrorBoundaryProps, ErrorBoundaryState> {
  private retryTimeoutId: NodeJS.Timeout | null = null;

  constructor(props: EnhancedErrorBoundaryProps) {
    super(props);
    
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: '',
      retryCount: 0
    };
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    return {
      hasError: true,
      error,
      errorId: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    const { onError, level = 'component', componentName, enableErrorReporting = true } = this.props;
    
    this.setState({ errorInfo });

    // Log the error
    if (enableErrorReporting) {
      ErrorLogger.log(error, errorInfo, {
        level,
        componentName,
        errorId: this.state.errorId,
        retryCount: this.state.retryCount
      });
    }

    // Call custom error handler
    onError?.(error, errorInfo);
  }, resetError = () => {
    const { maxRetries = 3 } = this.props;
    
    if (this.state.retryCount >= maxRetries) {
      console.warn(`Max retries (${maxRetries}) reached for error boundary`);
      return;
    }

    this.setState(prevState => ({
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: prevState.retryCount + 1
    }));
  };

  componentWillUnmount() {
    if (this.retryTimeoutId) {
      clearTimeout(this.retryTimeoutId);
    }
  }

  render() {
    const { 
      children, 
      fallback: CustomFallback;
      level = 'component',
      componentName,
      enableRetry = true
    } = this.props;
    
    const { hasError, error, errorInfo, retryCount, errorId } = this.state;

    if (hasError && error && errorInfo) {
      const FallbackComponent = CustomFallback || DefaultErrorFallback;
      
      return (
        <FallbackComponent
          error={error} errorInfo={errorInfo}, resetError={enableRetry ? this.resetError : () => {}}, retryCount={retryCount} level={level}, componentName={componentName} errorId={errorId}
        />
      );
    }

    return children;
  }
}

export default EnhancedErrorBoundary;
export { ErrorLogger, type ErrorFallbackProps };
