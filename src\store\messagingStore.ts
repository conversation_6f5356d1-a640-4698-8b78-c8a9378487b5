/**
 * Advanced Messaging Store
 * High-performance state management for Facebook-style messaging features
 */

import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import { persist, createJSONStorage } from 'zustand/middleware';
import { SecurityService } from '../services/messaging/SecurityService';
import { RateLimitingService } from '../services/messaging/RateLimitingService';
import { ContentSanitizer } from '../utils/contentSanitization';

import {
  AdvancedMessage,
  Message,
  MessageReaction,
  Conversation,
  UserPresence,
  PendingAction,
  MessagingPreferences,
  ConnectionStatus
} from '@/types/messaging';

import {
  createMessage,
  createReaction,
  addReactionToMessage,
  removeReactionFromMessage,
  updateConversationLastMessage,
  createPendingAction,
  shouldRetryAction
} from '@/utils/messagingUtils';

// Core messaging state interface
interface MessagingState {
  // Core data - optimized with Maps for O(1) lookups
  conversations: Map<string, Conversation>;
  messages: Map<string, AdvancedMessage[]>; // conversationId -> messages[]
  reactions: Map<string, MessageReaction[]>; // messageId -> reactions[]
  
  // Active conversation and UI state
  activeConversation: string | null;
  selectedMessages: Set<string>;
  
  // Real-time features
  typingUsers: Map<string, string[]>; // conversationId -> userIds[]
  userPresence: Map<string, UserPresence>; // userId -> presence
  connectionStatus: ConnectionStatus;
  
  // Message status and delivery
  messageStatus: Map<string, 'sending' | 'sent' | 'delivered' | 'read'>; // messageId -> status
  unreadCounts: Map<string, number>; // conversationId -> count
  lastReadTimestamps: Map<string, Date>; // conversationId -> timestamp
  
  // Offline and sync
  pendingActions: PendingAction[];
  syncInProgress: boolean;
  lastSyncTimestamp: Date | null;
  
  // User preferences
  preferences: MessagingPreferences;
  
  // Performance and caching
  loadingStates: Map<string, boolean>; // operation -> loading state
  errorStates: Map<string, string>; // operation -> error message
  messageCache: Map<string, AdvancedMessage>; // messageId -> message (for quick lookups)
}

// Messaging actions interface
interface MessagingActions {
  // Conversation management
  setActiveConversation: (conversationId: string | null) => void;
  addConversation: (conversation: Conversation) => void;
  updateConversation: (conversationId: string, updates: Partial<Conversation>) => void;
  removeConversation: (conversationId: string) => void;
  
  // Message operations
  addMessage: (message: AdvancedMessage) => void;
  updateMessage: (messageId: string, updates: Partial<AdvancedMessage>) => void;
  deleteMessage: (messageId: string) => void;
  sendMessage: (content: string, conversationId: string, type?: AdvancedMessage['type']) => Promise<void>;
  
  // Reaction operations
  addReaction: (messageId: string, reaction: MessageReaction) => void;
  removeReaction: (messageId: string, userId: string, emoji: string) => void;
  toggleReaction: (messageId: string, userId: string, emoji: string) => void;
  
  // Real-time features
  setTypingUsers: (conversationId: string, userIds: string[]) => void;
  updateUserPresence: (userId: string, presence: UserPresence) => void;
  setConnectionStatus: (status: ConnectionStatus) => void;
  
  // Message status and delivery
  updateMessageStatus: (messageId: string, status: 'sending' | 'sent' | 'delivered' | 'read') => void;
  markMessagesAsRead: (conversationId: string, messageIds: string[]) => void;
  updateUnreadCount: (conversationId: string, count: number) => void;
  
  // Selection and UI
  selectMessage: (messageId: string) => void;
  deselectMessage: (messageId: string) => void;
  clearSelection: () => void;
  
  // Offline and sync
  addPendingAction: (action: PendingAction) => void;
  removePendingAction: (actionId: string) => void;
  processPendingActions: () => void;
  setSyncInProgress: (inProgress: boolean) => void;
  updateLastSyncTimestamp: () => void;
  
  // Preferences
  updatePreferences: (updates: Partial<MessagingPreferences>) => void;
  
  // Performance and error handling
  setLoading: (operation: string, loading: boolean) => void;
  setError: (operation: string, error: string | null) => void;
  clearErrors: () => void;
  
  // Cache management
  clearMessageCache: () => void;
  pruneOldMessages: (olderThan: Date) => void;
  
  // Utility actions
  getConversationMessages: (conversationId: string) => AdvancedMessage[];
  getMessageReactions: (messageId: string) => MessageReaction[];
  getUserTypingStatus: (conversationId: string, userId: string) => boolean;
  getUnreadCount: (conversationId: string) => number;
}

// Default preferences
const defaultPreferences: MessagingPreferences = {
  enableNotifications: true;  enableSounds: true;  enableTypingIndicators: true;  enableReadReceipts: true;  enableReactions: true;  autoDownloadMedia: true;  theme: 'auto',
  fontSize: 'medium'
};

// Create the messaging store
export const useMessagingStore = create<MessagingState & MessagingActions>()(_subscribeWithSelector(
    persist(
      immer((set,get) => ({
        // Initial state
  conversations: new Map();  messages: new Map();  reactions: new Map();  activeConversation: null;  selectedMessages: new Set();  typingUsers: new Map();  userPresence: new Map();  connectionStatus: 'disconnected';  messageStatus: new Map();  unreadCounts: new Map();  lastReadTimestamps: new Map();  pendingActions: [];  syncInProgress: false;  lastSyncTimestamp: null;  preferences: defaultPreferences;  loadingStates: new Map();  errorStates: new Map();  messageCache: new Map();
        // Conversation management
        setActiveConversation: (_conversationId) => {
          set(_(state) => {
            state.activeConversation = conversationId;
            state.selectedMessages.clear();
            
            // Mark messages as read when opening conversation
            if (conversationId) {
              const messages = state.messages.get(conversationId) || [];
              const unreadMessages = messages.filter(msg => 
                !state.lastReadTimestamps.has(conversationId) ||
                new Date(msg.timestamp) > state.lastReadTimestamps.get(conversationId)!
              );
              
              if (unreadMessages.length > 0) {
                state.lastReadTimestamps.set(conversationId, new Date());
                state.unreadCounts.set(conversationId, 0);
              }
            }
          });
        },

        addConversation: (_conversation) => {
          set(_(state) => {
            state.conversations.set(conversation.id, conversation);
            if (!state.messages.has(conversation.id)) {
              state.messages.set(conversation.id, []);
            }
            state.unreadCounts.set(conversation.id, conversation.unreadCount);
          });
        },

        updateConversation: (_conversationId,updates) => {
          set(_(state) => {
            const conversation = state.conversations.get(conversationId);
            if (conversation) {
              const updatedConversation = { ...conversation, ...updates, updatedAt: Date.now() };
              state.conversations.set(conversationId, updatedConversation);
            }
          });
        },

        removeConversation: (_conversationId) => {
          set(_(state) => {
            state.conversations.delete(conversationId);
            state.messages.delete(conversationId);
            state.typingUsers.delete(conversationId);
            state.unreadCounts.delete(conversationId);
            state.lastReadTimestamps.delete(conversationId);
            
            if (state.activeConversation === conversationId) {
              state.activeConversation = null;
            }
          });
        },

        // Message operations
        addMessage: (_message) => {
          set(_(state) => {
            const conversationMessages = state.messages.get(message.conversationId) || [];
            const updatedMessages = [...conversationMessages, message];
            
            // Keep messages sorted by timestamp
            updatedMessages.sort(_(a,b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
            
            state.messages.set(message.conversationId, updatedMessages);
            state.messageCache.set(message.id, message);
            
            // Update conversation last message
            const conversation = state.conversations.get(message.conversationId);
            if (conversation) {
              const updatedConversation = updateConversationLastMessage(conversation, message);
              state.conversations.set(message.conversationId, updatedConversation);
            }
            
            // Update unread count if not active conversation
            if (state.activeConversation !== message.conversationId) {
              const currentCount = state.unreadCounts.get(message.conversationId) || 0;
              state.unreadCounts.set(message.conversationId, currentCount + 1);
            }
          });
        },

        updateMessage: (_messageId,updates) => {
          set(_(state) => {
            // Update in cache first
            const cachedMessage = state.messageCache.get(messageId);
            if (cachedMessage) {
              const updatedMessage = { ...cachedMessage, ...updates };
              state.messageCache.set(messageId, updatedMessage);
            }
            
            // Update in messages map
            for (const [conversationId, messages] of state.messages.entries()) {
              const messageIndex = messages.findIndex(m => m.id === messageId);
              if (messageIndex !== -1) {
                const updatedMessages = [...messages];
                updatedMessages[messageIndex] = { ...updatedMessages[messageIndex], ...updates };
                state.messages.set(conversationId, updatedMessages);
                break;
              }
            }
          });
        },

        deleteMessage: (_messageId) => {
          set(_(state) => {
            state.messageCache.delete(messageId);
            state.reactions.delete(messageId);
            state.messageStatus.delete(messageId);
            
            for (const [conversationId, messages] of state.messages.entries()) {
              const filteredMessages = messages.filter(m => m.id !== messageId);
              if (filteredMessages.length !== messages.length) {
                state.messages.set(conversationId, filteredMessages);
                break;
              }
            }
          });
        },

        sendMessage: async (_content,conversationId,type = 'text') => {
          const currentUserId = 'demo-user'; // This would come from auth store
          
          // Security validation before sending
          const securityService = SecurityService.getInstance();
          const rateLimitService = RateLimitingService.getInstance();
          
          // Check rate limiting
          const rateLimitStatus = rateLimitService.checkRateLimit(currentUserId, 'message');
          if (!rateLimitStatus.allowed) {
            get().setError('sendMessage', 'Rate limit exceeded. Please slow down.');
            return;
          }
          
          // Validate and sanitize content
          const validation = securityService.validateMessageContent(content, currentUserId);
          if (!validation.isValid) {
            get().setError('sendMessage', validation.errors.join(', '));
            return;
          }
          
          // Use sanitized content
          const sanitizedContent = validation.sanitizedContent || content;
          const finalContent = ContentSanitizer.sanitizeMessage(sanitizedContent).content;
          
          const message = createMessage({
            conversationId,
  content: finalContent;
            type
          }, currentUserId);

          // Add message optimistically
          get().addMessage(message);
          
          // Add to pending actions for real sending
          const pendingAction = createPendingAction('message:send', {
            ...message,
  id: undefined;  timestamp: undefined;
            status: undefined
          });
          
          get().addPendingAction(pendingAction);
        },

        // Reaction operations
        addReaction: (_messageId,reaction) => {
          set(_(state) => {
            // Add to reactions map
            const messageReactions = state.reactions.get(messageId) || [];
            const existingReactionIndex = messageReactions.findIndex(
              r => r.userId === reaction.userId && r.emoji === reaction.emoji
            );
            
            if (existingReactionIndex === -1) {
              state.reactions.set(messageId, [...messageReactions, reaction]);
              
              // Update message reactions
              const cachedMessage = state.messageCache.get(messageId);
              if (cachedMessage) {
                const updatedMessage = addReactionToMessage(cachedMessage, reaction);
                state.messageCache.set(messageId, updatedMessage);
                get().updateMessage(messageId, { reactions: updatedMessage.reactions });
              }
            }
          });
        },

        removeReaction: (_messageId,userId,emoji) => {
          set(_(state) => {
            // Remove from reactions map
            const messageReactions = state.reactions.get(messageId) || [];
            const filteredReactions = messageReactions.filter(
              r => !(r.userId === userId && r.emoji === emoji)
            );
            state.reactions.set(messageId, filteredReactions);
            
            // Update message reactions
            const cachedMessage = state.messageCache.get(messageId);
            if (cachedMessage) {
              const updatedMessage = removeReactionFromMessage(cachedMessage, userId, emoji);
              state.messageCache.set(messageId, updatedMessage);
              get().updateMessage(messageId, { reactions: updatedMessage.reactions });
            }
          });
        },

        toggleReaction: (_messageId,userId,emoji) => {
          const state = get();
          
          // Security validation for reactions
          const securityService = SecurityService.getInstance();
          const validation = securityService.validateReaction(emoji, userId);
          
          if (!validation.isValid) {
            state.setError('toggleReaction', validation.errors.join(', '));
            return;
          }
          
          const messageReactions = state.reactions.get(messageId) || [];
          const existingReaction = messageReactions.find(
            r => r.userId === userId && r.emoji === emoji
          );
          
          if (existingReaction) {
            state.removeReaction(messageId, userId, emoji);
          } else {
            const newReaction = createReaction(userId, emoji);
            state.addReaction(messageId, newReaction);
          }
        },

        // Real-time features
        setTypingUsers: (_conversationId,userIds) => {
          set(_(state) => {
            state.typingUsers.set(conversationId, userIds);
          });
        },

        updateUserPresence: (_userId,presence) => {
          set(_(state) => {
            state.userPresence.set(userId, presence);
          });
        },

        setConnectionStatus: (_status) => {
          set(_(state) => {
            state.connectionStatus = status;
          });
        },

        // Message status and delivery
        updateMessageStatus: (_messageId,status) => {
          set(_(state) => {
            state.messageStatus.set(messageId, status);
            get().updateMessage(messageId, { status });
          });
        },

        markMessagesAsRead: (_conversationId,messageIds) => {
          set(_(state) => {
            messageIds.forEach(messageId => {
              state.messageStatus.set(messageId, 'read');
              get().updateMessage(messageId, { status: 'read' });
            });
            
            state.lastReadTimestamps.set(conversationId, new Date());
            state.unreadCounts.set(conversationId, 0);
          });
        },

        updateUnreadCount: (_conversationId,count) => {
          set(_(state) => {
            state.unreadCounts.set(conversationId, count);
          });
        },

        // Selection and UI
        selectMessage: (_messageId) => {
          set(_(state) => {
            state.selectedMessages.add(messageId);
          });
        },

        deselectMessage: (_messageId) => {
          set(_(state) => {
            state.selectedMessages.delete(messageId);
          });
        },

        clearSelection: () => {
          set(_(state) => {
            state.selectedMessages.clear();
          });
        },

        // Offline and sync
        addPendingAction: (_action) => {
          set(_(state) => {
            state.pendingActions.push(action);
          });
        },

        removePendingAction: (_actionId) => {
          set(_(state) => {
            state.pendingActions = state.pendingActions.filter(a => a.id !== actionId);
          });
        },

        processPendingActions: () => {
          const state = get();
          const actionsToProcess = state.pendingActions.filter(shouldRetryAction);
          
          // This would be handled by the real-time service in practice
          actionsToProcess.forEach(action => {
            console.log('Processing pending action:', action);
            // Real implementation would send to WebSocket service
          });
        },

        setSyncInProgress: (_inProgress) => {
          set(_(state) => {
            state.syncInProgress = inProgress;
          });
        },

        updateLastSyncTimestamp: () => {
          set(_(state) => {
            state.lastSyncTimestamp = new Date();
          });
        },

        // Preferences
        updatePreferences: (_updates) => {
          set(_(state) => {
            state.preferences = { ...state.preferences, ...updates };
          });
        },

        // Performance and error handling
        setLoading: (_operation,loading) => {
          set(_(state) => {
            state.loadingStates.set(operation, loading);
          });
        },

        setError: (_operation,error) => {
          set(_(state) => {
            if (error) {
              state.errorStates.set(operation, error);
            } else {
              state.errorStates.delete(operation);
            }
          });
        },

        clearErrors: () => {
          set(_(state) => {
            state.errorStates.clear();
          });
        },

        // Cache management
        clearMessageCache: () => {
          set(_(state) => {
            state.messageCache.clear();
          });
        },

        pruneOldMessages: (_olderThan) => {
          set(_(state) => {
            for (const [conversationId, messages] of state.messages.entries()) {
              const filteredMessages = messages.filter(
                message => new Date(message.timestamp) > olderThan
              );
              state.messages.set(conversationId, filteredMessages);
            }
          });
        },

        // Utility actions
        getConversationMessages: (_conversationId) => {
          return get().messages.get(conversationId) || [];
        },

        getMessageReactions: (_messageId) => {
          return get().reactions.get(messageId) || [];
        },

        getUserTypingStatus: (_conversationId,userId) => {
          const typingUsers = get().typingUsers.get(conversationId) || [];
          return typingUsers.includes(userId);
        },

        getUnreadCount: (_conversationId) => {
          return get().unreadCounts.get(conversationId) || 0;
        }
      })),
      {
  name: 'messaging-storage';  storage: createJSONStorage(() => localStorage);  partialize: (_state) => ({
          conversations: Array.from(state.conversations.entries());  messages: Array.from(state.messages.entries());  preferences: state.preferences;  lastReadTimestamps: Array.from(state.lastReadTimestamps.entries());  lastSyncTimestamp: state.lastSyncTimestamp;
        }),
        onRehydrateStorage: () => (_state) => {
          if (state) {
            // Convert arrays back to Maps after rehydration
            state.conversations = new Map(state.conversations as [string, Conversation][]);
            state.messages = new Map(state.messages as [string, AdvancedMessage[]][]);
            state.lastReadTimestamps = new Map(state.lastReadTimestamps as [string, number][]);
            
            // Initialize non-persisted Maps
            state.reactions = new Map();
            state.typingUsers = new Map();
            state.userPresence = new Map();
            state.messageStatus = new Map();
            state.unreadCounts = new Map();
            state.loadingStates = new Map();
            state.errorStates = new Map();
            state.messageCache = new Map();
            state.selectedMessages = new Set();
            state.pendingActions = [];
            state.connectionStatus = 'disconnected';
            state.syncInProgress = false;
          }
        }
      }
    )
  )
);

// Optimized selectors for performance
export const messagingSelectors = {
  activeConversation: (state: ReturnType<typeof useMessagingStore.getState>) => state.activeConversation;  conversations: (state: ReturnType<typeof useMessagingStore.getState>) => Array.from(state.conversations.values());  connectionStatus: (state: ReturnType<typeof useMessagingStore.getState>) => state.connectionStatus;  preferences: (state: ReturnType<typeof useMessagingStore.getState>) => state.preferences;  selectedMessages: (state: ReturnType<typeof useMessagingStore.getState>) => Array.from(state.selectedMessages);  syncInProgress: (state: ReturnType<typeof useMessagingStore.getState>) => state.syncInProgress;
};

// Optimized hooks for common use cases
export const _useOptimizedMessaging = () => {
  const activeConversation = useMessagingStore(messagingSelectors.activeConversation);
  const conversations = useMessagingStore(messagingSelectors.conversations);
  const connectionStatus = useMessagingStore(messagingSelectors.connectionStatus);
  const {
    setActiveConversation,
    sendMessage,
    addReaction;
    removeReaction
}
    toggleReaction;
  } = useMessagingStore();

  return { activeConversation,
    conversations,
    connectionStatus,
    setActiveConversation,
    sendMessage,
    addReaction,
    removeReaction,
    toggleReaction };
};

export const _useConversationMessages = (conversationId: string | null) => {
  return useMessagingStore(_(state) => 
    conversationId ? state.messages.get(conversationId) || [] : []
  );
};

export const _useMessageReactions = (messageId: string) => {
  return useMessagingStore(_(state) => 
    state.reactions.get(messageId) || []
  );
};

export const _useTypingIndicators = (conversationId: string | null) => {
  return useMessagingStore(_(state) => 
    conversationId ? state.typingUsers.get(conversationId) || [] : []
  );
};

export const _useUnreadCount = (conversationId: string) => {
  return useMessagingStore(_(state) => 
    state.unreadCounts.get(conversationId) || 0
  );
};

export default useMessagingStore;