#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

class FinalSyntaxFixer {
  constructor() {
    this.fixedFiles = 0;
    this.fixedErrors = 0;
  }

  fixSyntaxIssues(content) {
    let modified = false;
    let fixes = 0;

    // Fix JSX fragment issues - missing parent element
    if (content.includes('JSX expressions must have one parent element')) {
      content = content.replace(/return\s*\(\s*\n([\s\S]*?)\n\s*\)/g, (match, body) => {
        const lines = body.split('\n');
        const nonEmptyLines = lines.filter(line => line.trim() && !line.trim().startsWith('//'));
        if (nonEmptyLines.length > 1) {
          fixes++;
          modified = true;
          return `return (\n    <>\n${body}\n    </>\n  )`;
        }
        return match;
      });
    }

    // Fix object destructuring in parameters
    content = content.replace(
      /(\w+)\s*:\s*\{\s*([^}]+)\s*\}\s*,?\s*\)/g,
      (match, paramName, types) => {
        fixes++;
        modified = true;
        return `${paramName}: { ${types.trim()} })`;
      }
    );

    // Fix variable declarations with trailing commas
    content = content.replace(
      /^(\s*)(const|let|var)\s+([^=]+),\s*$/gm,
      (match, indent, keyword, declaration) => {
        fixes++;
        modified = true;
        return `${indent}${keyword} ${declaration.trim()};`;
      }
    );

    // Fix array declarations with trailing commas
    content = content.replace(
      /\[\s*([^[\]]*),\s*\]/g,
      (match, content_inner) => {
        fixes++;
        modified = true;
        return `[${content_inner.trim()}]`;
      }
    );

    // Fix object literals with trailing commas before closing brace
    content = content.replace(
      /,(\s*)\}/g,
      (match, whitespace) => {
        fixes++;
        modified = true;
        return `${whitespace}}`;
      }
    );

    // Fix function calls with trailing commas
    content = content.replace(
      /(\w+)\s*\(\s*([^)]*),\s*\)/g,
      (match, funcName, args) => {
        if (args.trim()) {
          fixes++;
          modified = true;
          return `${funcName}(${args.trim()})`;
        }
        return match;
      }
    );

    // Fix type annotations with trailing commas
    content = content.replace(
      /:\s*([^,;{}]+),\s*;/g,
      (match, type) => {
        fixes++;
        modified = true;
        return `: ${type.trim()};`;
      }
    );

    // Fix JSX closing tags alignment issues
    content = content.replace(
      />\s*\n\s*<\/([a-zA-Z][a-zA-Z0-9]*)\s*>\s*$/gm,
      (match, tagName) => {
        fixes++;
        modified = true;
        return `>\n    </${tagName}>`;
      }
    );

    // Fix broken JSX prop syntax where identifier is expected
    content = content.replace(
      /(\w+)=\{([^}]*)\}\s+(\w+)(?!=)/g,
      (match, prop1, value1, prop2) => {
        fixes++;
        modified = true;
        return `${prop1}={${value1}}, ${prop2}`;
      }
    );

    return { content, modified, fixes };
  }

  fixFile(filePath) {
    try {
      const originalContent = fs.readFileSync(filePath, 'utf8');
      const { content: newContent, modified, fixes } = this.fixSyntaxIssues(originalContent);

      if (modified) {
        fs.writeFileSync(filePath, newContent, 'utf8');
        this.fixedFiles++;
        this.fixedErrors += fixes;
        console.log(`✅ Fixed ${fixes} final syntax issues in ${path.basename(filePath)}`);
        return true;
      }

      return false;
    } catch (error) {
      console.error(`❌ Error fixing ${filePath}:`, error.message);
      return false;
    }
  }

  processDirectory(dirPath) {
    const entries = fs.readdirSync(dirPath, { withFileTypes: true });

    for (const entry of entries) {
      const fullPath = path.join(dirPath, entry.name);

      if (entry.isDirectory() && !entry.name.startsWith('.') && entry.name !== 'node_modules') {
        this.processDirectory(fullPath);
      } else if (entry.isFile() && /\.(ts|tsx|js|jsx)$/.test(entry.name)) {
        this.fixFile(fullPath);
      }
    }
  }

  run() {
    console.log('🔧 Starting final syntax fixing pass...\n');

    const srcPath = path.join(process.cwd(), 'src');
    if (fs.existsSync(srcPath)) {
      this.processDirectory(srcPath);
    }

    console.log('\n📊 Final Syntax Fix Summary:');
    console.log(`✅ Files fixed: ${this.fixedFiles}`);
    console.log(`🔧 Syntax errors fixed: ${this.fixedErrors}`);
    console.log('\n🎉 Final syntax fixing complete!');

    return {
      filesFixed: this.fixedFiles,
      errorsFixed: this.fixedErrors
    };
  }
}

// Run the fixer
const fixer = new FinalSyntaxFixer();
fixer.run();
