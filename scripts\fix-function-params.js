#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

function fixFunctionParameters(content) {
  let modified = false;
  let result = content;
  
  // Fix 1: setTimeout(resolve; 2000) -> setTimeout(resolve, 2000)
  const timeoutPattern = /setTimeout\(([^,)]+);\s*(\d+)\)/g;
  const newResult1 = result.replace(timeoutPattern, 'setTimeout($1, $2)');
  if (newResult1 !== result) {
    result = newResult1;
    modified = true;
  }
  
  // Fix 2: setInterval(callback; 1000) -> setInterval(callback, 1000)
  const intervalPattern = /setInterval\(([^,)]+);\s*(\d+)\)/g;
  const newResult2 = result.replace(intervalPattern, 'setInterval($1, $2)');
  if (newResult2 !== result) {
    result = newResult2;
    modified = true;
  }
  
  // Fix 3: Promise resolve; -> Promise resolve,
  const promiseResolvePattern = /resolve\s*;\s*(\d+)/g;
  const newResult3 = result.replace(promiseResolvePattern, 'resolve, $1');
  if (newResult3 !== result) {
    result = newResult3;
    modified = true;
  }
  
  // Fix 4: Function calls with semicolons instead of commas
  const functionCallPattern = /(\w+)\(([^,)]+);\s*([^)]+)\)/g;
  const newResult4 = result.replace(functionCallPattern, '$1($2, $3)');
  if (newResult4 !== result) {
    result = newResult4;
    modified = true;
  }
  
  // Fix 5: Array/object destructuring with semicolons
  const destructuringPattern = /\{([^}]+);\s*([^}]+)\}/g;
  const newResult5 = result.replace(destructuringPattern, '{$1, $2}');
  if (newResult5 !== result) {
    result = newResult5;
    modified = true;
  }
  
  return { content: result, modified };
}

function fixFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const { content: fixedContent, modified } = fixFunctionParameters(content);
    
    if (modified) {
      fs.writeFileSync(filePath, fixedContent, 'utf8');
      console.log(`Fixed: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`Error fixing ${filePath}:`, error.message);
    return false;
  }
}

function walkDirectory(dir, extensions = ['.tsx', '.ts', '.jsx', '.js']) {
  const files = [];
  
  function walk(currentDir) {
    try {
      const items = fs.readdirSync(currentDir);
      
      for (const item of items) {
        const fullPath = path.join(currentDir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          walk(fullPath);
        } else if (stat.isFile() && extensions.some(ext => item.endsWith(ext))) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      console.error(`Error reading directory ${currentDir}:`, error.message);
    }
  }
  
  walk(dir);
  return files;
}

function main() {
  const srcDir = path.join(__dirname, '..', 'src');
  const files = walkDirectory(srcDir);
  
  console.log(`Found ${files.length} files to check for function parameter errors...`);
  
  let fixedCount = 0;
  
  files.forEach(file => {
    if (fixFile(file)) {
      fixedCount++;
    }
  });
  
  console.log(`\nFixed function parameters in ${fixedCount} files out of ${files.length} total files.`);
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}
