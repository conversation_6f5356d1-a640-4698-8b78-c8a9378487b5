// File Attachment Display Component
import React, { useState, useCallback, memo } from 'react';
import {
  File,
  Image as ImageIcon,
  Video,
  Music,
  FileText,
  Download,
  Eye,
  Play,
  Share2,
  ExternalLink,
  Shield,
  Clock
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { MessageAttachment } from '@/types/enhanced-messaging';
import { useMessageFileService } from '@/hooks/useMessageFileService';
import { cn } from '@/lib/utils';

// Import proper service types
import { FileUploadService } from '@/services/FileUploadService';
import { EnhancedMessagingService } from '@/services/EnhancedMessagingService';
import { CryptoService } from '@/services/CryptoService';
import { WebSocketManager } from '@/services/WebSocketManager';

interface FileAttachmentProps {
  attachment: MessageAttachment;
  conversationId?: string;
  showMetadata?: boolean;
  className?: string;
  services: {
    fileUploadService: FileUploadService, messagingService: EnhancedMessagingService, cryptoService: CryptoService, webSocketManager: WebSocketManager;
  };
}

const FileAttachment: React.FC<FileAttachmentProps> = ({
  attachment,
  conversationId,
  showMetadata = false,
  className,
  services
}) => {
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);

  const fileService = useMessageFileService(
    services.fileUploadService,
    services.messagingService,
    services.cryptoService,
    services.webSocketManager
  );

  // Get file icon based on type
  const getFileIcon = () => {
    switch (attachment.type) {
      case 'image':
        return <ImageIcon className="w-5 h-5" />;
      case 'video':
        return <Video className="w-5 h-5" />;
      case 'audio':
        return <Music className="w-5 h-5" />;
      default:
        if (attachment.mimeType?.includes('pdf') || 
            attachment.mimeType?.includes('document') || 
            attachment.mimeType?.includes('text')) {
          return <FileText className="w-5 h-5" />;
        }
        return <File className="w-5 h-5" />;
    }
  };

  // Format file size
  const formatFileSize = (bytes: number) => {
    const sizes = ['B', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 B';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${Math.round(bytes / Math.pow(1024, i) * 100) / 100} ${sizes[i]}`;
  };

  // Format duration for media files
  const formatDuration = (duration: number) => {
    const minutes = Math.floor(duration / 60);
    const seconds = Math.floor(duration % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  // Handle download
  const handleDownload = useCallback(async () => {
    try {
      setIsDownloading(true);
      await fileService.saveFile(attachment, conversationId);
    } catch (error) {
      console.error('Failed to download file:', error);
    } finally {
      setIsDownloading(false);
    }
  }, [fileService, attachment, conversationId]);

  // Handle preview
  const handlePreview = useCallback(() => {
    if (attachment.type === 'image' || attachment.type === 'video') {
      setIsPreviewOpen(true);
    } else {
      // For other file types, open in new tab
      window.open(attachment.url, '_blank');
    }
  }, [attachment]);

  // Handle share
  const handleShare = useCallback(async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: attachment.name,
          url: attachment.url
        });
      } catch {
        // Fallback to clipboard
        navigator.clipboard.writeText(attachment.url);
      }
    } else {
      navigator.clipboard.writeText(attachment.url);
    }
  }, [attachment]);

  const isImage = attachment.type === 'image';
  const isVideo = attachment.type === 'video';
  const isAudio = attachment.type === 'audio';
  const isEncrypted = attachment.secureMetadata?.isEncrypted;

  return (
    <>
      <Card className={cn("max-w-sm overflow-hidden", className)}>
        <CardContent className="p-0">
          {/* Media Preview */}
          {isImage && attachment.thumbnail ? (
            <div className="relative group">
              <img
                src={attachment.thumbnail} alt={attachment.name}, className="w-full h-48 object-cover"
              />
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 flex items-center justify-center">
                <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex gap-2">
                  <Button
                    variant="secondary"
                    size="sm"
                    onClick={handlePreview} className="bg-white bg-opacity-90 hover:bg-opacity-100"
                  >
                    <Eye className="w-4 h-4" />
    </Button>
                  <Button
                    variant="secondary"
                    size="sm"
                    onClick={handleDownload} disabled={isDownloading}, className="bg-white bg-opacity-90 hover:bg-opacity-100"
                  >
                    <Download className="w-4 h-4" />
    </Button>
                </div>
    </div>
              {isEncrypted && (
                <Badge variant="secondary" className="absolute top-2 left-2">
                  <Shield className="w-3 h-3 mr-1" />
                  Encrypted
                </Badge>
              )}
              {attachment.duration && (
                <Badge variant="secondary" className="absolute bottom-2 right-2">
                  {formatDuration(attachment.duration)}
                </Badge>
              )}
            </div>
          ) : isVideo && attachment.thumbnail ? (
            <div className="relative group">
              <img
                src={attachment.thumbnail} alt={attachment.name}, className="w-full h-48 object-cover"
              />
              <div className="absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center">
                <Button
                  variant="secondary"
                  size="lg"
                  onClick={handlePreview} className="bg-white bg-opacity-90 hover:bg-opacity-100"
                >
                  <Play className="w-6 h-6" />
    </Button>
              </div>
              {isEncrypted && (
                <Badge variant="secondary" className="absolute top-2 left-2">
                  <Shield className="w-3 h-3 mr-1" />
                  Encrypted
                </Badge>
              )}
              {attachment.duration && (
                <Badge variant="secondary" className="absolute bottom-2 right-2">
                  {formatDuration(attachment.duration)}
                </Badge>
              )}
            </div>
          ) : (
            /* File Icon Display */
            <div className="p-6 flex flex-col items-center justify-center bg-gray-50 dark:bg-gray-800">
              <div className="w-16 h-16 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center mb-3">
                {getFileIcon()}
              </div>
              {isEncrypted && (
                <Badge variant="secondary" className="mb-2">
                  <Shield className="w-3 h-3 mr-1" />
                  Encrypted
                </Badge>
              )}
            </div>
          )}

          {/* File Info */}
          <div className="p-4">
            <div className="flex items-start justify-between">
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                  {attachment.name}
                </p>
                <div className="flex items-center space-x-2 mt-1">
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {formatFileSize(attachment.size)}
                  </p>
                  {attachment.duration && !isImage && (
                    <>
                      <span className="text-gray-300">•</span>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {formatDuration(attachment.duration)}
                      </p>
                    </>
                  )}
                  {attachment.dimensions && (
                    <>
                      <span className="text-gray-300">•</span>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {attachment.dimensions.width}×{attachment.dimensions.height}
                      </p>
                    </>
                  )}
                </div>
                
                {showMetadata && attachment.secureMetadata && (
                  <div className="mt-2 space-y-1">
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Uploaded: {attachment.secureMetadata.uploadedAt.toLocaleString()}
                    </p>
                    {attachment.secureMetadata.checksum && (
                      <p className="text-xs text-gray-500 dark:text-gray-400 font-mono">
                        SHA256: {attachment.secureMetadata.checksum.slice(0, 16)}...
                      </p>
                    )}
                  </div>
                )}
              </div>
    </div>
            {/* Actions */}
            <div className="flex items-center space-x-2 mt-3">
              {!isImage && !isVideo && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handlePreview} className="flex-1"
                >
                  <ExternalLink className="w-3 h-3 mr-1" />
                  Open
                </Button>
              )}
              
              <Button
                variant="outline"
                size="sm"
                onClick={handleDownload} disabled={isDownloading}, className={isImage || isVideo ? "flex-1" : ""}
              >
                {isDownloading ? (
                  <Clock className="w-3 h-3 mr-1 animate-spin" />
                ) : (
                  <Download className="w-3 h-3 mr-1" />
                )}
                Download
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={handleShare}
              >
                <Share2 className="w-3 h-3" />
    </Button>
            </div>

            {/* Audio Player for Audio Files */}
            {isAudio && (
              <div className="mt-3">
                <audio
                  controls
                  className="w-full"
                  preload="metadata"
                >
                  <source src={attachment.url} type={attachment.mimeType} />
                  Your browser does not support the audio element.
                </audio>
    </div>
            )}
          </div>
    </CardContent>
      </Card>

      {/* Preview Dialog */}
      <Dialog open={isPreviewOpen} onOpenChange={setIsPreviewOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              {getFileIcon()}
              {attachment.name}
            </DialogTitle>
    </DialogHeader>
          <div className="flex items-center justify-center p-4">
            {isImage ? (
              <img
                src={attachment.url} alt={attachment.name}, className="max-w-full max-h-[70vh] object-contain"
              />
            ) : isVideo ? (
              <video
                controls
                className="max-w-full max-h-[70vh]"
              >
                <source src={attachment.url} type={attachment.mimeType} />
                Your browser does not support the video element.
              </video>
            ) : (
              <div className="text-center">
                <p className="text-gray-500 dark:text-gray-400">
                  Preview not available for this file type
                </p>
                <Button
                  variant="outline"
                  onClick={handleDownload} className="mt-4"
                >
                  <Download className="w-4 h-4 mr-2" />
                  Download to view
                </Button>
    </div>
            )}
          </div>
    </DialogContent>
      </Dialog>
    </>
  );
};

export default memo(FileAttachment);
