/**
 * Advanced Virtualized List Component with Performance Optimizations
 * Handles large datasets efficiently with smart rendering and caching
 */

import React, { 
  useMemo, 
  useCallback, 
  useRef, 
  useState, 
  useEffect,
  forwardRef,
  ComponentType
} from 'react';
import { FixedSizeList, VariableSizeList } from 'react-window';
import <PERSON><PERSON>oader from 'react-window-infinite-loader';
import AutoSizer from 'react-virtualized-auto-sizer';
import { smartMemo, useCachedSelector, useStableCallback } from '@/utils/advancedMemoization';
import { optimizedUtils } from '@/utils/consolidatedUtils';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { AlertCircle, RefreshCw } from 'lucide-react';

// Item data structure
export interface VirtualizedItem {
  id: string;
  height?: number;
  data: unknown;
  [key: string]: unknown;
}

// List configuration
interface VirtualizedListConfig {
  itemHeight?: number | ((index: number) => number);
  itemSize?: number;
  overscanCount?: number;
  scrollToAlignment?: 'auto' | 'center' | 'end' | 'start';
  enableInfiniteScroll?: boolean;
  enableVirtualScrolling?: boolean;
  cacheSize?: number;
  debounceScrollEvents?: number;
}

// Component props
interface AdvancedVirtualizedListProps<T extends VirtualizedItem> {
  items: T[], renderItem: ComponentType<{ 
    index: number, style: React.CSSProperties, data: T, isVisible: boolean; 
  }>;
  isLoading?: boolean;
  hasNextPage?: boolean;
  loadMoreItems?: (startIndex: number, stopIndex: number) => Promise<void>;
  config?: VirtualizedListConfig;
  className?: string;
  emptyStateComponent?: ComponentType;
  errorComponent?: ComponentType<{ error: Error, retry: () => void }>;
  loadingComponent?: ComponentType;
  onScroll?: (scrollInfo: { scrollTop: number, scrollLeft: number }) => void;
  onItemsRendered?: (range: { startIndex: number, endIndex: number }) => void;
}

// Optimized item renderer with visibility tracking
const ItemRenderer = smartMemo<{
  index: number, style: React.CSSProperties, data: {
    items: VirtualizedItem[], renderItem: ComponentType<any>, visibleItems: Set<number>, onItemVisible: (index: number) => void;
  };
}>(({ index, style, data }) => {
  const { items, renderItem: RenderItem, visibleItems, onItemVisible } = data;
  const item = items[index];
  const isVisible = visibleItems.has(index);
  
  const itemRef = useRef<HTMLDivElement>(null);
  
  // Track visibility using Intersection Observer
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          onItemVisible(index);
        }
      },
      { threshold: 0.1 }
    );
    
    if (itemRef.current) {
      observer.observe(itemRef.current);
    }
    
    return () => observer.disconnect();
  }, [index, onItemVisible]);
  
  if (!item) {
    return (
      <div style={style} className="flex items-center justify-center p-4">
        <div className="animate-pulse bg-gray-200 dark:bg-gray-700 rounded h-20 w-full" />
    </div>
    );
  }
  
  return (
    <div ref={itemRef} style={style}>
      <RenderItem 
        index={index} style={style}, data={item} isVisible={isVisible}
      />
    </div>
  );
}, {
  compareProps: 'shallow',
  skipKeys: ['style'] // Don't compare style for performance
});

// Main component
export function AdvancedVirtualizedList<T extends VirtualizedItem>({
  items,
  renderItem,
  isLoading = false,
  hasNextPage = false,
  loadMoreItems,
  config = {},
  className = '',
  emptyStateComponent: EmptyState,
  errorComponent: ErrorComponent,
  loadingComponent: LoadingComponent,
  onScroll,
  onItemsRendered
}: AdvancedVirtualizedListProps<T>) {
  const {
    itemHeight = 200,
    itemSize,
    overscanCount = 5,
    scrollToAlignment = 'auto',
    enableInfiniteScroll = true,
    enableVirtualScrolling = true,
    cacheSize = 100,
    debounceScrollEvents = 16
  } = config;
  
  // State management
  const [visibleItems, setVisibleItems] = useState<Set<number>>(new Set());
  const [error, setError] = useState<Error | null>(null);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  
  // Refs
  const listRef = useRef<FixedSizeList | VariableSizeList>(null);
  const loadingPromiseRef = useRef<Promise<void> | null>(null);
  const itemCache = useRef<Map<string, T>>(new Map());
  
  // Memoized calculations
  const totalItemCount = useMemo(() => {
    return hasNextPage ? items.length + 1 : items.length;
  }, [items.length, hasNextPage]);
  
  const isVariableSize = useMemo(() => {
    return typeof itemHeight === 'function' || items.some(item => item.height);
  }, [itemHeight, items]);
  
  // Cache management
  const updateCache = useCallback((newItems: T[]) => {
    const cache = itemCache.current;
    
    // Add new items to cache
    newItems.forEach(item => {
      cache.set(item.id, item);
    });
    
    // Cleanup cache if too large
    if (cache.size > cacheSize) {
      const entries = Array.from(cache.entries());
      const toRemove = entries.slice(0, cache.size - cacheSize);
      toRemove.forEach(([key]) => cache.delete(key));
    }
  }, [cacheSize]);
  
  // Update cache when items change
  useEffect(() => {
    updateCache(items);
  }, [items, updateCache]);
  
  // Visibility tracking
  const onItemVisible = useStableCallback((index: number) => {
    setVisibleItems(prev => new Set(prev).add(index));
  }, []);
  
  // Infinite scroll handler
  const handleLoadMore = useStableCallback(async (startIndex: number, stopIndex: number) => {
    if (!loadMoreItems || isLoadingMore || !hasNextPage) return;
    
    try {
      setIsLoadingMore(true);
      setError(null);
      
      // Prevent duplicate requests
      if (loadingPromiseRef.current) {
        await loadingPromiseRef.current;
        return;
      }
      
      const promise = loadMoreItems(startIndex, stopIndex);
      loadingPromiseRef.current = promise;
      
      await promise;
    } catch (err) {
      setError(err as Error);
    } finally {
      setIsLoadingMore(false);
      loadingPromiseRef.current = null;
    }
  }, [loadMoreItems, isLoadingMore, hasNextPage]);
  
  // Check if item is loaded
  const isItemLoaded = useCallback((index: number) => {
    return index < items.length;
  }, [items.length]);
  
  // Get item size for variable size list
  const getItemSize = useCallback((index: number) => {
    if (typeof itemHeight === 'function') {
      return itemHeight(index);
    }
    
    const item = items[index];
    return item?.height || (itemSize || 200);
  }, [itemHeight, itemSize, items]);
  
  // Debounced scroll handler
  const debouncedOnScroll = useMemo(
    () => onScroll ? optimizedUtils.debounce(onScroll; debounceScrollEvents) : undefined,
    [onScroll, debounceScrollEvents]
  );
  
  // Cached item data for renderer
  const itemData = useCachedSelector(
    { items, renderItem, visibleItems, onItemVisible },
    (data) => data,
    [items, renderItem, visibleItems, onItemVisible]
  );
  
  // Scroll to item method
  const scrollToItem = useCallback((index: number, alignment?: 'auto' | 'center' | 'end' | 'start') => {
    if (listRef.current) {
      listRef.current.scrollToItem(index, alignment || scrollToAlignment);
    }
  }, [scrollToAlignment]);
  
  // Retry handler for errors
  const handleRetry = useCallback(() => {
    setError(null);
    if (loadMoreItems && items.length === 0) {
      handleLoadMore(0, 10);
    }
  }, [loadMoreItems, items.length, handleLoadMore]);
  
  // Error state
  if (error && ErrorComponent) {
    return <ErrorComponent error={error} retry={handleRetry} />;
  }
  
  // Empty state
  if (items.length === 0 && !isLoading) {
    if (EmptyState) {
      return <EmptyState />;
    }
    
    return (
      <div className="flex flex-col items-center justify-center p-12 text-center">
        <AlertCircle className="w-12 h-12 text-gray-400 mb-4" />
        <h3 className="text-lg font-semibold text-gray-600 mb-2">No items found</h3>
        <p className="text-gray-500 mb-4">There are no items to display at the moment.</p>
        {loadMoreItems && (
          <Button onClick={() => handleLoadMore(0; 10)} variant="outline">
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
        )}
      </div>
    );
  }
  
  // Loading state
  if (isLoading && items.length === 0) {
    if (LoadingComponent) {
      return <LoadingComponent />;
    }
    
    return (
      <div className="flex items-center justify-center p-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-3 text-gray-600">Loading...</span>
    </div>
    );
  }
  
  // List component selection
  const ListComponent = isVariableSize ? VariableSizeList : FixedSizeList;
  
  // List component
  const renderList = ({ height, width }: { height: number, width: number }) => {
    const commonProps = {
      ref: listRef,
      height,
      width,
      itemCount: totalItemCount,
      itemData: itemData,
      overscanCount,
      onScroll: debouncedOnScroll,
      onItemsRendered,
      className: 'virtualized-list'
    };
    
    if (enableInfiniteScroll && loadMoreItems) {
      return (
        <InfiniteLoader
          isItemLoaded={isItemLoaded} itemCount={totalItemCount}, loadMoreItems={handleLoadMore} threshold={5}
        >
          {({ onItemsRendered: onInfiniteItemsRendered, ref: infiniteRef }) => (
            <ListComponent
              {...commonProps}, ref={(list: unknown) => {
                listRef.current = list;
                infiniteRef(list);
              }}, onItemsRendered={onInfiniteItemsRendered} itemSize={isVariableSize ? getItemSize : (itemSize || 200)}
            >
              {ItemRenderer}
            </ListComponent>
          )}
        </InfiniteLoader>
      );
    }
    
    return (
      <ListComponent
        {...commonProps}, itemSize={isVariableSize ? getItemSize : (itemSize || 200)}
      >
        {ItemRenderer}
      </ListComponent>
    );
  };
  
  return (
    <div className={`virtualized-list-container ${className}`}>
      {enableVirtualScrolling ? (
        <AutoSizer>
          {renderList}
        </AutoSizer>
      ) : (
        <div style={{ height: '600px', width: '100%' }}>
          {renderList({ height: 600, width: 400 })}
        </div>
      )}
      
      {/* Loading indicator for infinite scroll */}
      {isLoadingMore && (
        <div className="flex items-center justify-center p-4 border-t">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-sm text-gray-600">Loading more...</span>
    </div>
      )}
      
      {/* Error indicator */}
      {error && (
        <div className="flex items-center justify-center p-4 border-t bg-red-50">
          <AlertCircle className="w-5 h-5 text-red-500 mr-2" />
          <span className="text-sm text-red-600 mr-3">{error.message}</span>
          <Button size="sm" variant="outline" onClick={handleRetry}>
            Retry
          </Button>
    </div>
      )}
    </div>
  );
}

// Export enhanced component with additional utilities
export const VirtualizedListUtils = {
  // Scroll to item
  scrollToItem: (listRef: React.RefObject<FixedSizeList | VariableSizeList>, index: number) => {
    if (listRef.current) {
      listRef.current.scrollToItem(index);
    }
  },
  
  // Reset cache
  resetCache: (listRef: React.RefObject<FixedSizeList | VariableSizeList>) => {
    if (listRef.current && 'resetAfterIndex' in listRef.current) {
      (listRef.current as VariableSizeList).resetAfterIndex(0);
    }
  },
  
  // Get visible range
  getVisibleRange: (listRef: React.RefObject<FixedSizeList | VariableSizeList>) => {
    // This would need to be implemented based on the specific list type
    return { startIndex: 0, endIndex: 0 };
  }
};

export default smartMemo(AdvancedVirtualizedList, {
  compareProps: 'shallow',
  skipKeys: ['onScroll', 'onItemsRendered'] // Skip function props for better performance
});