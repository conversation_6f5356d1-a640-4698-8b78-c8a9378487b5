/**
 * Offline Indicator Component
 * Shows offline status and sync information
 */

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import {
  WifiOff,
  Wifi,
  RefreshCw,
  Clock,
  Database,
  Download,
  Upload,
  Trash2,
  Info,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { formatDistanceToNow } from 'date-fns';

interface OfflineIndicatorProps {
  isOnline: boolean, isSyncing: boolean, lastSyncTime: Date | null, pendingActionsCount: number, onSyncNow: () => void; onClearData: () => void; onExportData: () => void;
  storageStats?: {
    totalSize: number, messageCount: number, conversationCount: number, pendingActionCount: number;
  };
  className?: string;
  variant?: 'compact' | 'detailed';
}

export const OfflineIndicator: React.FC<OfflineIndicatorProps> = ({
  isOnline,
  isSyncing,
  lastSyncTime,
  pendingActionsCount,
  onSyncNow,
  onClearData,
  onExportData,
  storageStats,
  className,
  variant = 'compact'
}) => {
  const [showDetails, setShowDetails] = useState(false);

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getStatusColor = () => {
    if (!isOnline) return 'text-red-500';
    if (isSyncing) return 'text-yellow-500';
    if (pendingActionsCount > 0) return 'text-orange-500';
    return 'text-green-500';
  };

  const getStatusIcon = () => {
    if (!isOnline) return WifiOff;
    if (isSyncing) return RefreshCw;
    return Wifi;
  };

  const StatusIcon = getStatusIcon();

  if (variant === 'compact') {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}, animate={{ opacity: 1, scale: 1 }}, className={cn("flex items-center space-x-2", className)}
      >
        <StatusIcon 
          className={cn(
            "w-4 h-4",
            getStatusColor(),
            isSyncing && "animate-spin"
          )} 
        />
        
        <div className="flex items-center space-x-1">
          <Badge 
            variant={isOnline ? "default" : "destructive"} className="text-xs"
          >
            {isOnline ? 'Online' : 'Offline'}
          </Badge>
          
          {pendingActionsCount > 0 && (
            <Badge variant="outline" className="text-xs">
              {pendingActionsCount} pending
            </Badge>
          )}
        </div>

        {variant === 'compact' && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowDetails(!showDetails)} className="h-6 w-6 p-0"
          >
            <Info className="w-3 h-3" />
    </Button>
        )}
      </motion.div>
    );
  }

  return (
    <Card className={cn("w-full max-w-md", className)}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center justify-between text-sm">
          <div className="flex items-center space-x-2">
            <StatusIcon 
              className={cn(
                "w-4 h-4",
                getStatusColor(),
                isSyncing && "animate-spin"
              )} 
            />
            <span>Connection Status</span>
    </div>
          <Badge 
            variant={isOnline ? "default" : "destructive"} className="text-xs"
          >
            {isOnline ? 'Online' : 'Offline'}
          </Badge>
    </CardTitle>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Sync Status */}
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600 dark:text-gray-400">Last Sync</span>
            <span className="font-medium">
              {lastSyncTime 
                ? formatDistanceToNow(lastSyncTime, { addSuffix: true })
                : 'Never'
              }
            </span>
    </div>
          {isSyncing && (
            <div className="space-y-1">
              <div className="flex items-center justify-between text-xs text-gray-500">
                <span>Syncing...</span>
                <RefreshCw className="w-3 h-3 animate-spin" />
    </div>
              <Progress value={undefined} className="h-1" />
    </div>
          )}
        </div>

        {/* Pending Actions */}
        {pendingActionsCount > 0 && (
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600 dark:text-gray-400">Pending Actions</span>
              <Badge variant="outline" className="text-xs">
                {pendingActionsCount}
              </Badge>
    </div>
            <div className="text-xs text-gray-500">
              {pendingActionsCount} {pendingActionsCount === 1 ? 'action' : 'actions'} waiting to sync
            </div>
    </div>
        )}

        {/* Storage Stats */}
        {storageStats && (
          <div className="space-y-2">
            <Separator />
            <div className="space-y-2">
              <div className="flex items-center space-x-2 text-sm font-medium">
                <Database className="w-4 h-4" />
                <span>Offline Storage</span>
    </div>
              <div className="grid grid-cols-2 gap-2 text-xs">
                <div className="space-y-1">
                  <div className="text-gray-600 dark:text-gray-400">Messages</div>
                  <div className="font-medium">{storageStats.messageCount}</div>
    </div>
                <div className="space-y-1">
                  <div className="text-gray-600 dark:text-gray-400">Conversations</div>
                  <div className="font-medium">{storageStats.conversationCount}</div>
    </div>
                <div className="space-y-1">
                  <div className="text-gray-600 dark:text-gray-400">Storage Used</div>
                  <div className="font-medium">{formatFileSize(storageStats.totalSize)}</div>
    </div>
                <div className="space-y-1">
                  <div className="text-gray-600 dark:text-gray-400">Pending</div>
                  <div className="font-medium">{storageStats.pendingActionCount}</div>
    </div>
              </div>
    </div>
          </div>
        )}

        {/* Actions */}
        <div className="space-y-2">
          <Separator />
          <div className="flex flex-wrap gap-2">
            <Button
              size="sm"
              onClick={onSyncNow} disabled={!isOnline || isSyncing}, className="flex-1"
            >
              <RefreshCw className={cn("w-3 h-3 mr-1", isSyncing && "animate-spin")} />
              {isSyncing ? 'Syncing...' : 'Sync Now'}
            </Button>
            
            <Button
              size="sm"
              variant="outline"
              onClick={onExportData} className="flex-1"
            >
              <Download className="w-3 h-3 mr-1" />
              Export
            </Button>
    </div>
          <Button
            size="sm"
            variant="outline"
            onClick={onClearData} className="w-full text-red-600 hover:text-red-700"
          >
            <Trash2 className="w-3 h-3 mr-1" />
            Clear Offline Data
          </Button>
    </div>
        {/* Status Messages */}
        <AnimatePresence>
          {!isOnline && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}, animate={{ opacity: 1, height: 'auto' }}, exit={{ opacity: 0, height: 0 }}, className="flex items-center space-x-2 p-2 bg-red-50 dark:bg-red-900/20 rounded-lg text-sm text-red-700 dark:text-red-300"
            >
              <AlertCircle className="w-4 h-4 flex-shrink-0" />
              <span>You're offline. Messages will sync when reconnected.</span>
            </motion.div>
          )}
          
          {isOnline && pendingActionsCount === 0 && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}, animate={{ opacity: 1, height: 'auto' }}, exit={{ opacity: 0, height: 0 }}, className="flex items-center space-x-2 p-2 bg-green-50 dark:bg-green-900/20 rounded-lg text-sm text-green-700 dark:text-green-300"
            >
              <CheckCircle className="w-4 h-4 flex-shrink-0" />
              <span>All messages are synced.</span>
            </motion.div>
          )}
        </AnimatePresence>
    </CardContent>
    </Card>
  );
};

// Simple offline banner
interface OfflineBannerProps {
  isOnline: boolean, pendingActionsCount: number, onSyncNow: () => void;
  className?: string;
}

export const OfflineBanner: React.FC<OfflineBannerProps> = ({
  isOnline,
  pendingActionsCount,
  onSyncNow,
  className
}) => {
  if (isOnline && pendingActionsCount === 0) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: -50 }}, animate={{ opacity: 1, y: 0 }}, exit={{ opacity: 0, y: -50 }}, className={cn(
          "flex items-center justify-between p-3 border-b",
          !isOnline 
            ? "bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800" 
            : "bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800",
          className
        )}
      >
        <div className="flex items-center space-x-2">
          {!isOnline ? (
            <WifiOff className="w-4 h-4 text-red-500" />
          ) : (
            <Clock className="w-4 h-4 text-yellow-500" />
          )}
          
          <span className={cn(
            "text-sm font-medium",
            !isOnline ? "text-red-700 dark:text-red-300" : "text-yellow-700 dark:text-yellow-300"
          )}>
            {!isOnline 
              ? 'You\'re offline' 
              : `${pendingActionsCount} messages waiting to sync`
            }
          </span>
    </div>
        {isOnline && pendingActionsCount > 0 && (
          <Button size="sm" onClick={onSyncNow} variant="outline">
            <RefreshCw className="w-3 h-3 mr-1" />
            Sync Now
          </Button>
        )}
      </motion.div>
    </AnimatePresence>
  );
};

export default OfflineIndicator;