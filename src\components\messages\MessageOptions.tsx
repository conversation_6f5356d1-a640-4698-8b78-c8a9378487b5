import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle
} from '@/components/ui/alert-dialog';
import {
  MoreVertical,
  Reply,
  Forward,
  Copy,
  Trash2,
  Edit,
  Pin,
  Star,
  MessageCircle
} from 'lucide-react';
import { Message } from '@/types/enhanced-messaging';

interface MessageOptionsProps {
  message: Message, isOwnMessage: boolean;
  onReply?: (message: Message) => void;
  onForward?: (message: Message) => void;
  onCopy?: (message: Message) => void;
  onEdit?: (message: Message) => void;
  onDelete?: (messageId: string, deleteForEveryone: boolean) => void;
  onPin?: (message: Message) => void;
  onStar?: (message: Message) => void;
  onStartThread?: (message: Message) => void;
  className?: string;
}

const MessageOptions: React.FC<MessageOptionsProps> = React.memo(({
  message,
  isOwnMessage,
  onReply,
  onForward,
  onCopy,
  onEdit,
  onDelete,
  onPin,
  onStar,
  onStartThread,
  className = ''
}) => {
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [deleteForEveryone, setDeleteForEveryone] = useState(false);

  // Check if message can be deleted for everyone (within time limit)
  const canDeleteForEveryone = isOwnMessage && message.timestamp && 
    (new Date().getTime() - new Date(message.timestamp).getTime()) < 24 * 60 * 60 * 1000; // 24 hours

  const handleDeleteConfirm = () => {
    if (onDelete) {
      onDelete(message.id, deleteForEveryone);
    }
    setShowDeleteDialog(false);
    setDeleteForEveryone(false);
  };

  const handleDeleteClick = (forEveryone: boolean) => {
    setDeleteForEveryone(forEveryone);
    setShowDeleteDialog(true);
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className={`h-8 w-8 p-0 opacity-100 hover:bg-gray-200 dark:hover:bg-gray-700 transition-all duration-200 ${className}`}, title="Message options"
          >
            <MoreVertical className="h-4 w-4" />
    </Button>
        </DropdownMenuTrigger>
        
        <DropdownMenuContent align="end" className="w-48">
          {/* Reply */}
          {onReply && !message.isDeleted && (
            <DropdownMenuItem onClick={() => onReply(message)}>
              <Reply className="w-4 h-4 mr-2" />
              Reply
            </DropdownMenuItem>
          )}

          {/* Start Thread */}
          {onStartThread && !message.isDeleted && (
            <DropdownMenuItem onClick={() => onStartThread(message)}>
              <MessageCircle className="w-4 h-4 mr-2" />
              Start Thread
            </DropdownMenuItem>
          )}
          
          {/* Forward */}
          {onForward && !message.isDeleted && (
            <DropdownMenuItem onClick={() => onForward(message)}>
              <Forward className="w-4 h-4 mr-2" />
              Forward
            </DropdownMenuItem>
          )}
          
          {/* Copy */}
          {onCopy && !message.isDeleted && message.content && (
            <DropdownMenuItem onClick={() => onCopy(message)}>
              <Copy className="w-4 h-4 mr-2" />
              Copy Text
            </DropdownMenuItem>
          )}
          
          {/* Star/Save */}
          {onStar && !message.isDeleted && (
            <DropdownMenuItem onClick={() => onStar(message)}>
              <Star className="w-4 h-4 mr-2" />
              Star Message
            </DropdownMenuItem>
          )}
          
          {/* Pin (for group messages) */}
          {onPin && !message.isDeleted && (
            <DropdownMenuItem onClick={() => onPin(message)}>
              <Pin className="w-4 h-4 mr-2" />
              {message.isPinned ? 'Unpin' : 'Pin'} Message
            </DropdownMenuItem>
          )}
          
          {/* Edit (only for own text messages) */}
          {onEdit && isOwnMessage && !message.isDeleted && message.type === 'text' && (
            <DropdownMenuItem onClick={() => onEdit(message)}>
              <Edit className="w-4 h-4 mr-2" />
              Edit
            </DropdownMenuItem>
          )}
          
          {/* Delete options */}
          {onDelete && !message.isDeleted && (
            <>
              <DropdownMenuSeparator />
              
              {/* Delete for me */}
              <DropdownMenuItem
                onClick={() => handleDeleteClick(false)} className="text-red-600 focus:text-red-600"
              >
                <Trash2 className="w-4 h-4 mr-2" />
                Delete for me
              </DropdownMenuItem>
              
              {/* Delete for everyone (only for own messages within time limit) */}
              {canDeleteForEveryone && (
                <DropdownMenuItem
                  onClick={() => handleDeleteClick(true)} className="text-red-600 focus:text-red-600"
                >
                  <Trash2 className="w-4 h-4 mr-2" />
                  Delete for everyone
                </DropdownMenuItem>
              )}
            </>
          )}
        </DropdownMenuContent>
    </DropdownMenu>
      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {deleteForEveryone ? 'Delete message for everyone?' : 'Delete message for you?'}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {deleteForEveryone ? (
                <>
                  This message will be deleted for everyone in this chat. 
                  Recipients may have already seen this message.
                </>
              ) : (
                <>
                  This message will be deleted for you only. 
                  Other participants will still be able to see it.
                </>
              )}
            </AlertDialogDescription>
    </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setShowDeleteDialog(false)}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteConfirm} className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
            >
              Delete
            </AlertDialogAction>
    </AlertDialogFooter>
        </AlertDialogContent>
    </AlertDialog>
    </>
  );
});

MessageOptions.displayName = 'MessageOptions';

export default MessageOptions;