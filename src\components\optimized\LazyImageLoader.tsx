import React, { useState, useRef, useEffect, memo, useCallback } from 'react';
import { useIntersectionObserver } from '@/hooks/useIntersectionObserver';

interface LazyImageProps {
  src: string, alt: string;
  placeholder?: string;
  className?: string;
  width?: number;
  height?: number;
  quality?: number;
  onLoad?: () => void;
  onError?: () => void;
  priority?: boolean;
  sizes?: string;
}

const LazyImageLoader: React.FC<LazyImageProps> = memo(({
  src,
  alt,
  placeholder = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PC9zdmc+',
  className = '',
  width,
  height,
  quality = 80,
  onLoad,
  onError,
  priority = false,
  sizes
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isError, setIsError] = useState(false);
  const [currentSrc, setCurrentSrc] = useState(placeholder);
  const imgRef = useRef<HTMLImageElement>(null);
  const [shouldLoad, setShouldLoad] = useState(priority);

  // Optimize image URL with parameters
  const optimizedSrc = React.useMemo(() => {
    if (!src) return placeholder;
    
    try {
      const url = new URL(src, window.location.origin);
      if (width) url.searchParams.set('w', width.toString());
      if (height) url.searchParams.set('h', height.toString());
      url.searchParams.set('q', quality.toString());
      url.searchParams.set('f', 'webp'); // Prefer WebP format
      return url.toString();
    } catch {
      return src;
    }
  }, [src, width, height, quality, placeholder]);

  // Handle image loading
  const handleLoad = useCallback(() => {
    setIsLoaded(true);
    setIsError(false);
    onLoad?.();
  }, [onLoad]);

  const handleError = useCallback(() => {
    setIsError(true);
    setCurrentSrc(placeholder);
    onError?.();
  }, [onError, placeholder]);

  // Load image when in viewport (unless priority)
  useIntersectionObserver(
    imgRef,
    useCallback((isVisible) => {
      if (isVisible && !shouldLoad && !isLoaded && !isError) {
        setShouldLoad(true);
      }
    }, [shouldLoad, isLoaded, isError]),
    { 
      threshold: 0.1,
      rootMargin: '50px'
    }
  );

  // Load the actual image when shouldLoad is true
  useEffect(() => {
    if (!shouldLoad || isLoaded || isError) return;

    const img = new Image();
    
    img.onload = () => {
      setCurrentSrc(optimizedSrc);
      handleLoad();
    };
    
    img.onerror = handleError;
    
    // Support responsive images
    if (sizes) {
      img.sizes = sizes;
    }
    
    img.src = optimizedSrc;

    return () => {
      img.onload = null;
      img.onerror = null;
    };
  }, [shouldLoad, optimizedSrc, isLoaded, isError, handleLoad, handleError, sizes]);

  // Preload critical images
  useEffect(() => {
    if (priority && !isLoaded) {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'image';
      link.href = optimizedSrc;
      if (sizes) link.setAttribute('imagesizes', sizes);
      document.head.appendChild(link);

      return () => {
        document.head.removeChild(link);
      };
    }
  }, [priority, optimizedSrc, isLoaded, sizes]);

  return (
    <div className={`relative overflow-hidden ${className}`}>
      <img
        ref={imgRef} src={currentSrc}, alt={alt} width={width}, height={height} className={`transition-opacity duration-300 ${
          isLoaded ? 'opacity-100' : 'opacity-70'
        } ${className}`}, loading={priority ? 'eager' : 'lazy'} decoding="async"
        sizes={sizes}
      />
      
      {/* Loading indicator */}
      {shouldLoad && !isLoaded && !isError && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100 dark:bg-gray-800">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
    </div>
      )}
      
      {/* Error state */}
      {isError && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100 dark:bg-gray-800 text-gray-500">
          <span className="text-sm">Failed to load image</span>
    </div>
      )}
    </div>
  );
});

LazyImageLoader.displayName = 'LazyImageLoader';

export default LazyImageLoader;