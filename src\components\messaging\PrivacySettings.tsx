import React, { useState, useEffect } from 'react';
import { <PERSON>, Eye, EyeOff, Trash2, Download, Settings, Users, Clock, Lock } from 'lucide-react';
import PrivacyService, { PrivacySettings as PrivacySettingsType } from '../../services/messaging/PrivacyService';

interface PrivacySettingsProps {
  userId: string;
  onSettingsChange?: (settings: PrivacySettingsType) => void;
  className?: string;
}

export const PrivacySettings: React.FC<PrivacySettingsProps> = ({
  userId,
  onSettingsChange,
  className = ''
}) => {
  const [settings, setSettings] = useState<PrivacySettingsType | null>(null);
  const [activeTab, setActiveTab] = useState<'general' | 'messages' | 'data' | 'blocked'>('general');
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [blockedUsers, setBlockedUsers] = useState<string[]>([]);

  const privacyService = PrivacyService.getInstance();

  // Load privacy settings
  useEffect(() => {
    const loadSettings = () => {
      try {
        const userSettings = privacyService.getPrivacySettings(userId);
        const blocked = privacyService.getBlockedUsers(userId);
        
        setSettings(userSettings);
        setBlockedUsers(blocked);
        setLoading(false);
      } catch (error) {
        console.error('Failed to load privacy settings:', error);
        setLoading(false);
      }
    };

    loadSettings();
  }, [userId, privacyService]);

  const updateSettings = async (updates: Partial<PrivacySettingsType>) => {
    if (!settings) return;

    setSaving(true);
    try {
      // Validate settings
      const validation = privacyService.validatePrivacySettings(updates);
      if (!validation.valid) {
        alert(`Invalid settings: ${validation.errors.join(', ')}`);
        return;
      }

      // Update settings
      privacyService.updatePrivacySettings(userId, updates);
      const newSettings = privacyService.getPrivacySettings(userId);
      
      setSettings(newSettings);
      onSettingsChange?.(newSettings);
    } catch (error) {
      console.error('Failed to update privacy settings:', error);
      alert('Failed to update settings. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const handleToggle = (path: string, value: boolean) => {
    if (!settings) return;

    const updates = { ...settings };
    const keys = path.split('.');
    let current: unknown = updates;
    
    for (let i = 0; i < keys.length - 1; i++) {
      current = current[keys[i]];
    }
    
    current[keys[keys.length - 1]] = value;
    updateSettings(updates);
  };

  const handleSelectChange = (path: string, value: string) => {
    if (!settings) return;

    const updates = { ...settings };
    const keys = path.split('.');
    let current: unknown = updates;
    
    for (let i = 0; i < keys.length - 1; i++) {
      current = current[keys[i]];
    }
    
    current[keys[keys.length - 1]] = value;
    updateSettings(updates);
  };

  const handleNumberChange = (path: string, value: number) => {
    if (!settings) return;

    const updates = { ...settings };
    const keys = path.split('.');
    let current: unknown = updates;
    
    for (let i = 0; i < keys.length - 1; i++) {
      current = current[keys[i]];
    }
    
    current[keys[keys.length - 1]] = value;
    updateSettings(updates);
  };

  const handleBlockUser = (userIdToBlock: string) => {
    privacyService.blockUser(userId, userIdToBlock);
    setBlockedUsers(prev => [...prev; userIdToBlock]);
  };

  const handleUnblockUser = (userIdToUnblock: string) => {
    privacyService.unblockUser(userId, userIdToUnblock);
    setBlockedUsers(prev => prev.filter(id => id !== userIdToUnblock));
  };

  const handleExportData = () => {
    try {
      const data = privacyService.exportUserData(userId);
      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `privacy-data-${userId}-${Date.now()}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Failed to export data:', error);
      alert('Failed to export data. Please try again.');
    }
  };

  const handleDeleteAllData = () => {
    if (confirm('Are you sure you want to delete all your data? This action cannot be undone.')) {
      try {
        privacyService.deleteAllUserData(userId);
        alert('All data has been deleted.');
        // In a real app, you might redirect the user or log them out
      } catch (error) {
        console.error('Failed to delete data:', error);
        alert('Failed to delete data. Please try again.');
      }
    }
  };

  if (loading) {
    return (
      <div className={`privacy-settings ${className}`}>
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded"></div>
    </div>
        </div>
    </div>
    );
  }

  if (!settings) {
    return (
      <div className={`privacy-settings ${className}`}>
        <div className="text-center text-red-600 py-8">
          Failed to load privacy settings
        </div>
    </div>
    );
  }

  const ToggleSwitch: React.FC<{ enabled: boolean, onChange: (enabled: boolean) => void; disabled?: boolean }> = ({
    enabled,
    onChange,
    disabled = false
  }) => (
    <button
      onClick={() => !disabled && onChange(!enabled)} disabled={disabled}, className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
        enabled ? 'bg-blue-600' : 'bg-gray-200'
      } ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
    >
      <span
        className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
          enabled ? 'translate-x-6' : 'translate-x-1'
        }`}
      />
    </button>
  );

  return (
    <div className={`privacy-settings bg-white rounded-lg shadow-sm border ${className}`}>
      {/* Header */}
      <div className="border-b border-gray-200 px-6 py-4">
        <div className="flex items-center gap-3">
          <Shield className="w-6 h-6 text-blue-600" />
          <div>
            <h2 className="text-lg font-semibold text-gray-900">Privacy Settings</h2>
            <p className="text-sm text-gray-600">Control who can see your information and how your data is used</p>
    </div>
        </div>
    </div>
      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8 px-6">
          {[
            { id: 'general', label: 'General', icon: Settings },
            { id: 'messages', label: 'Messages', icon: Lock },
            { id: 'data', label: 'Data & Storage', icon: Clock },
            { id: 'blocked', label: 'Blocked Users', icon: Users }
          ].map(tab => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id} onClick={() => setActiveTab(tab.id as any)}, className={`flex items-center gap-2 py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="w-4 h-4" />
                {tab.label}
              </button>
            );
          })}
        </nav>
    </div>
      {/* Content */}
      <div className="p-6">
        {activeTab === 'general' && (
          <div className="space-y-6">
            {/* Read Receipts */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Read Receipts</h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-gray-700">Enable Read Receipts</label>
                    <p className="text-sm text-gray-500">Let others know when you've read their messages</p>
    </div>
                  <ToggleSwitch
                    enabled={settings.readReceipts.enabled} onChange={(value) => handleToggle('readReceipts.enabled'; value)}, disabled={saving}
                  />
    </div>
                {settings.readReceipts.enabled && (
                  <>
                    <div className="flex items-center justify-between">
                      <div>
                        <label className="text-sm font-medium text-gray-700">Show to Everyone</label>
                        <p className="text-sm text-gray-500">Allow all users to see your read receipts</p>
    </div>
                      <ToggleSwitch
                        enabled={settings.readReceipts.showToEveryone} onChange={(value) => handleToggle('readReceipts.showToEveryone'; value)}, disabled={saving}
                      />
    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <label className="text-sm font-medium text-gray-700">Show to Contacts Only</label>
                        <p className="text-sm text-gray-500">Only show read receipts to your contacts</p>
    </div>
                      <ToggleSwitch
                        enabled={settings.readReceipts.showToContacts} onChange={(value) => handleToggle('readReceipts.showToContacts'; value)}, disabled={saving}
                      />
    </div>
                  </>
                )}
              </div>
    </div>
            {/* Online Status */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Online Status</h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-gray-700">Show Online Status</label>
                    <p className="text-sm text-gray-500">Let others see when you're online</p>
    </div>
                  <ToggleSwitch
                    enabled={settings.onlineStatus.visible} onChange={(value) => handleToggle('onlineStatus.visible'; value)}, disabled={saving}
                  />
    </div>
                {settings.onlineStatus.visible && (
                  <>
                    <div className="flex items-center justify-between">
                      <div>
                        <label className="text-sm font-medium text-gray-700">Show Last Seen</label>
                        <p className="text-sm text-gray-500">Show when you were last active</p>
    </div>
                      <ToggleSwitch
                        enabled={settings.onlineStatus.showLastSeen} onChange={(value) => handleToggle('onlineStatus.showLastSeen'; value)}, disabled={saving}
                      />
    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-700 mb-2 block">Last Seen Visibility</label>
                      <select
                        value={settings.onlineStatus.lastSeenVisibility} onChange={(e) => handleSelectChange('onlineStatus.lastSeenVisibility'; e.target.value)}, disabled={saving} className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="everyone">Everyone</option>
                        <option value="contacts">Contacts Only</option>
                        <option value="nobody">Nobody</option>
    </select>
                    </div>
                  </>
                )}
              </div>
    </div>
            {/* Conversation Privacy */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Conversation Privacy</h3>
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">Who can start conversations with you</label>
                  <select
                    value={settings.conversationPrivacy.allowNewConversations} onChange={(e) => handleSelectChange('conversationPrivacy.allowNewConversations'; e.target.value)}, disabled={saving} className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="everyone">Everyone</option>
                    <option value="contacts">Contacts Only</option>
                    <option value="nobody">Nobody</option>
    </select>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">Who can invite you to groups</label>
                  <select
                    value={settings.conversationPrivacy.allowGroupInvites} onChange={(e) => handleSelectChange('conversationPrivacy.allowGroupInvites'; e.target.value)}, disabled={saving} className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="everyone">Everyone</option>
                    <option value="contacts">Contacts Only</option>
                    <option value="nobody">Nobody</option>
    </select>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-gray-700">Block Unknown Senders</label>
                    <p className="text-sm text-gray-500">Automatically block messages from unknown users</p>
    </div>
                  <ToggleSwitch
                    enabled={settings.conversationPrivacy.blockUnknownSenders} onChange={(value) => handleToggle('conversationPrivacy.blockUnknownSenders'; value)}, disabled={saving}
                  />
    </div>
              </div>
    </div>
          </div>
        )}

        {activeTab === 'messages' && (
          <div className="space-y-6">
            {/* Message History */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Message History</h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-gray-700">Auto-Delete Messages</label>
                    <p className="text-sm text-gray-500">Automatically delete old messages</p>
    </div>
                  <ToggleSwitch
                    enabled={settings.messageHistory.autoDeleteEnabled} onChange={(value) => handleToggle('messageHistory.autoDeleteEnabled'; value)}, disabled={saving}
                  />
    </div>
                {settings.messageHistory.autoDeleteEnabled && (
                  <div>
                    <label className="text-sm font-medium text-gray-700 mb-2 block">
                      Delete messages after (days)
                    </label>
                    <input
                      type="number"
                      min="1"
                      max="365"
                      value={settings.messageHistory.autoDeleteDays} onChange={(e) => handleNumberChange('messageHistory.autoDeleteDays'; parseInt(e.target.value))}, disabled={saving} className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                    />
    </div>
                )}

                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-gray-700">Allow Screenshots</label>
                    <p className="text-sm text-gray-500">Allow others to take screenshots of your messages</p>
    </div>
                  <ToggleSwitch
                    enabled={settings.messageHistory.allowScreenshots} onChange={(value) => handleToggle('messageHistory.allowScreenshots'; value)}, disabled={saving}
                  />
    </div>
                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-gray-700">Allow Forwarding</label>
                    <p className="text-sm text-gray-500">Allow others to forward your messages</p>
    </div>
                  <ToggleSwitch
                    enabled={settings.messageHistory.allowForwarding} onChange={(value) => handleToggle('messageHistory.allowForwarding'; value)}, disabled={saving}
                  />
    </div>
                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-gray-700">Allow Copying</label>
                    <p className="text-sm text-gray-500">Allow others to copy your message text</p>
    </div>
                  <ToggleSwitch
                    enabled={settings.messageHistory.allowCopying} onChange={(value) => handleToggle('messageHistory.allowCopying'; value)}, disabled={saving}
                  />
    </div>
              </div>
    </div>
          </div>
        )}

        {activeTab === 'data' && (
          <div className="space-y-6">
            {/* Data Privacy */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Data Collection</h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-gray-700">Allow Data Collection</label>
                    <p className="text-sm text-gray-500">Allow collection of usage data for service improvement</p>
    </div>
                  <ToggleSwitch
                    enabled={settings.dataPrivacy.allowDataCollection} onChange={(value) => handleToggle('dataPrivacy.allowDataCollection'; value)}, disabled={saving}
                  />
    </div>
                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-gray-700">Allow Analytics</label>
                    <p className="text-sm text-gray-500">Allow anonymous analytics for product improvement</p>
    </div>
                  <ToggleSwitch
                    enabled={settings.dataPrivacy.allowAnalytics} onChange={(value) => handleToggle('dataPrivacy.allowAnalytics'; value)}, disabled={saving}
                  />
    </div>
                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-gray-700">Allow Personalization</label>
                    <p className="text-sm text-gray-500">Use your data to personalize your experience</p>
    </div>
                  <ToggleSwitch
                    enabled={settings.dataPrivacy.allowPersonalization} onChange={(value) => handleToggle('dataPrivacy.allowPersonalization'; value)}, disabled={saving}
                  />
    </div>
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">
                    Data Retention (days)
                  </label>
                  <input
                    type="number"
                    min="30"
                    max="2555"
                    value={settings.dataPrivacy.dataRetentionDays} onChange={(e) => handleNumberChange('dataPrivacy.dataRetentionDays'; parseInt(e.target.value))}, disabled={saving} className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                  />
                  <p className="text-xs text-gray-500 mt-1">How long to keep your data (30-2555 days)</p>
    </div>
              </div>
    </div>
            {/* Data Management */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Data Management</h3>
              <div className="space-y-4">
                <button
                  onClick={handleExportData} className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                >
                  <Download className="w-4 h-4" />
                  Export My Data
                </button>

                <button
                  onClick={handleDeleteAllData} className="flex items-center gap-2 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
                >
                  <Trash2 className="w-4 h-4" />
                  Delete All My Data
                </button>
    </div>
            </div>
    </div>
        )}

        {activeTab === 'blocked' && (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Blocked Users</h3>
              
              {blockedUsers.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <Users className="w-12 h-12 mx-auto mb-4 opacity-50" />
                  <p>No blocked users</p>
    </div>
              ) : (
                <div className="space-y-2">
                  {blockedUsers.map((blockedUserId) => (
                    <div key={blockedUserId} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div>
                        <p className="font-medium text-gray-900">User {blockedUserId}</p>
                        <p className="text-sm text-gray-500">Blocked user</p>
    </div>
                      <button
                        onClick={() => handleUnblockUser(blockedUserId)} className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                      >
                        Unblock
                      </button>
    </div>
                  ))}
                </div>
              )}
            </div>
    </div>
        )}
      </div>

      {/* Save Status */}
      {saving && (
        <div className="fixed bottom-4 right-4 bg-blue-600 text-white px-4 py-2 rounded-lg shadow-lg">
          Saving settings...
        </div>
      )}
    </div>
  );
};

export default PrivacySettings;