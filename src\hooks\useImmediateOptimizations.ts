/**
 * Immediate Performance Optimization Hook
 * Ready-to-use performance optimization utilities for existing components
 */

import { useCallback, useMemo, useRef, useEffect, useState } from 'react';

// Performance monitoring and optimization hook
export const useImmediateOptimization = (componentName: string = 'Component') => {
  const renderCount = useRef(0);
  const lastRenderTime = useRef(Date.now());
  const mountTime = useRef(Date.now());

  // Track renders for performance monitoring
  useEffect(() => {
    renderCount.current += 1;
    const now = Date.now();
    const timeSinceLastRender = now - lastRenderTime.current;
    
    // Warn about frequent re-renders in development (only after 3+ renders within 100ms)
    if (process.env.NODE_ENV === 'development' && 
        timeSinceLastRender < 100 && 
        renderCount.current > 3 &&
        renderCount.current % 5 === 0) { // Only warn every 5th excessive render
      console.warn(`⚠️ ${componentName} re-rendered too quickly (${timeSinceLastRender}ms, render #${renderCount.current})`);
    }
    
    lastRenderTime.current = now;
  });

  // Optimized callback factory - prevents function recreation
  function createOptimizedCallback<T extends (...args: unknown[]) => any>(
    callback: T,
    deps: React.DependencyList
  ): T {
    // eslint-disable-next-line react-hooks/exhaustive-deps
    // Casting is safe because useCallback preserves the function signature
    // and T is unchanged.
    // eslint-disable-next-line react-hooks/exhaustive-deps
    // We deliberately ignore exhaustive-deps because deps are passed explicitly to this factory.
    // Return a memoized version of the callback
    // eslint-disable-next-line react-hooks/exhaustive-deps
    return useCallback(callback, deps) as unknown as T;
  }

  // Optimized state setter - debounces rapid state changes
  function createDebouncedSetter<T>(
    setter: (value: T) => void,
    delay: number = 16 // One frame delay by default
  ) {
    const timeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

    return useCallback(
      (value: T) => {
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
        }

        timeoutRef.current = setTimeout(() => {
          setter(value);
        }, delay);
      },
      [setter, delay]
    );
  }

  // Memoized object factory - prevents object recreation
  function createMemoizedObject<T extends Record<string, any>>(
    factory: () => T,
    deps: React.DependencyList
  ): T {
    // eslint-disable-next-line react-hooks/exhaustive-deps
    return useMemo(factory, deps);
  }

  // Virtual scrolling optimization for lists
  function createVirtualScrollConfig(
    itemCount: number,
    itemHeight: number,
    containerHeight: number,
    overscan: number = 3
  ) {
    const [scrollTop, setScrollTop] = useState(0);

    const setScrollTopDebounced = createDebouncedSetter(setScrollTop, 16);

    const config = useMemo(() => {
      const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
      const endIndex = Math.min(
        itemCount - 1,
        Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
      );

      const visibleItemCount = Math.max(0, endIndex - startIndex + 1);
      const totalHeight = itemCount * itemHeight;
      const offsetY = startIndex * itemHeight;

      return {
        startIndex,
        endIndex,
        visibleItemCount,
        offsetY,
        totalHeight,
        setScrollTop: setScrollTopDebounced,
      };
    }, [scrollTop, itemCount, itemHeight, containerHeight, overscan, setScrollTopDebounced]);

    return config;
  }

  // Performance metrics
  const getPerformanceMetrics = useCallback(() => {
    const age = Date.now() - mountTime.current;
    return {
      renderCount: renderCount.current,
      componentAge: age,
      averageRenderFrequency: age > 0 ? renderCount.current / (age / 1000) : 0,
      timeSinceLastRender: Date.now() - lastRenderTime.current,
    };
  }, []);

  return {
    // Core optimization utilities
    createOptimizedCallback,
    createDebouncedSetter,
    createMemoizedObject,
    createVirtualScrollConfig,

    // Performance monitoring
    getPerformanceMetrics,
    renderCount: renderCount.current,

    // Common optimization patterns
    optimizeEventHandler: createOptimizedCallback,
    optimizeCalculation: createMemoizedObject,
    optimizeStateUpdate: createDebouncedSetter,
  };
};

// Optimized input hook
export const useOptimizedInput = (initialValue: string = '', delay: number = 300) => {
  const [value, setValue] = useState(initialValue);
  const [debouncedValue, setDebouncedValue] = useState(initialValue);
  const { createDebouncedSetter } = useImmediateOptimization('OptimizedInput');
  
  const debouncedSetValue = useMemo(
    () => createDebouncedSetter(setDebouncedValue, delay),
    [createDebouncedSetter, delay]
  );
  
  const handleChange = useCallback((newValue: string) => {
    setValue(newValue);
    debouncedSetValue(newValue);
  }, [debouncedSetValue]);
  
  return {
    value,
    debouncedValue,
    setValue: handleChange,
    reset: () => {
      setValue(initialValue);
      setDebouncedValue(initialValue);
    },
  };
};

// Optimized async operations hook
export const useOptimizedAsync = <T>(
  asyncFunction: () => Promise<T>,
  deps: React.DependencyList
) => {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const { createOptimizedCallback } = useImmediateOptimization('OptimizedAsync');
  
  const execute = createOptimizedCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await asyncFunction();
      setData(result);
    } catch (err) {
      setError(err as Error);
    } finally {
      setLoading(false);
    }
  }, deps);
  
  return { data, loading, error, execute, refetch: execute };
};

// Optimized modal/dialog hook
export const useOptimizedModal = (initialOpen: boolean = false) => {
  const [isOpen, setIsOpen] = useState(initialOpen);
  const { createOptimizedCallback } = useImmediateOptimization('OptimizedModal');
  
  const open = createOptimizedCallback(() => setIsOpen(true), []);
  const close = createOptimizedCallback(() => setIsOpen(false), []);
  const toggle = createOptimizedCallback(() => setIsOpen(prev => !prev), []);
  
  return {
    isOpen,
    open,
    close,
    toggle,
    setIsOpen
  };
};

// Performance utility functions
export const OptimizationUtils = {
  // Debounce function
  debounce: <T extends (...args: unknown[]) => any>(func: T, delay: number): ((...args: Parameters<T>) => void) => {
    let timeoutId: ReturnType<typeof setTimeout> | null = null;
    return (...args: Parameters<T>) => {
      if (timeoutId) clearTimeout(timeoutId);
      timeoutId = setTimeout(() => func(...args), delay);
    };
  },

  // Throttle function
  throttle: <T extends (...args: unknown[]) => any>(func: T, limit: number): ((...args: Parameters<T>) => void) => {
    let inThrottle = false;
    return (...args: Parameters<T>) => {
      if (!inThrottle) {
        func(...args);
        inThrottle = true;
        setTimeout(() => {
          inThrottle = false;
        }, limit);
      }
    };
  },

  // Deep equality check for memo comparisons
  deepEqual: (obj1: unknown, obj2: unknown): boolean => {
    if (obj1 === obj2) return true;
    if (obj1 == null || obj2 == null) return false;
    if (typeof obj1 !== typeof obj2) return false;
    
    if (typeof obj1 !== 'object') return obj1 === obj2;
    
    const keys1 = Object.keys(obj1);
    const keys2 = Object.keys(obj2);
    
    if (keys1.length !== keys2.length) return false;
    
    for (const key of keys1) {
      if (!keys2.includes(key)) return false;
      if (!OptimizationUtils.deepEqual(obj1[key], obj2[key])) return false;
    }
    
    return true;
  },

  // Shallow equality check for memo comparisons  
  shallowEqual: (obj1: unknown, obj2: unknown): boolean => {
    const keys1 = Object.keys(obj1);
    const keys2 = Object.keys(obj2);
    
    if (keys1.length !== keys2.length) return false;
    
    for (const key of keys1) {
      if (obj1[key] !== obj2[key]) return false;
    }
    
    return true;
  }
};

export default {
  useImmediateOptimization,
  useOptimizedInput,
  useOptimizedAsync,
  useOptimizedModal,
  OptimizationUtils
};
