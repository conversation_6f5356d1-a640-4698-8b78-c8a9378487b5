{"compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "module": "ESNext", "lib": ["ES2020", "DOM", "DOM.Iterable", "WebWorker"], "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "exactOptionalPropertyTypes": false, "noImplicitReturns": false, "noUncheckedIndexedAccess": false, "noImplicitAny": false, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "types": ["vite/client", "node"]}, "include": ["src", "src/**/*", "vite-env.d.ts"], "exclude": ["node_modules", "dist"]}