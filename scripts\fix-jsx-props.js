#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

function fixJSXProps(content) {
  // Fix the specific pattern: prop={value} prop2={value2} -> prop={value}\n      prop2={value2}
  // This handles the comma placement issue in JSX props
  
  let lines = content.split('\n');
  let modified = false;
  
  for (let i = 0; i < lines.length; i++) {
    let line = lines[i];
    
    // Look for JSX prop patterns that need fixing
    // Pattern: prop={value} prop2={value2}
    const jsxPropPattern = /(\s*)(\w+)=\{([^}]+)\}\s+(\w+)=\{/;
    const match = line.match(jsxPropPattern);
    
    if (match) {
      const [fullMatch, indent, prop1, value1, prop2] = match;
      const beforeMatch = line.substring(0, line.indexOf(fullMatch));
      const afterMatch = line.substring(line.indexOf(fullMatch) + fullMatch.length);
      
      // Split into two lines with proper indentation
      const newLine1 = beforeMatch + indent + prop1 + '={' + value1 + '}';
      const newLine2 = indent + '                      ' + prop2 + '={' + afterMatch;
      
      lines[i] = newLine1;
      lines.splice(i + 1, 0, newLine2);
      modified = true;
      i++; // Skip the newly inserted line
    }
    
    // Fix semicolon issues in function calls
    if (line.includes('setTimeout(') && line.includes(';')) {
      lines[i] = line.replace(/setTimeout\(([^,]+);\s*(\d+)\)/g, 'setTimeout($1, $2)');
      modified = true;
    }
    
    // Fix resolve; pattern
    if (line.includes('resolve;')) {
      lines[i] = line.replace(/resolve\s*;\s*(\d+)/g, 'resolve, $1');
      modified = true;
    }
  }
  
  return modified ? lines.join('\n') : content;
}

function fixFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const fixedContent = fixJSXProps(content);
    
    if (fixedContent !== content) {
      fs.writeFileSync(filePath, fixedContent, 'utf8');
      console.log(`Fixed: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`Error fixing ${filePath}:`, error.message);
    return false;
  }
}

function walkDirectory(dir, extensions = ['.tsx', '.ts', '.jsx', '.js']) {
  const files = [];
  
  function walk(currentDir) {
    try {
      const items = fs.readdirSync(currentDir);
      
      for (const item of items) {
        const fullPath = path.join(currentDir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          walk(fullPath);
        } else if (stat.isFile() && extensions.some(ext => item.endsWith(ext))) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      console.error(`Error reading directory ${currentDir}:`, error.message);
    }
  }
  
  walk(dir);
  return files;
}

function main() {
  const srcDir = path.join(__dirname, '..', 'src');
  const files = walkDirectory(srcDir);
  
  console.log(`Found ${files.length} files to check...`);
  
  let fixedCount = 0;
  
  files.forEach(file => {
    if (fixFile(file)) {
      fixedCount++;
    }
  });
  
  console.log(`\nFixed ${fixedCount} files out of ${files.length} total files.`);
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}
