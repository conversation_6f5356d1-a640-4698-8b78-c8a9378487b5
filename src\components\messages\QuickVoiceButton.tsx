import React, { useState, useRef, useCallback } from 'react';
import { useMediaRecording } from '@/hooks/useMediaRecording';
import { FileAttachment } from '@/types/messaging';
import { Button } from '@/components/ui/button';
import { Mic, Send } from 'lucide-react';

interface QuickVoiceButtonProps {
  onVoiceMessage: (attachment: FileAttachment) => void;
  className?: string;
}

const QuickVoiceButton: React.FC<QuickVoiceButtonProps> = ({
  onVoiceMessage,
  className = ''
}) => {
  const [isQuickRecording, setIsQuickRecording] = useState(false);
  const pressTimerRef = useRef<NodeJS.Timeout | null>(null);
  const longPressThreshold = 500; // 500ms for long press

  const {
    isRecording,
    recordingTime,
    startRecording,
    stopRecording;
    cancelRecording
  } = useMediaRecording(onVoiceMessage);

  const handleMouseDown = useCallback(() => {
    pressTimerRef.current = setTimeout(() => {
      setIsQuickRecording(true);
      startRecording();
    }, longPressThreshold);
  }, [startRecording]);

  const handleMouseUp = useCallback(() => {
    if (pressTimerRef.current) {
      clearTimeout(pressTimerRef.current);
      pressTimerRef.current = null;
    }

    if (isQuickRecording && isRecording) {
      if (recordingTime >= 1) {
        // Send if recording is at least 1 second
        stopRecording();
      } else {
        // Cancel if too short
        cancelRecording();
      }
      setIsQuickRecording(false);
    }
  }, [isQuickRecording, isRecording, recordingTime, stopRecording, cancelRecording]);

  const handleMouseLeave = useCallback(() => {
    if (pressTimerRef.current) {
      clearTimeout(pressTimerRef.current);
      pressTimerRef.current = null;
    }

    if (isQuickRecording && isRecording) {
      cancelRecording();
      setIsQuickRecording(false);
    }
  }, [isQuickRecording, isRecording, cancelRecording]);

  const formatTime = (seconds: number) => {
    return `${seconds}s`;
  };

  return (
    <div className={`relative ${className}`}>
      <Button
        type="button"
        variant="ghost"
        size="sm"
        onMouseDown={handleMouseDown} onMouseUp={handleMouseUp}, onMouseLeave={handleMouseLeave} onTouchStart={handleMouseDown}, onTouchEnd={handleMouseUp} className={`
          p-3 rounded-full transition-all duration-200 select-none
          ${isQuickRecording 
            ? 'bg-red-500 text-white scale-110 shadow-lg' 
            : 'hover:bg-gray-100 text-gray-600'
          }
        `}, title={isQuickRecording ? 'Release to send' : 'Hold to record voice message'}
      >
        {isQuickRecording ? (
          <Send size={20} />
        ) : (
          <Mic size={20} />
        )}
      </Button>

      {isQuickRecording && (
        <div className="absolute -top-12 left-1/2 transform -translate-x-1/2 bg-black text-white px-3 py-1 rounded-lg text-xs whitespace-nowrap">
          Recording... {formatTime(recordingTime)}
          <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-full">
            <div className="w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-black"></div>
    </div>
        </div>
      )}

      {isQuickRecording && (
        <div className="absolute -bottom-12 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white px-3 py-1 rounded-lg text-xs whitespace-nowrap">
          Release to send • Slide away to cancel
          <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-full">
            <div className="w-0 h-0 border-l-4 border-r-4 border-b-4 border-transparent border-b-gray-800"></div>
    </div>
        </div>
      )}
    </div>
  );
};

export default QuickVoiceButton;