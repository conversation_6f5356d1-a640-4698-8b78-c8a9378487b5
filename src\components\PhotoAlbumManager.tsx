import React, { useState, useCallback } from 'react';
import {
  Plus,
  Grid,
  List,
  Search,
  Download,
  Share2,
  Trash2,
  Edit,
  Eye,
  Heart,
  MessageCircle,
  MoreVertical,
  Users,
  Lock,
  Globe
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

// Simple dropdown replacement with select
const DropdownMenu: React.FC<{ children: React.ReactNode }> = ({ children }) => <div className="relative">{children}</div>;
const DropdownMenuTrigger: React.FC<{ asChild?: boolean, children: React.ReactNode }> = ({ children }) => <>{children}</>;
const DropdownMenuContent: React.FC<{ children: React.ReactNode }> = ({ children }) => <div className="absolute top-full left-0 bg-white border rounded-lg shadow-lg z-10">{children}</div>;
const DropdownMenuItem: React.FC<{ onClick?: () => void; children: React.ReactNode; className?: string }> = ({ onClick, children, className }) => (
  <button onClick={onClick} className={`w-full text-left px-4 py-2 hover:bg-gray-50 transition-colors ${className}`}>{children}</button>
);
import { format } from 'date-fns';
import { toast } from 'sonner';

interface Photo {
  id: string, url: string, thumbnail: string;
  caption?: string;
  uploadedAt: Date;
  location?: string;
  tags: string[], likes: number, comments: number, isLiked: boolean, privacy: 'public' | 'friends' | 'private';
}

interface Album {
  id: string, title: string;
  description?: string;
  coverPhoto?: string;
  photos: Photo[], createdAt: Date, updatedAt: Date, privacy: 'public' | 'friends' | 'private', collaborators: {
    id: string, name: string, avatar: string;
  }[];
}

interface PhotoAlbumManagerProps {
  albums: Album[], onCreateAlbum: (album: Omit<Album, 'id' | 'createdAt' | 'updatedAt'>) => void;
  onUpdateAlbum: (albumId: string, updates: Partial<Album>) => void; onDeleteAlbum: (albumId: string) => void; onAddPhotos: (albumId: string, photos: File[]) => void; onRemovePhoto: (albumId: string, photoId: string) => void; onLikePhoto: (photoId: string) => void; onShareAlbum: (albumId: string) => void;
}

const PhotoAlbumManager: React.FC<PhotoAlbumManagerProps> = ({
  albums,
  onCreateAlbum,
  onUpdateAlbum: _onUpdateAlbum,
  onDeleteAlbum,
  onAddPhotos,
  onRemovePhoto,
  onLikePhoto,
  onShareAlbum
}) => {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedAlbum, setSelectedAlbum] = useState<Album | null>(null);
  const [isCreating, setIsCreating] = useState(false);
  const [newAlbumTitle, setNewAlbumTitle] = useState('');
  const [newAlbumDescription, setNewAlbumDescription] = useState('');
  const [selectedPhotos, setSelectedPhotos] = useState<Set<string>>(new Set());
  const [isSelectionMode, setIsSelectionMode] = useState(false);

  const filteredAlbums = albums.filter(album =>
    album.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    album.description?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleCreateAlbum = () => {
    if (!newAlbumTitle.trim()) {
      toast.error('Please enter an album title');
      return;
    }, onCreateAlbum({
      title: newAlbumTitle,
      description: newAlbumDescription,
      photos: [],
      privacy: 'friends',
      collaborators: []
    });

    setNewAlbumTitle('');
    setNewAlbumDescription('');
    setIsCreating(false);
    toast.success('Album created successfully');
  };

  const handlePhotoUpload = useCallback((albumId: string, event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    if (files.length > 0) {
      onAddPhotos(albumId, files);
      toast.success(`${files.length} photo${files.length !== 1 ? 's' : ''} uploaded`);
    }
  }, [onAddPhotos]);

  const handlePhotoSelect = (photoId: string) => {
    const newSelection = new Set(selectedPhotos);
    if (newSelection.has(photoId)) {
      newSelection.delete(photoId);
    } else {
      newSelection.add(photoId);
    }
    setSelectedPhotos(newSelection);
  };

  const handleBulkDelete = () => {
    if (selectedAlbum && selectedPhotos.size > 0) {
      selectedPhotos.forEach(photoId => {
        onRemovePhoto(selectedAlbum.id, photoId);
      });
      setSelectedPhotos(new Set());
      setIsSelectionMode(false);
      toast.success(`${selectedPhotos.size} photo${selectedPhotos.size !== 1 ? 's' : ''} deleted`);
    }
  };

  const getPrivacyIcon = (privacy: string) => {
    switch (privacy) {
      case 'public': return Globe;
      case 'friends': return Users;
      case 'private': return Lock;
      default: return Users;
    }
  };

  if (selectedAlbum) {
    const PrivacyIcon = getPrivacyIcon(selectedAlbum.privacy);
    
    return (
      <div className="space-y-6">
        {/* Album Header */}
        <div className="flex items-center justify-between">
          <Button variant="ghost" onClick={() => setSelectedAlbum(null)}>
            ← Back to Albums
          </Button>
          <div className="flex items-center space-x-2">
            {isSelectionMode && selectedPhotos.size > 0 && (
              <>
                <Button variant="destructive" size="sm" onClick={handleBulkDelete}>
                  <Trash2 className="w-4 h-4 mr-2" />
                  Delete ({selectedPhotos.size})
                </Button>
                <Button variant="outline" size="sm" onClick={() => setIsSelectionMode(false)}>
                  Cancel
                </Button>
              </>
            )}
            {!isSelectionMode && (
              <>
                <Button variant="outline" size="sm" onClick={() => setIsSelectionMode(true)}>
                  Select
                </Button>
                <label className="cursor-pointer">
                  <Button size="sm">
                    <Plus className="w-4 h-4 mr-2" />
                    Add Photos
                  </Button>
                  <input
                    type="file"
                    multiple
                    accept="image/*"
                    className="hidden"
                    onChange={(e) => handlePhotoUpload(selectedAlbum.id; e)}
                  />
    </label>
              </>
            )}
          </div>
    </div>
        <Card>
          <CardHeader>
            <div className="flex items-start justify-between">
              <div>
                <CardTitle className="flex items-center space-x-2">
                  <span>{selectedAlbum.title}</span>
                  <PrivacyIcon className="w-4 h-4 text-gray-500" />
    </CardTitle>
                {selectedAlbum.description && (
                  <p className="text-gray-600 mt-2">{selectedAlbum.description}</p>
                )}
                <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                  <span>{selectedAlbum.photos.length} photos</span>
                  <span>Created {format(selectedAlbum.createdAt, 'MMM d, yyyy')}</span>
                  {selectedAlbum.collaborators.length > 0 && (
                    <span>{selectedAlbum.collaborators.length} collaborators</span>
                  )}
                </div>
    </div>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm">
                    <MoreVertical className="w-4 h-4" />
    </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem onClick={() => onShareAlbum(selectedAlbum.id)}>
                    <Share2 className="w-4 h-4 mr-2" />
                    Share Album
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <Edit className="w-4 h-4 mr-2" />
                    Edit Album
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <Download className="w-4 h-4 mr-2" />
                    Download All
                  </DropdownMenuItem>
                  <DropdownMenuItem 
                    className="text-red-600"
                    onClick={() => onDeleteAlbum(selectedAlbum.id)}
                  >
                    <Trash2 className="w-4 h-4 mr-2" />
                    Delete Album
                  </DropdownMenuItem>
    </DropdownMenuContent>
              </DropdownMenu>
    </div>
          </CardHeader>
          <CardContent>
            {selectedAlbum.photos.length === 0 ? (
              <div className="text-center py-12">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Plus className="w-8 h-8 text-gray-400" />
    </div>
                <h3 className="text-lg font-medium mb-2">No photos yet</h3>
                <p className="text-gray-500 mb-4">Start adding photos to your album</p>
                <label className="cursor-pointer">
                  <Button>
                    <Plus className="w-4 h-4 mr-2" />
                    Add Photos
                  </Button>
                  <input
                    type="file"
                    multiple
                    accept="image/*"
                    className="hidden"
                    onChange={(e) => handlePhotoUpload(selectedAlbum.id; e)}
                  />
    </label>
              </div>
            ) : (
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                {selectedAlbum.photos.map((photo) => (
                  <div key={photo.id} className="relative group">
                    <div 
                      className={`relative overflow-hidden rounded-lg cursor-pointer ${
                        isSelectionMode && selectedPhotos.has(photo.id) 
                          ? 'ring-2 ring-blue-500' 
                          : ''
                      }`}, onClick={() => isSelectionMode ? handlePhotoSelect(photo.id) : null}
                    >
                      <img
                        src={photo.thumbnail} alt={photo.caption || 'Photo'}, className="w-full h-48 object-cover transition-transform group-hover:scale-105"
                      />
                      
                      {/* Selection Checkbox */}
                      {isSelectionMode && (
                        <div className="absolute top-2 right-2">
                          <div className={`w-6 h-6 rounded-full border-2 flex items-center justify-center ${
                            selectedPhotos.has(photo.id) 
                              ? 'bg-blue-500 border-blue-500' 
                              : 'bg-white border-gray-300'
                          }`}>
                            {selectedPhotos.has(photo.id) && (
                              <div className="w-2 h-2 bg-white rounded-full" />
                            )}
                          </div>
    </div>
                      )}
                      
                      {/* Photo Overlay */}
                      <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors">
                        <div className="absolute bottom-0 left-0 right-0 p-3 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity">
                          <div className="flex items-center justify-between text-white text-sm">
                            <div className="flex items-center space-x-2">
                              <button 
                                onClick={(e) => {
                                  e.stopPropagation();
                                  onLikePhoto(photo.id);
                                }}, className={`flex items-center space-x-1 ${photo.isLiked ? 'text-red-500' : ''}`}
                              >
                                <Heart className="w-4 h-4" />
                                <span>{photo.likes}</span>
    </button>
                              <div className="flex items-center space-x-1">
                                <MessageCircle className="w-4 h-4" />
                                <span>{photo.comments}</span>
    </div>
                            </div>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm" className="text-white hover:bg-white/20">
                                  <MoreVertical className="w-4 h-4" />
    </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent>
                                <DropdownMenuItem>
                                  <Eye className="w-4 h-4 mr-2" />
                                  View Full Size
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <Download className="w-4 h-4 mr-2" />
                                  Download
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <Share2 className="w-4 h-4 mr-2" />
                                  Share
                                </DropdownMenuItem>
                                <DropdownMenuItem 
                                  className="text-red-600"
                                  onClick={() => onRemovePhoto(selectedAlbum.id; photo.id)}
                                >
                                  <Trash2 className="w-4 h-4 mr-2" />
                                  Remove
                                </DropdownMenuItem>
    </DropdownMenuContent>
                            </DropdownMenu>
    </div>
                        </div>
    </div>
                    </div>
                    
                    {photo.caption && (
                      <p className="text-sm text-gray-600 mt-2 line-clamp-2">{photo.caption}</p>
                    )}
                  </div>
                ))}
              </div>
            )}
          </CardContent>
    </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Photo Albums</h1>
        <Button onClick={() => setIsCreating(true)}>
          <Plus className="w-4 h-4 mr-2" />
          Create Album
        </Button>
    </div>
      {/* Search and Filters */}
      <div className="flex items-center space-x-4">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <Input
            placeholder="Search albums..."
            value={searchQuery} onChange={(e) => setSearchQuery(e.target.value)}, className="pl-10"
          />
    </div>
        <div className="flex items-center space-x-2">
          <Button
            variant={viewMode === 'grid' ? 'default' : 'outline'} size="sm"
            onClick={() => setViewMode('grid')}
          >
            <Grid className="w-4 h-4" />
    </Button>
          <Button
            variant={viewMode === 'list' ? 'default' : 'outline'} size="sm"
            onClick={() => setViewMode('list')}
          >
            <List className="w-4 h-4" />
    </Button>
        </div>
    </div>
      {/* Create Album Modal */}
      {isCreating && (
        <Card>
          <CardHeader>
            <CardTitle>Create New Album</CardTitle>
    </CardHeader>
          <CardContent className="space-y-4">
            <Input
              placeholder="Album title"
              value={newAlbumTitle} onChange={(e) => setNewAlbumTitle(e.target.value)}
            />
            <Input
              placeholder="Description (optional)"
              value={newAlbumDescription} onChange={(e) => setNewAlbumDescription(e.target.value)}
            />
            <div className="flex items-center space-x-2">
              <Button onClick={handleCreateAlbum}>Create</Button>
              <Button variant="outline" onClick={() => setIsCreating(false)}>Cancel</Button>
    </div>
          </CardContent>
    </Card>
      )}

      {/* Albums Grid */}
      <div className={viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6' : 'space-y-4'}>
        {filteredAlbums.map((album) => {
          const PrivacyIcon = getPrivacyIcon(album.privacy);
          
          return (
            <Card 
              key={album.id} className="cursor-pointer hover:shadow-lg transition-shadow"
              onClick={() => setSelectedAlbum(album)}
            >
              <div className="aspect-video relative overflow-hidden rounded-t-lg">
                {album.coverPhoto ? (
                  <img
                    src={album.coverPhoto} alt={album.title}, className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full bg-gray-100 flex items-center justify-center">
                    <div className="text-center">
                      <Plus className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                      <p className="text-sm text-gray-500">No photos</p>
    </div>
                  </div>
                )}
              </div>
              <CardContent className="p-4">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h3 className="font-semibold flex items-center space-x-2">
                      <span>{album.title}</span>
                      <PrivacyIcon className="w-4 h-4 text-gray-500" />
    </h3>
                    {album.description && (
                      <p className="text-sm text-gray-600 mt-1 line-clamp-2">{album.description}</p>
                    )}
                    <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                      <span>{album.photos.length} photos</span>
                      <span>{format(album.updatedAt, 'MMM d')}</span>
    </div>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" onClick={(e) => e.stopPropagation()}>
                        <MoreVertical className="w-4 h-4" />
    </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent>
                      <DropdownMenuItem onClick={() => onShareAlbum(album.id)}>
                        <Share2 className="w-4 h-4 mr-2" />
                        Share
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Edit className="w-4 h-4 mr-2" />
                        Edit
                      </DropdownMenuItem>
                      <DropdownMenuItem 
                        className="text-red-600"
                        onClick={() => onDeleteAlbum(album.id)}
                      >
                        <Trash2 className="w-4 h-4 mr-2" />
                        Delete
                      </DropdownMenuItem>
    </DropdownMenuContent>
                  </DropdownMenu>
    </div>
              </CardContent>
    </Card>
          );
        })}
      </div>

      {filteredAlbums.length === 0 && (
        <div className="text-center py-12">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Plus className="w-8 h-8 text-gray-400" />
    </div>
          <h3 className="text-lg font-medium mb-2">No albums found</h3>
          <p className="text-gray-500 mb-4">
            {searchQuery ? 'Try a different search term' : 'Create your first photo album'}
          </p>
          <Button onClick={() => setIsCreating(true)}>
            <Plus className="w-4 h-4 mr-2" />
            Create Album
          </Button>
    </div>
      )}
    </div>
  );
};

export default PhotoAlbumManager;
