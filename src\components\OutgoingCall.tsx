import React, { useState, useEffect } from 'react';
import {
  Phone,
  PhoneOff,
  Video,
  Clock
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui';
import { Badge } from '@/components/ui/badge';
import { motion } from 'framer-motion';
import type { OutgoingCallProps } from '@/types/call';

export const OutgoingCall: React.FC<OutgoingCallProps> = ({
  recipientName,
  recipientId,
  callType,
  onCancel
}) => {
  const [timeElapsed, setTimeElapsed] = useState(0);
  const [callStatus, setCallStatus] = useState<'calling' | 'connecting' | 'failed'>('calling');

  useEffect(() => {
    const interval = setInterval(() => {
      setTimeElapsed(prev => prev + 1);
      
      // Auto-cancel after 30 seconds
      if (timeElapsed >= 30) {
        setCallStatus('failed');
        setTimeout(() => {
          onCancel();
        }, 2000);
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [timeElapsed, onCancel]);

  const formatTimeElapsed = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const getStatusText = () => {
    switch (callStatus) {
      case 'calling':
        return 'Calling...';
      case 'connecting':
        return 'Connecting...';
      case 'failed':
        return 'Call failed';
      default:
        return 'Calling...';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}, animate={{ opacity: 1, scale: 1 }}, exit={{ opacity: 0, scale: 0.9 }}, className="fixed inset-0 z-50 flex items-center justify-center bg-black/80 backdrop-blur-sm"
    >
      <Card className="w-96 bg-gradient-to-br from-gray-800 to-gray-900 text-white border-0 shadow-2xl">
        <CardContent className="p-8 text-center">
          {/* Recipient Avatar */}
          <motion.div
            animate={{ 
              scale: callStatus === 'calling' ? [1, 1.05, 1] : 1,
              rotate: callStatus === 'connecting' ? 360 : 0
            }}, transition={{ 
              duration: callStatus === 'calling' ? 2 : 1, 
              repeat: callStatus === 'calling' ? Infinity : 0 
            }}, className="mb-6"
          >
            <Avatar className="w-24 h-24 mx-auto border-4 border-white/20">
              <AvatarImage src={`https://api.dicebear.com/7.x/avataaars/svg?seed=${recipientId}`} />
              <AvatarFallback className="text-2xl bg-white/20">
                {recipientName[0]}
              </AvatarFallback>
    </Avatar>
          </motion.div>

          {/* Recipient Info */}
          <div className="mb-6">
            <h2 className="text-2xl font-bold mb-2">{recipientName}</h2>
            <div className="flex items-center justify-center space-x-2 mb-2">
              {callType === 'video' ? (
                <Video className="w-5 h-5" />
              ) : (
                <Phone className="w-5 h-5" />
              )}
              <span className="text-lg">{getStatusText()}</span>
    </div>
            <div className="flex items-center justify-center space-x-2 text-white/80">
              <Clock className="w-4 h-4" />
              <span className="text-sm">{formatTimeElapsed(timeElapsed)}</span>
    </div>
          </div>

          {/* Status Indicator */}
          <div className="mb-6">
            <Badge 
              variant="secondary" 
              className={`${
                callStatus === 'failed' 
                  ? 'bg-red-500/20 text-red-300 border-red-500/30'
                  : 'bg-blue-500/20 text-blue-300 border-blue-500/30'
              }`}
            >
              {callStatus === 'calling' && (
                <motion.div
                  animate={{ opacity: [1, 0.5, 1] }}, transition={{ duration: 1, repeat: Infinity }}, className="flex items-center"
                >
                  <div className="w-2 h-2 bg-current rounded-full mr-2" />
                  {callType === 'video' ? 'Video Call' : 'Voice Call'}
                </motion.div>
              )}
              {callStatus === 'connecting' && 'Connecting...'}
              {callStatus === 'failed' && 'Call Failed'}
            </Badge>
    </div>
          {/* Cancel Button */}
          <motion.div
            whileHover={{ scale: 1.1 }}, whileTap={{ scale: 0.9 }}
          >
            <Button
              variant="destructive"
              size="lg"
              onClick={onCancel} className="rounded-full w-16 h-16 bg-red-500 hover:bg-red-600 border-0 shadow-lg"
            >
              <PhoneOff className="w-6 h-6" />
    </Button>
          </motion.div>

          {/* Additional Info */}
          <div className="mt-6 text-sm text-white/60">
            {callStatus === 'calling' && (
              <p>Waiting for {recipientName} to answer...</p>
            )}
            {callStatus === 'failed' && (
              <p>Unable to connect. Please try again.</p>
            )}
          </div>
    </CardContent>
      </Card>
    </motion.div>
  );
};

export default OutgoingCall;
